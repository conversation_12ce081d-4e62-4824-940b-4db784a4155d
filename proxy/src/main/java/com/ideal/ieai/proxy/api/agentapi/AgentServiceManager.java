package com.ideal.ieai.proxy.api.agentapi;

import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.log4j.Logger;

import com.ideal.ieai.proxy.api.ProxyWSClient;
import com.ideal.ieai.proxy.webservice.service.ActivityExecRequest;
import com.opensymphony.oscache.base.Cache;
import com.opensymphony.oscache.base.NeedsRefreshException;

/**
 * Class AgentServiceManager
 * To manage all webservice connections to iEAI servers that has requests sent to current agent.
 * Since multiple iEAI Servers connecting to one agent is possible( theoretically ), we need to 
 * maintain a map from server IP:port to web service client.
 * A cache is used to dispose client when it has been idle long enough 
 * <AUTHOR>
 *
 */
public class AgentServiceManager
{
    static public final AgentServiceManager getInstance ()
    {
        return _inst;
    }

    private AgentServiceManager()
    {
        _servicesCache = new Cache(true, false);
        _servicesCache.setCapacity(10);// cache 10 iEAI Service connection the most
        _servicesProxyCache = new Cache(true, false);
        _servicesProxyCache.setCapacity(10);// cache 10 iEAI Service connection the most
    }

    public AgentWSClient getAgentService ( final ActivityExecRequest Req )
    {
        return getAgentService(Req.serverHost, Req.serverPort);
    }

    /**
     * Get webservice connects to the specified host and port.
     * It trys to get from cache. If not found in cache, create one and put in to cache.
     * @param ServerIP
     * @param ServerPort
     * @return
     */
    public AgentWSClient getAgentService ( final String ServerIP, final int ServerPort )
    {
        String ServerIp = renderServerip(ServerIP, ServerPort);
        String key = ServerIp + ':' + ServerPort;
        try
        {
            AgentWSClient ret = (AgentWSClient) _servicesCache.getFromCache(key, WS_REFRESH_TIME);
            if (null == ret)
            {
                ret = createAgentClient(ServerIp, ServerPort);
                _servicesCache.putInCache(key, ret);
                return ret;
            }
            return ret;
        } catch (NeedsRefreshException e)
        {
            // need to refresh this web service
            AgentWSClient ret = createAgentClient(ServerIp, ServerPort);
            _servicesCache.putInCache(key, ret);
            return ret;
        }
    }

    private AgentWSClient createAgentClient ( final String ServerIP, final int ServerPort )
    {
        return new AgentWSClient(ServerIP, ServerPort);
    }

    /**
     * 
     * @Title: renderServerip   
     * @Description: (将serveripList进行逐个校验，如果有生效的ip，则使用此ip进行通信)         
     * @author: yunpeng_zhang 
     * @date:   2017年9月12日 下午3:28:00
     */
    public String renderServerip ( String serverIp, int serverPort )
    {
        String[] serverHostList = serverIp.split(",");
        if (serverHostList != null && serverHostList.length > 1)
        {
            for (String serverHost : serverHostList)
            {
                if (urlIsReach("http://" + serverHost + ":" + serverPort + "/aoms/"))
                {
                    return serverHost;
                } else
                {
                    _log.info("IP(" + serverHost + ")请求不通!");
                }
            }
        }
        return serverIp;
    }

    public boolean urlIsReach ( String url )
    {
        if (url == null)
        {
            return false;
        }
        try
        {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setConnectTimeout(1000);// 连接超时
            if (HttpURLConnection.HTTP_OK == connection.getResponseCode())
            {
                return true;
            }
        } catch (Exception e)
        {
            return false;
        }
        return false;
    }

    private Cache                            _servicesCache;
    private Cache                            _servicesProxyCache;
    static final private AgentServiceManager _inst           = new AgentServiceManager();
    /**
     * Refresh webservice connection every 4 hours.
     */
    static final private int                 WS_REFRESH_TIME = 4 * 3600 * 1000;

    private static final Logger              _log            = Logger.getLogger(AgentServiceManager.class);

    public ProxyWSClient getProxyService ( final ActivityExecRequest Req )
    {
        return getProxyService(Req.serverHost, Req.serverPort);
    }

    /**
     * Get webservice connects to the specified host and port.
     * It trys to get from cache. If not found in cache, create one and put in to cache.
     * @param ServerIP
     * @param ServerPort
     * @return
     */
    public ProxyWSClient getProxyService ( final String ServerIP, final int ServerPort )
    {
        String ServerIp = renderServerip(ServerIP, ServerPort);
        String key = ServerIp + ':' + ServerPort;
        try
        {
            ProxyWSClient ret = (ProxyWSClient) _servicesProxyCache.getFromCache(key, WS_REFRESH_TIME);
            if (null == ret)
            {
                ret = createProxyClient(ServerIp, ServerPort);
                _servicesProxyCache.putInCache(key, ret);
                return ret;
            }
            return ret;
        } catch (NeedsRefreshException e)
        {
            // need to refresh this web service
            ProxyWSClient ret = createProxyClient(ServerIp, ServerPort);
            _servicesProxyCache.putInCache(key, ret);
            return ret;
        }
    }

    private ProxyWSClient createProxyClient ( final String ServerIP, final int ServerPort )
    {
        return new ProxyWSClient(ServerIP, ServerPort);
    }

}

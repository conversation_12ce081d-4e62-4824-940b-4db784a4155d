package com.ideal.ieai.agent.healthcheck.util;

import java.util.Hashtable;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.ideal.dubbo.interfaces.IScriptInstance;
import com.ideal.ieai.agent.AgentEnv;
import com.ideal.ieai.agent.IEAIRemoteAgent;
import com.ideal.ieai.commons.Constants;

public class SendScriptServerUtil
{
    
    private static final Logger log = Logger.getLogger(SendScriptServerUtil.class);
    public  static boolean sendToScriptServer ( String reqid, Hashtable table)
    {
        boolean send = false;
        Map res = null;
        try
        {
            ClassPathXmlApplicationContext context = IEAIRemoteAgent.getInstance().getContext();
            if (context.containsBeanDefinition(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME))
            {
                log.info("scriptInstance name is "
                        + context.containsBeanDefinition(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME));
            }
            if (context.containsBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME))
            {
                IScriptInstance instance = (IScriptInstance) context
                        .getBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME);
                res = instance.updateResultService(reqid, table, 2);
            }
            if(res==null) {
                log.error("发送新巡检结未收到返回消息");
            }else if(res.get("success")==null) {
                log.error("发送新巡检结未收到具体接收成功或者失败标识");
            }else if(!(Boolean)res.get("success")) {
                log.error("发送新巡检结果收到接收方返回消息为存储失败；原因："+res.get("message"));
            }else {
                log.info("巡检结果成功发送并存储；");
                send = true;
            }
        } catch (Exception e)
        {
            log.error("sendToServer error :" + e.getMessage(), e);
        }
        return send;
    }
    
    
    public  static boolean sendToScriptServerOfProv ( String reqid,String providerinstance, Hashtable table)
    {
        boolean send = false;
        Map res = null;
        try
        {
            ClassPathXmlApplicationContext context = IEAIRemoteAgent.getInstance().getContext();
            if (context.containsBeanDefinition(providerinstance))
            {
                log.info("scriptInstance name is "
                        + context.containsBeanDefinition(providerinstance));
            }
            if (context.containsBean(providerinstance))
            {
                IScriptInstance instance = (IScriptInstance) context
                        .getBean(providerinstance);
                res = instance.updateResultService(reqid, table, 2);
            }
            if(res==null) {
                log.error("发送新巡检结未收到返回消息");
            }else if(res.get("success")==null) {
                log.error("发送新巡检结未收到具体接收成功或者失败标识");
            }else if(!(Boolean)res.get("success")) {
                log.error("发送新巡检结果收到接收方返回消息为存储失败；原因："+res.get("message"));
            }else {
                log.info("巡检结果成功发送并存储；");
                send = true;
            }
        } catch (Exception e)
        {
            log.error("sendToServer error :" + e.getMessage(), e);
        }
        return send;
    }
    
    
    public  static boolean sendToScriptServerForPullHc ( String reqid, Hashtable table,String providerInstance)
    {
        Map res = null;
        boolean send = false;
        try
        {
            ClassPathXmlApplicationContext context = IEAIRemoteAgent.getInstance().getContext();
            if (context.containsBean(providerInstance))
            {
                IScriptInstance instance = (IScriptInstance) context.getBean(providerInstance);
                res = instance.updateResultService(reqid, table, 2);
                send = true;
            }
        } catch (Exception e)
        {
            log.error("sendToScriptServerForPullHc error :" + e.getMessage(), e);
            send = false;
        }
        return send;
    }
}

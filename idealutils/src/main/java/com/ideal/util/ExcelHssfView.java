package com.ideal.util;

import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.displaytag.Messages;
import org.displaytag.exception.BaseNestableJspTagException;
import org.displaytag.exception.SeverityEnum;
import org.displaytag.export.BinaryExportView;
import org.displaytag.model.*;

import javax.servlet.jsp.JspException;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Excel exporter using POI HSSF.
 * 
 * <AUTHOR> Giustina
 * @version $Revision: 3 $ ($Author: Fgiust $)
 */
public class ExcelHssfView implements BinaryExportView
{

    /**
     * TableModel to render.
     */
    private TableModel model;

    /**
     * export full list?
     */
    private boolean    exportFull;

    /**
     * include header in export?
     */
    private boolean    header;

    /**
     * decorate export?
     */
    private boolean    decorated;

    /**
     * Generated sheet.
     */
    private HSSFSheet  sheet;

    /**
     * @see org.displaytag.export.ExportView#setParameters(TableModel, boolean,
     *      boolean, boolean)
     */
    public void setParameters ( TableModel tableModel, boolean exportFullList,
            boolean includeHeader, boolean decorateValues )
    {
        this.model = tableModel;
        this.exportFull = exportFullList;
        this.header = includeHeader;
        this.decorated = decorateValues;
    }

    /**
     * @see org.displaytag.export.BaseExportView#getMimeType()
     * @return "application/vnd.ms-excel"
     */
    public String getMimeType ()
    {
        return "application/vnd.ms-excel" + ";charset=utf-16"; //$NON-NLS-1$
    }
    
    /**
     * filter all html element. 
     * For example:<a href="www.sohu.com/test">hello!</a>
     * The filter result is :hello!
     * Notice:This method filter the text between "<" and ">"
     * @param element
     * @return
     */
    public static String getTxtWithoutHTMLElement (String element)
    {
//      String reg="<[^<|^>]+>";
//      return  element.replaceAll(reg,"");
        
        if(null==element||"".equals(element.trim()))
        {
            return element;
        }

        Pattern pattern=Pattern.compile("<[^<|^>]*>");
        Matcher matcher=pattern.matcher(element);
        StringBuffer txt=new StringBuffer();
        while(matcher.find())
        {
            String group=matcher.group();
            if(group.matches("<[\\s]*>"))
            {
                matcher.appendReplacement(txt,group);    
            }
            else
            {
                matcher.appendReplacement(txt,"");
            }
        }
        matcher.appendTail(txt);
        repaceEntities(txt,"&amp;","&");
        repaceEntities(txt,"&lt;","<");        
        repaceEntities(txt,"&gt;",">");
        repaceEntities(txt,"&quot;","\"");
        repaceEntities(txt,"&nbsp;","");
        
        return txt.toString();
    }

    private static void repaceEntities ( StringBuffer txt,String entity,String replace)
    {
        int pos=-1;
        while(-1!=(pos=txt.indexOf(entity)))
        {
            txt.replace(pos,pos+entity.length(),replace);
        }
    }

    /**
     * @see org.displaytag.export.BinaryExportView#doExport(OutputStream)
     */
    public void doExport ( OutputStream out ) throws JspException
    {
        try
        {
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet("-");

            int rowNum = 0;
            int colNum = 0;

            if (this.header)
            {
                // Create an header row
                HSSFRow xlsRow = sheet.createRow(rowNum++);

                HSSFCellStyle headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(FillPatternType.FINE_DOTS);
                headerStyle.setFillBackgroundColor(IndexedColors.BLUE_GREY.getIndex());
                HSSFFont bold = wb.createFont();
                bold.setBold(true);
                bold.setColor(IndexedColors.WHITE.getIndex());
                headerStyle.setFont(bold);

                Iterator iterator = this.model.getHeaderCellList().iterator();

                while (iterator.hasNext())
                {
                    HeaderCell headerCell = (HeaderCell) iterator.next();

                    String columnHeader = headerCell.getTitle();

                    if (columnHeader == null)
                    {
                        columnHeader = StringUtils.capitalize(headerCell
                                .getBeanPropertyName());
                    }

                    HSSFCell cell = xlsRow.createCell((short) colNum++);
                    cell.setCellValue(columnHeader);
                    cell.setCellStyle(headerStyle);
                }
            }

            // get the correct iterator (full or partial list according to the
            // exportFull field)
            RowIterator rowIterator = this.model
                    .getRowIterator(this.exportFull);
            // iterator on rows

            while (rowIterator.hasNext())
            {
                Row row = rowIterator.next();
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                colNum = 0;

                // iterator on columns
                ColumnIterator columnIterator = row
                        .getColumnIterator(this.model.getHeaderCellList());

                while (columnIterator.hasNext())
                {
                    Column column = columnIterator.nextColumn();

                    // Get the value to be displayed for the column
                    Object value = column.getValue(this.decorated);

                    HSSFCell cell = xlsRow.createCell((short) colNum++);

                    if (value instanceof Number)
                    {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else if (value instanceof Date)
                    {
                        cell.setCellValue((Date) value);
                    } else if (value instanceof Calendar)
                    {
                        cell.setCellValue((Calendar) value);
                    } else
                    {
                        String result=StringUtils.trimToEmpty(ObjectUtils.toString(value));
                        cell.setCellValue(getTxtWithoutHTMLElement(result));
                    }

                }
            }

            wb.write(out);

        } catch (Exception e)
        {
            throw new ExcelGenerationException(e);
        }
    }

    /**
     * Wraps IText-generated exceptions.
     * 
     * <AUTHOR> Giustina
     * @version $Revision: 3 $ ($Author: Fgiust $)
     */
    static class ExcelGenerationException extends BaseNestableJspTagException
    {

        /**
         * D1597A17A6.
         */
        private static final long serialVersionUID = 899149338534L;

        /**
         * Instantiate a new PdfGenerationException with a fixed message and the
         * given cause.
         * 
         * @param cause
         *            Previous exception
         */
        public ExcelGenerationException(Throwable cause)
        {
            super(ExcelHssfView.class, Messages
                    .getString("PdfView.errorexporting"), cause); //$NON-NLS-1$
        }

        /**
         * @see org.displaytag.exception.BaseNestableJspTagException#getSeverity()
         */
        public SeverityEnum getSeverity ()
        {
            return SeverityEnum.ERROR;
        }
    }
}

--4.7.18
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER' AND COLNAME = 'ICREATETIME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_USER ADD COLUMN ICREATETIME NUMERIC(19)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$
--4.7.19
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_OPERATION_STATUS';
	    IF LI_EXISTS = 1 THEN
	    SET LS_SQL ='DROP TABLE IEAI_COLLECT_OPERATION_STATUS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	    END IF;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESGROUP_MODEL';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_RESGROUP_MODEL(IID NUMERIC(19) NOT NULL,IGROUPMESID  NUMERIC(19),IISTRUE INTEGER,CONSTRAINT PK_IEAI_RESGROUP_MODEL PRIMARY KEY(IID))';
                   PREPARE      SQLA FROM LS_SQL;
                   EXECUTE SQLA;
        END     IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_OPERATION_STATUS';
	    IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_COLLECT_OPERATION_STATUS(IID DECIMAL(15) NOT NULL,  ISTATUS smallint,  CONSTRAINT  PK_IEAI_COLLECT_OPE_STATUS PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	    END IF;
	    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AUDIT_CLOB';
	    IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_AUDIT_CLOB ( AUDITID DECIMAL(19,0) NOT NULL, ICONTENT	CLOB(1048576), CONSTRAINT PK_IEAI_AUDIT_CLOB PRIMARY KEY(AUDITID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	    END IF;

END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ROLE' AND COLNAME = 'ICREATETIME';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL = 'ALTER TABLE IEAI_ROLE ADD COLUMN ICREATETIME NUMERIC(19)';
                   PREPARE      SQLA FROM LS_SQL;
                   EXECUTE SQLA;
        END     IF;

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_RESGROUP_MODEL WHERE IID = -1;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_RESGROUP_MODEL (IID,IGROUPMESID,IISTRUE) VALUES(-1,3,1)';
                        PREPARE SQLA FROM LS_SQL;
                        EXECUTE SQLA;
                        commit;
        END IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_COLLECT_OPERATION_STATUS WHERE  IID=10;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_COLLECT_OPERATION_STATUS(IID,ISTATUS) VALUES(10, 0)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=15;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info6666.png'' where GROUPID=15';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=100;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info85.png'' where GROUPID=100';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=10;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info6667.png'' where GROUPID=10';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
END
$
--4.7.20

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTINFO_DELETE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_AGENTINFO_DELETE(IAGENTINFO_ID DECIMAL(19) NOT NULL,IEQUIPMENT_OR_VM_ID DECIMAL(19),IAGENT_NAME VARCHAR(255),IAGENT_DES VARCHAR(255),IAGENT_IP VARCHAR(255) NOT NULL,IAGENT_PORT DECIMAL(19),ICLUSTER_ID DECIMAL(19),IDELETE_FLAG DECIMAL(1) DEFAULT 0,IOS_NAME VARCHAR(255),ICOM_NAME VARCHAR(255),ISTART_USER VARCHAR(255),IAGENT_STATE DECIMAL(1),IAGENT_VERSION VARCHAR(100),IAGENT_ACTIVITIES CLOB,IAGENT_ACTIVITY_NUM DECIMAL(19),IAGENTUP_ID DECIMAL(19),IPALIAS DECIMAL(12),IENV_TYPE DECIMAL(2) DEFAULT 1,IAGENT_CKSTATE DECIMAL(1),IAGENT_CKTIME DECIMAL(19),IAGENT_CCHANGE DECIMAL(1),JOURNAL CLOB,IAGENT_IF_SENDMSG DECIMAL(1) DEFAULT 1,IACTNUM DECIMAL(10),IACTNUMMAX DECIMAL(10),ICPUVALUE VARCHAR(255),IMEMVALUE VARCHAR(255),ICHECKTIME VARCHAR(255),ICUSTOM_CMD VARCHAR(1000),ICUSTOM_MESS CLOB,ICREATETIME DECIMAL(19),ICREATEUSER DECIMAL(19),ISSUED DECIMAL(1) DEFAULT 0,IDELETEUSER VARCHAR(255),IDELETETIME DECIMAL(19),IDELETEDESC VARCHAR(255),CONSTRAINT PK_IEAI_AGENTINFO_DELETE PRIMARY KEY(IAGENTINFO_ID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_DOUBLECHECK_COLVALUE' AND INDNAME='IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID)';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQU_GROUP_BUSINESS';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EQU_GROUP_BUSINESS (IID DECIMAL(19,0) NOT NULL,EQUGROUPID DECIMAL(19,0),BUSINESSID DECIMAL(19,0),BUSINESSNAME VARCHAR(255),PROTYPE DECIMAL(5,0) ,CONSTRAINT PK_IEAI_EQU_GROUP_BUSINESS PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=145;
	IF LI_EXISTS = 1 THEN
	SET LS_SQL ='UPDATE IEAI_HIGHOPER SET IBUTTONURL=''deleteFormationByCP.do'' WHERE IBUTTONID=145';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=146;
	IF LI_EXISTS = 1 THEN
	SET LS_SQL ='UPDATE IEAI_HIGHOPER SET IBUTTONURL=''saveFormationByCP.do'' WHERE IBUTTONID=146';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
	END IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EXCEL_UT_REVIEW';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_EXCEL_UT_REVIEW(IID DECIMAL(19,0) NOT NULL,IFLOWID DECIMAL(19,0),IACTID DECIMAL(19,0),IACTNAME VARCHAR(255),IACYTYPE DECIMAL(2),IUSERNAME VARCHAR(255),IUSERID DECIMAL(19,0),IREVIEWTAKEOVERTIME DECIMAL(19,0),IREVIEWTIME DECIMAL(19,0),IREVIEWFINISHTIME DECIMAL(19,0),CONSTRAINT PK_IEAI_EXCEL_UT_REVIEW PRIMARY KEY(IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	
	END
	$
--4.7.21
    BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=16;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info171.png'' where GROUPID=16';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE LI_EXISTS  NUMERIC(2);

        
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 20 AND IGROUPMESSGEID=20;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (20,20,''指令操作维护源'',0)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
	   SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_COLLECT_ITEM WHERE IID = -100 ;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_COLLECT_ITEM(IID, NAME, ITEM, WIN, LINUX, AIX, INFO, ISET)  VALUES(-100, ''主动采集对比'', '' '', '' '', '' '', '' '', '' '', 1)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-20;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADNUM, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-20, 0, ''所有指令操作业务系统'', 0, 0, 0, 0, 0, 0, 20, -20, -20, -20)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INUMBERS' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN INUMBERS VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INUMBERS' AND TABNAME='IEAI_AGENTINFO_DELETE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO_DELETE ADD COLUMN INUMBERS VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$
--4.7.22
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_AGENTINFO_CZ';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_CMDB_AGENTINFO_CZ (IID DECIMAL(19) NOT NULL,IHOSTNAME	VARCHAR(255),IIP	VARCHAR(255),IMAC	VARCHAR(255),IMEMSIZE	VARCHAR(255),IDISKSIZE	VARCHAR(255),IOSSYSTEM	VARCHAR(255),IOSARCHITECTURE	VARCHAR(255),IOSVERSION	VARCHAR(255),IOSDISTRO	VARCHAR(255),IOSRELEASE	VARCHAR(255),IINSTANCEID	VARCHAR(255),IAGENTSTATUS	VARCHAR(255),ICPUMODEL	VARCHAR(255),CONSTRAINT PK_CMDB_AGENTINFO_CZ PRIMARY KEY (IID)) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_ROLE_RELATION';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_USER_ROLE_RELATION(IID  DECIMAL(19) NOT NULL,  IUSERID  DECIMAL(19),  IROLEID  DECIMAL(19),  IVALIDSTARTTIME DECIMAL(19),  IVALIDENDTIME   DECIMAL(19),  IOPERUSERID     DECIMAL(19),  IOPERTIME   DECIMAL(19),  CONSTRAINT PK_IEAI_USER_ROLE_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IREVIEWSTATE INTEGER default 1 ' ;
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IREVIEWSTATE INTEGER default 1 ';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IREVIEWSTATE INTEGER default 1 ';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPWDFORAPPLY' AND TABNAME='IEAI_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN IPWDFORAPPLY VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		  

END
$
--4.7.23
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBASEPASSWORD' AND TABNAME='IEAI_USER';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN IBASEPASSWORD VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT	COUNT(IID) INTO LI_EXISTS FROM  IEAI_PROPERTYCONFIG P WHERE P.IPROPERTYNAME='AutoChangePublicPasswordTime';
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROPERTYCONFIG(IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES(2,''AutoChangePublicPasswordTime'',''30'',''浦发自动变更public密码署时长设置'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAPPNAME' AND TABNAME='IEAI_CMDB_AGENTINFO_CZ';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD IAPPNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA; 
		END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISAPP' AND TABNAME='IEAI_MENU';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_MENU ADD COLUMN IISAPP INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA; 
	END IF;	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_GRID_INFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_GRID_INFO(IID DECIMAL(19) NOT NULL ,IGRIDNAME VARCHAR(255) ,ICOLNAME VARCHAR(255) ,IATTRIBUTE VARCHAR(255) ,IATTRVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_GRID_INFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$
--4.7.24
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
   
        
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_IPMPSERVICEINFO';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_IPMPSERVICEINFO(  IID DECIMAL(19,0) NOT NULL,  IRESOURCENAME VARCHAR(255),  IURL          VARCHAR(1000),  IUSERNAME     VARCHAR(255),  IPASSWORD     VARCHAR(255),  ISCOMMON      DECIMAL(2) DEFAULT 0,  ICREATEUSERID DECIMAL(19,0),  IUPDATEUSERID DECIMAL(19,0),  ICREATETIME   TIMESTAMP(6),  IUPDATETIME   TIMESTAMP(6) DEFAULT CURRENT TIMESTAMP,  IDELETE       DECIMAL(2) DEFAULT 0,  CONSTRAINT PK_IEAI_IPMPSERVICEINFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROXY_LIST' AND COLNAME = 'ISTATE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_PROXY_LIST ADD ISTATE DECIMAL (2, 0)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SHOW_SERVERINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SHOW_SERVERINFO(IID DECIMAL(19) NOT NULL ,ILISTID DECIMAL(19) ,ICPU VARCHAR(255) ,IMEMORY VARCHAR(255) ,IDISK VARCHAR(255) ,ITASKNUM DECIMAL(19) ,ICREATETIME TIMESTAMP(6) ,CONSTRAINT PK_IEAI_SHOW_SERVERINFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SHOW_SERVERLIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SHOW_SERVERLIST(IID DECIMAL(19) NOT NULL ,IIP VARCHAR(255) ,IDESC VARCHAR(255) ,ISTARTTIME DECIMAL(19) ,ICREATETIME DECIMAL(19) ,CONSTRAINT PK_IEAI_SHOW_SERVERLIST PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_PORTAL_MIDDLE';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_USER_PORTAL_MIDDLE(IID DECIMAL(19) NOT NULL ,USERID DECIMAL(19) NOT NULL ,PROTALID DECIMAL(19) NOT NULL ,IROW DECIMAL(2) NOT NULL ,ICOLUMN DECIMAL(3) NOT NULL ,POSITION DECIMAL(1) DEFAULT 1 ,CONSTRAINT PK_IEAI_USER_PORTAL_MIDDLE PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORTAL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_PORTAL(IID DECIMAL(19) NOT NULL ,TITLE VARCHAR(225) NOT NULL ,NAME VARCHAR(225) NOT NULL ,DATAURL VARCHAR(100) ,FIELDURL VARCHAR(100) ,ITYPE DECIMAL(2) NOT NULL ,DES VARCHAR(225) ,CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PORTALCLO' AND TABNAME='IEAI_USER';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN PORTALCLO DECIMAL(1)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IEFFECT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IEFFECT DECIMAL(1) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_DETAIL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE TABLE IEAI_AGENT_DETAIL  ( IID DECIMAL(19,0) NOT NULL,AGENTIP    	VARCHAR(255),AGENTPORT  	DECIMAL(19,0),ICREATETIME	DECIMAL(19,0),AGENTDIR   	VARCHAR(255),DISKSIZE   	VARCHAR(255),LOGLOCATION	VARCHAR(255),CPURATE    	VARCHAR(255),MEMORYRATE 	VARCHAR(255),IOMESS     	VARCHAR(255),CONSTRAINT PK_IEAI_AGENT_DETAIL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTSYSTEM_PERSONINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENTSYSTEM_PERSONINFO(IID DECIMAL(19) NOT NULL ,ISYSNAME VARCHAR(255) ,IPERSONNAME VARCHAR(255) ,IEMAIL VARCHAR(255) ,IPHONE VARCHAR(255) ,CONSTRAINT PK_IEAI_AGENTSYSTEM_PERSONINFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	

END
$
--4.7.25
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_ENTITY_MODEL(IID DECIMAL(19) NOT NULL,ENAME VARCHAR(255),EID VARCHAR(255),ESYSCODING VARCHAR(255),EPARCODING VARCHAR(255),EDESC VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_ENTITY_MODEL PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_EQUIPMENT(IID DECIMAL(19) NOT NULL,EID VARCHAR(255),EIUPDTIME VARCHAR(50),ESYSCODING VARCHAR(255),EIROOTMARK DECIMAL(19),EIVERSION DECIMAL(10),EISMALLVERSION DECIMAL(10),EIDESC VARCHAR(255),EISTATUS DECIMAL(4),EIINSDATE VARCHAR(50),EIIP VARCHAR(18),EINAME VARCHAR(255),EIID DECIMAL(19),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUIPMENT PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_EQUI_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_EQUI_RELATION(IID DECIMAL(19) NOT NULL,RLCODE VARCHAR(255),RLCATEGORY DECIMAL(5),FROMEID VARCHAR(255),TOEID VARCHAR(255),RLDESC VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUI_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_FINAL_RELATION(IID DECIMAL(19) NOT NULL,RLID DECIMAL(19),RLCODE VARCHAR(255),TOEIID DECIMAL(19),RLCATEGORY DECIMAL(4),FROMEIID DECIMAL(19),UPDATETIME VARCHAR(50),EIROOMARK VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_FINAL_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_SYS_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_SYS_RELATION(IID DECIMAL(19) NOT NULL,CMDBEIID DECIMAL(19),IPRJUPPERID DECIMAL(19),IUPDATETIME DECIMAL(19),IOPERUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_SYS_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
END
	 $
	 
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUI_RELATION' AND INDNAME='IDX_CMDB_IG_EQUI_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL' AND INDNAME='IDX_CMDB_IG_ENTITY_MODEL_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL' AND INDNAME='IDX_CMDB_IG_ENTITY_MODEL_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_03';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_SYS_RELATION' AND INDNAME='IDX_CMDB_IG_SYS_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_03';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT (EIID, EIIP, EINAME)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
END
	 $

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SEGMENT' AND COLNAME = 'IFUNCTIONALTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SEGMENT ADD IFUNCTIONALTYPE INTEGER WITH DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_INACRIVE_NUM' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO  ADD IAGENT_INACRIVE_NUM  DECIMAL(19) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_CLASSIFY';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_CLASSIFY(IID DECIMAL(19,0) NOT NULL, ICLASSNAME VARCHAR(255),  DES VARCHAR(1000),  CONSTRAINT PK_IEAI_COLLECTION_CLASSIFY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISPARAMETER' AND TABNAME='IEAI_COLLECT_ITEM';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_COLLECT_ITEM ADD COLUMN ISPARAMETER DECIMAL(2) DEFAULT 1';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IREMARK' AND TABNAME='IEAI_COLLECT_DICTIONARY';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_DICTIONARY ADD COLUMN IREMARK VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CLASSID' AND TABNAME='IEAI_COLLECT_DICTIONARY';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_DICTIONARY ADD COLUMN CLASSID DECIMAL(2) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


END
$



--4.7.26
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_MODULE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_MODULE(IMODULEID DECIMAL(19) NOT NULL ,IMODULECODE VARCHAR(30) ,IMODULENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_MODULE PRIMARY KEY (IMODULEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_TYPE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_TYPE(ITYPEID DECIMAL(19) NOT NULL ,ITYPECODE VARCHAR(30) ,ITYPENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_TYPE PRIMARY KEY (ITYPEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_LEVEL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_LEVEL(ILEVELID DECIMAL(19) NOT NULL ,ILEVELCODE VARCHAR(30) ,ILEVELNAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_LEVEL PRIMARY KEY (ILEVELID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_SCENE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_WARN_SCENE(ISCENEID DECIMAL(19) NOT NULL ,IMODULECODE VARCHAR(30) ,ITYPECODE VARCHAR(30) ,ILEVELCODE VARCHAR(30) ,ISTATUS DECIMAL(1) DEFAULT 0,CONSTRAINT PK_IEAI_WARN_SCENE PRIMARY KEY (ISCENEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_WARN( IWARNID DECIMAL(19) NOT NULL, IMODULECODE VARCHAR(30),ITYPECODE VARCHAR(30), ILEVELCODE VARCHAR(30), IIP VARCHAR(30), IHAPPENTIME TIMESTAMP, IWARNMSG VARCHAR(4000), CREATETIME TIMESTAMP, CONSTRAINT PK_IEAI_WARN PRIMARY KEY (IWARNID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
        
        
        
        
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL');
		commit;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DATAURL' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN DATAURL  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF; 
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FIELDURL' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN FIELDURL  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF; 
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITYPE' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN ITYPE  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL');
			commit;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CUSTOMCLASS' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ADD COLUMN CUSTOMCLASS VARCHAR(225)';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL'); 
			commit;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_QUICK_MIDDLE';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_USER_QUICK_MIDDLE(IID DECIMAL(19) NOT NULL ,QUICKID DECIMAL(19) ,USERID DECIMAL(19),ISNODEF DECIMAL(2) ,ICON VARCHAR(255),CONSTRAINT PK_IEAI_USER_QUICK_MIDDLE PRIMARY KEY (IID) )';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NOTICE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NOTICE_MANAGE(IID DECIMAL(19) NOT NULL,NOTICENAME VARCHAR(225),NOTICECONTENT VARCHAR(3000),ISTATE DECIMAL(2) DEFAULT 1,TAKEEFFECTTIME TIMESTAMP DEFAULT CURRENT TIMESTAMP,INVALIDTIME TIMESTAMP,USERID DECIMAL(19) NOT NULL,CONSTRAINT PK_IEAI_NOTICE_MANAGE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PLATFORM_NEWS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_PLATFORM_NEWS(IID DECIMAL(19) NOT NULL,NEWSSOURCE DECIMAL(2),NEWCONTENT VARCHAR(2000),ICREATETIME TIMESTAMP(6) DEFAULT CURRENT TIMESTAMP,USERID DECIMAL(19) NOT NULL,IENDTIME TIMESTAMP(6),PROJECTID DECIMAL(19),CONSTRAINT PK_IEAI_PLATFORM_NEWS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ORGMANAGEMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_ORGMANAGEMENT(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,IPARENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ORGMANAGEMENT_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_ORGMANAGEMENT_USER(IID DECIMAL(19) NOT NULL ,MANAGEMENTID DECIMAL(19) ,USERID DECIMAL(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT_USER PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILECLASS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILECLASS(IID DECIMAL(19) NOT NULL ,ICLASSNAME VARCHAR(255) ,ICLASSDESC VARCHAR(255) ,ICREATETIME TIMESTAMP ,IPARENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_FILECLASS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILE_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILE_USER(IID DECIMAL(19) NOT NULL ,IFILEID DECIMAL(19) ,IUSERID DECIMAL(19) ,CONSTRAINT PK_IEAI_FILE_USER PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILEINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILEINFO(IID DECIMAL(19) NOT NULL ,ICLASSIDONE DECIMAL(19) ,ICLASSIDTWO DECIMAL(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT BLOB(20971520) ,IFILEVERSION DECIMAL(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID DECIMAL(19) ,ISTATE DECIMAL(1) ,CONSTRAINT PK_IEAI_FILEINFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILEINFO_HIS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILEINFO_HIS(IID DECIMAL(19) NOT NULL ,IMYFILEID DECIMAL(19) ,ICLASSIDONE DECIMAL(19) ,ICLASSIDTWO DECIMAL(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT BLOB(20971520) ,IFILEVERSION DECIMAL(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID DECIMAL(19) ,ISTATE DECIMAL(1) ,CONSTRAINT PK_IEAI_FILEINFO_HIS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AUDIT_DATADICTIONARY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AUDIT_DATADICTIONARY(IID DECIMAL(19) NOT NULL,URL VARCHAR(500),MODELTYPE DECIMAL(19),KEYNAME VARCHAR(100),CNNAME VARCHAR(100),DICTIONARYDESC VARCHAR(4000),CONSTRAINT PK_IEAI_AUDIT_DATADICTIONARY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	
	
	
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORTAL_TOP';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PORTAL_TOP(IID DECIMAL(19) NOT NULL ,EQUIPMENTNUM DECIMAL(19) ,ONLINEUSERNUM DECIMAL(5) ,PROCESSRELEASE DECIMAL(19) ,SCRIPTNUM DECIMAL(19) ,SYSTEMNUM DECIMAL(19) ,USERLOGINNUM DECIMAL(19) ,CONSTRAINT PK_IEAI_PORTAL_TOP PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	
	
	
	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DICTIONARY VALUES WHERE DID = 998;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_DICTIONARY VALUES(998,0,998,''消息展示天数'',15)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTSHELL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENTSHELL(IID DECIMAL(19) NOT NULL, IAGENTINFO_ID DECIMAL(19) NOT NULL, ICHECKTIME DECIMAL(19),ISTATE DECIMAL(1), IMESSAGE CLOB, CONSTRAINT PK_IEAI_AGENTSHELL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_LIST(IID DECIMAL(19) NOT NULL ,TASKID DECIMAL(19) ,TASKNAME VARCHAR(225) ,STRATEGYTYPE DECIMAL(2) ,INAME VARCHAR(225) ,STATUS VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_LIST PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_STATUS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_STATUS(IID DECIMAL(19) NOT NULL ,STATUS VARCHAR(50) ,TOTAL DECIMAL(10) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_STATUS PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_DETAILS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_DETAILS(IID DECIMAL(19) NOT NULL ,IP VARCHAR(225) ,TASKNAME VARCHAR(225) ,TASKID DECIMAL(19) ,STATUS VARCHAR(225) ,CPNAME VARCHAR(225) ,STRATEGYTYPE DECIMAL(1) ,IFLOWID DECIMAL(19) ,ISTARTUSER VARCHAR(225) ,IEXECUSER VARCHAR(225) ,PERFORMUSER VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_DETAILS PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''1'', ''日常操作-执行中的运维监控'', ''collectEnvironment'', ''getDmRunningList.do'', ''getColurltaskMinitor.do'', ''1'', ''日常操作-执行中的运维监控'', NULL)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''2'', ''日常操作-待执行的运维监控'', ''taskMonitor'', ''getDmNotRunList.do'', ''getTaskMontior.do'', ''1'', ''日常操作-待执行的运维监控'', NULL)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''3'', ''标准运维统计'', ''standardLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.StandardLineChart'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=4;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''4'', ''脚本服务化'', ''scriptServerLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.ScriptServerLineChart'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ILOGINNAME' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN ILOGINNAME VARCHAR(255) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITYPE' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN ITYPE DECIMAL(1) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWIDTH' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN IWIDTH VARCHAR(10) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_ENVIRONMENT';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_COLLECT_ENVIRONMENT(IID DECIMAL(14) NOT NULL,  INAME VARCHAR(32),  IP VARCHAR(25),  CONSTRAINT  PK_COLLECT_ENVIRONMENT PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HOSTNAME' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN HOSTNAME VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_CHART';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_CHART(IID DECIMAL(19) NOT NULL ,TOTALFLWO DECIMAL(19) ,TORUNFLWO DECIMAL(19) ,ENDFLWO DECIMAL(19) ,CREATETIME TIMESTAMP DEFAULT CURRENT TIMESTAMP ,CONSTRAINT PK_IEAI_STANDA_SCREEN_CHART PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


END
$
--4.7.27
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENCYTASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENCYTASK (IID DECIMAL(19) NOT NULL, SUM_NO VARCHAR(255),SUM_REMARK VARCHAR(255),SUM_CONTENT VARCHAR(255),MODEL_TYPE VARCHAR(255),MODEL_NAME VARCHAR(255),OPER_ID VARCHAR(255),USER_ID VARCHAR(255),CALL_ID VARCHAR(255),CALL_NAME VARCHAR(255),CALL_DEPT VARCHAR(255), CREATE_TIME VARCHAR(255), DUE_TIME VARCHAR(255),TX_TIME VARCHAR(255),TX_CODE VARCHAR(255),TRADE_CODE VARCHAR(255),CLI_SERIAL_NO VARCHAR(255),TERMI_MARK VARCHAR(255),MAC_NAME VARCHAR(255),MAC_KEY_NAME VARCHAR(255),MAC_DATA VARCHAR(255),RESERVE VARCHAR(255),AUDIT_STATE DECIMAL(2),EXEC_STATE DECIMAL(2),RECORD0_CNT VARCHAR(255),RECORD0 VARCHAR(255),RECORD1_CNT VARCHAR(255),RECORD1 VARCHAR(255),CONSTRAINT PK_IEAI_AGENCYTASK PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BATCH_UPGRADE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BATCH_UPGRADE(IID DECIMAL(19) NOT NULL ,ISRCPATH VARCHAR(255) ,IDESTPATH VARCHAR(255) ,IBAKPATH VARCHAR(255) ,IOSTYPE DECIMAL(1) ,CONSTRAINT PK_IEAI_BATCH_UPGRADE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=29;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(29,''batchUpgradeFtpInfo'',''-1'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GROUPNAME' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN GROUPNAME VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INSTANCETIME' AND TABNAME='IEAI_STANDARD_TASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_STANDARD_TASK ADD COLUMN INSTANCETIME DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INSTANCETIME' AND TABNAME='TEMP_IEAI_DM_TASKLIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE TEMP_IEAI_DM_TASKLIST ADD COLUMN INSTANCETIME DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTEPIDENTITY' AND TABNAME='IEAI_INSTANCEINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN ISTEPIDENTITY VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_DETAIL';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_DETAIL(IID DECIMAL(19) NOT NULL ,ISENCEID DECIMAL(19) ,IWARNTYPE VARCHAR(255) ,ITYPE DECIMAL(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_WARN_DETAIL PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_RELATION';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_USER_RELATION(IID DECIMAL(19) NOT NULL ,IUSERID DECIMAL(19) ,ISENCEID DECIMAL(19) ,CONSTRAINT PK_IEAI_USER_RELATION PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;

	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSTEMNAME' AND TABNAME='IEAI_WARN';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN ISYSTEMNAME VARCHAR(255) ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	COMMIT;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBINFO';
		 IF LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE TABLE IEAI_CMDBINFO (
						IID DECIMAL(19) NOT NULL,
						IINSTANCEID  Varchar(255),
						IIP 	Varchar(255),
						IHOSTNAME Varchar(255),
						IPORT	Varchar(50),
						IOPERATESYSTEM	Varchar(255),
						ISYSTEMVERSION	Varchar(255),
						ICPU	Varchar(255),
						IARCHITECTURE	Varchar(255),
						IPHYSICALCORES	Varchar(10),
						ILOGICALCORES	Varchar(10),
						IMEMORYSIZE	Varchar(100),
						IEQUIPMENT  Varchar(255),
						IOUTBANDIP 	Varchar(255),
					    IAGENTSTATUS VARCHAR(255),
					    IEQUTYPE VARCHAR(2),
						CONSTRAINT PK_IEAI_CMDBINFO PRIMARY KEY (IID)
					)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB_LOCAL';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB_LOCAL  (
						   IID DECIMAL(19) NOT NULL,   
						   ICMDBINFOID Varchar(19), 
						   IINSTANCEID  Varchar(19),  
						   ICOLUMNNAME Varchar(255), 
						   ITYPE   Varchar(255), 
						   INULL INTEGER,  
						   ICONTENT CLOB,  
						   IAID DECIMAL(19),  
						   ICPID DECIMAL(19),   
						   IEQUTYPE VARCHAR(2), 
						CONSTRAINT PK_IEAI_CMDBCLOB_LOCAL PRIMARY KEY (IID)
						)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;   	 

			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB  (
					   IID DECIMAL(19) NOT NULL,   
					   ICMDBINFOID Varchar(19), 
					   IINSTANCEID  Varchar(19),  
					   ICOLUMNNAME Varchar(255), 
					   ITYPE   Varchar(255), 
					   INULL INTEGER,  
					   ICONTENT CLOB,  
					   IAID DECIMAL(19),  
					   ICPID DECIMAL(19),   
					   IEQUTYPE VARCHAR(2), 
					CONSTRAINT PK_IEAI_CMDBCLOB PRIMARY KEY (IID)
					)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;  

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB_SUS';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB_SUS  (
						   IID DECIMAL(19) NOT NULL, 
						   INULL INTEGER,  
						   ICONTENT CLOB , 
						CONSTRAINT PK_IEAI_CMDBCLOB_SUS PRIMARY KEY (IID)
						)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;  

END
$

--4.7.28

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER_QUICK_MIDDLE' AND COLNAME='IORDER';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_USER_QUICK_MIDDLE ADD IORDER DECIMAL(2)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	 END IF;
	 
	 	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AUDIT_DATADICTIONARY' AND COLNAME='SQLTEXT';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_AUDIT_DATADICTIONARY ADD SQLTEXT VARCHAR(4000)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	 END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IIDARR' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN IIDARR VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CREATETIME' AND TABNAME='IEAI_STANDA_SCREEN_DETAILS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_STANDA_SCREEN_DETAILS ADD COLUMN CREATETIME TIMESTAMP(6)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNCCMDB_INFO';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_SYNCCMDB_INFO (IID DECIMAL(19) NOT NULL,SERVICERUNFLAG	VARCHAR(4),CRONVALUE	VARCHAR(20),SYNC_IP_COUNT    VARCHAR(10),TIMINGJOBFLAG  VARCHAR(4),CMDBTHREADFLAG  VARCHAR(4),CONSTRAINT PK_IEAI_SYNCCMDB_INFO PRIMARY KEY (IID))';
           PREPARE    SQLA FROM LS_SQL;
           EXECUTE SQLA;
        END  IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='RECORD_NUM' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN RECORD_NUM VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='RECORD_KEY' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN RECORD_KEY VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNCCMDB_INFO';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_SYNCCMDB_INFO (IID DECIMAL(19) NOT NULL,SERVICERUNFLAG	VARCHAR(4),CRONVALUE	VARCHAR(20),SYNC_IP_COUNT    VARCHAR(10),TIMINGJOBFLAG  VARCHAR(4),CMDBTHREADFLAG  VARCHAR(4),CONSTRAINT PK_IEAI_SYNCCMDB_INFO PRIMARY KEY (IID))';
           PREPARE    SQLA FROM LS_SQL;
           EXECUTE SQLA;
        END  IF;

END
$

--4.7.29

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AUDIT_USER' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN AUDIT_USER VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AUTOCLEARSAVEDAY' AND TABNAME='IEAI_CLEARDATA';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_CLEARDATA ADD COLUMN AUTOCLEARSAVEDAY DECIMAL(19,0) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISAUTOCLEAR' AND TABNAME='IEAI_CLEARDATA';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_CLEARDATA ADD COLUMN ISAUTOCLEAR DECIMAL(19,0) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMSGTYPE' AND TABNAME='IEAI_WARN_DETAIL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN_DETAIL ADD COLUMN IMSGTYPE VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_USERS';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_USERS(IID DECIMAL(19) NOT NULL ,IWARNID DECIMAL(19) ,IUSERNAME VARCHAR(255) ,ICONNTYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_USERS PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_COLUMNS';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_COLUMNS(IID DECIMAL(19) NOT NULL ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID DECIMAL(19) ,ITYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_COLUMNS PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_MESSAGE';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_MESSAGE(IID DECIMAL(19) NOT NULL ,IWARNID DECIMAL(19) ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID DECIMAL(19) ,IVALUE CLOB ,CONSTRAINT PK_IEAI_WARN_MESSAGE PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ANNOUNCEMENT_USER';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_ANNOUNCEMENT_USER(IID DECIMAL(19) NOT NULL ,USERID DECIMAL(19) ,ITYPE VARCHAR(255)  ,CONSTRAINT PK_IEAI_ANNOUNCEMENT_USER PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOMPARERULE' AND TABNAME='IEAI_WARN';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN ICOMPARERULE VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHOSTNAME' AND TABNAME='IEAI_WARN';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN IHOSTNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_GUARDPORT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_GUARDPORT DECIMAL(19) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_GUARDPORT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_GUARDPORT DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=23;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES(23,23,''数据采集源'','''','''','''','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_AZNAME' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_AZNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CRONVALUE' AND TABNAME='IEAI_COLLECT_ITEM';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_ITEM ADD COLUMN CRONVALUE VARCHAR(255)  ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISCRONTASK' AND TABNAME='IEAI_COLLECT_ITEM';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_ITEM ADD COLUMN ISCRONTASK DECIMAL(2) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICMDBKFCS_UPDATE_LASTTIME' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN ICMDBKFCS_UPDATE_LASTTIME VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICMDB_UPDATE_LASTTIME' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN ICMDB_UPDATE_LASTTIME VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICMDB_UPDATE_DESC' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN ICMDB_UPDATE_DESC VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICMDB_DELIVER_TIME' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN ICMDB_DELIVER_TIME VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='RECYCLE_STATUS' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN RECYCLE_STATUS VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICHARGEUSERMAIL' AND TABNAME='IEAI_AGENT_PROJECT_CONFIG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENT_PROJECT_CONFIG ADD COLUMN ICHARGEUSERMAIL VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_NETID' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_NETID VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_NETID' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN IAGENT_NETID VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	
	COMMIT;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PROXY_VMIP';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PROXY_VMIP(IID DECIMAL(19,0) NOT NULL,IPROXYID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),ISWITCHIP VARCHAR(25),ISWITCHOUTIP VARCHAR(25),CONSTRAINT PK_IEAI_PROXY_VMIP PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EXTJS_KEYVAL';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_EXTJS_KEYVAL(IID DECIMAL(19,0) NOT NULL,IEXTKEY VARCHAR(4000),IEXTVAL VARCHAR(4000),CONSTRAINT PK_IEAI_EXTJS_KEYVAL PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN

	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 1 THEN
		SET LS_SQL ='DROP INDEX IDX_IEAI_COMPUTER_LIST_01';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 1 THEN
		SET LS_SQL ='CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$
BEGIN

	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_PROPERTYCONFIG';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_PROPERTYCONFIG'',1000)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
 		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =1000 WHERE IVALUE <1000 AND ICLASSNAME=''IEAI_PROPERTYCONFIG''';
  	PREPARE	SQLA FROM LS_SQL;
  	EXECUTE SQLA;
	END	IF;
		COMMIT;
END
$

BEGIN

	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_MONITOR_STATE' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='alter table IEAI_AGENTINFO add IAGENT_MONITOR_STATE DECIMAL(1)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$
BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SQL_RESULT';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SQL_RESULT(IID  DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IFOREIGNHEADID DECIMAL(19), IRESULT CLOB, ISQLKEY VARCHAR(255), IFROM DECIMAL(1), ISQL CLOB, CONSTRAINT PK_IEAI_SCRIPT_SQL_RESULT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
		
			 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SQL_HEAD';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SQL_HEAD(IID DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IRESULT CLOB,IFROM DECIMAL(1),ISQLKEY VARCHAR(255),CONSTRAINT PK_IEAI_SCRIPT_SQL_HEAD PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 

END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISQL' AND TABNAME='IEAI_SCRIPT_OPER_RESULT';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD  ISQL CLOB';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$

--20201010

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='BUSINESSNAME' AND TABNAME='IEAI_CMDBINFO';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_CMDBINFO ADD COLUMN BUSINESSNAME VARCHAR(225)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ENVIRONMENT' AND TABNAME='IEAI_CMDBINFO';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_CMDBINFO ADD COLUMN ENVIRONMENT VARCHAR(225)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPZX' AND TABNAME='IEAI_CMDBINFO';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_CMDBINFO ADD COLUMN IPZX VARCHAR(225)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS  WHERE tabname = 'IEAI_CMDBCLOB_LOCAL 'AND COLNAME='ICONTENT' and TYPENAME='CLOB';
	IF LI_EXISTS > 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_CMDBCLOB_LOCAL DROP COLUMN ICONTENT';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICONTENT' AND TABNAME='IEAI_CMDBCLOB_LOCAL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD COLUMN ICONTENT VARCHAR(1000)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBLABEL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDBLABEL(IID DECIMAL(19) NOT NULL ,HOSTTYPE VARCHAR(10) ,LABELNAME VARCHAR(225) ,TYPE VARCHAR(10) ,CONSTRAINT PK_IEAI_CMDBLABEL PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
    END IF;
	COMMIT;
END
$

---8.3.0
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDMARK' AND TABNAME='IEAI_DATASYNC_TABLEINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_DATASYNC_TABLEINFO ADD COLUMN IDMARK VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDISK' AND TABNAME='IEAI_SERVERLIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SERVERLIST ADD COLUMN IDISK VARCHAR(20) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTINFO' AND  COLNAME='IAGENT_MONITOR_STATE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_MONITOR_STATE NUMERIC (19)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$

---8.4.0
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PARAMETER_MANAGER';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_PARAMETER_MANAGER (IID  DECIMAL(19,0) NOT NULL, IPARAMNAME VARCHAR(255),IPARAMVALUE VARCHAR(255),IPARAMDESC VARCHAR(255),ISCOPE VARCHAR(255), ICREATEUSERID DECIMAL(19,0),IUPDATEUSERID DECIMAL(19,0),ICREATETIME DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),CONSTRAINT PK_IEAI_PARAMETER_MANAGER PRIMARY KEY (IID))';
           PREPARE    SQLA FROM LS_SQL;
           EXECUTE SQLA;
        END  IF;

	COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HOSTTYPE' AND TABNAME='IEAI_CMDBCLOB_LOCAL';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD COLUMN HOSTTYPE VARCHAR(10)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HOSTTYPE' AND TABNAME='IEAI_CMDBCLOB';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_CMDBCLOB ADD COLUMN HOSTTYPE VARCHAR(10)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTINFO' AND  COLNAME='IOSBASEVALUE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTINFO ADD IOSBASEVALUE VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	
END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_WARN_AGENT';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_WARN_AGENT (IID NUMERIC (19) NOT NULL,IAGENTID NUMERIC (19) ,IWARNTYPE VARCHAR (5) ,IWARNCOUNT NUMERIC (19) ,IFIRSTTIME NUMERIC (19) ,ILASTTIME NUMERIC (19), CONSTRAINT PK_IEAI_WARN_AGENT PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_MESSAGE';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_MESSAGE(IID DECIMAL(19) NOT NULL ,IP VARCHAR(255) ,IHOSTNAME VARCHAR(255) ,ISYSTEMINFO VARCHAR(225) ,ICENTERNAME VARCHAR(255) ,ISYSADMIN VARCHAR(255) ,IAPPADMIN VARCHAR(255) ,ICOLLECTRESULT VARCHAR(255) ,IKEYNAME VARCHAR(255) ,ICMDB_UPDATE_DESC VARCHAR(50) ,ICMDB_UPDATE_LASTTIME VARCHAR(50) ,CONSTRAINT PK_IEAI_CMDB_MESSAGE PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
		END IF;
	COMMIT;
END
$
	
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_AGENT_START_STOP';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_AGENT_START_STOP (IID NUMERIC (19) NOT NULL,IAGENTIP VARCHAR (255) ,IAGENTPORT NUMERIC (19) ,IOSNAME VARCHAR (255) ,ISTATE NUMERIC (19) ,IUSERNAME VARCHAR (255) ,ITIME NUMERIC (19) ,ITYPE NUMERIC (19) ,IUPDATESTATE NUMERIC (19) ,IMESSAGE VARCHAR (255) , CONSTRAINT PK_IEAI_AGENT_START_STOP PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_IPMP_SYSTEM_RELATON';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_IPMP_SYSTEM_RELATON (IID NUMERIC (19) NOT NULL,IPROJECTID NUMERIC (19),IPMPSYSNAME VARCHAR (255), CONSTRAINT PK_IEAI_IPMP_SYSTEM_RELATON PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_LOGINRANGE_VALIDATION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_LOGINRANGE_VALIDATION (IID NUMERIC (19) NOT NULL,IIPRANGE VARCHAR (255) ,CREATORUSER VARCHAR (255) ,CREATORDATE NUMERIC (19) ,UPDATEUSER VARCHAR (255) ,UPDATEDATE NUMERIC (19) , CONSTRAINT PK_IEAI_LOGINRANGE_VALIDATION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

----8.6.0----
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_BATCH_UPGRADE' AND  COLNAME='IUPDATEVERSION';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IUPDATEVERSION VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_BATCH_UPGRADE' AND  COLNAME='IACTFINISHMAXWAIT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IACTFINISHMAXWAIT NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_BATCH_UPGRADE' AND  COLNAME='IWRITEBUFFERSIZE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IWRITEBUFFERSIZE NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTUPDATE_INFO' AND  COLNAME='UPDATEVERSION';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD UPDATEVERSION VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTUPDATE_INFO' AND  COLNAME='ACTFINISHMAXWAIT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD ACTFINISHMAXWAIT NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTUPDATE_INFO' AND  COLNAME='WRITEBUFFERSIZE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD WRITEBUFFERSIZE NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENT_MAINTAIN_TASK' AND  COLNAME='IUPDATEVERSION';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IUPDATEVERSION VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENT_MAINTAIN_TASK' AND  COLNAME='IACTFINISHMAXWAIT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IACTFINISHMAXWAIT NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENT_MAINTAIN_TASK' AND  COLNAME='IWRITEBUFFERSIZE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IWRITEBUFFERSIZE NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_ENTEGOR_CONFIG';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_ENTEGOR_CONFIG (IID NUMERIC (19) NOT NULL,IKEY VARCHAR (255) ,IVALUE VARCHAR (255) ,IDES VARCHAR (1500) ,ITYPE NUMERIC (19) ,ISTATUS NUMERIC (19) ,IDEFAULT VARCHAR (255) , CONSTRAINT PK_IEAI_ENTEGOR_CONFIG PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
----8.7.0----
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_LOGIN_KAPTCHA';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_LOGIN_KAPTCHA(IID DECIMAL(19,0) NOT NULL, IURL VARCHAR(255), USERID DECIMAL(19,0),OPERTIME DECIMAL(19,0),ISTATE INTEGER,IP VARCHAR(25),IMAC VARCHAR(25),HOSTNAME VARCHAR(255),IKAPTCHA VARCHAR(10),CONSTRAINT PK_IEAI_LOGIN_KAPTCHA PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 50008;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(50008, ''失效备援信息'', 100, ''lostthenhelplist.do'', 2, '''', ''images/info81.png'')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
        END IF;
		
END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTINFO' AND  COLNAME='SYSTEMSTATUS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTINFO ADD SYSTEMSTATUS VARCHAR(100) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		
END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_COMPUTER_LIST' AND  COLNAME='CPNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_COMPUTER_LIST MODIFY COLUMN CPNAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		
END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 240;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(240, ''保存'', ''saveIosName.do'', ''新agent管理--保存'')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
        END IF;
        
        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 240 AND IMENUID = 202;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(240, 202, 240, '''')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
        END IF;
		
END
$


BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 2261;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(2261, ''告警看板'', 100, ''initIeaiWarnViewPage.do'', 2261, ''告警管理'', ''images/info83.png'')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
        END IF;
		
END
$


BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SYS_DICTIONARY WHERE DID = 200;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SYS_DICTIONARY(DID, DNAME, DTYPE, DINTVALUE) VALUES(200, ''LOGINCONFIG'', ''INTEGER'', ''0'')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
        END IF;
		
END
$
----8.9.0----

BEGIN
    DECLARE LS_SQL VARCHAR(2000);
    DECLARE  LI_EXISTS  NUMERIC(2);

       SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROXY_LIST' AND  COLNAME='IVERSION';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_PROXY_LIST ADD IVERSION VARCHAR(25) '; 
				   PREPARE	SQLA FROM LS_SQL; 
				   EXECUTE SQLA;
			END	IF;
		
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_PUBLIC_NOTICE_MESSAGE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_PUBLIC_NOTICE_MESSAGE (IID NUMERIC (19) NOT NULL,INOTICEINFO VARCHAR (255),IEXECUCYCLE VARCHAR (255),IISSHOW NUMERIC (1), CONSTRAINT PK_IEAI_PUBLIC_NOTICE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_DEPT';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_DEPT (IID NUMERIC (19) NOT NULL,IDEPTID NUMERIC (19) ,IDEPTNAME VARCHAR (255) ,ICREATETIME VARCHAR (255) ,IPARENTID NUMERIC (19) ,IDEPTDESC VARCHAR (255) , CONSTRAINT PK_IEAI_DEPT PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT' AND  COLNAME='ISYSTEMNUMBER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT ADD ISYSTEMNUMBER VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_IPMP_SYSTEM_RELATON' AND  COLNAME='ISYSCODE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_IPMP_SYSTEM_RELATON ADD ISYSCODE VARCHAR(255)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$

--- 8.11.0
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'MAINDEPARTMENT';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD MAINDEPARTMENT  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'BUSINESSPERSONNEL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD BUSINESSPERSONNEL  VARCHAR(500)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'ARTISAN';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD ARTISAN  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'KEYSTEPS';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD KEYSTEPS  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'PLANTIME';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD PLANTIME  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'MAINDEPARTMENT';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE  ADD MAINDEPARTMENT  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'BUSINESSPERSONNEL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE  ADD BUSINESSPERSONNEL  VARCHAR(500)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'ARTISAN';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE  ADD ARTISAN  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'KEYSTEPS';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE  ADD KEYSTEPS  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'PLANTIME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE  ADD PLANTIME  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'MAINDEPARTMENT';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE_HIS  ADD MAINDEPARTMENT  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'BUSINESSPERSONNEL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE_HIS  ADD BUSINESSPERSONNEL  VARCHAR(500)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'ARTISAN';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE_HIS  ADD ARTISAN  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'KEYSTEPS';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE_HIS  ADD KEYSTEPS  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'PLANTIME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_RUNINFO_INSTANCE_HIS  ADD PLANTIME  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'MAINDEPARTMENT';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_INSTANCEINFO_HIS  ADD MAINDEPARTMENT  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'BUSINESSPERSONNEL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_INSTANCEINFO_HIS  ADD BUSINESSPERSONNEL  VARCHAR(500)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'ARTISAN';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_INSTANCEINFO_HIS  ADD ARTISAN  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'KEYSTEPS';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_INSTANCEINFO_HIS  ADD KEYSTEPS  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'PLANTIME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_INSTANCEINFO_HIS  ADD PLANTIME  VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROXY_LIST' AND COLNAME = 'PROXYIP';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PROXY_LIST alter column PROXYIP set data type varchar(200)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PROXY_LIST');
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ADAPTOR_BLOB' AND COLNAME = 'ICONTENT';
		IF	LI_EXISTS != 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_ADAPTOR_BLOB alter column ICONTENT set data type BLOB(1113048576)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_ADAPTOR_BLOB');

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROXY_LIST' AND COLNAME = 'PROXYIPOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PROXY_LIST alter column PROXYIPOUT set data type varchar(200)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END	IF;
	CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PROXY_LIST');
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND  COLNAME='IACTDES';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE alter column IACTDES set data type varchar(1000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		END	IF;
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND  COLNAME='IACTDES';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA alter column IACTDES set data type varchar(1000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE');
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS_DATA');
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND  COLNAME='IACTDES';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS alter column IACTDES set data type varchar(1000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS_DATA');
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUNINFO_INSTANCE_HIS');
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND  COLNAME='IACTDES';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO alter column IACTDES set data type varchar(1000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO');
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND  COLNAME='IACTDES';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO_HIS alter column IACTDES set data type varchar(1000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		commit;
		END	IF;
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCEINFO_HIS');
END
$

CREATE  or replace PROCEDURE PROC_MV_RUN_INSTANCE    (IN WORKID NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
			INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME)   SELECT  IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME  FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
			INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID)  SELECT  IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2 ,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID    FROM IEAI_RUN_INSTANCE WHERE  IID =WORKID;
			--如果IEAI_RUNINFO_INSTANCE_PKG表结束时间为0，则查询并更新
			FOR PKG_C AS SELECT	IPKGNAME,	IENDTIME	 FROM  IEAI_RUNINFO_INSTANCE_PKG  WHERE  IRUNINSID=WORKID DO 
			IF PKG_C.IENDTIME=0 THEN
			UPDATE IEAI_RUNINFO_INSTANCE_PKG SET IENDTIME=(SELECT MAX(IENDTIME) FROM IEAI_RUNINFO_INSTANCE T WHERE T.IRUNINSID=WORKID AND T.IPKGNAME=PKG_C.IPKGNAME);
			END IF;
			END FOR;
			INSERT INTO IEAI_RUNINFO_INSTANCE_PKG_HIS(IID, IRUNINSID, IPKGNAME, ISTARTTIME, IENDTIME)   SELECT IID, IRUNINSID, IPKGNAME, ISTARTTIME, IENDTIME FROM  IEAI_RUNINFO_INSTANCE_PKG  WHERE  IRUNINSID=WORKID;

			DELETE FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
			DELETE FROM  IEAI_RUN_INSTANCE  WHERE IID=WORKID;
			DELETE FROM  IEAI_RUNINFO_INSTANCE_PKG  WHERE  IRUNINSID=WORKID;
	END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND  COLNAME='IBGTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD IBGTYPE VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCE_VERSION');

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND  COLNAME='ICQTASKID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ICQTASKID VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_INSTANCE_VERSION');

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE' AND  COLNAME='IBGTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE ADD IBGTYPE VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUN_INSTANCE');

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE' AND  COLNAME='ICQTASKID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE ADD ICQTASKID VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUN_INSTANCE');

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE_HIS' AND  COLNAME='IBGTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IBGTYPE VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUN_INSTANCE_HIS');

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE_HIS' AND  COLNAME='ICQTASKID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ICQTASKID VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_RUN_INSTANCE_HIS');
END
$

CREATE	OR REPLACE	PROCEDURE PROC_GANT_STATE 
		(
			IN IN_IACTDEFNAME VARCHAR (255),
			IN IN_IFLOWID INTEGER,
			IN IN_ISTATE VARCHAR (255),
			IN IN_IDESC VARCHAR (255),
			IN IN_IACTNAME VARCHAR (255),
			IN IN_IBEGINEXCTIME VARCHAR (255),
			IN IN_IREADYTIME VARCHAR (255),
			IN IN_IENDTIME VARCHAR (255) 
		)
	BEGIN
		DECLARE V_IPROJECTNAME VARCHAR(255); DECLARE V_IFLOWNAME VARCHAR(255); DECLARE V_IFLOWINSNAME VARCHAR(255); DECLARE V_IACTSTATE VARCHAR(255); DECLARE V_IISFAIL DECIMAL(1); 
		DECLARE V_IFAILSTATE DECIMAL(1); DECLARE V_IISWARN DECIMAL(1); DECLARE V_IFLAG DECIMAL(1); DECLARE V_COUNT DECIMAL(1); DECLARE LN_OPT_ID NUMERIC( 19,0); 
	BEGIN
	SET V_IFAILSTATE = 0; 
	SELECT	COUNT(T.IADAPTORID) INTO	V_IFLAG FROM	IEAI_TOPO_ADAPTOR T WHERE T.IADAPTORSTATE=1 AND T.IADAPACTNAME=IN_IACTDEFNAME; 
	IF V_IFLAG>0 THEN 
	SELECT
		A.IPROJECTNAME,
		A.IFLOWNAME,
		A.IFLOWINSNAME 
	INTO
		V_IPROJECTNAME,
		V_IFLOWNAME,
		V_IFLOWINSNAME 
	FROM
		IEAI_WORKFLOWINSTANCE A 
	WHERE
		A.IFLOWID = IN_IFLOWID; 
	SELECT
		F_INSTANCENAME_CHECK_DATE(V_IFLOWINSNAME) 
	INTO
		V_IFLAG 
	FROM
		IDUAL; 
	IF V_IFLAG = 0 THEN 
	SET
		V_IFLOWINSNAME = TO_CHAR(TO_DATE(V_IFLOWINSNAME, 'YYYYMMDD'), 'YYYY-MM-DD'); 
	IF IN_ISTATE = 'Fail:DisConnectAgent' THEN 
	SET
		V_IISFAIL = 1; 
	SET
		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail:Timeoff' THEN 
	SET
		V_IISFAIL = 2; 
	SET
		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail' THEN 
	SET
		V_IISFAIL = 4; 
	SET
		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail:Business' THEN 
	SET
		V_IISFAIL = 3; 
	SET
		V_IFAILSTATE = 1; ELSE 
	SET
		V_IISFAIL = 0; 
	END
		IF; IF IN_ISTATE = 'Null' THEN 
	SET
		V_IACTSTATE = '1'; ELSEIF IN_ISTATE = 'Finished' THEN 
	SET
		V_IACTSTATE = '2'; 
	SET
		V_IISFAIL = 0; 
	SET
		V_IISWARN = 0; ELSEIF IN_ISTATE = 'Skipped' THEN 
	SET
		V_IACTSTATE = '2'; 
	SET
		V_IISFAIL = 0; 
	SET
		V_IISWARN = 0; ELSE 
	SET
		V_IACTSTATE = '0'; 
	END
		IF; 
	IF IN_IACTDEFNAME = 'UserTask' AND (IN_IDESC LIKE '异常_%' OR IN_IDESC LIKE '%_异常%' OR IN_IACTNAME LIKE '异常_%' OR IN_IACTNAME LIKE '_异常%') THEN IF IN_IACTNAME LIKE '异常_%' 
		THEN 
		
	IF IN_ISTATE = 'Finished' OR IN_ISTATE = 'ManualFinish' THEN 
		UPDATE IEAI_ACT_TOPO_INSTANCE B 
		SET
			B.IISFAIL = 0,
			B.IACTSTATE = '2',
			B.IFAILSTATE = 1 
		WHERE
			B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 
			
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE
			IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 2,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = 1 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.INSTANCENAME = V_IFLOWINSNAME; 
		UPDATE
			IEAI_TOPO_WARNING 
		SET
			ICONFIRMRING = 1 
		WHERE
			IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)); 
		
	ELSE 
		UPDATE
			IEAI_ACT_TOPO_INSTANCE B 
		SET
			B.IISFAIL = 3,
			B.IACTSTATE = '0',
			B.IFAILSTATE = V_IFAILSTATE 
		WHERE
			B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE
			IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 3,
			B.IISFAIL = 1 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.INSTANCENAME = V_IFLOWINSNAME; 

		CALL  PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 

		SELECT COUNT(1) INTO V_COUNT
        FROM IEAI_ACT_TOPO_INSTANCE B
        WHERE B.IPROJECTNAME = V_IPROJECTNAME
                 AND B.IFLOWNAME = V_IFLOWNAME
                 AND B.IACTNAME =
                     SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME))
                 AND B.IDATADATE = V_IFLOWINSNAME;
				IF V_COUNT > 0 THEN
					INSERT
					INTO
						IEAI_TOPO_WARNING 
						(
							IID,
							IDATADATE,
							IWARNTIME,
							IPRJNAME,
							IFLOWNAME,
							IACTNAME,
							IWARNINGTYPE,
							ICONFIRMRING 
						)
					VALUES
						(
							LN_OPT_ID,
							V_IFLOWINSNAME,
							FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP, 8),
							V_IPROJECTNAME,
							V_IFLOWNAME,
							SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)),
							3,
							0 
						);
		END IF;			
	END	IF; 
	ELSE 
	IF IN_ISTATE = 'Finished' OR IN_ISTATE = 'ManualFinish' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IISFAIL = 0,
		B.IACTSTATE = '2',
		B.IFAILSTATE = 1 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = 2,
		B.IISFAIL = 0 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.INSTANCENAME = V_IFLOWINSNAME; 
	
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3); 
	ELSE 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IISFAIL = 3,
		B.IACTSTATE = '0',
		B.IFAILSTATE = V_IFAILSTATE 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = 0,
		B.IISFAIL = 3 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.INSTANCENAME = V_IFLOWINSNAME; 
	CALL  PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 
	IF V_IACTSTATE = '2' THEN 
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3); 
	END
		IF; 
	END
		IF; 
	END
		IF; 
	ELSE 
	IF IN_IACTDEFNAME = 'UserTask' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IREALBEGINTIME = IN_IREADYTIME,
		B.IREALENDTIME = IN_IENDTIME,
		B.IACTSTATE = V_IACTSTATE,
		B.IISFAIL = V_IISFAIL,
		B.IFAILSTATE = DECODE(B.IFAILSTATE, 1, B.IFAILSTATE, V_IFAILSTATE),
		B.IISWARN = DECODE(V_IISWARN, NULL, B.IISWARN, V_IISWARN) 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = V_IACTSTATE,
		B.IREALBEGINTIME= IN_IBEGINEXCTIME,
		B.IREALFINISHTIME = IN_IENDTIME,
		B.IISFAIL = 0 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME
		-- AND B.IINFLOWNAME = V_IFLOWNAME
		AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	ELSE 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IREALBEGINTIME = IN_IBEGINEXCTIME,
		B.IREALENDTIME = IN_IENDTIME,
		B.IACTSTATE = V_IACTSTATE,
		B.IISFAIL = V_IISFAIL,
		B.IFAILSTATE = DECODE(B.IFAILSTATE, 1, B.IFAILSTATE, V_IFAILSTATE),
		B.IISWARN = DECODE(V_IISWARN, NULL, B.IISWARN, V_IISWARN) 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 

	IF V_IISFAIL>0 THEN 
	
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	
		UPDATE	IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 3,
			B.IREALBEGINTIME= IN_IBEGINEXCTIME,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = V_IISFAIL 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	ELSE 
		UPDATE 	IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = V_IACTSTATE,
			B.IREALBEGINTIME= IN_IBEGINEXCTIME,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = V_IISFAIL 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	END IF;
	END IF; 
	END IF; 

	IF V_IISFAIL!=0 THEN IF IN_ISTATE = 'Fail:Timeoff' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.ITIMEOUTNUM = B.ITIMEOUTNUM + 1 
	WHERE
		B.ITIMEOUTNUM > 0 AND B.ITIMEOUTNUM IS NOT NULL AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.ITIMEOUTTIME = 0,
		B.IFIRSTTIMEOUTTIME = FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8),
		B.ITIMEOUTNUM = 1 
	WHERE
		(B.ITIMEOUTNUM = 0 OR B.ITIMEOUTNUM IS NULL) AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	END		IF;
	
	SELECT COUNT(1) INTO V_COUNT
			FROM IEAI_ACT_TOPO_INSTANCE B
			WHERE B.IPROJECTNAME = V_IPROJECTNAME
					 AND B.IFLOWNAME = V_IFLOWNAME
					 AND B.IACTNAME = IN_IACTNAME
					 AND B.IDATADATE = V_IFLOWINSNAME;
	IF V_COUNT > 0 THEN
		IF V_IISFAIL != 0 THEN 

		CALL PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 
		INSERT
		INTO
			IEAI_TOPO_WARNING 
			(
				IID,
				IDATADATE,
				IWARNTIME,
				IPRJNAME,
				IFLOWNAME,
				IACTNAME,
				IWARNINGTYPE,
				ICONFIRMRING 
			)
		VALUES
			(
				LN_OPT_ID,
				V_IFLOWINSNAME,
				FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8),
				V_IPROJECTNAME,
				V_IFLOWNAME,
				IN_IACTNAME,
				V_IISFAIL,
				0 
			)
			; 
		END		IF; 
	END		IF;
	IF V_IACTSTATE = '2' THEN 
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = IN_IACTNAME; 
	END		IF; 
	END		IF; 
	END		IF; 
	END		IF; 
	END; 
END
$
-- 8.14.0 version TOPO patch is as follows
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SSO_APP_CONFIG';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SSO_APP_CONFIG (IID NUMERIC (19) NOT NULL,IAPPNAME VARCHAR (50) ,IISOTHER NUMERIC (19) ,ILOGINURL VARCHAR (255) ,IAUTHURL VARCHAR (255) ,ISTATE NUMERIC (19) ,IAPPDISC VARCHAR (255) ,IUPDATEUSER NUMERIC (19) ,IUPDATETIME TIMESTAMP  ,IIMAGE VARCHAR (255) ,IENGLISHSIGN VARCHAR (50) ,IAPPTYPE NUMERIC (19) , CONSTRAINT PK_IEAI_SSO_APP_CONFIG PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_WARN_USERS' AND COLNAME = 'ISENDTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_WARN_USERS ADD COLUMN ISENDTYPE VARCHAR(25)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$

BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATUS' AND TABNAME='IEAI_USER';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE ieai_user add  ISTATUS VARCHAR(255) DEFAULT 0';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$
	
-- 8.14.1
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SMS_SYS_CONFIG';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE  IEAI_SMS_SYS_CONFIG(IID NUMERIC(19,0) not null,ISYSCODE VARCHAR(128),ISYSAPP VARCHAR(56),ISYSCREATETIME NUMERIC(19,0),ISYSUPDATETIME NUMERIC(19,0),ICALLMETHOD VARCHAR(256),ISECRETKEY VARCHAR(128),ICODE VARCHAR(28),ICREATEUSER VARCHAR(56),IUPDATEUSER VARCHAR(56),CONSTRAINT PK_IEAI_SMS_SYS_CONFIG PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SMS_APPROVE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SMS_APPROVE(IID NUMERIC(19,0) NOT NULL,ISYSCODE VARCHAR(128),ISYSAPP VARCHAR(56),ITASKCODE VARCHAR(128),ISMSHEADER VARCHAR(128),ISMSCONTENT   VARCHAR(2000),IAPPROVEPHONE NUMERIC(19,0),ICREATEAPPROVEPHONE NUMERIC(19,0),ISMSUSERTYPE INTEGER,ILASTSMSUSERTYPE INTEGER,IAPPROVEFORWARDSTATE INTEGER,IAPPROVEFORWARDTIME  NUMERIC(19,0),ICREATEAPPROVEFORWARDSTATE INTEGER,ICREATEAPPROVEFORWARDTIME  NUMERIC(19,0),ICREATESMSCONTENT   VARCHAR(2000),IDYNAMICCODE  VARCHAR(10),IRECEIPTSTATE   INTEGER,IRECEIPTTIME  NUMERIC(19,0),ICALLBACKCONTENT VARCHAR(2000),ICALLBACKSTATE   INTEGER,ICALLBACKTIME  NUMERIC(19,0),IAPPROVALTESULT  VARCHAR(2000),IAPPROVALCONTENT VARCHAR(2000),INOTICEFORWARDSTATE INTEGER,INOTICEFORWARDTIME  NUMERIC(19,0),ICREATETIME NUMERIC(19,0),ICODE VARCHAR(28),ICREATEUSER VARCHAR(56),CONSTRAINT PK_IEAI_IEAI_SMS_APPROVE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

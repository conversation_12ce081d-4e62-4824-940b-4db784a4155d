--4.7.17 not exists
--4.7.18 not exists
--4.7.19 not exists
--4.7.20 not exists
--4.7.21 not exists
--4.7.22 not exists
--4.7.23 
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	--CREATE TABLE IEAI_FSH_*
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_TYPE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_TYPE (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), IKEYWORD VARCHAR2(255), IPLATFORM NUMBER(4), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_ALARM_TYPE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCENE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCENE (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), ITYPE NUMBER(4), ITASKID NUMBER(19), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), ITASKNAME VARCHAR2(255), CONSTRAINT PK_IEAI_FSH_SCENE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_TASK' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_TASK (IID NUMBER(19) NOT NULL, IALARMINFOID NUMBER(19), ISCHEMEID NUMBER(19), ICREATETIME NUMBER(19), ISTARTTIME NUMBER(19), IENDTIME NUMBER(19), ISTATE NUMBER(4), ITASK_EXECRESULT VARCHAR2(100), ITASKTYPE NUMBER(4), IAOMSTASKID NUMBER(19), IALARMIP VARCHAR2(20), ISUBMITUSER VARCHAR2(255), IAUDITOR VARCHAR2(255), ITIMEING VARCHAR2(50), IEXAMINETYPE NUMBER(4), CONSTRAINT PK_IEAI_FSH_TASK PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_TASK_HISTORY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_TASK_HISTORY	(IID NUMBER(19) NOT NULL, IALARMINFOID NUMBER(19), ISCHEMEID NUMBER(19), ICREATETIME NUMBER(19), ISTARTTIME NUMBER(19), IENDTIME NUMBER(19), ISTATE NUMBER(4), ITASK_EXECRESULT VARCHAR2(100), ITASKTYPE NUMBER(4), IAOMSTASKID NUMBER(19), IALARMIP VARCHAR2(20), ISUBMITUSER VARCHAR2(255), IAUDITOR VARCHAR2(255), ITIMEING VARCHAR2(50), IEXAMINETYPE NUMBER(4), CONSTRAINT PK_IEAI_FSH_TASK_HISTORY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARMINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARMINFO (IID NUMBER(19) NOT NULL, IALARMID VARCHAR2(100), IPLATFORM VARCHAR2(100), IAPPNAME VARCHAR2(255), IHOSTIP VARCHAR2(25), IALARMCONTENT VARCHAR2(500), IALARMCODE VARCHAR2(255), IALARM_TIME NUMBER(19), ICONTANT VARCHAR2(500), IPHONE VARCHAR2(500), IALARM_MESSAGE VARCHAR2(2000), IIS_TRIGGER NUMBER(4), ITASK_HANDLE_DETAIL VARCHAR2(100), IALARMTYPEID VARCHAR2(1000), ICREATETIME NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARMINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARMINFO_HISTORY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARMINFO_HISTORY (IID NUMBER(19) NOT NULL, IALARMID VARCHAR2(100), IPLATFORM VARCHAR2(100), IAPPNAME VARCHAR2(255), IHOSTIP VARCHAR2(25), IALARMCONTENT VARCHAR2(500), IALARMCODE VARCHAR2(255), IALARM_TIME NUMBER(19), ICONTANT VARCHAR2(500), IPHONE VARCHAR2(500), IALARM_MESSAGE VARCHAR2(2000), IIS_TRIGGER NUMBER(4), ITASK_HANDLE_DETAIL VARCHAR2(100), IALARMTYPEID VARCHAR2(1000), ICREATETIME NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARMINFO_HISTORY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_COMBINATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_COMBINATION (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), IDES VARCHAR2(255), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_ALARM_COMBINATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_COM_TYPE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_COM_TYPE (IID NUMBER(19) NOT NULL, ICOMBINATIONID NUMBER(19), IALARMTYPEID NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARM_COM_TYPE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCHEME' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCHEME (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), ITYPE NUMBER(4), IALARMCOMBINATIONID NUMBER(19), ISCENE NUMBER(19), IEXECMODEL NUMBER(4), IFEEDBACK_TIME NUMBER(4), ICOOLING_TIME NUMBER(4), IOVERTIME_TIME NUMBER(4), ISTATE NUMBER(4), ICREATETIME NUMBER(19), IONLINETIME NUMBER(19), ICREATEUSER VARCHAR2(50), IAPPLYUSER VARCHAR2(50), IAUDITORUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_SCHEME PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCHEME_EQU' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCHEME_EQU (IID NUMBER(19) NOT NULL, ISCHEMEID NUMBER(19), IEQUID NUMBER(19), IEQUIP VARCHAR2(20), CONSTRAINT PK_IEAI_FSH_SCHEME_EQU PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='ISSELFHEALING';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD ISSELFHEALING NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ISSELFHEALING';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;

	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='ISCHEMEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_ALARMINFO ADD ISCHEMEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='ISCHEMEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_ALARMINFO_HISTORY ADD ISCHEMEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;

END;
/
--4.7.24 
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCENE_TSAKCONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_FSH_SCENE_TSAKCONFIG (IID NUMBER(19) NOT NULL, ITASKID NUMBER(19), ICOMBINATIONID NUMBER(19), IALARMINFO VARCHAR2(255), IPARAMNAME  VARCHAR2(255),CONSTRAINT PK_IEAI_FSH_SCENE_TSAKCONFIG PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK' AND COLUMN_NAME='IAUDITOR';
	IF LI_EXISTS = 1 THEN 
			LS_SQL := 'ALTER TABLE IEAI_FSH_TASK MODIFY (IAUDITOR VARCHAR2(4000))';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME='IAUDITOR';
	IF LI_EXISTS = 1 THEN 
			LS_SQL := 'ALTER TABLE IEAI_FSH_TASK_HISTORY MODIFY (IAUDITOR VARCHAR2(4000))';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
--4.7.25 
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO rename column IALARMTYPEID to IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO add IALARMTYPEID VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'update IEAI_FSH_ALARMINFO set IALARMTYPEID=IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY rename column IALARMTYPEID to IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY add IALARMTYPEID VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'update IEAI_FSH_ALARMINFO_HISTORY set IALARMTYPEID=IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME='ISCENEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD ISCENEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME='ISCENEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD IALARMTYPEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/ 
--4.7.26 not exists

--4.7.17 not exists
--4.7.18 not exists
--4.7.19 not exists
--4.7.20 not exists
--4.7.21 not exists

--4.7.22
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COLLECTION_UPLOAD' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_COLLECTION_UPLOAD (IID NUMBER(19) NOT NULL,RECORDID VARCHAR2(255),TASKTYPE NUMBER(4),AGENTIP VARCHAR2(30),SAVEFILEPATH VARCHAR2(255),FILECONTENT VARCHAR2(255),IUNZIP VARCHAR2(10),UNZIPPATH VARCHAR2(255),ISSUCCESS VARCHAR2(10),<PERSON>ERPERMISSION VARCHAR2(255),<PERSON><PERSON><PERSON><PERSON>ERMISSION VARCHAR2(255),PARAMPERMISSION VARCHAR2(255),OPDESC VARCHAR2(255),OPUSER VARCHAR2(255),OPTIME NUMBER(19),CONSTRAINT PK_IEAI_COLLECTION_UPLOAD PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COLLECTION_PICKUPINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_COLLECTION_PICKUPINFO  (IID NUMBER(19) NOT NULL, RECORDID VARCHAR2(255),TASKTYPE NUMBER(4), IINSTANCEID NUMBER(19), SENDURL VARCHAR2(255), SENDUSER VARCHAR2(50), SENDIP VARCHAR2(20), SENDTIME NUMBER(19), EXECSTATUS NUMBER(4), IDESC VARCHAR2(255), ISSENDSUCSTATUS NUMBER(2),CONSTRAINT PK_IEAI_COLLECTION_PICKUPINFO PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='ISYSTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD ISYSTYPE NUMBER(19) DEFAULT 0 ';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='DEVICETYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD DEVICETYPE VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='TASKTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD TASKTYPE NUMBER(4)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='OUTPATH';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD OUTPATH VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='GLOBALIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD GLOBALIP VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='MOBILEID';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD MOBILEID VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='TASKTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD TASKTYPE NUMBER(4)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='OUTPATH';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD OUTPATH VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='GLOBALIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD GLOBALIP VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='MOBILEID';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD MOBILEID VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
END;
/

--4.7.23
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COLLECTION_RESCALL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_COLLECTION_RESCALL(IID NUMBER(19) NOT NULL, RECORDID VARCHAR2(255), IINSTANCEID NUMBER(19), STARTTIME NUMBER(19), ENDTIME NUMBER(19), STATUS NUMBER(2), CONSTRAINT PK_IEAI_COLLECTION_RESCALL PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECTION_PICKUPINFO' AND COLUMN_NAME='SENDMSG';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD SENDMSG VARCHAR2(4000)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='ISYSTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD ISYSTYPE NUMBER(19) DEFAULT 0 ';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='DEVICETYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD DEVICETYPE VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='DBINSNAME';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD DBINSNAME VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='USERPERMISSION';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD USERPERMISSION VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='GROUPPERMISSION';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD GROUPPERMISSION VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='HD_FTP_SCRIPT' AND COLUMN_NAME='PARAMPERMISSION';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE HD_FTP_SCRIPT ADD PARAMPERMISSION VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;		
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='TASKTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD TASKTYPE NUMBER(4)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='OUTPATH';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD OUTPATH VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='GLOBALIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD GLOBALIP VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='TASKTYPE';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD TASKTYPE NUMBER(4)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='OUTPATH';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD OUTPATH VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='GLOBALIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD GLOBALIP VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='NETEQUIIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD NETEQUIIP VARCHAR2(20)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='NETEQUIIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD NETEQUIIP VARCHAR2(20)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME='NETEQUIIP';
	IF LI_EXISTS = 0 THEN
 		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD NETEQUIIP VARCHAR2(20)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;	
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 19;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (19,19,''信息采集源'',0)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID = -19;
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-19, 0, ''所有采集信息业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 19, -19, -19, -19)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID = 19;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_GROUPMESSAGE(GROUPID, GROUPNAME, GROUPDESCRIPTION, IORDER, IIMG)	VALUES(19, ''信息采集'', ''信息采集模块组'', 19, ''images/info110.png'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FTPTYPE WHERE TID=45;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_FTPTYPE(TID,FTPTYPENAME) VALUES(45,''信息采集'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
END;
/

--4.7.24
	DECLARE
		LS_SQL    VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECTION_PICKUPINFO' AND COLUMN_NAME='AGENTIP';
		IF LI_EXISTS = 0 THEN
	 		LS_SQL := 'ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD AGENTIP VARCHAR2(50) ';
	    	EXECUTE IMMEDIATE LS_SQL;
		END IF;

	END;
/

--4.7.25
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 19;
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'UPDATE IEAI_DBSOURCE SET IDBSOURCENAME = ''递蓝科源'' WHERE IDBSOURCEID = 19';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
END;
/

--4.7.26 not exists

					DECLARE
						LI_SIGN SMALLINT;
						LS_SQL  VARCHAR2(2000);
						LI_IID NUMBER(20);
					BEGIN


						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTOUTPUT_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTOUTPUT_01 ON IEAI_ACTOUTPUT (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTOUTPUT_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTOUTPUT_02 ON IEAI_ACTOUTPUT (IACTSCOPEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNINFO_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNINFO_01 ON IEAI_ACTRUNINFO (IFLOWID ASC, IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNINFO_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNINFO_02 ON IEAI_ACTRUNINFO (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_01 ON IEAI_ACTRUNTIME (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_02 ON IEAI_ACTRUNTIME (IISMONITORACT ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_03 ON IEAI_ACTRUNTIME (IFLOWID ASC, IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_04' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_04 ON IEAI_ACTRUNTIME (IACTNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_07' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_07 ON IEAI_ACTRUNTIME (IERRORTASKID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTTIMECONFIG_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTTIMECONFIG_01 ON IEAI_ACTTIMECONFIG (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ADAPTOR_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ADAPTOR_01 ON IEAI_ADAPTOR (INAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ADAPTOR_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ADAPTOR_02 ON IEAI_ADAPTOR (IUUID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ATTACHMENT' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ATTACHMENT_01 ON IEAI_ATTACHMENT (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_BRANCHINFO_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_BRANCHINFO_01 ON IEAI_BRANCHINFO (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_BRANCHINFO_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_BRANCHINFO_02 ON IEAI_BRANCHINFO (ISTRUTINFOID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_BRANCHSCOPE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_BRANCHSCOPE_01 ON IEAI_BRANCHSCOPE (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_BRANCHSCOPE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_BRANCHSCOPE_02 ON IEAI_BRANCHSCOPE (IPARENTSCOPE ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_CALENDAR_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_CALENDAR_01 ON IEAI_CALENDAR (INAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ERRORTASKROLE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ERRORTASKROLE_01 ON IEAI_ERRORTASKROLE (IERRORTASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ERRORTASKROLE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ERRORTASKROLE_02 ON IEAI_ERRORTASKROLE (IROLEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ERRORTASKUSER_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ERRORTASKUSER_01 ON IEAI_ERRORTASKUSER (IERRORTASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ERRORTASKUSER_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ERRORTASKUSER_02 ON IEAI_ERRORTASKUSER (IUSERID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_01 ON IEAI_EXECACT (IFLOWID ASC, ISTATE ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_02 ON IEAI_EXECACT (IREXECREQUESTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_03 ON IEAI_EXECACT (ISTATE ASC, IID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_04' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_04 ON IEAI_EXECACT (IFLOWID ASC, IACTID ASC, IREXECREQUESTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_05' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_05 ON IEAI_EXECACT (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXECACT_06' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXECACT_06 ON IEAI_EXECACT (ISCOPEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_FLOWENV_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_FLOWENV_01 ON IEAI_FLOWENV (IFLOWID ASC, IMARKED ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_HOLIDAY_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_HOLIDAY_01 ON IEAI_HOLIDAY (ICALID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_IEXECERROR_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_IEXECERROR_01 ON IEAI_IEXECERROR (IEXECACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_IEXECERROR_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_IEXECERROR_02 ON IEAI_IEXECERROR (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_IROLEPERM_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_IROLEPERM_01 ON IEAI_IROLEPERM (IROLEID ASC, ITYPE ASC, IENABLED ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICE_01 ON IEAI_NOTICE (IBEGINTIME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICE_02 ON IEAI_NOTICE (IINVALIDTIME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICE_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICE_03 ON IEAI_NOTICE (IFLOWNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICE_04' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICE_04 ON IEAI_NOTICE (IPROJECTNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICE_05' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICE_05 ON IEAI_NOTICE (IVALIDTIME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICECFMUSER_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICECFMUSER_01 ON IEAI_NOTICECFMUSER (INOTICEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICECFMUSER_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICECFMUSER_02 ON IEAI_NOTICECFMUSER (IUSERID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICEOP_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICEOP_01 ON IEAI_NOTICEOP (IFLOWINSTANCEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICEOP_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICEOP_02 ON IEAI_NOTICEOP (INOTICEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_NOTICEOP_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_NOTICEOP_03 ON IEAI_NOTICEOP (ITASKNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_OPERATION_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_OPERATION_01 ON IEAI_OPERATION (ITASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_PROJECT_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_PROJECT_01 ON IEAI_PROJECT (INAME ASC, IUPLOADTIME DESC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_PROJECT_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_PROJECT_03 ON IEAI_PROJECT (IUUID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RECOVERYPOINT_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_RECOVERYPOINT_01 ON IEAI_RECOVERYPOINT (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RECOVERYPOINT_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_RECOVERYPOINT_02 ON IEAI_RECOVERYPOINT (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RECOVERYPOINT_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_RECOVERYPOINT_03 ON IEAI_RECOVERYPOINT (ISCOPEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_REMOTEEXECACT_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_REMOTEEXECACT_01 ON IEAI_REMOTEEXECACT (IFLOWNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_REMOTEEXECACT_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_REMOTEEXECACT_02 ON IEAI_REMOTEEXECACT (IACTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_REMOTEEXECACT_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_REMOTEEXECACT_03 ON IEAI_REMOTEEXECACT (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_REMOTEEXECACT_04' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_REMOTEEXECACT_04 ON IEAI_REMOTEEXECACT (ISCOPEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_STRUCTINFO_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_STRUCTINFO_01 ON IEAI_STRUCTINFO (IFLOWID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_STRUCTINFO_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_STRUCTINFO_02 ON IEAI_STRUCTINFO (ISCOPEID ASC, IFINISHED ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_TASKITEM_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_TASKITEM_01 ON IEAI_TASKITEM (ITASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_TASKPARAM_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_TASKPARAM_01 ON IEAI_TASKPARAM (ITASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_USER_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE UNIQUE INDEX IDX_IEAI_USER_01 ON IEAI_USER (ILOGINNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_USERPERM_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_USERPERM_01 ON IEAI_USERPERM (ITYPE ASC, IENABLED ASC, IPERMNAME ASC, IPRJADPNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WEEKLYDAY_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_WEEKLYDAY_01 ON IEAI_WEEKLYDAY (ICALID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKDAY_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_WORKDAY_01 ON IEAI_WORKDAY (ICALID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWINSTANCE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_01 ON IEAI_WORKFLOWINSTANCE (IFLOWID ASC, ISTATUS ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWINSTANCE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_02 ON IEAI_WORKFLOWINSTANCE (IPRJUUID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWINSTANCE_03' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_03 ON IEAI_WORKFLOWINSTANCE (IPROJECTNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWINSTANCE_04' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_04 ON IEAI_WORKFLOWINSTANCE (ISTATUS ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKINGTIME_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_WORKINGTIME_01 ON IEAI_WORKINGTIME (IPARENTID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKINGTIME_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_WORKINGTIME_02 ON IEAI_WORKINGTIME (ITYPE ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXECACT_DELAY_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXECACT_DELAY_01 ON IEAI_EXECACT_DELAY (IACTID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXECACT_DELAY_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXECACT_DELAY_02 ON IEAI_EXECACT_DELAY (IFLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE UNIQUE INDEX IDX_IEAI_SYS_01 ON IEAI_SYS (SYSNAME ASC, PRJTYPE ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COM_CHK_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_COM_CHK_01 ON IEAI_COM_CHK (APPLOGO ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COM_CHK_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_COM_CHK_02 ON IEAI_COM_CHK (CPID ASC, STARTLOGO ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_DATA_LAST_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_DATA_LAST_01 ON HD_CHECK_RESULT_DATA_LAST (CPID, COMCHKID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_DATA_LAST_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_DATA_LAST_02 ON HD_CHECK_RESULT_DATA_LAST (IP, PORT)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_RESULT_DATA_CACHE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_RESULT_DATA_CACHE_01 ON HD_CHECK_RESULT_DATA_CACHE (CPID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_RESULT_DATA_CACHE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_RESULT_DATA_CACHE_02 ON HD_CHECK_RESULT_DATA_CACHE (MEID, ISYSID, CIID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_STATUS_LAST_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_STATUS_LAST_01 ON HD_CHECK_STATUS_LAST (CPID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_STATUS_CACHE_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_STATUS_CACHE_01 ON HD_CHECK_STATUS_CACHE(RSDID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_STATUS_CACHE_02' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_STATUS_CACHE_02 ON HD_CHECK_STATUS_CACHE(CPID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HD_CHECK_STATUS_HIS_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_HD_CHECK_STATUS_HIS_01 ON HD_CHECK_STATUS_HIS(RSDID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COMPUTER_LIST_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COMPUTER_LIST_FLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE  INDEX IDX_IEAI_COMPUTER_LIST_FLOWID ON IEAI_COMPUTER_LIST (FLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_CHKITEM_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE UNIQUE INDEX IDX_IEAI_CHKITEM_01 ON IEAI_CHKITEM (ICHKITEMNAME ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IRUNINSID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IRUNINSID ON IEAI_RUNINFO_INSTANCE_HIS (IRUNINSID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HOST_PORT_FLOWID' AND OBJECT_TYPE = 'INDEX';
            IF	LI_SIGN = 0 THEN
	            LS_SQL := 'CREATE INDEX IDX_HOST_PORT_FLOWID ON TEMP_IEAI_REMOTEEXECACT (IAGENTHOST, IAGENTPORT, IFLOWID)';
	            EXECUTE IMMEDIATE LS_SQL;
            END	IF;

            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'SENDREMOTE_SEND_ID' AND OBJECT_TYPE = 'INDEX';
            IF	LI_SIGN = 0 THEN
	            LS_SQL := 'CREATE INDEX SENDREMOTE_SEND_ID ON IEAI_SENDREMOTE (SEND_ID)';
	            EXECUTE IMMEDIATE LS_SQL;
            END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTRUNTIME_HISTORY
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTRUNTIMEH_IACTNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IACTRUNTIMEH_IACTNAME ON IEAI_ACTRUNTIME_HISTORY (IACTNAME)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTRUNTIMEH_IACTTYPE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IACTRUNTIMEH_IACTTYPE ON IEAI_ACTRUNTIME_HISTORY (IACTTYPE)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTRUNTIMEH_IBEGINEXCTIME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IACTRUNTIMEH_IBEGINEXCTIME ON IEAI_ACTRUNTIME_HISTORY (IBEGINEXCTIME)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTRUNTIMEH_IENDTIME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IACTRUNTIMEH_IENDTIME ON IEAI_ACTRUNTIME_HISTORY (IENDTIME)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTRUNTIMEH_IFLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IACTRUNTIMEH_IFLOWID ON IEAI_ACTRUNTIME_HISTORY (IFLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IDX_IEAIWORKFLOWHISTORY_IFN
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAIWORKFLOWHISTORY_IFN' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAIWORKFLOWHISTORY_IFN ON IEAI_WORKFLOWHISTORY (IFLOWNAME)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						
						-- CREATE OR REPLACE IDX FOR TABLE IDX_WORKFLOW_AVGSTART
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOW_AVGSTART' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOW_AVGSTART ON IEAI_WORKFLOW_AVGSTART (IPRJNAME, IFLOWNAME, IPRJUPPERID, IRELATIVESTIME)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						
						-- CREATE OR REPLACE IDX FOR TABLE IDX_ACTOTC_HIS_FLOWID
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTOTC_HIS_FLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_ACTOTC_HIS_FLOWID ON IEAI_ACTOUTPUTHISTORY (IFLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IDX_WORKFLOW_JOBNUM_FLOWID
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOW_JOBNUM_FLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_WORKFLOW_JOBNUM_FLOWID ON IEAI_WORKFLOW_JOBNUM (IFLOWID,IPRJID)';
							EXECUTE IMMEDIATE LS_SQL;
							COMMIT;
						END	IF;
						
						-- CREATE OR REPLACE IDX FOR TABLE INX_ACTOTC_FLOWID
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_ACTOTC_FLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INX_ACTOTC_FLOWID ON IEAI_ACTOUTPUTCICLE (IFLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						
						-- CREATE OR REPLACE IDX FOR TABLE IDX_PROJECT_USER_NAME
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_PROJECT_USER_NAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_PROJECT_USER_NAME ON IEAI_PROJECT_USER (IPROJECTNAME)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_RUN
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_RUN' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_RUN ON IEAI_ACTMONITOR_RUN (IFLOWID,IMONITORID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_RUNINFO
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_RUNINFO' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_RUNINFO ON IEAI_ACTMONITOR_RUNINFO (IFLOWID,IMONITORID,ISTATUS)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDMONTH
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_VALIDMONTH' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDMONTH ON IEAI_ACTMONITOR_VALIDMONTH (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDMONTH
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_VALIDWEEK' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDWEEK ON IEAI_ACTMONITOR_VALIDWEEK (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDDAY
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_VALIDDAY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDDAY ON IEAI_ACTMONITOR_VALIDDAY (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDDAY
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_VALIDMINUTE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDMINU ON IEAI_ACTMONITOR_VALIDMINUTE (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDDAY
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTMONITOR_VALIDHOUR' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDHOUR ON IEAI_ACTMONITOR_VALIDHOUR (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_CALLWORKFLOW_INFO' AND INDEX_NAME='IDX_IEAI_CALLWORKFLOW_INFO_01' ;
         		IF LI_SIGN = 0 THEN
         		 	LS_SQL := 'CREATE INDEX IDX_IEAI_CALLWORKFLOW_INFO_01 ON IEAI_CALLWORKFLOW_INFO(IMAINFLOWID ASC)';
             	EXECUTE IMMEDIATE LS_SQL;
         		END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_CALLWORKFLOW_INFO' AND INDEX_NAME='IDX_IEAI_CALLWORKFLOW_INFO_02' ;
		         IF LI_SIGN = 0 THEN
		         		 LS_SQL := 'CREATE INDEX IDX_IEAI_CALLWORKFLOW_INFO_02 ON IEAI_CALLWORKFLOW_INFO(ICALLFLOWID ASC)';
		             EXECUTE IMMEDIATE LS_SQL;
		         END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_PROJECT' AND INDEX_NAME='IDX_IEAI_PROJECT_04' ;
		         IF LI_SIGN = 0 THEN
		         		 LS_SQL := 'CREATE INDEX IDX_IEAI_PROJECT_04 ON IEAI_PROJECT(IUPPERID ASC)';
		             EXECUTE IMMEDIATE LS_SQL;
		         END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_PROJECT' AND INDEX_NAME='IDX_IEAI_PROJECT_05' ;
		         IF LI_SIGN = 0 THEN
		         		 LS_SQL := 'CREATE INDEX IDX_IEAI_PROJECT_05 ON IEAI_PROJECT(ILATESTID ASC)';
		             EXECUTE IMMEDIATE LS_SQL;
		         END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_PROJECT' AND INDEX_NAME='IDX_IEAI_PROJECT_06' ;
		         IF LI_SIGN = 0 THEN
		         		 LS_SQL := 'CREATE INDEX IDX_IEAI_PROJECT_06 ON IEAI_PROJECT(PROTYPE ASC)';
		             EXECUTE IMMEDIATE LS_SQL;
		         END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_WORKFLOWINSTANCE' AND INDEX_NAME='IDX_WORKFLOWINSTANCE_05' ;
		         IF LI_SIGN = 0 THEN
		         		 LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_05 ON IEAI_WORKFLOWINSTANCE(IPRJUPPERID ASC)';
		             EXECUTE IMMEDIATE LS_SQL;
		         END IF;

		         SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXCELMODEL_ACTSYSNAME' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_ACTSYSNAME ON IEAI_EXCELMODEL (IACTNAME, ISYSTEM)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXCELMODEL_IACTNAME' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_IACTNAME ON IEAI_EXCELMODEL (IACTNAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXEXECLMODEL' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXEXECLMODEL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXEXECLMODELFLAG' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXEXECLMODELFLAG ON IEAI_EXCELMODEL (IMAINPRONAME, IFLAG)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXOPERIDNAME' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXOPERIDNAME ON IEAI_EXCELMODEL (IOPERATIONID, IMAINLINENAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXPROACTNAME' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXPROACTNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IACTNAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_EXUNIONALL' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_EXUNIONALL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME, ICHILDPRONAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_IEAI_EXCELMODEL_ICLDPNAME' ;
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_IEAI_EXCELMODEL_ICLDPNAME ON IEAI_EXCELMODEL (ICHILDPRONAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT SIGN(COUNT(1)) INTO LI_SIGN FROM USER_INDEXES WHERE TABLE_NAME='IEAI_EXCELMODEL' AND INDEX_NAME='IDX_MODEL_ID_MAINNAME' ;
						 IF	LI_SIGN = 0 THEN
							 	 LS_SQL := 'CREATE INDEX IDX_MODEL_ID_MAINNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDDAYBAK_IVALIDTIMEID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
							 	 LS_SQL := 'CREATE INDEX IDX_VALIDDAYBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDDAY_BAK(IVALIDTIMEID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDHOURBAK_IVALIDTIMEID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
							 	 LS_SQL := 'CREATE INDEX IDX_VALIDHOURBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDHOUR_BAK(IVALIDTIMEID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDMONTHBAK_IVALIDTIMEID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								 LS_SQL := 'CREATE INDEX IDX_VALIDMONTHBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDMONTH_BAK(IVALIDTIMEID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDWEEKBAK_IVALIDTIMEID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
							 	LS_SQL := 'CREATE INDEX IDX_VALIDWEEKBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDWEEK_BAK(IVALIDTIMEID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDTIME_IMONITORINFOID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_VALIDTIME_IMONITORINFOID ON IEAI_ACTMONITOR_VALIDTIME(IMONITORINFOID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MENU_BUTTON_IBUTTONID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_MENU_BUTTON_IBUTTONID ON IEAI_MENU_BUTTON(IBUTTONID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMEBAK_IMONITORINFOID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_TIMEBAK_IMONITORINFOID ON IEAI_ACTMONITOR_VALIDTIME_BAK(IMONITORINFOID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MONITORINFOBAK_IMONITORID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_MONITORINFOBAK_IMONITORID ON IEAI_ACTMONITOR_INFO_BAK(IMONITORID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_PERMISSION_IMENUBUTTONID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_PERMISSION_IMENUBUTTONID ON IEAI_HIGHOPER_PERMISSION(IMENUBUTTONID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MINUTEBAK_IVALIDTIMEID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_MINUTEBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_MINUTE_BAK(IVALIDTIMEID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTMONITORINFO_IMONITORID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_ACTMONITORINFO_IMONITORID ON IEAI_ACTMONITOR_INFO(IMONITORID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWHISTORY_IFLOWID' AND OBJECT_TYPE = 'INDEX';
						 IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_WORKFLOWHISTORY_IFLOWID ON IEAI_WORKFLOWHISTORY(IFLOWID)';
						 EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXCELMODEL_ACTSYSNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_ACTSYSNAME_COPY ON IEAI_EXCELMODEL_COPY (IACTNAME, ISYSTEM)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXCELMODEL_IACTNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_IACTNAME_COPY ON IEAI_EXCELMODEL_COPY (IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ICLDPNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ICLDPNAME_COPY ON IEAI_EXCELMODEL_COPY (ICHILDPRONAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MODEL_ID_MAINNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_MODEL_ID_MAINNAME_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXEXECLMODELFLAG_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXEXECLMODELFLAG_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IFLAG)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXEXECLMODEL_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXEXECLMODEL_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME, IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXOPERIDNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXOPERIDNAME_COPY ON IEAI_EXCELMODEL_COPY (IOPERATIONID, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXPROACTNAME_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXPROACTNAME_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXUNIONALL_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXUNIONALL_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME, IACTNAME, ICHILDPRONAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_ID_MAINNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TMP_ID_MAINNAME ON TMP_IEAI_EXCELMODEL (IOPERATIONID, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_OPERID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TMP_OPERID ON TMP_IEAI_EXCELMODEL (IOPERATIONID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_MAINNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TMP_MAINNAME ON TMP_IEAI_EXCELMODEL (IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_PROCHILDNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TMP_PROCHILDNAME ON TMP_IEAI_EXCELMODEL (IMAINPRONAME, ICHILDPRONAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_PROMAINLINENAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TMP_PROMAINLINENAME ON TMP_IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXACTPRE_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXACTPRE_COPY ON IEAI_ACTPRE_COPY (IOPERATIONID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTPRE_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTPRE_COPY ON IEAI_ACTPRE_COPY (IPREACTNAME, IPROJECTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTSUCC_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTSUCC_COPY ON IEAI_ACTSUCC_COPY (ISUCCACTNAME, IPROJECTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INDEXACTSUCC_COPY' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INDEXACTSUCC_COPY ON IEAI_ACTSUCC_COPY (IOPERATIONID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_FLOW_PARAM' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_FLOW_PARAM ON IEAI_FLOW_PARAM (IID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SON_PROJECT' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_SON_PROJECT ON IEAI_SON_PROJECT (IID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SYSTEMGROUP_GROUPID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_SYSTEMGROUP_GROUPID ON IEAI_SYSTEM_GROUP (ISYSGROUPNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXCELMODEL_ACTSYSNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_ACTSYSNAME ON IEAI_EXCELMODEL (IACTNAME, ISYSTEM)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXCELMODEL_IACTNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXCELMODEL_IACTNAME ON IEAI_EXCELMODEL (IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXEXECLMODEL' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXEXECLMODEL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXEXECLMODELFLAG' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXEXECLMODELFLAG ON IEAI_EXCELMODEL (IMAINPRONAME, IFLAG)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXOPERIDNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXOPERIDNAME ON IEAI_EXCELMODEL (IOPERATIONID, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXPROACTNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXPROACTNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXUNIONALL' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXUNIONALL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME, ICHILDPRONAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXCELMODEL_ICLDPNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_EXCELMODEL_ICLDPNAME ON IEAI_EXCELMODEL (ICHILDPRONAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MODEL_ID_MAINNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_MODEL_ID_MAINNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXACTPRE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXACTPRE ON IEAI_ACTPRE (IOPERATIONID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTPRE_PREPN' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTPRE_PREPN ON IEAI_ACTPRE (IPREACTNAME, IPROJECTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_EXACTSUCC' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_EXACTSUCC ON IEAI_ACTSUCC (IOPERATIONID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTSUCC_SUPN' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTSUCC_SUPN ON IEAI_ACTSUCC (ISUCCACTNAME, IPROJECTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						-- CREATE OR REPLACE IDX FOR TABLE IEAI_ACTMONITOR_VALIDDAY
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTMONITOR_VALIDHOUR' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTMONITOR_VALIDHOUR ON IEAI_ACTMONITOR_VALIDHOUR (IVALIDTIMEID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_PROJECT_USER_NAME' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX IDX_PROJECT_USER_NAME ON IEAI_PROJECT_USER (IPROJECTNAME)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_ACTRUNTIME_PART_ACTID' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_ACTRUNTIME_PART_ACTID ON IEAI_ACTRUNTIME_PART (IACTID)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_PART_FLOWID_ACTNAME' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_PART_FLOWID_ACTNAME ON IEAI_ACTRUNTIME_PART (IACTNAME, IFLOWID)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_RUNTIME_PART_FLOWID' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_RUNTIME_PART_FLOWID ON IEAI_ACTRUNTIME_PART (IFLOWID)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_RUNTIME_PART_FLOWID_ACTID' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_RUNTIME_PART_FLOWID_ACTID ON IEAI_ACTRUNTIME_PART (IFLOWID, IACTID)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_ACT_RELATION_MESS' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_ACT_RELATION_MESS ON IEAI_ACT_RELATION_MESS (IPROJECTNAME, IFLOWNAME, IACTNAME)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_ACT_TOPO_INSTANCE' AND OBJECT_TYPE = 'INDEX'; IF LI_SIGN = 0 THEN LS_SQL := 'CREATE INDEX INX_ACT_TOPO_INSTANCE ON IEAI_ACT_TOPO_INSTANCE (IPROJECTNAME, IFLOWNAME, IACTNAME)'; EXECUTE IMMEDIATE LS_SQL;
					  END IF;

					  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDPART_IPRJNAME' AND OBJECT_TYPE = 'INDEX';
					  IF  LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX IDX_VALIDPART_IPRJNAME ON IEAI_ACTRUNTIME_VALIDPART (IPRJNAME)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END  IF;
					  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDPART_NAME' AND OBJECT_TYPE = 'INDEX';
					  IF  LI_SIGN = 0 THEN
					    LS_SQL := 'create index IDX_VALIDPART_NAME on IEAI_ACTRUNTIME_VALIDPART (IPRJNAME, IFLOWNAME, IACTNAME)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END  IF;
					  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'VALIDPART_IDX' AND OBJECT_TYPE = 'INDEX';
					  IF  LI_SIGN = 0 THEN
					    LS_SQL := 'create index VALIDPART_IDX on IEAI_ACTRUNTIME_VALIDPART (IBEGINEXCTIME ,IPRJNAME,IFLOWNAME,IACTNAME,IDELFLAG,IDESC,IACTDEFNAME,IACTNAMETYPE)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END  IF;

					  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'ERRORINFO_PAR_IDX' AND OBJECT_TYPE = 'INDEX';
					  IF  LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX ERRORINFO_PAR_IDX ON IEAI_ERRORINFO_PART (IBEGINTIME ,IPRJNAME,IFLOWNAME,IERRORTYPE,IACTTYPE)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END  IF;

					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_AVGRECORD_EVERYDAY' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX IDX_AVGRECORD_EVERYDAY ON IEAI_AVGRECORD_EVERYDAY(IPRJNAME, IACTNAME,IRECORDDAY)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_AVGRECORD_EVERYDAY_WEEK' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX IDX_AVGRECORD_EVERYDAY_WEEK ON IEAI_AVGRECORD_EVERYDAY_WEEK(IPRJNAME, IACTNAME,IRECORDDAY)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_AVGRECORD_EVERYDAY_MON' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX IDX_AVGRECORD_EVERYDAY_MON ON IEAI_AVGRECORD_EVERYDAY_MON(IPRJNAME, IACTNAME,IRECORDDAY)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;

					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACT_AVGTIME' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE  INDEX IDX_ACT_AVGTIME ON TMP_ACT_AVGTIME (IACTNAME)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;
					  
					  -- CREATE SEQUENCES
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_TEXT';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE SEQUENCE SEQ_TEXT MINVALUE 1 MAXVALUE 999999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_SON_PROJECT';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create sequence SEQ_SON_PROJECT minvalue 1 maxvalue 999999999999999999999999999 start with 1 increment by 1 cache 20';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_SON_PROJECT';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create sequence SEQ_SON_EXCELMODEL_DAYSTART minvalue 1 maxvalue 999999999999999999999999999 start with 1 increment by 1 cache 20';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_SON_EXCELMOLDE_PREPRO';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create sequence SEQ_SON_EXCELMOLDE_PREPRO minvalue 1 maxvalue 999999999999999999999999999 start with 1 increment by 1 cache 20';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'ERRORINFO_PAR_IDX' AND OBJECT_TYPE = 'INDEX';
						IF LI_SIGN = 0 THEN
						  LS_SQL := 'create index ERRORINFO_PAR_IDX on IEAI_ERRORINFO_PART (IBEGINTIME, IPRJNAME, IFLOWNAME, IERRORTYPE, IACTTYPE)';
						  EXECUTE IMMEDIATE LS_SQL;
						END IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDPART_IPRJNAME' AND OBJECT_TYPE = 'INDEX';
						IF LI_SIGN = 0 THEN
						  LS_SQL := 'create index IDX_VALIDPART_IPRJNAME on IEAI_ACTRUNTIME_VALIDPART (IPRJNAME)';
						  EXECUTE IMMEDIATE LS_SQL;
						END IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_VALIDPART_NAME' AND OBJECT_TYPE = 'INDEX';
						IF LI_SIGN = 0 THEN
						  LS_SQL := 'create index IDX_VALIDPART_NAME on IEAI_ACTRUNTIME_VALIDPART (IPRJNAME, IFLOWNAME, IACTNAME)';
						  EXECUTE IMMEDIATE LS_SQL;
						END IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'VALIDPART_IDX' AND OBJECT_TYPE = 'INDEX';
						IF LI_SIGN = 0 THEN
						  LS_SQL := 'create index VALIDPART_IDX on IEAI_ACTRUNTIME_VALIDPART (IBEGINEXCTIME, IPRJNAME, IFLOWNAME, IACTNAME, IDELFLAG, IDESC, IACTDEFNAME, IACTNAMETYPE)';
						  EXECUTE IMMEDIATE LS_SQL;
						END IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_FLOWEND_FLAG_IACTNUM' AND OBJECT_TYPE = 'INDEX';
						IF LI_SIGN = 0 THEN
						  LS_SQL := 'CREATE INDEX IDX_IEAI_FLOWEND_FLAG_IACTNUM ON IEAI_FLOWEND_FLAG (IACTNUM)';
						  EXECUTE IMMEDIATE LS_SQL;
						END IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_DEPRELACTION_ACTTYPE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_DEPRELACTION_ACTTYPE ON IEAI_ACT_DEPRELACTION (IACTTYPE)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_DEPRELACTION_CALLFLOWNAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_DEPRELACTION_CALLFLOWNAME ON IEAI_ACT_DEPRELACTION (ICALLFLOWNAME)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_DEPRELACTION_PRJFLOWACT' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_DEPRELACTION_PRJFLOWACT ON IEAI_ACT_DEPRELACTION (IPROJECTNAME, IFLOWNAME, IACTNAME)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_PRE_RELACTION_PAR' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_PRE_RELACTION_PAR ON IEAI_ACT_PRE_RELACTION (IPARID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_PRE_RELACTION_PRERE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_PRE_RELACTION_PRERE ON IEAI_ACT_PRE_RELACTION (IPRERELATION)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SUCC_RELACTION_PAR' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_SUCC_RELACTION_PAR ON IEAI_ACT_SUCC_RELACTION (IPARID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SUCC_RELACTION_SUCCRE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_SUCC_RELACTION_SUCCRE ON IEAI_ACT_SUCC_RELACTION (ISUCCRELATION)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_ACTRUNTIME_VAL_IFLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX INX_ACTRUNTIME_VAL_IFLOWID ON IEAI_ACTRUNTIME_VALIDPART(IFLOWID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_AGENTRES_AGENTNAME' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX INX_AGENTRES_AGENTNAME ON IEAI_AGENTRESOURCE (IAGENTNAME)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;

					  SELECT SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'INX_AGENTRES_PRJNAME' AND OBJECT_TYPE = 'INDEX';
					  IF LI_SIGN = 0 THEN
					    LS_SQL := 'CREATE INDEX INX_AGENTRES_PRJNAME ON IEAI_AGENTRESOURCE (IPRJNAME)';
					    EXECUTE IMMEDIATE LS_SQL;
					  END IF;

					  SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_FLOWID_SHELLCMD_OUTPUT' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_FLOWID_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT(IFLOWID)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTFINISHED_NAME' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_ACTFINISHED_NAME ON IEAI_ACTFINISHED_FLAG (IDATADATE, IPRONAME, IFLOWNAME, IACTNAME)';
						EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTLAST_FLOWID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_ACTLAST_FLOWID ON IEAI_ACT_LASTLINE (IFLOW_ID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AGENT_MAINTAIN_TASK' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_AGENT_MAINTAIN_TASK ON IEAI_AGENT_MAINTAIN_TASK (IREQUESTID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ISSUERECORD_SJC' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_SCRIPT_ISSUERECORD_SJC ON IEAI_SCRIPT_ISSUERECORD (ISJC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ISSUERECORD_DATE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_SCRIPT_ISSUERECORD_DATE ON IEAI_SCRIPT_ISSUERECORD (IDATE)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ISTEPID_SHELLCMD_OUTPUT' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_ISTEPID_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (ISTEPID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_COLVALUE' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;


						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SAIPS_WRKITEMID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create index IDX_SAIPS_WRKITEMID ON IEAI_SCRIPT_AUDITING_IPS(IWORKITEMID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SINSTANCE_MAINID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create index IDX_SINSTANCE_MAINID on IEAI_SCRIPT_INSTANCE (IMAINID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ST_SRTUUID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create index IDX_ST_SRTUUID ON IEAI_SCRIPT_TEST(ISCRIPTUUID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

                        SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SERVICES_SRTUUID' AND OBJECT_TYPE = 'INDEX';
                        IF	LI_SIGN = 0 THEN
                                LS_SQL := 'create index IDX_SERVICES_SRTUUID ON IEAI_SCRIPT_SERVICES(ISCRIPTUUID)';
                        EXECUTE IMMEDIATE LS_SQL;
                        END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SC_SU_FROM_FLAG' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'create index IDX_SC_SU_FROM_FLAG ON IEAI_SCRIPTS_COAT(ISTART_USER,IFROM,FLAG)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_OUTPUT_IPID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TIMETASK_OUTPUT_IPID ON IEAI_TIMETASK_OUTPUT(IPID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_HIS_TASKID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TIMETASK_HIS_TASKID ON IEAI_TIMETASK_HIS (TASKID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_IP_HIS_IP' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TIMETASK_IP_HIS_IP ON IEAI_TIMETASK_IP_HIS (IP)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_IP_HIS_TASKHISID' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_TIMETASK_IP_HIS_TASKHISID ON IEAI_TIMETASK_IP_HIS (TASKHISID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_INSTANCEINFO01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_INSTANCEINFO01 ON IEAI_INSTANCEINFO (IINSTANCEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SUS_ENV_INFO01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_SUS_ENV_INFO01 ON IEAI_SUS_ENV_INFO (IINSTANCEID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME01 ON IEAI_ACTRUNTIME (ITASKID ASC)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;
						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RUN_INSTANCE_HIS_01' AND OBJECT_TYPE = 'INDEX';
						IF	LI_SIGN = 0 THEN
							LS_SQL := 'CREATE INDEX IDX_IEAI_RUN_INSTANCE_HIS_01 ON IEAI_RUN_INSTANCE_HIS(ITASKID)';
							EXECUTE IMMEDIATE LS_SQL;
						END	IF;

						SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IPRJUPPERID' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IPRJUPPERID ON IEAI_ACTINFO (IPRJUPPERID)';
								EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IACTNAME' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IACTNAME ON IEAI_ACTINFO (IACTNAME) ';
								EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IFLOWUPPERID' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IFLOWUPPERID ON IEAI_ACTINFO (IFLOWUPPERID) ';
								EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ILASTID' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_ILASTID ON IEAI_ACTINFO (ILASTID)';
								EXECUTE IMMEDIATE LS_SQL;
						 END	IF;

						 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IEAI_ACTINFO' AND OBJECT_TYPE = 'TABLE';
							 IF LI_SIGN = 1 THEN
								SELECT  NVL(MAX(IID),0)+10  INTO LI_IID FROM IEAI_ACTINFO ;
							  SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_ACTINFO';
									IF	LI_SIGN = 0 THEN
										LS_SQL := 'CREATE SEQUENCE SEQ_ACTINFO MINVALUE ' || LI_IID || ' MAXVALUE 99999999999999 START WITH ' || LI_IID || ' INCREMENT BY 1 CACHE 500';
									EXECUTE IMMEDIATE LS_SQL;
								END	IF;
							END	IF;


						 	SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUI_RELATION_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_02' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_02' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_02' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_03' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_SYS_RELATION_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_03' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT (EIID, EIIP, EINAME)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAISUSITSMTASKIDREL_01' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAISUSITSMTASKIDREL_01 ON IEAI_SUS_ITSM_TASKID_REL (IITSM_TASKID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;


							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AUTOSINGLE_ACT_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_AUTOSINGLE_ACT_01 ON IEAI_AUTOSINGLE_ACT (REQID ASC, IACTSTATE DESC)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_01 ON IEAI_ITSM_AUTO_CHILD (ISTATE ASC, IP ASC, ITASKID ASC,IINSTANCEID ASC)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_02' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_02 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ASC,IENDTIME ASC)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_03' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_03 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ASC,ISTARTTIME ASC)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_04' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_04 ON IEAI_ITSM_AUTO_CHILD (  ITASKID ASC,IINSTANCEID ASC,ISTATE ASC)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;



							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TEMP_LOOKMONITOR_BASE_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_01 ON TEMP_LOOKMONITOR_BASE (IPRJNAME)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TEMP_LOOKMONITOR_BASE_02' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_02 ON TEMP_LOOKMONITOR_BASE (IID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTSUCC_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ACTSUCC_01 ON IEAI_ACTSUCC (IOPERATIONID,ISUCCACTNAME)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCHEDULER_ACT_03' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_03 ON IEAI_SCHEDULER_ACT (IFLOWID,IACTNAME,IISSTATE)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCHEDULER_ACT_04' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_04 ON IEAI_SCHEDULER_ACT (IFLOWID,IPRENUM,IISSTATE)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCHEDULER_ACT_05' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_05 ON IEAI_SCHEDULER_ACT (IFLOWID,IFLOWINS,IPRENUM,IISSTATE,IOPERATIONID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_EXCELMODEL_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_EXCELMODEL_01 ON IEAI_EXCELMODEL (IACTNAME,IMAINPRONAME)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_09' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_09 ON IEAI_ACTRUNTIME (IFLOWID,IACTNAME,ISTATE)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ODSACTFINISHED_FLAG_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_ODSACTFINISHED_FLAG_01 ON IEAI_ODSACTFINISHED_FLAG (IPRJID,IDATADATE,IPRONAME,IFLOWNAME,IACTNAME)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTFINISHED_FLAG_NEW_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_01 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IOPERATIONID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_KEYFLOW_INFO_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_KEYFLOW_INFO_01 ON IEAI_KEYFLOW_INFO (IFLOWRUNOWNID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKFLOW_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_WORKFLOW_01 ON IEAI_WORKFLOW (IFLOWID,IPRJID,ISTATUS,ILATESTID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TEMP_IEAI_REMOTEEXECACT_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TEMP_IEAI_REMOTEEXECACT_01 ON TEMP_IEAI_REMOTEEXECACT (IFLOWID,IAGENTHOST,IAGENTPORT)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AGENTINFO_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_AGENTINFO_01 ON IEAI_AGENTINFO (IAGENT_IP,IAGENT_PORT)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_FLOWDEF_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_FLOWDEF_01 ON IEAI_FLOWDEF (IFLOWNAME)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_FLOWDEF_02' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_FLOWDEF_02 ON IEAI_FLOWDEF (IPRJID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_PERMISSION_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_SYS_PERMISSION_01 ON IEAI_SYS_PERMISSION (IPROID,IPERMISSION,IROLEID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKFLOW_JOBNUM_01' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_01 ON IEAI_WORKFLOW_JOBNUM (IDATADATE,IPRJNAME,IQUERY)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKFLOW_JOBNUM_02' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_02 ON IEAI_WORKFLOW_JOBNUM (IQUERY)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WORKFLOW_03' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_WORKFLOW_03 ON IEAI_WORKFLOW (IPRJID,ISTATUS)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_WORKFLOWINSTANCE_06' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_06 ON IEAI_WORKFLOWINSTANCE (IPROJECTNAME,IFLOWNAME,IFLOWINSNAME,IFLOWID,ISTATUS)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME_08' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME_08 ON IEAI_ACTRUNTIME (ISTATE)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;


							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_EXECACT_NEW' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TMP_EXECACT_NEW ON TMP_EXECACT_NEW (IUUID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_EXEC_ACTNEXTINFO_NEW' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TMP_EXEC_ACTNEXTINFO_NEW ON TMP_EXEC_ACTNEXTINFO_NEW (IUUID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_QUERY_ACTNEXTINFO_NEW' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TMP_QUERY_ACTNEXTINFO_NEW ON TMP_QUERY_ACTNEXTINFO_NEW (IUUID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_TMP_MUTEX_INFO_NEW' AND OBJECT_TYPE = 'INDEX';
									IF	LI_SIGN = 0 THEN
									LS_SQL := 'CREATE INDEX IDX_TMP_MUTEX_INFO_NEW ON TMP_MUTEX_INFO_NEW (IUUID)';
									EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD01' AND OBJECT_TYPE = 'INDEX';
		                    IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD01 ON IEAI_ITSM_AUTO_CHILD (IINSTANCEID)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME ON IEAI_ACTRUNTIME (IERRORTASKID,IFLOWID,IACTNAME)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SHELLCMD_OUTPUT' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (IFLOWID,IFLOWNAME)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;
							SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DATACOLLECT_SIZE_01' AND OBJECT_TYPE = 'INDEX';
							IF  LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_DATACOLLECT_SIZE_01 ON IEAI_DATACOLLECT_SIZE (IINSTID)';
								EXECUTE IMMEDIATE LS_SQL;
							END IF;

							 SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTFINISHED_FLAG_NEW_01' AND OBJECT_TYPE = 'INDEX';
								IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_01 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IOPERATIONID)';
								EXECUTE IMMEDIATE LS_SQL;
							 END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ACTFINISHED_FLAG_NEW_02' AND OBJECT_TYPE = 'INDEX';
								IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_02 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IPRONAME,IFLOWNAME,IACTNAME)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCRIPT_FLOW' AND OBJECT_TYPE = 'INDEX';
								IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_SCRIPT_FLOW ON IEAI_SCRIPT_FLOW (IWORKITEMID)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_C_IWC' AND OBJECT_TYPE = 'INDEX';
								IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWC  ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID,ICOLHEADER)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_C_IWCH' AND OBJECT_TYPE = 'INDEX';
								IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWCH ON IEAI_DOUBLECHECK_COLVALUE_HIS (IWORKITEMID,ICOLHEADER)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_WARNOPER_OPERTYPE' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_WARNOPER_OPERTYPE ON IEAI_WARNOPER (IOPERNAME, IOPERUSER, IOPERTYPE)';
								EXECUTE IMMEDIATE LS_SQL;
							END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_FLOW_SCRIPTUUID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'CREATE INDEX IDX_SCRIPT_FLOW_SCRIPTUUID ON IEAI_SCRIPT_FLOW (IMXSERVICEID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ITSM_TASKUUID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'CREATE INDEX IDX_SCRIPT_ITSM_TASKUUID ON IEAI_SCRIPT_ITSMPUBLISH (TASKUUID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ITSMHTML_TASKUUID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                            LS_SQL := 'CREATE INDEX IDX_SCRIPT_ITSMHTML_TASKUUID ON IEAI_SCRIPT_CIB_HTMLFILE (ITASKUUID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCRIPT_FUJIAN_TEMP' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                    LS_SQL := 'CREATE INDEX IDX_IEAI_SCRIPT_FUJIAN_TEMP ON IEAI_SCRIPT_ATTACHMENT_TEMP (IWORKITEMID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_EXECTIME_UUID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                    LS_SQL := 'CREATE INDEX IDX_SCRIPT_EXECTIME_UUID ON IEAI_SCRIPT_EXECTIME(SCRIPTUUID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPTDEPEND_SRCUUID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                    LS_SQL := 'CREATE INDEX IDX_SCRIPTDEPEND_SRCUUID ON IEAI_SCRIPT_DEPENDSCRIPT (SRCSCRIPTUUID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COLLECT_SIZENEW_02' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                    LS_SQL := 'CREATE INDEX IDX_IEAI_COLLECT_SIZENEW_02 ON IEAI_DATACOLLECT_SIZENEW (IINSTID)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COLLECT_SIZENEW_03' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                    LS_SQL := 'CREATE INDEX IDX_IEAI_COLLECT_SIZENEW_03 ON IEAI_DATACOLLECT_SIZENEW (IDATETIME)';
                            EXECUTE IMMEDIATE LS_SQL;
                            END	IF;



							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTIVITY_PORT_ACTID' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_ACTIVITY_PORT_ACTID ON IEAI_ACTIVITY_PORT(ACTID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

							SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTIVITY_PORT_PORTID' AND OBJECT_TYPE = 'INDEX';
							IF	LI_SIGN = 0 THEN
								LS_SQL := 'CREATE INDEX IDX_IEAI_ACTIVITY_PORT_PORTID ON IEAI_ACTIVITY_PORT(PORTID)';
							EXECUTE IMMEDIATE LS_SQL;
							END	IF;

                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPTS_COAT_WORKID' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'CREATE INDEX IDX_SCRIPTS_COAT_WORKID ON IEAI_SCRIPTS_COAT(IWORKITEMID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END IF;

                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_I_SCRIPT_SCOPE_VARIABLE' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'CREATE INDEX IDX_I_SCRIPT_SCOPE_VARIABLE ON IEAI_SCRIPT_SCOPE_VARIABLE (IVARIABLE_ID, IBIND_ID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;
                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_I_SCRIPT_SCOPE_VARIABLE_P' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'CREATE INDEX IDX_I_SCRIPT_SCOPE_VARIABLE_P ON IEAI_SCRIPT_SCOPE_VARIABLE_P (IVARIABLE_ID, IBIND_ID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;

                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_SCOPE_FUNC' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'create index IDX_SCRIPT_SCOPE_FUNC ON IEAI_SCRIPT_SCOPE_FUNCLIB(IFUNC_ID, IBIND_ID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;

                            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_SCOPE_FUN_PUB' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'create index IDX_SCRIPT_SCOPE_FUN_PUB ON IEAI_SCRIPT_SCOPE_FUNCLIB_PUB(IFUNC_ID, IBIND_ID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;

	           SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MPS' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'create index IDX_MPS on IEAI_MENU_PERMISSIONS (IMENUID, IROLEID, IPERMISSION)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;

	          SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_MENU_IPG' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_SIGN = 0 THEN
                                LS_SQL := 'create index IDX_MENU_IPG on IEAI_MENU (IID, IPARENTNAME, IGROUPMESSID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;

					SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_MQ_CONSUMER_01' AND OBJECT_TYPE = 'INDEX';
                    IF  LI_SIGN = 0 THEN
  	                    LS_SQL := 'CREATE INDEX IDX_IEAI_MQ_CONSUMER_01 ON IEAI_MQ_CONSUMER (IFLOWID)';
                        EXECUTE IMMEDIATE LS_SQL;
                    END  IF;
	                SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_MQ_CONSUMER_02' AND OBJECT_TYPE = 'INDEX';
                    IF  LI_SIGN = 0 THEN
  	                    LS_SQL := 'CREATE INDEX IDX_IEAI_MQ_CONSUMER_02 ON IEAI_MQ_CONSUMER (IUUID)';
                        EXECUTE IMMEDIATE LS_SQL;
                    END  IF;
	                SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_MQ_CONSUMER_HIS_01' AND OBJECT_TYPE = 'INDEX';
                    IF  LI_SIGN = 0 THEN
  	                    LS_SQL := 'CREATE INDEX IDX_IEAI_MQ_CONSUMER_HIS_01 ON IEAI_MQ_CONSUMER_HIS (IFLOWID)';
                        EXECUTE IMMEDIATE LS_SQL;
                    END  IF;
	                SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_MQ_CONSUMER_HIS_02' AND OBJECT_TYPE = 'INDEX';
                    IF  LI_SIGN = 0 THEN
  	                    LS_SQL := 'CREATE INDEX IDX_IEAI_MQ_CONSUMER_HIS_02 ON IEAI_MQ_CONSUMER_HIS (IUUID)';
                        EXECUTE IMMEDIATE LS_SQL;
                    END  IF;
                    SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CITASK_VERSION' AND OBJECT_TYPE = 'INDEX';
                    IF  LI_SIGN = 0 THEN
  	                    LS_SQL := 'CREATE INDEX IDX_CITASK_VERSION ON IEAI_CITASK_VERSION (PROJ_ID,CITASKNAME)';
                        EXECUTE IMMEDIATE LS_SQL;
                    END  IF;
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_INSTANCEINFO' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_INSTANCEINFO ON IEAI_INSTANCEINFO(IPARACHECK,ISYSTYPE)';
						EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_INSTANCEINFO_IPS' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_INSTANCEINFO_IPS ON IEAI_INSTANCEINFO_IPS(INSTANCEINFO_ID)';
						EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RUNINFO_INSTANCE_IPS' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_RUNINFO_INSTANCE_IPS ON IEAI_RUNINFO_INSTANCE_IPS(INSTANCEINFO_ID)';
						EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_RUNINFO_INSTANCE_HIS_IPS' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_RUNINFO_INSTANCE_HIS_IPS ON IEAI_RUNINFO_INSTANCE_HIS_IPS(INSTANCEINFO_ID)';
						EXECUTE IMMEDIATE LS_SQL;
					END	IF;
        SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SS_BINDFUC_VAR_UID' AND OBJECT_TYPE = 'INDEX';
        IF	LI_SIGN = 0 THEN
            LS_SQL := 'CREATE INDEX IDX_SS_BINDFUC_VAR_UID ON IEAI_SCRIPT_BIND_FUNC_VAR(ISCRIPTUUID)';
            EXECUTE IMMEDIATE LS_SQL;
        END	IF;
        
        SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IND_SUS_BASIC_INFO1' AND OBJECT_TYPE = 'INDEX';
			  IF  LI_SIGN = 0 THEN
			    LS_SQL := 'CREATE INDEX IND_SUS_BASIC_INFO1 ON IEAI_SUS_BASIC_INFO(IPARAMETER)';
			    EXECUTE IMMEDIATE LS_SQL;
			  END IF;
			  
			  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IND_DOUBLECHECK_WORKITEM_HIS1' AND OBJECT_TYPE = 'INDEX';
			  IF  LI_SIGN = 0 THEN
			    LS_SQL := 'CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS1 ON IEAI_DOUBLECHECK_WORKITEM_HIS(ITASKID)';
			    EXECUTE IMMEDIATE LS_SQL;
			  END IF;
			  
			   SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IND_DOUBLECHECK_WORKITEM_HIS2' AND OBJECT_TYPE = 'INDEX';
			  IF  LI_SIGN = 0 THEN
			    LS_SQL := 'CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS2 ON IEAI_DOUBLECHECK_WORKITEM_HIS(IEXECUSER)';
			    EXECUTE IMMEDIATE LS_SQL;
			  END IF;

              SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IND_DOUBLECHECK_WORKITEM_HIS3' AND OBJECT_TYPE = 'INDEX';
              IF  LI_SIGN = 0 THEN
			    LS_SQL := 'CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS3 ON IEAI_DOUBLECHECK_WORKITEM_HIS(IFLOWID)';
                EXECUTE IMMEDIATE LS_SQL;
              END IF;

               SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_CR_GROUP_MAPPING_CPID' AND OBJECT_TYPE = 'INDEX';
                 IF  LI_SIGN = 0 THEN
			    LS_SQL := 'CREATE INDEX IDX_CR_GROUP_MAPPING_CPID ON IEAI_COMPUTER_GROUP_MAPPING (ICPID)';
               EXECUTE IMMEDIATE LS_SQL;
               END IF;
               

				SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IUUID' AND OBJECT_TYPE = 'INDEX';
				  IF  LI_SIGN = 0 THEN
					  LS_SQL := 'CREATE INDEX IDX_IUUID ON IEAI_TOOLS_AGENT_BIND (IUUID)';
					  EXECUTE IMMEDIATE LS_SQL;
				  END  IF; 
				  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IRESULT_ID' AND OBJECT_TYPE = 'INDEX';
				  IF  LI_SIGN = 0 THEN
					  LS_SQL := 'CREATE INDEX IDX_IRESULT_ID ON IEAI_TOOLS_AGENT_BIND (IRESULT_ID)';
					  EXECUTE IMMEDIATE LS_SQL;
				  END  IF; 
				  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ISTATUS' AND OBJECT_TYPE = 'INDEX';
				  IF  LI_SIGN = 0 THEN
					  LS_SQL := 'CREATE INDEX IDX_ISTATUS ON IEAI_TOOLS_AGENT_BIND (ISTATUS)';
					  EXECUTE IMMEDIATE LS_SQL;
				  END  IF; 
				  SELECT  SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_AGENT_ID' AND OBJECT_TYPE = 'INDEX';
				  IF  LI_SIGN = 0 THEN
					  LS_SQL := 'CREATE INDEX IDX_AGENT_ID ON IEAI_TOOLS_AGENT_BIND (ITOOLS_AGENT_ID,IRESULT_ID)';
					  EXECUTE IMMEDIATE LS_SQL;
				  END  IF;
					  
					  
			    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AGENTINFO_GROUP_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_AGENTINFO_GROUP_01 ON IEAI_AGENTINFO_GROUP(INUM,INOWNUM,IGROUPID,INODEID)';
					EXECUTE IMMEDIATE LS_SQL;
				END	IF;




                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HC_ACTWARNING_01' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
			        LS_SQL := 'CREATE INDEX IDX_HC_ACTWARNING_01 ON IEAI_HC_ACTWARNING (CPID, IP)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_HC_ACTWARNING_02' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
			        LS_SQL := 'CREATE INDEX IDX_HC_ACTWARNING_02 ON IEAI_HC_ACTWARNING (IP)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_RELATION_01' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                     LS_SQL := 'CREATE INDEX IDX_IEAI_SYS_RELATION_01 ON IEAI_SYS_RELATION (IP)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_RELATION_02' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_SYS_RELATION_02 ON IEAI_SYS_RELATION (SYSTEMID, IP)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_PERMISSION_02' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_SYS_PERMISSION_02 ON IEAI_SYS_PERMISSION (IROLEID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SYS_PERMISSION_03' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_SYS_PERMISSION_03 ON IEAI_SYS_PERMISSION (IPERMISSION)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;
                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_ERRORTASK_01' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_ERRORTASK_01 ON IEAI_ERRORTASK (IFLOWID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_WORKFLOWINSTANCE_07' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_07 ON IEAI_WORKFLOWINSTANCE (IPRJID, IFLOWID, IFLOWNAME, ISTATUS, ISTARTTIME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_IEAI_WORKFLOW_IPRJUPPERID' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_WORKFLOW_IPRJUPPERID ON IEAI_WORKFLOW (IPRJUPPERID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_TEMP_LOOKMONITOR_BASE_03' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_03 ON TEMP_LOOKMONITOR_BASE (IPRJUPPERID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IEAI_ACT_PRIORITY' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_ACT_PRIORITY ON IEAI_ACT_PRIORITY (IACTNAME, IFLOWNAME, IPRJNAME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IEAI_RUN_SQLSCRIPT' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_RUN_SQLSCRIPT ON IEAI_RUN_SQLSCRIPT (IBATCHINDEX, IFILEINDEX, IRUNINFO_ID, ISQLINDEX)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IEAI_WORKFLOW_SYSTEM_RECORD' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_WORKFLOW_SYSTEM_RECORD_01 ON IEAI_WORKFLOW_SYSTEM_RECORD (IDATADATE, IOPERTYPE, ISTAUS, ISYSTEMNAME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IEAI_AGENT_DETAIL' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_AGENTDETAIL_01 ON IEAI_AGENT_DETAIL (AGENTIP, AGENTPORT, ICREATETIME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;
					
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AGENT_SDINFO_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_AGENT_SDINFO_01 ON IEAI_AGENT_SDINFO (IAGENTINFOID)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;

					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AGENTUPDATE_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_AGENTUPDATE_01 ON IEAI_AGENTUPDATE_INFO(IAGENTUP_ID,ITRANSMISSION_ID)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_PROXY_RELATION_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_PROXY_RELATION_01 ON IEAI_PROXY_LIST_RELATION(PROXYID)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_COMPUTER_LIST_ID' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_COMPUTER_LIST_ID ON IEAI_COMPUTER_LIST(IAGENTINFO_ID)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;
					
					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_UNIFY_SA_RELATION' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
						LS_SQL := 'CREATE INDEX IDX_IEAI_UNIFY_SA_RELATION ON IEAI_UNIFYAGENT_SA_RELATION(ISYSID)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;

					SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_WORKFLOWINSTANCE_08' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_WORKFLOWINSTANCE_08 ON IEAI_WORKFLOWINSTANCE (IFLOWNAME)';
					EXECUTE IMMEDIATE LS_SQL;
					END	IF;
                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_TK_STATIS_INFOID_DATE' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_TK_STATIS_INFOID_DATE ON IEAI_TIMETASK_EXEC_STATIS (CREATEDATE, TASKINFO_ID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_SCRIPT_INS_TIMEOUT' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_SCRIPT_INS_TIMEOUT ON IEAI_SCRIPT_INSTANCE (ITIMEOUT, ICONSUMERIP, ICONSUMERPORT)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDEX_PAAS_ITSM_RELATION_HIS' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDEX_PAAS_ITSM_RELATION_HIS ON IEAI_PAAS_ITSM_RELATION_HIS (IORDERID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_DOUBLEC_WORKITEM_ISTATE' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_DOUBLEC_WORKITEM_ISTATE ON IEAI_DOUBLECHECK_WORKITEM (ISTATE)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_DOUBLEC_WORK_IITEMTYPE' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_DOUBLEC_WORK_IITEMTYPE ON IEAI_DOUBLECHECK_WORKITEM (IITEMTYPE)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_DOUBLEC_WORK_ISTARTTIME' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_DOUBLEC_WORK_ISTARTTIME ON IEAI_DOUBLECHECK_WORKITEM (ISTARTTIME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_DOUBLEC_WORK_FINISHTIME' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_DOUBLEC_WORK_FINISHTIME ON IEAI_DOUBLECHECK_WORKITEM (IFINISHEDTIME)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_DOUBLEC_WORK_AUDITYPE' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_DOUBLEC_WORK_AUDITYPE ON IEAI_DOUBLECHECK_WORKITEM (IAUDITYPE)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_IEAI_DOUBLEC_WORKI_IAID' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLEC_WORKI_IAID ON IEAI_DOUBLECHECK_WORKITEM (IRESOURCE_APPLY_ID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDEX_SC_RUN_I_ATID' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDEX_SC_RUN_I_ATID ON IEAI_SCRIPT_RUNTIME_INFO (IEXECATID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDEX_SC_RUN_I_WID' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDEX_SC_RUN_I_WID ON IEAI_SCRIPT_RUNTIME_INFO (WORKFLOWID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDEX_SC_RUN_INFO' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDEX_SC_RUN_INFO ON IEAI_SCRIPT_RUNTIME_INFO (ISCRIPTFLOWID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_STD_TASK_RELA' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_STD_TASK_RELA ON IEAI_STANDARD_TASK_RELATION (IWORKITEMID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_IEAI_S_T_R_TASKID' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_S_T_R_TASKID ON IEAI_STANDARD_TASK_RELATION (ITASKID)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;

                    SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_IEAI_TIMETASK_IP' AND OBJECT_TYPE = 'INDEX';
                    IF	LI_SIGN = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_IEAI_TIMETASK_IP ON IEAI_TIMETASK_IP (TASKINSID, TASKIPSTATE)';
                    EXECUTE IMMEDIATE LS_SQL;
                    END	IF;


                    END;

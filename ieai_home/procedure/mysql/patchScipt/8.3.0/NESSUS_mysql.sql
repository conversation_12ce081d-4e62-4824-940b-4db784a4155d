-- 4.7.22
DELIMITER ;;
	DROP PROCEDURE IF <PERSON>XIS<PERSON> UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_RESULT_CSV (IID NUMERIC(19) NOT NULL ,PLUGINID VARCHAR(50) ,<PERSON><PERSON> VARCHAR(50) ,<PERSON>VSS VARCHAR(50) ,<PERSON><PERSON><PERSON> VARCHAR(50) ,HOST VARCHAR(50) ,PROTOCOL VARCHAR(50) ,PORT NUMERIC(6) ,NAME VARCHAR(255) ,SYNOPSIS VARCHAR(255) ,DESCRIPTION VARCHAR(1000) ,<PERSON>OLUT<PERSON> VARCHAR(500) ,<PERSON>EA<PERSON><PERSON> VARCHAR(255) ,<PERSON><PERSON><PERSON><PERSON><PERSON>OUTPUT LONGTEXT ,INESSUSID NUMERIC(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_RESULT_CSV PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_FILE_CONTENT (IID NUMERIC(19) NOT NULL ,IP VARCHAR(255) ,BUSINESSNAME VARCHAR(255) ,<PERSON>PERATSYSTEM VARCHAR(255) ,BINARYFILEIID NUMERIC(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_FILE_CONTENT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_BINARY_FILE (IID NUMERIC(19) NOT NULL ,USERID NUMERIC(19) ,BINARYFILE BLOB ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,EXECOPERATION NUMERIC(5) ,FILENAME VARCHAR(1000) ,CONSTRAINT PK_IEAI_BINARY_FILE PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_EQUIPMENT (IID NUMERIC(19) NOT NULL ,INESSUSID NUMERIC(19) NOT NULL ,IP VARCHAR(255) NOT NULL ,CPID NUMERIC(19) ,ISTATUS NUMERIC(2) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,CONSTRAINT PK_IEAI_NESSUS_EQUIPMENT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_RESULT (IID NUMERIC(19) NOT NULL ,INESSUSPLUS VARCHAR(255) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,IEQUIPMENTID NUMERIC(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_RESULT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_DISTRIBUTED_TASK (IID NUMERIC(19) NOT NULL ,ISTATUS NUMERIC(5) DEFAULT 1 ,SERVERIP VARCHAR(50) ,JOBIID NUMERIC(19) ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,JOBCLASS VARCHAR(255) ,CONSTRAINT PK_IEAI_DISTRIBUTED_TASK PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_REPAIR_TASK (IID NUMERIC(19) NOT NULL ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ITYPE NUMERIC(5) ,ITASKNAME VARCHAR(255) ,REQID VARCHAR(100) ,IENDTIME TIMESTAMP ,CONSTRAINT PK_IEAI_NESSUS_REPAIR_TASK PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_NESSUS_QUERY_RESULT (IID NUMERIC(19) NOT NULL ,IP VARCHAR(255) ,CURNESSUS VARCHAR(255) ,CURREPAIR VARCHAR(255) ,CURREPAIRTIME TIMESTAMP NULL DEFAULT NULL ,CURNESSUSID NUMERIC(19) ,LASTNESSUS VARCHAR(255) ,LASTREPAIR VARCHAR(255) ,LASTREPAIRTIME TIMESTAMP NULL DEFAULT NULL ,LASTNESSUSID NUMERIC(19) ,CONSTRAINT PK_IEAI_NESSUS_QUERY_RESULT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_REPAIR_PLUS (IID NUMERIC(19) NOT NULL ,INESSUSPLUS VARCHAR(225) ,INESSUSID NUMERIC(19) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ISTATUS NUMERIC(2) DEFAULT 10 ,CONSTRAINT PK_IEAI_REPAIR_PLUS PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_REPAIR_ACT_LIST (IID NUMERIC(19) NOT NULL ,IACTNAME VARCHAR(255) ,IFOLLOWID NUMERIC(19) ,ISTATUS NUMERIC(2) DEFAULT 11 ,IPARENTID NUMERIC(19) ,IBASEAUTOID NUMERIC(19) ,IACTCLASS VARCHAR(255) ,ISTANDARDID NUMERIC(19) ,CONSTRAINT PK_IEAI_REPAIR_ACT_LIST PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_REPAIR_EQUIPMENT (IID NUMERIC(19) NOT NULL ,INESSUSPLUS VARCHAR(255) ,IREPAIRID NUMERIC(19) ,INESSUSID NUMERIC(19) NOT NULL ,IP VARCHAR(255) NOT NULL ,CPID NUMERIC(19) ,IRESULT VARCHAR(2000) ,ISTATUS NUMERIC(2) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ERRORDESC VARCHAR(1000) ,CONSTRAINT PK_IEAI_REPAIR_EQUIPMENT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_SAFETY_BASE (IID NUMERIC(19) NOT NULL ,SAFETYCONFIGNAME VARCHAR(255) ,NESSUSPLUGINID VARCHAR(255) ,SAFETYDEFECTNAME VARCHAR(225) ,DEFECTDESC VARCHAR(1000) ,ISMUST NUMERIC(2) ,HARMLEVEL NUMERIC(2) ,USEDIFFICULT NUMERIC(2) ,CONFIGOBJECT VARCHAR(255) ,BASEASK VARCHAR(1000) ,BASECOMMAND VARCHAR(600) ,REMARKS VARCHAR(1000) ,BINARYFILEIID NUMERIC(19) ,SERIALNUMBER VARCHAR(200) ,AOMS VARCHAR(500) ,CONSTRAINT PK_IEAI_SAFETY_BASE PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_SAFETY_BASE_AUTO (IID NUMERIC(19) NOT NULL ,BASEIID NUMERIC(19) ,OPERATENAME VARCHAR(255) ,OPERATEVALUE VARCHAR(255) ,UPDATETIME TIMESTAMP ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,STATE NUMERIC(2) ,CONSTRAINT PK_IEAI_SAFETY_BASE_AUTO PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_REPAIR_PROGRAM (IID NUMERIC(19) NOT NULL ,INAME VARCHAR(255) ,ICONFIG VARCHAR(255) ,IDESC VARCHAR(1000) ,ITYPE NUMERIC(3) ,CONSTRAINT PK_IEAI_REPAIR_PROGRAM PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_ASYNCHRONY_RUNTIME (IID NUMERIC(19) NOT NULL ,ITASKID NUMERIC(19) ,ISERVERIP VARCHAR(50) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ISTATUS NUMERIC(5) DEFAULT 1 ,ITASKCLASS VARCHAR(500) ,CONSTRAINT PK_IEAI_ASYNCHRONY_RUNTIME PRIMARY KEY (IID));
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_EQUIPMENT' AND COLUMN_NAME = 'ERRORDESC') THEN
	    ALTER TABLE IEAI_NESSUS_EQUIPMENT ADD ERRORDESC VARCHAR(1000);
	END IF;
	
	IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ASYNCHRONY_RUNTIME' AND COLUMN_NAME = 'ISTATUS') THEN
    	ALTER TABLE IEAI_ASYNCHRONY_RUNTIME MODIFY  ISTATUS  NUMERIC(5) DEFAULT 1;
	END IF;
	
	IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_QUERY_RESULT' AND COLUMN_NAME = 'CURREPAIRTIME') THEN
    	ALTER TABLE IEAI_NESSUS_QUERY_RESULT MODIFY  CURREPAIRTIME  TIMESTAMP NULL DEFAULT NULL;
	END IF;
	
	IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_QUERY_RESULT' AND COLUMN_NAME = 'LASTREPAIRTIME') THEN
    	ALTER TABLE IEAI_NESSUS_QUERY_RESULT MODIFY  LASTREPAIRTIME  TIMESTAMP NULL DEFAULT NULL;
	END IF;
	
	IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_REPAIR_TASK' AND COLUMN_NAME = 'IENDTIME') THEN
    	ALTER TABLE IEAI_NESSUS_REPAIR_TASK MODIFY  IENDTIME  TIMESTAMP NULL DEFAULT NULL;
	END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.23
    DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	CREATE TABLE IF NOT EXISTS IEAI_USER_MANAGE_MENT (IID NUMERIC(19) NOT NULL, ITYPE NUMERIC(5), IADDRESSOR VARCHAR(50), ISSEND NUMERIC(2), ICOPYUSER VARCHAR(50), IEMAIL VARCHAR(50),CONSTRAINT PK_IEAI_USER_MANAGE_MENT PRIMARY KEY (IID));	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_REPAIR_EQUIPMENT' AND COLUMN_NAME = 'SAFETYDEFECTNAME') THEN
    ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD SAFETYDEFECTNAME VARCHAR(255);
	END IF;
	
	CREATE TABLE IF NOT EXISTS IEAI_BINARY_MIDDLE (IID NUMERIC(19) NOT NULL ,BINARY_ID NUMERIC(19) NOT NULL ,RELATION_ID NUMERIC(19) NOT NULL ,OPERATIONTYPE NUMERIC(5) NOT NULL ,CONSTRAINT PK_IEAI_BINARY_MIDDLE PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.25	

      DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_REPAIR_EQUIPMENT' AND COLUMN_NAME = 'SERIALNUMBER') THEN
		    ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD SERIALNUMBER VARCHAR(200);
		END IF;

      IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_QUERY_RESULT' AND COLUMN_NAME = 'TASKNAME') THEN
			ALTER TABLE IEAI_NESSUS_QUERY_RESULT modify LASTNESSUS VARCHAR(1000);
		END IF;
		
		IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_QUERY_RESULT' AND COLUMN_NAME = 'TASKNAME') THEN
			ALTER TABLE IEAI_NESSUS_QUERY_RESULT modify CURNESSUS VARCHAR(1000);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_NESSUS_EQUIPMENT' AND COLUMN_NAME = 'ISCREATE') THEN
		    ALTER TABLE IEAI_NESSUS_EQUIPMENT ADD ISCREATE NUMERIC(2);
		END IF;

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_REPAIR_PLUS' AND COLUMN_NAME = 'FLAG') THEN
		    ALTER TABLE IEAI_REPAIR_PLUS ADD FLAG NUMERIC(19);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_REPAIR_EQUIPMENT' AND COLUMN_NAME = 'FLAG') THEN
		    ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD FLAG NUMERIC(19);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_NESSUS_REPAIR_KEY (IID NUMERIC(19) NOT NULL ,NESSUSTADKID NUMERIC(19) ,REPAIRTADKID NUMERIC(19) ,TYPE NUMERIC(2)  DEFAULT 0,CONSTRAINT PK_IEAI_NESSUS_REPAIR_KEY PRIMARY KEY (IID));


  END;;
  DELIMITER ;
  CALL UPPRDE();
 
 


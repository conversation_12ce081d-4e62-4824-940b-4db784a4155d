-- 4.7.17 version does not have patches for HC 
-- 4.7.18 version does not have patches for HC

-- 4.7.19 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_HD_DATAOVERVIEW;;
	CREATE PROCEDURE PROC_HD_DATAOVERVIEW(IN AN_USERID NUMERIC(19,0), IN AN_OBJ_ID NUMERIC(19,0), IN AI_OBJ_TYPE SMALLINT, IN AI_PAGE_ID INTEGER, IN AI_PAGE_SIZE INTEGER,IN AI_DATA_CENTER VARCHAR(255), OUT AN_OPT_ID NUMERIC(19,0), OUT AI_REC_AMOUNT INTEGER)
	LABEL_PRO:BEGIN
		DECLARE	CN_MENU_ID			SMALLINT DEFAULT 72;
		DECLARE	V_COUNT				INTEGER;
		DECLARE	LI_PAGE_FROM		INTEGER;
		DECLARE	LI_PAGE_TO			INTEGER;
		DECLARE	LI_PAGE_COUNT		INTEGER;

		IF	AI_OBJ_TYPE < 0 OR AI_OBJ_TYPE > 4	THEN
			LEAVE LABEL_PRO;
		END	IF;
		
		CALL	PROC_GET_NEXT_ID ('TMP_DATASHOW', AN_OPT_ID);
		
		IF	AN_OPT_ID IS NULL	THEN
			LEAVE LABEL_PRO;
		END	IF;
		
		IF AI_OBJ_TYPE BETWEEN 0 AND 1 THEN
			SELECT
				COUNT(P.IID) 
			INTO V_COUNT 
			FROM
				IEAI_USERINHERIT U,
				IEAI_SYS_PERMISSION S,
				IEAI_PROJECT P 
			WHERE
				U.IUSERID = AN_USERID 
				AND S.IROLEID = U.IROLEID
				AND S.IPERMISSION = 1 
				AND S.IPROID = -7
				AND P.IID = S.IPROID 
				AND (P.PROTYPE = -7 OR P.PROTYPE = 7)
				;
		END IF;
		
		
		IF	AI_OBJ_TYPE = 0	THEN
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,						
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S, IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.IID > 0
								AND S.IID=SS.IID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND S.IID=SS.IID
								AND		S.IID >= 0
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID	
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
										
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 1	THEN
		
			
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
										
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		S.IID >= 0
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 2	THEN
			IF LENGTH(AI_DATA_CENTER)>0 THEN 
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
											SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
											SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
											SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
											SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
											SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
											SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								AND A.NDCNAME = AI_DATA_CENTER
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							) AA,(SELECT @rownum1:=0) rr
				ORDER	BY	NDCNAME, NIP;
			ELSE
			
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
											SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
											SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
											SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
											SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
											SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
											SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							) AA,(SELECT @rownum1:=0) rr
				ORDER	BY	NDCNAME, NIP;
		END IF;
		ELSEIF	AI_OBJ_TYPE = 3	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						A.CENTERNAME,
						A.OID,
						AN_OBJ_ID AS POID,
						A.OBJ_NAME,
						(AI_OBJ_TYPE + 1),
						IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
						SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
						SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
						SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
						SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
						SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
						SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
						SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
						NULL,
						NULL,
						(@rownum:=@rownum+1) AS rownum 
			FROM		(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID AS OID,
										T.ICHKITEMNAME AS OBJ_NAME
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKITEM T
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		T.ICHKITEMID = C.CHKITEMID
						) A,
						(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID,
										D.CPSTATUS,
										COUNT(*) AS DAT_CNT
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKPOINT P,
										HD_CHECK_RESULT_DATA_LAST D
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		P.SCID = C.SCID
							AND		P.STARTLOGO = 1
							AND		D.CPID = P.CPID
							GROUP BY R.CENTERNAME, C.SCID, D.CPSTATUS
						) B,(SELECT @rownum:=0) rr
			WHERE		B.CENTERNAME = A.CENTERNAME
			AND		B.SCID = A.OID
			GROUP BY A.CENTERNAME, A.OID, A.OBJ_NAME
			ORDER	BY	A.OID;
		ELSEIF	AI_OBJ_TYPE = 4	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						NULL,
						D.RSDID,
						AN_OBJ_ID AS POID,
						D.CPTEXT,
						(AI_OBJ_TYPE + 1),
						1 AS AMOUNT,
						(case when D.CPSTATUS = 0 then 1 else 0 end) AS CL0,
						(case when D.CPSTATUS = 1 then 1 else 0 end) AS CL1,
						(case when D.CPSTATUS = 2 then 1 else 0 end) AS CL2,
						(case when D.CPSTATUS = 3 then 1 else 0 end) AS CL3,
						(case when D.CPSTATUS = 4 then 1 else 0 end) AS CL4,
						(case when D.CPSTATUS = 5 then 1 else 0 end) AS CL5,
						(case when D.CPSTATUS = -1 then 1 else 0 end) AS CHECKING,
						FUN_GET_TIMESTAMP(D.CPTIME, 0),
						D.CPSTATUS,
						(@rownum:=@rownum+1) AS rownum 
			FROM		IEAI_CHKPOINT P,
						HD_CHECK_RESULT_DATA_LAST D
						,(SELECT @rownum:=0) rr
			WHERE		P.SCID = AN_OBJ_ID
			AND		P.STARTLOGO = 1
			AND		D.CPID = P.CPID;
		END	IF;
		
		
		SELECT	COUNT(*)
		INTO		AI_REC_AMOUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID;
		
		
		SET	LI_PAGE_COUNT = AI_REC_AMOUNT / AI_PAGE_SIZE;
		
		IF MOD(AI_REC_AMOUNT, AI_PAGE_SIZE) > 0 THEN
			SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
		END	IF;
		
		IF	AI_PAGE_ID < 1 THEN
			SET	AI_PAGE_ID = 1;
		END	IF;
		
		IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
			SET	AI_PAGE_ID = LI_PAGE_COUNT;
		END	IF;
		
		SET	LI_PAGE_FROM = (AI_PAGE_ID - 1) * AI_PAGE_SIZE + 1;
		SET	LI_PAGE_TO = AI_PAGE_ID * AI_PAGE_SIZE;
		
		SELECT	SIGN(COUNT(*))
		INTO		V_COUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID
		AND		(ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		
		IF V_COUNT > 0 THEN
			DELETE	FROM	TMP_DATASHOW WHERE OPT_ID = AN_OPT_ID AND (ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		END IF;
	END ;;
	DELIMITER ;

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_CHECK_RESULT_DATA_LASTN;;
	CREATE PROCEDURE PROC_CHECK_RESULT_DATA_LASTN(IN AV_IP VARCHAR(25),IN AV_CPORT NUMERIC(12,0))
	PROLABEL:BEGIN

		DECLARE	DEL_ROWNUM		INTEGER;

		SELECT  COUNT(1) INTO DEL_ROWNUM 
		FROM HD_CHECK_RESULT_DATA_LAST
		WHERE		IP = AV_IP
		AND		NOT EXISTS
						(
							SELECT	1
							FROM		IEAI_COM_CHK B2,
										IEAI_CHKPOINT B3
							WHERE		B2.CPID = HD_CHECK_RESULT_DATA_LAST.MEID
							AND		B3.SCID = B2.SCID
							AND		B3.CPID = HD_CHECK_RESULT_DATA_LAST.CPID
						);
		
		IF DEL_ROWNUM > 0 THEN 
		UPDATE	HD_CHECK_RESULT_DATA_LAST
		SET		CISTATUS = 99
		WHERE		IP = AV_IP
		AND		NOT EXISTS
						(
							SELECT	1
							FROM		IEAI_COM_CHK B2,
										IEAI_CHKPOINT B3
							WHERE		B2.CPID = HD_CHECK_RESULT_DATA_LAST.MEID
							AND		B3.SCID = B2.SCID
							AND		B3.CPID = HD_CHECK_RESULT_DATA_LAST.CPID
						);
		
		COMMIT WORK;
		
		DELETE	FROM	HD_CHECK_STATUS_LAST
		WHERE	CRID IN (
							SELECT	CRID
							FROM	(
										SELECT	B2.CRID
										FROM		HD_CHECK_RESULT_DATA_LAST B1,
													HD_CHECK_STATUS_LAST B2
										WHERE		B1.IP = AV_IP
										AND		B1.CISTATUS = 99
										AND		B2.CPID = B1.CPID
									) C1
						);
		
		DELETE	FROM	HD_CHECK_RESULT_DATA_LAST
		WHERE		IP = AV_IP
		AND		CISTATUS = 99;
		
		COMMIT WORK;
		
		END IF;

		/*UPDATE	HD_CHECK_RESULT_DATA_LAST
		SET		CISTATUS = 0
		WHERE		IP = AV_IP;*/
		
		-- DELETE FROM HD_CHECK_RESULT_DATA_LAST WHERE IP= AV_IP AND CISTATUS =2;
		-- COMMIT WORK;

		UPDATE HD_CHECK_RESULT_DATA_LAST SET CISTATUS =2 
        WHERE  IP= AV_IP  
        AND CISTATUS IN (0,1)
        AND  EXISTS (SELECT 1 FROM TEMP_INI_RESLUT B1 WHERE B1.IP=AV_IP AND  B1.CPID=HD_CHECK_RESULT_DATA_LAST.CPID);

		COMMIT WORK;	

		UPDATE	HD_CHECK_RESULT_DATA_LAST
		SET		CISTATUS = 0
		WHERE		IP = AV_IP
        AND CISTATUS=1;
		
		COMMIT WORK;
		
		INSERT	INTO	HD_CHECK_RESULT_DATA_LAST
				(
					RSDID,
					ISYSID,
					ISYSNAME,
					CIID,
					CINAME,
					CIPARSRULE,
					CISTATUS,
					MEID,
					IP,
					PORT,
					CPID,
					CPTEXT,
					CPTIME,
					COMCHKID,
					CPSTATUS,
					CPTIMESTAMP
				)
		SELECT	RSDID,
					ISYSID,
					SYSNAME,
					CHKITEMID,
					ICHKITEMNAME,
					IPARSERULE,
					1,
					MEID,
					IP,
					CPPORT,
					CPID,
					CHKPOINT,
					0,
					COMCHID,
					-1,
					CURRENT_TIMESTAMP
		FROM		TEMP_INI_RESLUT
		WHERE		IP = AV_IP;
		COMMIT WORK;
	END ;;
	DELIMITER ;

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HC_CONFIG_TEM ( IID      DECIMAL(19,0) not null,INAME    VARCHAR(255) not null, IDESC       VARCHAR(255), CONSTRAINT PK_IEAI_HC_CONFIG_TEM PRIMARY KEY(IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_CHKPOINT_TEMP  ( CPID DECIMAL(12, 0) NOT NULL, SCID DECIMAL(12, 0) NOT NULL,CHKPOINT VARCHAR(512), INTLHOUR DECIMAL(12, 0), INTLMINUTE DECIMAL(12, 0),INTLLENGTH DECIMAL(12, 0), STARTLOGO SMALLINT, CRTUSER VARCHAR(100), CRTTIME DECIMAL(19, 0), UPUSER VARCHAR(100), UPTIME DECIMAL(19, 0), SCRIPTTAG  VARCHAR(8) DEFAULT '',AUTOHC  SMALLINT DEFAULT 1, ALARMLEVEL VARCHAR(5) DEFAULT  0, EMPRJUPID DECIMAL(12, 0), CONSTRAINT PK_IEAI_CHKPOINT_TEMP PRIMARY KEY(CPID));
		
		CREATE TABLE IF NOT EXISTS HD_CHK_TIME_INTERVAL_CONF_TEMP   (CFGID DECIMAL(19,0) NOT NULL,DEVID DECIMAL(19,0) NOT NULL,CIID DECIMAL(19,0) NOT NULL,CPID DECIMAL(19,0) NOT NULL,CYCLE_TYPE SMALLINT NOT NULL,START_DAY  SMALLINT,END_DAY SMALLINT,START_TIME TIME NOT NULL,END_TIME  TIME NOT NULL,SET_START  INTEGER,SET_END INTEGER,ENABLED SMALLINT DEFAULT 1,CONSTRAINT PK_CHK_TIME_INTERVAL_CFG_TEMP PRIMARY KEY(CFGID)  );
		
		CREATE TABLE IF NOT EXISTS IEAI_COM_CHK_TEMP  (SCID DECIMAL(12, 0) NOT NULL, CPID DECIMAL(12, 0) NOT NULL, CHKITEMID DECIMAL(12, 0) NOT NULL, PERFORMUSER VARCHAR(100), APPLOGO DECIMAL(12, 0) DEFAULT -1, STARTLOGO SMALLINT DEFAULT 1, CRTUSER VARCHAR(100), CRTTIME DECIMAL(19, 0), UPUSER VARCHAR(100), UPTIME DECIMAL(19, 0), IHIDDEN SMALLINT DEFAULT 0, IFORMATIONID NUMERIC(19,0),CONSTRAINT PK_IEAI_COM_CHK_TEMP PRIMARY KEY(SCID));
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 以由8.5.0版本更新修正PROC_GET_RESULT_ALL_REPORT
	
	
	
-- 4.7.20 version HC patch is as follows
-- TRB_I_HC_ACTWARNING and TRB_U_HC_ACTWARNING update to v8.0
	
	DELIMITER ;;
	DROP FUNCTION IF EXISTS FUN_GET_END_DAY;;
	CREATE FUNCTION FUN_GET_END_DAY(AD_DATE DATE, AI_DAYS SMALLINT) RETURNS smallint(6)
	BEGIN
		DECLARE	LI_DAY			SMALLINT;
		DECLARE	LI_BEFORE_DAY	SMALLINT;
		DECLARE	LI_END_DAY		SMALLINT;
		
		IF AD_DATE IS NULL THEN
			SET	LI_DAY = NULL;
		ELSE
			SET	LI_BEFORE_DAY = ABS(IFNULL(AI_DAYS, 0));
			
			
			
			IF	LI_BEFORE_DAY = 0 THEN
				SET	LI_BEFORE_DAY = 50;
			END IF;
			
			IF LI_BEFORE_DAY > 60 THEN
				SET	LI_BEFORE_DAY = 60;
			END IF;
			
			SET	LI_DAY = DAY(AD_DATE);
			
			SELECT DATE_ADD(AD_DATE, INTERVAL 1 MONTH) INTO AD_DATE;
			SELECT DATE_SUB(AD_DATE, INTERVAL LI_DAY DAY) INTO AD_DATE;
			SET	LI_END_DAY = DAY(AD_DATE);
			
			IF	LI_BEFORE_DAY >= 1 AND LI_BEFORE_DAY <= 31 THEN
				IF LI_BEFORE_DAY > LI_END_DAY THEN
					SET	LI_DAY = LI_END_DAY;
				ELSE
					SET	LI_DAY = LI_BEFORE_DAY;
				END IF;
			ELSE
				SET	LI_DAY = LI_END_DAY - LI_BEFORE_DAY + 50;
			END IF;
		END	IF;
		
		RETURN	LI_DAY;
	END ;;
	DELIMITER ;

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ICALARMCONFIG' AND COLUMN_NAME = 'OTHERNAME') THEN
		ALTER TABLE IEAI_ICALARMCONFIG ADD OTHERNAME VARCHAR(255);
	END IF;
		
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ICALARMCONFIG' AND COLUMN_NAME = 'OVERTIME') THEN
		ALTER TABLE IEAI_ICALARMCONFIG ADD OVERTIME VARCHAR(12);
	END IF;

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ICALARMCONFIG' AND COLUMN_NAME = 'MAILBOX') THEN
		ALTER TABLE IEAI_ICALARMCONFIG ADD MAILBOX VARCHAR(255);
	END IF;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.21 version HC patch is as follows
	
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN	
		
		CREATE TABLE IF NOT EXISTS IEAI_SYS_HCTEMP (IID NUMERIC(12) NOT NULL ,SYSTEMID NUMERIC(12) NOT NULL ,TEMID NUMERIC(19) NOT NULL ,SYSNAME VARCHAR(200) ,OPUSER NUMERIC(19) ,OPUSERNAME VARCHAR(255) ,OPTIME NUMERIC(19) ,EXT VARCHAR(200) ,EXT2 VARCHAR(100) ,CONSTRAINT PK_IEAI_SYS_TEMPID PRIMARY KEY (IID));

		
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.22 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKITEM' AND COLUMN_NAME = 'REMARKS') THEN
			ALTER TABLE IEAI_CHKITEM ADD REMARKS VARCHAR(500);
		END IF;	

	END;;
	DELIMITER ;
	CALL UPPRDE();

-- 以由8.5.0版本更新修正PROC_GET_RESULT_REPORT_GRID
	

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_RESULT_REPORT_CHART;
	CREATE  PROCEDURE PROC_GET_RESULT_REPORT_CHART(IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_COLUMN_ID INTEGER, IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	BEGIN
		DECLARE	LV_SQL	VARCHAR(4000);
		DECLARE	CONS_SEP VARCHAR(6);
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);

		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST AA
		WHERE		IP = AV_IP;

		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT AA
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;
		
		SET	CONS_SEP = '!!##!!';
		
		SET	LV_SQL = '';
		
		IF	AV_TIME_START < 0 AND AV_TIME_END < 0 THEN
				SELECT COUNT(*) INTO RECORDCOUNT
				FROM ( 
						SELECT	AAAA.CPTIME,GROUP_CONCAT(AAAA.CPTEXT SEPARATOR '!!##!!') CPTEXT
						FROM(
										SELECT	AAA.CPTIME,AAA.CPTEXT,B.I1
										FROM(
													SELECT	CPTIME,CPTEXT,RSDID
													FROM(
																	SELECT	A.CPTIME,A.CPTEXT,A.RSDID
																	FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																	WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																	AND		A.MEID = LN_CPID
																	AND   A.RSDID=BB.RSDID
					AND   A.CPID=BB.CPID
																	AND		A.CIID = AV_CHECK_ITEM_ID
															) AA
												) AAA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AAA.RSDID = B.RSDID
								 ) AAAA GROUP BY AAAA.CPTIME
				 ) AFA;
			
			SET	@LV_SQL = CONCAT ('INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ',
			'SELECT CPTIME, CPTEXT, VV FROM ( ',
			'SELECT (@rownum:=@rownum+1) AS RECID, AAA.CPTIME, ',
			'  GROUP_CONCAT(AAA.CPTEXT SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') CPTEXT, '
			'  GROUP_CONCAT(AAA.I',AV_COLUMN_ID,' SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') VV '
			' FROM ( ',
			'    SELECT AA.CPTIME, AA.CPTEXT, B.I' , AV_COLUMN_ID ,' FROM (  ',
			'        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ',
			'          (A.ISYSID = ', LN_SYSID ,' OR A.ISYSID = -1) AND A.MEID = ' , LN_CPID , ' AND A.CIID =' , AV_CHECK_ITEM_ID ,' ',
			'    ) AA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AA.RSDID = B.RSDID WHERE ',
			'        B.I' , AV_COLUMN_ID ,' IS NOT NULL  ',
			') AAA ,(SELECT @rownum:=0) r GROUP BY AAA.CPTIME  ORDER BY AAA.CPTIME ',
			') SFAD WHERE RECID > ' , AV_START , ' AND RECID <= ' , AV_END);

		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
			SELECT	COUNT(*) INTO		RECORDCOUNT
			FROM(
							SELECT	AAAA.CPTIME, GROUP_CONCAT(AAAA.CPTEXT SEPARATOR '!!##!!') CPTEXT
							FROM(
											SELECT	AAA.CPTIME, AAA.CPTEXT, B.I1
											FROM(
															SELECT	CPTIME, CPTEXT, RSDID
															FROM(
																			SELECT	A.CPTIME,A.CPTEXT,A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND   A.RSDID=BB.RSDID
					    AND   A.CPID=BB.CPID
																			AND		A.CPTIME <= AV_TIME_END
																	) AA
													) AAA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AAA.RSDID = B.RSDID
									) AAAA
							GROUP BY AAAA.CPTIME
						) AFA;
						
			SET	@LV_SQL = CONCAT('INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ',
			'SELECT CPTIME, CPTEXT, VV FROM ( ',
		'SELECT (@rownum:=@rownum+1) AS RECID, AAA.CPTIME, ',
		'  GROUP_CONCAT(AAA.CPTEXT SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') CPTEXT, '
		'  GROUP_CONCAT(AAA.I',AV_COLUMN_ID,' SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') VV '
		'FROM ( ',
		'    SELECT AA.CPTIME, AA.CPTEXT, B.I' , AV_COLUMN_ID ,' FROM (  ',
		'        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ',
		'          (A.ISYSID = ', LN_SYSID ,' OR A.ISYSID = -1) AND A.MEID = ' , LN_CPID , ' AND A.CIID =' , AV_CHECK_ITEM_ID , ' AND A.CPTIME <= ' , AV_TIME_END,
		'    ) AA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AA.RSDID = B.RSDID WHERE ',
		'        B.I' , AV_COLUMN_ID ,' IS NOT NULL  ',
		') AAA ,(SELECT @rownum:=0) r GROUP BY AAA.CPTIME  ORDER BY AAA.CPTIME ',
		') ADSFAD WHERE RECID > ' , AV_START , ' AND RECID <= ' , AV_END);
			
		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
			SELECT	COUNT(*) INTO		RECORDCOUNT
			FROM (
							SELECT	AAAA.CPTIME,GROUP_CONCAT(AAAA.CPTEXT SEPARATOR '!!##!!') CPTEXT
							FROM(
											SELECT	AAA.CPTIME,AAA.CPTEXT,B.I1
											FROM(
															SELECT	CPTIME,CPTEXT,RSDID
															FROM(
																			SELECT	A.CPTIME,A.CPTEXT,A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND   A.RSDID=BB.RSDID
					    AND   A.CPID=BB.CPID
																			AND		A.CPTIME >= AV_TIME_START
																	) AA
													) AAA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AAA.RSDID = B.RSDID
									) AAAA
							GROUP BY AAAA.CPTIME
						) AFA;
			
			SET	@LV_SQL = CONCAT('INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ',
			 'SELECT CPTIME, CPTEXT, VV FROM ( ',
		 'SELECT (@rownum:=@rownum+1) AS RECID, AAA.CPTIME, ',
		 '  GROUP_CONCAT(AAA.CPTEXT SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') CPTEXT, '
		 '  GROUP_CONCAT(AAA.I',AV_COLUMN_ID,' SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') VV '
		 'FROM ( ',
		 '    SELECT AA.CPTIME, AA.CPTEXT, B.I' , AV_COLUMN_ID ,' FROM (  ',
		 '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ',
		 '          (A.ISYSID = ', LN_SYSID ,' OR A.ISYSID = -1) AND A.MEID = ' , LN_CPID , ' AND A.CIID =' , AV_CHECK_ITEM_ID , ' AND A.CPTIME >= ' , AV_TIME_START,
		 '    ) AA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AA.RSDID = B.RSDID WHERE ',
		 '        B.I' , AV_COLUMN_ID ,' IS NOT NULL  ',
		') AAA ,(SELECT @rownum:=0) r GROUP BY AAA.CPTIME  ORDER BY AAA.CPTIME ',
		 ') ADSFAD WHERE RECID > ' , AV_START , ' AND RECID <= ' , AV_END);

		ELSE
			SELECT COUNT(*) INTO RECORDCOUNT
			FROM(
							SELECT	AAAA.CPTIME, GROUP_CONCAT(AAAA.CPTEXT SEPARATOR '!!##!!') CPTEXT
							FROM(
											SELECT	AAA.CPTIME,AAA.CPTEXT,B.I1
											FROM(
															SELECT	CPTIME,CPTEXT,RSDID
															FROM(
																			SELECT	A.CPTIME,A.CPTEXT,A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND   A.RSDID=BB.RSDID
					    AND   A.CPID=BB.CPID
																			AND		(A.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
																	) AA
													) AAA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AAA.RSDID = B.RSDID
									) AAAA
							GROUP BY AAAA.CPTIME
				 ) AFA;
			
			SET	@LV_SQL = CONCAT('INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ',
			 'SELECT CPTIME, CPTEXT, VV FROM ( ',
		 'SELECT (@rownum:=@rownum+1) AS RECID, AAA.CPTIME, ',
		 '  GROUP_CONCAT(AAA.CPTEXT SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') CPTEXT, '
		 '  GROUP_CONCAT(AAA.I',AV_COLUMN_ID,' SEPARATOR ',CHAR(39),CONS_SEP,CHAR(39),') VV '
		 'FROM ( ',
		 '    SELECT AA.CPTIME, AA.CPTEXT, B.I' , AV_COLUMN_ID ,' FROM (  ',
		 '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ',
		 '          (A.ISYSID = ', LN_SYSID ,' OR A.ISYSID = -1) AND A.MEID = ' , LN_CPID , ' AND A.CIID =' , AV_CHECK_ITEM_ID , ' AND A.CPTIME >= ' , AV_TIME_START , ' AND A.CPTIME <= ' , AV_TIME_END,
		 '    ) AA LEFT JOIN HD_CHECK_STATUS_CACHE B ON AA.RSDID = B.RSDID WHERE ',
		 '        B.I' , AV_COLUMN_ID ,' IS NOT NULL  ',
		') AAA ,(SELECT @rownum:=0) r GROUP BY AAA.CPTIME  ORDER BY AAA.CPTIME ',
		 ') ADSFAD WHERE RECID > ' , AV_START , ' AND RECID <= ' , AV_END);

		END	IF;
		
		PREPARE	SQLA FROM @LV_SQL;
		EXECUTE SQLA;
	END;;
	DELIMITER ;

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_RESULT_CPTEXT;
	CREATE PROCEDURE PROC_GET_RESULT_CPTEXT(IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0) ,IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0))
	BEGIN
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT AA
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;
		IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN
		INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
			SELECT  DISTINCT a.CPTEXT
			FROM
				HD_CHECK_RESULT_DATA_CACHE a
			WHERE
				(a.ISYSID = LN_SYSID OR
				a.ISYSID=-1 ) AND
				a.MEID = LN_CPID AND
				a.CIID = AV_CHECK_ITEM_ID;
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
				INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
				SELECT DISTINCT a.CPTEXT
				FROM
					HD_CHECK_RESULT_DATA_CACHE a
				WHERE
					(a.ISYSID = LN_SYSID OR
					a.ISYSID=-1 ) AND
					a.MEID = LN_CPID AND
					a.CIID = AV_CHECK_ITEM_ID
					and a.CPTIME <= AV_TIME_END;
		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
			INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
			SELECT DISTINCT a.CPTEXT
			FROM HD_CHECK_RESULT_DATA_CACHE a
			WHERE
			(a.ISYSID = LN_SYSID OR
			a.ISYSID=-1 ) AND
			a.MEID = LN_CPID AND
			a.CIID = AV_CHECK_ITEM_ID
			and a.CPTIME >= AV_TIME_START;
		ELSE
			INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
			SELECT DISTINCT a.CPTEXT
			FROM
			  HD_CHECK_RESULT_DATA_CACHE a
			WHERE
				(a.ISYSID = LN_SYSID OR
				a.ISYSID=-1 ) AND
				a.MEID = LN_CPID AND
				a.CIID = AV_CHECK_ITEM_ID
				and  (a.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END);
				END	IF;
	END ;;	
	DELIMITER ;

-- 4.7.23 version HC patch is as follows	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	DECLARE	LI_ROWCNT		INTEGER; 
	

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKITEM' AND COLUMN_NAME = 'HEALINGSIGN') THEN
	    ALTER TABLE IEAI_CHKITEM ADD HEALINGSIGN NUMERIC(19) DEFAULT 0;
	END IF;
	
	CREATE TABLE IF NOT EXISTS IEAI_EQUIPMENTTYPE (IID NUMERIC(19) NOT NULL ,INAME VARCHAR(255) NOT NULL ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME NUMERIC(19) ,ISTATE VARCHAR(1) ,CONSTRAINT PK_IEAI_EQUIPMENTTYPE PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_MANUFACTORY (IID NUMERIC(19) NOT NULL ,IOBJECTID VARCHAR(255) ,INAME VARCHAR(255) ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME NUMERIC(19) ,ISTATE VARCHAR(1) ,CONSTRAINT PK_IEAI_MANUFACTORY PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_EQUIPMENTMODEL (IID NUMERIC(19) NOT NULL ,IOBJECTID VARCHAR(255) ,ITYPEID NUMERIC(19) ,IFACTORYID NUMERIC(19) ,INAME VARCHAR(255) ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME NUMERIC(19) ,ISTATE VARCHAR(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_EQUIPMENTMODEL PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_EQUIPMENT (IID NUMERIC(19) NOT NULL ,ICPID NUMERIC(19) NOT NULL ,INAME VARCHAR(255) ,IMANUFACTORY_ID NUMERIC(19) ,IEQUTYPE_ID NUMERIC(19) ,IEQUMODELNUMBER_ID NUMERIC(19) ,ICHASSIS_SERIAL_NUMBER VARCHAR(255) ,ISLOTNUM VARCHAR(255) ,ISLOT_SERIAL_NUMBER VARCHAR(255) ,IIPADDR VARCHAR(255) ,ISOFT_VERSION VARCHAR(255) ,IBANKID NUMERIC(19) ,ICHILDBANKID NUMERIC(19) ,IAREAID NUMERIC(19) ,IEQU_USE VARCHAR(255) ,IRISK_LEVEL NUMERIC(1) ,IBUILDING_NAME VARCHAR(255) ,IEQU_ROOM VARCHAR(255) ,IEQU_BOX VARCHAR(255) ,ISTART_UNIT VARCHAR(255) ,IADMIN_A VARCHAR(255) ,IADMIN_B VARCHAR(255) ,IPROTLCOL VARCHAR(255) ,ISNMP_VERSION VARCHAR(255) ,ICOMMUNITY_NAME VARCHAR(255) ,ILOWPERMIT_USER VARCHAR(255) ,ILOWPERMIT_ACSKEY VARCHAR(255) ,ILOWPERMIT_ENABLE VARCHAR(255) ,IHIGHPERMIT_USER VARCHAR(255) ,IHIGHPERMIT_ACSKEY VARCHAR(255) ,IHIGHPERMIT_ENABLE VARCHAR(255) ,IIFSYS_LOWPERMIT_PWD NUMERIC(1) ,ISYSOBJECTID VARCHAR(255) ,IONLINE_STATE NUMERIC(1) ,IBUY_TIME NUMERIC(19) ,IINSURANCE_TIME NUMERIC(19) ,IISINSURANCE NUMERIC(1) ,IHARDWARE_TYPE VARCHAR(255) ,IBACK_EQUIP VARCHAR(255) ,ISTOREROOM VARCHAR(255) ,IEQUSTATE NUMERIC(1) ,ILASTMODIFYTIME NUMERIC(19) ,ITSMID VARCHAR(255) ,ISACCESSEQU NUMERIC(19) DEFAULT 0 ,IHIGHPERMIT_USER_ALIAS VARCHAR(255) ,ILOWPERMIT_USER_ALIAS VARCHAR(255) ,CONSTRAINT PK_IEAI_EQUIPMENT PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_CHKPOINT_CONFIG (IID NUMERIC(19) NOT NULL ,CHKITEMID NUMERIC(19) ,ILEVELID NUMERIC(12) ,POINTNAME VARCHAR(255) ,THRESHOLD VARCHAR(255) ,WARNLEVEL VARCHAR(255) ,DEFAULTWARNLEVEL VARCHAR(255) ,COMPARERULE VARCHAR(100) ,POINTTYPE DECIMAL(10) ,INTLHOUR NUMERIC(12) ,INTLMINUTE NUMERIC(12) ,INTLLENGTH NUMERIC(12) ,ICRON VARCHAR(255) ,CRTUSER VARCHAR(100) ,CRTTIME NUMERIC(19) ,UPUSER VARCHAR(100) ,UPTIME NUMERIC(19) ,CONSTRAINT PK_IEAI_CHKPOINT_CONFIG PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_CHECK_CMD (IID NUMERIC(19) ,CHKPOINTID NUMERIC(19) ,CMDNAME VARCHAR(255) ,EQUIPVENDOR NUMERIC(12) ,EQUIPTYPE NUMERIC(12) ,CMDINFO LONGTEXT ,MAINTENTIME NUMERIC(19) ,CMDMETHOD NUMERIC(10) ,ISTATE NUMERIC(10) ,CONSTRAINT PK_IEAI_CHECK_CMD PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_CHECK_CMD_EQUVERSION (IID NUMERIC(19) NOT NULL ,CHKCMDID NUMERIC(19) ,EQUVERSIONID NUMERIC(19) ,CONSTRAINT PK_IEAI_CHECK_CMD_EQUVERSION PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_CHECK_SCRIPT (IID NUMERIC(19) NOT NULL ,CHKCMDID NUMERIC(19) ,SCRIPTNAME VARCHAR(255) ,SCRIPTTYPE VARCHAR(100) ,SCRIPTINFO LONGTEXT ,STATUS NUMERIC(10) ,SCRIPTFLAG	 	NUMERIC(2),SCRIPTSERVER	 VARCHAR(19),CONSTRAINT PK_IEAI_CHECK_SCRIPT PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_COM_CHK_GROUP (SCID NUMERIC(12) NOT NULL ,CPID NUMERIC(12) NOT NULL ,CHKITEMID NUMERIC(12) NOT NULL ,PERFORMUSER VARCHAR(100) ,APPLOGO NUMERIC(12) DEFAULT -1 ,STARTLOGO SMALLINT DEFAULT 1 ,CRTUSER VARCHAR(100) ,CRTTIME NUMERIC(19) ,UPUSER VARCHAR(100) ,UPTIME NUMERIC(19) ,IHIDDEN SMALLINT DEFAULT 0 ,IGROUPID NUMERIC(19) ,IGROUPIID NUMERIC(19) ,CONSTRAINT PK_IEAI_COM_CHK_GROUP PRIMARY KEY (SCID));
  CREATE TABLE IF NOT EXISTS IEAI_CHKPOINT_GROUP (CPID NUMERIC(12) NOT NULL ,SCID NUMERIC(12) NOT NULL ,CHKPOINT VARCHAR(512) ,INTLHOUR NUMERIC(12) ,INTLMINUTE NUMERIC(12) ,INTLLENGTH NUMERIC(12) ,STARTLOGO SMALLINT ,CRTUSER VARCHAR(100) ,CRTTIME NUMERIC(19) ,UPUSER VARCHAR(100) ,UPTIME NUMERIC(19) ,ICOMPARERULE VARCHAR(255) ,IDEFAULTWARNLEVEL VARCHAR(255) ,IWARNLEVEL VARCHAR(255) ,ITHRESHOLD VARCHAR(255) ,ISHELLIID NUMERIC(19) ,ICHECKPOINTID NUMERIC(19) ,IPOINTTYPE NUMERIC(19) ,ICORN VARCHAR(255) ,ICHKCMDID NUMERIC(19) ,CONSTRAINT PK_IEAI_CHKPOINT_GROUP PRIMARY KEY (CPID));
  CREATE TABLE IF NOT EXISTS IEAI_AGENTG_BIND_CPG (IID NUMERIC(19) NOT NULL ,IAGENTGROUPID NUMERIC(19) ,ICPGROUPID NUMERIC(19) ,CONSTRAINT PK_IEAI_AGENTG_BIND_CPG PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_COM_CHK_SYS (SCID NUMERIC(12) NOT NULL ,CPID NUMERIC(12) NOT NULL ,CHKITEMID NUMERIC(12) NOT NULL ,PERFORMUSER VARCHAR(100) ,APPLOGO NUMERIC(12) DEFAULT -1 ,STARTLOGO INTEGER DEFAULT 1 ,CRTUSER VARCHAR(100) ,CRTTIME NUMERIC(19) ,UPUSER VARCHAR(100) ,UPTIME NUMERIC(19) ,IHIDDEN INTEGER DEFAULT 0  ,ISYSID NUMERIC(19) ,CONSTRAINT PK_IEAI_COM_CHK_SYS PRIMARY KEY (SCID));
  CREATE TABLE IF NOT EXISTS IEAI_CHKPOINT_SYS (CPID NUMERIC(12) NOT NULL ,SCID NUMERIC(12) NOT NULL ,CHKPOINT VARCHAR(512) ,INTLHOUR NUMERIC(12) ,INTLMINUTE NUMERIC(12) ,INTLLENGTH NUMERIC(12) ,STARTLOGO INTEGER ,CRTUSER VARCHAR(100) ,CRTTIME NUMERIC(19) ,UPUSER VARCHAR(100) ,UPTIME NUMERIC(19) ,ICOMPARERULE VARCHAR(255) ,IDEFAULTWARNLEVEL VARCHAR(255) ,IWARNLEVEL VARCHAR(255) ,ITHRESHOLD VARCHAR(255) ,ISHELLIID NUMERIC(19) ,ICHECKPOINTID NUMERIC(19) ,IPOINTTYPE NUMERIC(19) ,ICORN VARCHAR(255) ,ICHKCMDID NUMERIC(19) ,CONSTRAINT PK_IEAI_CHKPOINT_SYS PRIMARY KEY (CPID));
  CREATE TABLE IF NOT EXISTS IEAI_EQUIPCHAR (IID NUMERIC(19) NOT NULL ,VENDOR NUMERIC(19) NOT NULL ,EQUIPTYPE NUMERIC(19) NOT NULL ,SUCCESSCHAR VARCHAR(3000) ,IGNORECHAR VARCHAR(3000) ,SPECIALCHAR VARCHAR(3000) ,ENDCHAR VARCHAR(3000) ,NOCONFIGCHAR VARCHAR(3000) ,NOCINFIGCHAR VARCHAR(3000) ,ERRORCHAR VARCHAR(3000) ,CONSTRAINT PK_IEAI_EQUIPCHAR PRIMARY KEY (IID));
  CREATE TABLE IF NOT EXISTS IEAI_FORTKEY (IID NUMERIC(19) NOT NULL ,IALIAS VARCHAR(255) ,IIP VARCHAR(255) ,IUSER VARCHAR(255) ,IDES VARCHAR(255) ,CONSTRAINT PK_IEAI_FORTKEY PRIMARY KEY (IID));

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'EQTYPE') THEN
			 ALTER TABLE IEAI_COMPUTER_LIST ADD EQTYPE NUMERIC(2) DEFAULT 0 ;
		END IF;
		
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'EQID') THEN
    ALTER TABLE IEAI_COMPUTER_LIST ADD EQID NUMERIC(19) DEFAULT -1 ;
  END IF;

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP' AND COLUMN_NAME = 'GROUPTYPE') THEN
	    ALTER TABLE IEAI_COMPUTER_GROUP ADD GROUPTYPE NUMERIC(19) DEFAULT 0 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP' AND COLUMN_NAME = 'EQTYPE') THEN
	    ALTER TABLE IEAI_COMPUTER_GROUP ADD EQTYPE NUMERIC(10) DEFAULT 0 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP_MAPPING' AND COLUMN_NAME = 'HCSTATUS') THEN
	    ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD HCSTATUS SMALLINT DEFAULT 0 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP_MAPPING' AND COLUMN_NAME = 'IHCAGENTID') THEN
	    ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD IHCAGENTID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP_MAPPING' AND COLUMN_NAME = 'IHCSTATUS') THEN
	    ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD IHCSTATUS SMALLINT DEFAULT 0 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK' AND COLUMN_NAME = 'IGROUPIID') THEN
	    ALTER TABLE IEAI_COM_CHK ADD IGROUPIID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK' AND COLUMN_NAME = 'IPROJECTID') THEN
	    ALTER TABLE IEAI_COM_CHK ADD IPROJECTID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK' AND COLUMN_NAME = 'IHCSTATUS') THEN
	    ALTER TABLE IEAI_COM_CHK ADD IHCSTATUS NUMERIC(10) DEFAULT 0 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK' AND COLUMN_NAME = 'IHCAGENTID') THEN
	    ALTER TABLE IEAI_COM_CHK ADD IHCAGENTID NUMERIC(19) DEFAULT -1 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICHECKPOINTID') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ICHECKPOINTID NUMERIC(19) DEFAULT -1;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICRON') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ICRON VARCHAR(255) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IGROUPIID') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD IGROUPIID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IPOINTTYPE') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD IPOINTTYPE NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICORN') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ICORN VARCHAR(600) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICHKCMDID') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ICHKCMDID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IPROJECT') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD IPROJECT NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ISHELLIID') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ISHELLIID NUMERIC(19) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICOMPARERULE') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ICOMPARERULE VARCHAR(255) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IDEFAULTWARNLEVEL') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD IDEFAULTWARNLEVEL VARCHAR(255) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IWARNLEVEL') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD IWARNLEVEL VARCHAR(255) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ITHRESHOLD') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD ITHRESHOLD VARCHAR(255) ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'TIMESETFLAG') THEN
	    ALTER TABLE IEAI_CHKPOINT ADD TIMESETFLAG SMALLINT   DEFAULT -1 ;
	END IF;
		
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_WEBPORT') THEN
	    ALTER TABLE IEAI_AGENTINFO ADD IAGENT_WEBPORT NUMERIC(19) ;
  END IF;
  
  SELECT COUNT(*) INTO LI_ROWCNT FROM IEAI_EQUIPMENTTYPE  WHERE IID=-1;
		IF	LI_ROWCNT = 0 THEN
				insert into IEAI_EQUIPMENTTYPE(IID,INAME,IDESCRIPTION,ILASTMODIFYTIME,ISTATE)values(-1,'服务�?','默认描述',1574662575836,1);
	  END IF;	

	
	END;;
	DELIMITER ;
	CALL UPPRDE();

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_CHECK_RESULT_DATA_LAST;;
	CREATE PROCEDURE PROC_CHECK_RESULT_DATA_LAST(IN AV_IP VARCHAR(25),IN AV_CPORT NUMERIC(12,0))
	PROLABEL:BEGIN

		DECLARE	LI_NOW			INTEGER;
		DECLARE	LTM_NOW			TIME;
		DECLARE	LD_TODAY			DATE;
		DECLARE	LI_DayOfWeek	SMALLINT;
		DECLARE	LI_DAY			SMALLINT;
		DECLARE	LN_KEY			NUMERIC(19,0);
		DECLARE	LI_ROWCNT		INTEGER;
		
		SET	LTM_NOW = CURRENT_TIME;
		SET	LI_NOW = HOUR(LTM_NOW) * 60 + MINUTE(LTM_NOW);
		
		SET	LD_TODAY = CURRENT_DATE;
		
		
		 SET	LI_DayOfWeek = DATE_FORMAT(LD_TODAY,'%W') + 70;
		
		
		
		SET	LI_DAY = DAY(LD_TODAY);
		
		
		SELECT	COUNT(C.CPID)
		INTO		LI_ROWCNT
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B LEFT JOIN IEAI_PROJECT F ON F.IID = B.APPLOGO AND F.IID > 0,
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E
		WHERE		A.IP = AV_IP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND             C.ICHECKPOINTID=-1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN IFNULL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN IFNULL(B1.START_DAY, 0) AND IFNULL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					);
		
		
		IF	LI_ROWCNT = 0 THEN
			LEAVE PROLABEL;
		END IF;
		
		
		CALL	PROC_GET_MULT_ID ('HD_CHECK_RESULT_DATA_LAST', LI_ROWCNT, LN_KEY);

		
		IF LN_KEY IS NULL THEN
			LEAVE PROLABEL;
		END IF;
		
		COMMIT WORK;
		
		
		INSERT	INTO	TEMP_INI_RESLUT
				(
					IHIDDEN,
					COMCHID,
					ISYSID,
					SYSNAME,
					CHKITEMID,
					ICHKITEMNAME,
					IPARSERULE,
					MEID,
					IP,
					CPPORT,
					CPID,
					CHKPOINT,
					INIITEM,
					INICHKPOINT,
					RSDID,
					ITEM_TAG,
					POINT_TAG
				)
		SELECT	B.IHIDDEN,
					B.SCID AS COMCHID,
					B.APPLOGO AS ISYSID,
					F.INAME,
					B.CHKITEMID,
					E.ICHKITEMNAME,
					E.IPARSERULE,
					A.CPID AS MEID,
					A.IP,
					A.CPPORT,
					C.CPID, 
					C.CHKPOINT,    				
					CONCAT('CHECKITEM=' , E.ISHELLNAME , '|' , E.IVERSION , '|' , IFNULL(B.PERFORMUSER, '') , '|' , B.IHIDDEN) AS INIITEM,
					C.CHKPOINT AS INICHKPOINT,
					(@rownum:=@rownum+1) + LN_KEY - 1,
					E.SCRIPTTAG,
					C.SCRIPTTAG
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B  LEFT JOIN IEAI_PROJECT F ON F.IID = B.APPLOGO AND F.IID > 0,
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E ,(SELECT @rownum:=0) rr
		WHERE		A.IP = AV_IP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND             C.ICHECKPOINTID=-1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN IFNULL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN IFNULL(B1.START_DAY, 0) AND IFNULL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					)
		ORDER	BY	B.IHIDDEN DESC;
		
		COMMIT WORK;
		
	END ;;
	DELIMITER ;
-- 4.7.24 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING' AND COLUMN_NAME = 'SUGGESTION') THEN
			ALTER TABLE IEAI_HC_ACTWARNING ADD SUGGESTION VARCHAR(200);
		END IF;
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING' AND COLUMN_NAME = 'DISPOSETYPE') THEN
			ALTER TABLE IEAI_HC_ACTWARNING ADD DISPOSETYPE DECIMAL(4) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING_HIS' AND COLUMN_NAME = 'SUGGESTION') THEN
			ALTER TABLE IEAI_HC_ACTWARNING_HIS ADD SUGGESTION VARCHAR(200);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING_HIS' AND COLUMN_NAME = 'DISPOSETYPE') THEN
			ALTER TABLE IEAI_HC_ACTWARNING_HIS ADD DISPOSETYPE DECIMAL(4) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'TMP_HD_BUSINESSMONITOR' AND COLUMN_NAME = 'ALARMLEVEL') THEN
			ALTER TABLE TMP_HD_BUSINESSMONITOR ADD ALARMLEVEL DECIMAL(4) DEFAULT 0;
		END IF;	
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENTTYPE' AND COLUMN_NAME = 'INAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<70) THEN
			ALTER TABLE IEAI_EQUIPMENTTYPE MODIFY INAME VARCHAR(70) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENTTYPE' AND COLUMN_NAME = 'IDESCRIPTION' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENTTYPE MODIFY IDESCRIPTION VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_MANUFACTORY' AND COLUMN_NAME = 'INAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<70) THEN
			ALTER TABLE IEAI_MANUFACTORY MODIFY INAME VARCHAR(70) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_MANUFACTORY' AND COLUMN_NAME = 'IDESCRIPTION' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_MANUFACTORY MODIFY IDESCRIPTION VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENTMODEL' AND COLUMN_NAME = 'INAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<70) THEN
			ALTER TABLE IEAI_EQUIPMENTMODEL MODIFY INAME VARCHAR(70) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENTMODEL' AND COLUMN_NAME = 'IDESCRIPTION' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENTMODEL MODIFY IDESCRIPTION VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'INAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<63) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY INAME VARCHAR(63) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'ICHASSIS_SERIAL_NUMBER' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY ICHASSIS_SERIAL_NUMBER VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IIPADDR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<15) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IIPADDR VARCHAR(15) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'ISOFT_VERSION' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY ISOFT_VERSION VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IBUILDING_NAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IBUILDING_NAME VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IEQU_ROOM' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IEQU_ROOM VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IEQU_BOX' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IEQU_BOX VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IPROTLCOL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IPROTLCOL VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'ILOWPERMIT_USER' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY ILOWPERMIT_USER VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IHIGHPERMIT_USER' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IHIGHPERMIT_USER VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'IHIGHPERMIT_USER_ALIAS' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY IHIGHPERMIT_USER_ALIAS VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPMENT' AND COLUMN_NAME = 'ILOWPERMIT_USER_ALIAS' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_EQUIPMENT MODIFY ILOWPERMIT_USER_ALIAS VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'SUCCESSCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY SUCCESSCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'IGNORECHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY IGNORECHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'SPECIALCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY SPECIALCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'ENDCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY ENDCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'NOCONFIGCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY NOCONFIGCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'NOCINFIGCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY NOCINFIGCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EQUIPCHAR' AND COLUMN_NAME = 'ERRORCHAR' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3000) THEN
			ALTER TABLE IEAI_EQUIPCHAR MODIFY ERRORCHAR VARCHAR(3000) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FORTKEY' AND COLUMN_NAME = 'IALIAS' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<70) THEN
			ALTER TABLE IEAI_FORTKEY MODIFY IALIAS VARCHAR(70) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FORTKEY' AND COLUMN_NAME = 'IIP' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<15) THEN
			ALTER TABLE IEAI_FORTKEY MODIFY IIP VARCHAR(15) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FORTKEY' AND COLUMN_NAME = 'IUSER' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<70) THEN
			ALTER TABLE IEAI_FORTKEY MODIFY IUSER VARCHAR(70) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FORTKEY' AND COLUMN_NAME = 'IDES' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_FORTKEY MODIFY IDES VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'POINTNAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY POINTNAME VARCHAR(150) ;
		END IF;


		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'THRESHOLD' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY THRESHOLD VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'WARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY WARNLEVEL VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'DEFAULTWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<10) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY DEFAULTWARNLEVEL VARCHAR(10) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'COMPARERULE' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY COMPARERULE VARCHAR(3) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'ICRON' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG MODIFY ICRON VARCHAR(150) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_CMD' AND COLUMN_NAME = 'CMDNAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHECK_CMD MODIFY CMDNAME VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_CMD' AND COLUMN_NAME = 'EQUIPVENDOR') THEN
			ALTER TABLE IEAI_CHECK_CMD MODIFY EQUIPVENDOR NUMERIC(19) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_CMD' AND COLUMN_NAME = 'EQUIPTYPE') THEN
			ALTER TABLE IEAI_CHECK_CMD MODIFY EQUIPTYPE NUMERIC(19) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_SCRIPT' AND COLUMN_NAME = 'SCRIPTNAME' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHECK_SCRIPT MODIFY SCRIPTNAME VARCHAR(150) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'CHKPOINT' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY CHKPOINT VARCHAR(150) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'ICORN' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY ICORN VARCHAR(150) ;
		END IF;

		

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'ICOMPARERULE' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY ICOMPARERULE VARCHAR(3) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'IDEFAULTWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<10) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY IDEFAULTWARNLEVEL VARCHAR(10) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'IWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY IWARNLEVEL VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'ITHRESHOLD' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP MODIFY ITHRESHOLD VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'CHKPOINT' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY CHKPOINT VARCHAR(150) ;
		END IF;

		

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'ICORN' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY ICORN VARCHAR(150) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'ICOMPARERULE' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY ICOMPARERULE VARCHAR(3) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'IDEFAULTWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<10) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY IDEFAULTWARNLEVEL VARCHAR(10) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'IWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY IWARNLEVEL VARCHAR(100) ;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'ITHRESHOLD' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT_SYS MODIFY ITHRESHOLD VARCHAR(100) ;
		END IF;

		

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICORN' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<150) THEN
			ALTER TABLE IEAI_CHKPOINT MODIFY ICORN VARCHAR(150) ;
		END IF;


		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ICOMPARERULE' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<3) THEN
			ALTER TABLE IEAI_CHKPOINT MODIFY ICOMPARERULE VARCHAR(3) ;
		END IF;


		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IDEFAULTWARNLEVEL'  AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<10) THEN
			ALTER TABLE IEAI_CHKPOINT MODIFY IDEFAULTWARNLEVEL VARCHAR(10) ;
		END IF;


		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IWARNLEVEL' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT MODIFY IWARNLEVEL VARCHAR(100) ;
		END IF;


		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ITHRESHOLD' AND DATA_TYPE='VARCHAR' AND CHARACTER_MAXIMUM_LENGTH<100) THEN
			ALTER TABLE IEAI_CHKPOINT MODIFY ITHRESHOLD VARCHAR(100) ;
		END IF;
		
		CREATE TABLE IF NOT EXISTS HD_CHECK_TIME_RANGE_CONFIG (CFGID NUMERIC(19) NOT NULL ,DEVID NUMERIC(19) ,CIID NUMERIC(19) ,ICPID NUMERIC(19) ,CYCLE_TYPE INTEGER ,START_DAY INTEGER ,END_DAY INTEGER ,START_TIME TIME NOT NULL ,END_TIME TIME NOT NULL ,SET_START INTEGER ,SET_END INTEGER ,ENABLEDFLAG SMALLINT DEFAULT 1 ,CONSTRAINT PK_HD_CHECK_TIME_RANGE_CONFIG PRIMARY KEY (CFGID));

		

	END;;
	DELIMITER ;
	CALL UPPRDE();

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_HD_BUSINESSMONITOR;
	CREATE PROCEDURE PROC_HD_BUSINESSMONITOR(IN AN_USERID NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
	BEGIN
		DECLARE	ISPERM_COUNT	NUMERIC(19);
		DECLARE	LN_OPT_ID		NUMERIC(19,0);	
		DECLARE	ISPERM_ALL		NUMERIC(19);
		
		CALL	PROC_GET_NEXT_ID ('TMP_HD_BUSINESSMONITOR', LN_OPT_ID);
		SET	AN_OPT_ID = LN_OPT_ID;
		
		SELECT	COUNT(P.IID)
		INTO		ISPERM_ALL
		FROM		IEAI_SYS_PERMISSION P,
					IEAI_USERINHERIT U
		WHERE		P.IPROID = -7
		AND		P.IPERMISSION = 1
		AND		U.IUSERID = AN_USERID
		AND		P.IROLEID = U.IROLEID;
		
		IF ISPERM_ALL > 0 THEN
			
			INSERT INTO TMP_HD_BUSINESSMONITOR
					(
						OPT_ID,
						ISYSID,
						SYSNAME,
						CPSTATUS,
						ALARMLEVEL
					)
			SELECT	LN_OPT_ID,
						D.ISYSID,
						D.SYSNAME,
						MIN(
								CASE
									WHEN D.CPSTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1 
									WHEN D.CPSTATUS=-1 THEN 2 
									WHEN D.CPSTATUS=-6 THEN 3
									ELSE 4
								END
							) AS CPSTATUS,
							MAX(D.CPSTATUS) AS ALARMLEVEL
			FROM		VE_SERVICE_DATA D
			GROUP BY	D.ISYSID, D.SYSNAME
			ORDER BY	D.ISYSID;
		ELSE
			INSERT INTO TMP_HD_BUSINESSMONITOR
					(
						OPT_ID,
						ISYSID,
						SYSNAME,
						CPSTATUS,
						ALARMLEVEL
					)
			SELECT	LN_OPT_ID,
						D.ISYSID,
						D.SYSNAME,
						MIN(
								CASE
									WHEN D.CPSTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1 
									WHEN D.CPSTATUS=-1 THEN 2 
									WHEN D.CPSTATUS=-6 THEN 3
									ELSE 4
								END
							) AS CPSTATUS,
							MAX(D.CPSTATUS) AS ALARMLEVEL
			FROM		VE_SERVICE_DATA D,
						IEAI_USERINHERIT U,
						IEAI_SYS_PERMISSION P
			WHERE		U.IUSERID = AN_USERID
			AND		P.IROLEID = U.IROLEID
			AND		P.IPERMISSION = 1
			AND		D.ISYSID = P.IPROID
			GROUP BY	D.ISYSID, D.SYSNAME
			ORDER BY	D.ISYSID;
		END IF;
	END ;;
	DELIMITER ;
	
	
	
	
-- 4.7.25 version HC patch is as follows
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_VOLUME_PERMISSIONS(IID DECIMAL(19) PRIMARY KEY  NOT NULL,IROLEID DECIMAL(19),IFILESTORENAME VARCHAR(255),IPERMISSION SMALLINT(6));	
	    
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_FILE_STORE(IID int(19) PRIMARY KEY  NOT NULL AUTO_INCREMENT ,TYPE VARCHAR(19),INSERTTIME VARCHAR(255),FILE BLOB,FILENAME VARCHAR(255));	
	    
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_FILE_STORE  P WHERE P.TYPE='所有交易量' AND P.INSERTTIME='NULL') THEN
			INSERT INTO ieai_file_store (TYPE,INSERTTIME) VALUES ('所有交易量','NULL') ;
		END IF;		
	END;;
    DELIMITER ;
    CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_CREATE_CHECK_SNAPSHOT;
	CREATE PROCEDURE PROC_CREATE_CHECK_SNAPSHOT(IN AN_USER_ID NUMERIC(19,0))
	BEGIN
		DECLARE	LN_SNAP_ID	NUMERIC(19,0);
		DECLARE	LI_ROWCNT	INTEGER;
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
	DECLARE in_SNAP_ID NUMERIC(19,0);
	DECLARE  _done INT DEFAULT 0; 
	DECLARE _Cur CURSOR FOR SELECT A.SNAP_ID FROM (SELECT AA.SNAP_ID,(@rownum:=@rownum+1) AS R_NO FROM (SELECT SNAP_ID FROM IEAI_CHECK_SNAPSHOT WHERE USER_ID = AN_USER_ID ORDER BY SNAP_ID DESC) AA,(SELECT @rownum:=0) r ) A WHERE A.R_NO > 5;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET _done=1;

		SET	RETSQLCODE = SQLCODE;
		
		CALL	PROC_GET_NEXT_ID ('IEAI_CHECK_SNAPSHOT', LN_SNAP_ID);
		
		INSERT INTO IEAI_CHECK_SNAPSHOT
					(
						SNAP_ID,
						SNAP_DATE,
						USER_ID,
						USER_NAME
					)
		SELECT	LN_SNAP_ID,
					CURRENT_TIMESTAMP,
					IID,
					IFULLNAME
		FROM		IEAI_USER
		WHERE		IID = AN_USER_ID;
		
		IF	RETSQLCODE = 0 THEN
			
			SELECT	COUNT(*)
			INTO		LI_ROWCNT
			FROM		IEAI_USERINHERIT H,
						IEAI_SYS_PERMISSION P
			WHERE		H.IUSERID = AN_USER_ID
			AND		P.IROLEID = H.IROLEID
			AND		P.IPROID = -7;
			
			IF	LI_ROWCNT > 0 THEN
				INSERT INTO IEAI_CHECK_SNAPSHOT_DEVICES
							(
								SNAP_ID,
								DEV_ID,
								IP,
								DEV_STATUS
							)
				SELECT	DISTINCT
							LN_SNAP_ID,
							D.CPID,
							D.IP,
							IF(I.ISTATUS=0, 1, 0)
				FROM		

							IEAI_PROJECT S,
							IEAI_SYS_RELATION R,
							IEAI_COMPUTER_LIST D LEFT JOIN IEAI_WORKFLOWINSTANCE I ON I.IFLOWID = D.FLOWID
				WHERE		S.IID >= 0
				AND		S.PROTYPE = 7
				AND		R.SYSTEMID = S.IID
				AND		D.CPID = R.COMPUTERID;
			ELSE
				INSERT INTO IEAI_CHECK_SNAPSHOT_DEVICES
							(
								SNAP_ID,
								DEV_ID,
								IP,
								DEV_STATUS
							)
				SELECT	DISTINCT
							LN_SNAP_ID,
							D.CPID,
							D.IP,
							IF(I.ISTATUS=0, 1, 0)
				FROM		IEAI_USERINHERIT H,
							IEAI_SYS_PERMISSION P,
							IEAI_SYS_RELATION R,
							IEAI_COMPUTER_LIST D LEFT JOIN IEAI_WORKFLOWINSTANCE I ON I.IFLOWID = D.FLOWID
				WHERE		H.IUSERID = AN_USER_ID
				AND		P.IROLEID = H.IROLEID
				AND		P.IPROID >= 0
				AND		R.SYSTEMID = P.IPROID
				AND     R.PRJTYPE = 7 
				AND		D.CPID = R.COMPUTERID;
			END IF;
			
			IF RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
				OPEN _Cur;
				LOOP_LABEL:LOOP
					FETCH _Cur INTO in_SNAP_ID;
					IF _done = 1 THEN
						LEAVE LOOP_LABEL;
					ELSE
					 
						DELETE FROM IEAI_CHECK_SNAPSHOT_DEVICES WHERE SNAP_ID = in_SNAP_ID;
						DELETE FROM IEAI_CHECK_SNAPSHOT WHERE SNAP_ID = in_SNAP_ID;
					END IF;
				END LOOP;

	CLOSE _Cur;
	SET _done = 0;
				
				IF RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
					COMMIT WORK;
				ELSE
					ROLLBACK WORK;
				END IF;
			ELSE
				ROLLBACK WORK;
			END IF;
		ELSE
			ROLLBACK WORK;
		END IF;
	END ;;
	DELIMITER ;

-- 4.7.26 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_CONFIG_TEM' AND COLUMN_NAME = 'SOURCETYPE') THEN
		ALTER TABLE IEAI_HC_CONFIG_TEM ADD SOURCETYPE NUMERIC(2) DEFAULT 1 ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK_SYS' AND COLUMN_NAME = 'SOURCETYPE') THEN
			ALTER TABLE IEAI_COM_CHK_SYS ADD SOURCETYPE NUMERIC(2) DEFAULT 1 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_CHK_TIME_INTERVAL_CONF_TEMP' AND COLUMN_NAME = 'SOURCETYPE') THEN
			ALTER TABLE HD_CHK_TIME_INTERVAL_CONF_TEMP ADD SOURCETYPE NUMERIC(2) DEFAULT 1 ;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_CHECKHC_AGENT_H2_VERSION (IID NUMERIC(19) NOT NULL ,PATCHVERSION VARCHAR (50) NOT NULL ,IDESC VARCHAR (150) ,CUSER VARCHAR(20) ,INSERTTIME NUMERIC(19) ,UPUSER VARCHAR(20) ,UPTIME NUMERIC(19) ,PATCHFILE BLOB ,PATCHFILENAME VARCHAR(50) ,ENABLEDTYPE NUMERIC(1) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_V PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CHECKHC_AGENT_H2_CURRENT (IID NUMERIC(19) NOT NULL ,AGENTINFOID NUMERIC(19) NOT NULL ,PATCHIID NUMERIC(19) NOT NULL ,PATCHVERSION VARCHAR(50) ,UPGRADEDESC VARCHAR(150) ,PREPATCHVERSIONID NUMERIC(19) DEFAULT -100 ,PREPATCHVERSION VARCHAR(50) ,INITIALFLAG NUMERIC(1) DEFAULT 0 ,UPGRADETIME NUMERIC(19) ,UPGRADEUSER VARCHAR(20) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_C PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_AGENT_H2_UPGRADE_RECORD (IID NUMERIC(19) NOT NULL ,AGENTINFOID NUMERIC(19) NOT NULL ,PATCHIID NUMERIC(19) NOT NULL ,PATCHVERSION VARCHAR(50) ,IDESC VARCHAR (150) ,UPGRADEDESC VARCHAR(150) ,UPGRADUSER VARCHAR(20) ,UPGRADETIME NUMERIC(19) ,UPGRADERESULT NUMERIC(2) ,UPGRADERESULTMESSAGE VARCHAR(2000) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_R PRIMARY KEY (IID));

		
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'DYNPAR') THEN
			ALTER TABLE IEAI_CHKPOINT ADD DYNPAR VARCHAR(2000) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'DYNPAR') THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG ADD DYNPAR VARCHAR(2000) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'DYNPAR') THEN
			ALTER TABLE IEAI_CHKPOINT_SYS ADD DYNPAR VARCHAR(2000) ;
		END IF;
	
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECKHC_AGENT_H2_VERSION' AND COLUMN_NAME = 'PATCHFILE' ) THEN
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECKHC_AGENT_H2_VERSION' AND COLUMN_NAME = 'PATCHFILE' AND DATA_TYPE='LONGBLOB') THEN
				ALTER TABLE IEAI_CHECKHC_AGENT_H2_VERSION MODIFY COLUMN PATCHFILE LONGBLOB ;
			END IF;
		END IF;
 
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
-- 4.7.27 version HC patch is as follows	
-- PROC_SET_CHK_DATA_LAST_STATUS remove to v8.6.0 version change
	
	
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
	
		CREATE TABLE IF NOT EXISTS IEAI_HC_TEMP_TAG (IID NUMERIC(19) NOT NULL ,ITEMPIID NUMERIC(19) NOT NULL ,ITAG VARCHAR(100) NOT NULL ,SOURCETYPE NUMERIC(2) DEFAULT 1 ,CONSTRAINT PK_IEAI_HC_TEMP_TAG PRIMARY KEY(IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_SCRIPT' AND COLUMN_NAME = 'MODELFLAG') THEN
			ALTER TABLE IEAI_CHECK_SCRIPT ADD MODELFLAG NUMERIC(1) DEFAULT 0 ;
		END IF;


	END;;
	DELIMITER ;
	CALL UPPRDE();
	

	-- 4.7.28 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HCTASK_PERMISSIONS (IPERMISSION INTEGER ,TASKID NUMERIC(19) ,IROLEID NUMERIC(19) ,IID NUMERIC(19) NOT NULL ,IBUSNES_SYS NUMERIC(19),CONSTRAINT PK_IEAI_HCTASK_PERMISSIONS PRIMARY KEY(IID)) ;
		CREATE TABLE IF NOT EXISTS IEAI_HC_INSPECT_TASK (IID NUMERIC(19) NOT NULL ,INAME VARCHAR(255) ,TASKTYPE NUMERIC(1) DEFAULT 0 ,ICREATETIME NUMERIC(19) ,ICREATEUSER NUMERIC(19) NOT NULL,CONSTRAINT PK_HC_INSPECT_TASK PRIMARY KEY(IID) );
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COM_CHK' AND COLUMN_NAME = 'TASKID') THEN
   		ALTER TABLE IEAI_COM_CHK ADD TASKID NUMERIC(19) DEFAULT -1;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND INDEX_NAME = 'IDX_IEAI_COMPUTER_LIST_FLOWID') THEN
			CREATE INDEX IDX_IEAI_COMPUTER_LIST_FLOWID ON IEAI_COMPUTER_LIST (FLOWID);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	-- 4.7.29 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'SCRIPTTAG') THEN
		ALTER TABLE IEAI_CHKPOINT_CONFIG ADD SCRIPTTAG VARCHAR(8) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'SCRIPTPARAM') THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG ADD SCRIPTPARAM VARCHAR(2000) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'SCRIPTPARAM') THEN
			ALTER TABLE IEAI_CHKPOINT ADD SCRIPTPARAM VARCHAR(2000) ;
		END IF;


		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'DYNPAR') THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP ADD DYNPAR VARCHAR(2000) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'SCRIPTTAG') THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP ADD SCRIPTTAG VARCHAR(8) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'SCRIPTPARAM') THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP ADD SCRIPTPARAM VARCHAR(2000) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'SCRIPTTAG') THEN
			ALTER TABLE IEAI_CHKPOINT_SYS ADD SCRIPTTAG VARCHAR(8) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'SCRIPTPARAM') THEN
			ALTER TABLE IEAI_CHKPOINT_SYS ADD SCRIPTPARAM VARCHAR(2000) ;
		END IF;
		
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'ISTARTSTATUS') THEN
		ALTER TABLE IEAI_CHKPOINT ADD ISTARTSTATUS NUMERIC(10) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IHCAGENTID') THEN
			ALTER TABLE IEAI_CHKPOINT ADD IHCAGENTID NUMERIC(19) DEFAULT -1 ;
		END IF;

	END;;
	DELIMITER ;
	CALL UPPRDE();

	-- V8 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_LEVEL' AND COLUMN_NAME = 'ITYPE') THEN
		ALTER TABLE IEAI_LEVEL ADD ITYPE VARCHAR(100);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND INDEX_NAME = 'PK_IEAI_CHKPOINT_SCID_STATUS') THEN
			CREATE INDEX PK_IEAI_CHKPOINT_SCID_STATUS ON IEAI_CHKPOINT (SCID,ISTARTSTATUS);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_HC_RUNALARM_RECORD (IID NUMERIC(19) NOT NULL ,IFLOWID NUMERIC(19) ,IIP VARCHAR(15) ,IFLOWNAME VARCHAR(50) ,IACTNAME VARCHAR(50) ,BDATE NUMERIC(19) ,FREQUENCY NUMERIC(3) DEFAULT 0 ,LDATE NUMERIC(19) ,CONSTRAINT PK_HC_RUNALARM_RECORD_ID PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP TRIGGER IF EXISTS TRB_I_HC_ACTWARNING;;
	CREATE TRIGGER TRB_I_HC_ACTWARNING BEFORE INSERT ON IEAI_HC_ACTWARNING
		  FOR EACH ROW
		  BEGIN
			DECLARE	LV_MSG		VARCHAR(128);
			DECLARE	LV_PRGNAME	VARCHAR(50);
			DECLARE	LV_SUPPORT	VARCHAR(128);
			DECLARE	LI_WTYPE		INTEGER;
			
			SELECT	IFNULL(MAX(A.MSG_CN), ''),
						IFNULL(MAX(A.SUPPORT), '')
			INTO		LV_MSG,
						LV_SUPPORT
			FROM		IEAI_HD_ERROR_CODE_TABLE A
			WHERE		A.ERRCODE = NEW.ERRCODE;
			
			IF NEW.PRGNAME IS NULL THEN
				SET	LV_PRGNAME = NULL;
				SET	LI_WTYPE = 20;
			ELSE
				SELECT 	MAX(T1.ICHKITEMNAME),
							IFNULL(MAX(ILEVELID), 20)
				INTO		LV_PRGNAME,
							LI_WTYPE
				-- FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C
				FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C,IEAI_CHKPOINT P
				WHERE	-- T1.ISHELLNAME = NEW.PRGNAME
						-- AND  T1.ICHKITEMID=C.CHKITEMID;
						C.SCID=P.SCID 
						AND C.CHKITEMID=T1.ICHKITEMID
						AND P.CPID=NEW.CPID;
						
			END IF;
			
			SET NEW.WTYPE = LI_WTYPE;
			
			IF	NEW.AMESSAGE IS NULL OR NEW.AMESSAGE = '' THEN
			    SET NEW.WMESSAGE = CONCAT ( LV_MSG , '。');
			ELSE
				SET NEW.WMESSAGE = CONCAT (LV_MSG,'(' ,NEW.AMESSAGE, ')。'); 
			END IF;
			
			 IF LV_PRGNAME IS NULL OR NEW.CHKVALUE IS NULL OR NEW.THREADHOLD IS NULL THEN
					SET NEW.WMESSAGE =CONCAT('自动化巡检系统报告：' ,NEW.WMESSAGE ,LV_SUPPORT);
			 ELSE 
		
					SET NEW.WMESSAGE = CONCAT('自动化巡检系统报告：',NEW.WMESSAGE,LV_SUPPORT,'(',LV_PRGNAME,', 检测值:', NEW.CHKVALUE ,',基线:',NEW.THREADHOLD,')');
			 END IF; 
	END ;;
	DELIMITER ;
	DELIMITER ;;
	DROP TRIGGER IF EXISTS TRB_U_HC_ACTWARNING;;
	CREATE TRIGGER TRB_U_HC_ACTWARNING BEFORE UPDATE ON IEAI_HC_ACTWARNING
	  FOR EACH ROW
	  BEGIN
		DECLARE	LV_MSG		VARCHAR(128);
		DECLARE	LV_PRGNAME	VARCHAR(50);
		DECLARE	LV_SUPPORT	VARCHAR(128);
		DECLARE	LI_WTYPE		INTEGER;
		
		SELECT	IFNULL(MAX(A.MSG_CN), ''),
					IFNULL(MAX(A.SUPPORT), '')
		INTO		LV_MSG,
					LV_SUPPORT
		FROM		IEAI_HD_ERROR_CODE_TABLE A
		WHERE		A.ERRCODE = NEW.ERRCODE;
		
		IF NEW.PRGNAME IS NULL THEN
			SET	LV_PRGNAME = NULL;
			SET	LI_WTYPE = 20;
		ELSE
			SELECT 	MAX(T1.ICHKITEMNAME),
							IFNULL(MAX(ILEVELID), 20)
				INTO		LV_PRGNAME,
							LI_WTYPE
				-- FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C
				FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C,IEAI_CHKPOINT P
				WHERE	-- T1.ISHELLNAME = NEW.PRGNAME
						-- AND  T1.ICHKITEMID=C.CHKITEMID;
						C.SCID=P.SCID 
						AND C.CHKITEMID=T1.ICHKITEMID
						AND P.CPID=NEW.CPID;
						
		END IF;
		
		SET NEW.WTYPE = LI_WTYPE;
		
		IF	NEW.AMESSAGE IS NULL OR NEW.AMESSAGE = '' THEN
			
				 SET NEW.WMESSAGE = CONCAT ( LV_MSG , '。');
		ELSE
				SET NEW.WMESSAGE = CONCAT (LV_MSG,'(' ,NEW.AMESSAGE, ')。'); 
		END IF;
		
		IF LV_PRGNAME IS NULL OR NEW.CHKVALUE IS NULL OR NEW.THREADHOLD IS NULL THEN
				SET NEW.WMESSAGE =CONCAT('自动化巡检系统报告：' ,NEW.WMESSAGE ,LV_SUPPORT);
		 ELSE 
	
				SET NEW.WMESSAGE = CONCAT('自动化巡检系统报告：',NEW.WMESSAGE,LV_SUPPORT,'(',LV_PRGNAME,', 检测值:', NEW.CHKVALUE ,',基线:',NEW.THREADHOLD,')');
		 END IF; 
	END;;
	DELIMITER ;

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HC_AGREEMENT  (IAGREEMENTID    NUMERIC(19)  NOT NULL,IAGREEMENTNAME  VARCHAR(100),ICOMPUTERID   NUMERIC(19),IAGREEMENTTYPE  VARCHAR(20),IAGREEMENTIP  VARCHAR(100),IAGREEMENTDOMAIN VARCHAR(100),IAGREEMENTPORT  NUMERIC(5),IIPORDOMAN  NUMERIC(2),IUSERNAME  VARCHAR(100),IPASSWORD  VARCHAR(100),VERSION   VARCHAR(100),COMMUNITY   VARCHAR(100),CONTEXTNAME VARCHAR(100),SAFENAME VARCHAR(100),SAFELEVEL VARCHAR(100),VFTAGEEEMENT VARCHAR(100),VFTPASSWORD  VARCHAR(100),PRIVACYAGREEMENT  VARCHAR(100), PRIVATEKEY  VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT PRIMARY KEY (IAGREEMENTID));
	
	END;;
	DELIMITER ;
	CALL UPPRDE();

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_HD_ERROR_CODE_TABLE  P WHERE P.ERRCODE=500) THEN
			INSERT INTO IEAI_HD_ERROR_CODE_TABLE (ERRCODE, MSG_CN, MSG_EN, SUPPORT) VALUES (500, '解析异常', NULL, '需要AOMS技术支持人员协助�?') ;
		END IF;		
	END;;
        DELIMITER ;
        CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HC_AGREEMENT_CATEGORY (IID  NUMERIC(19)   NOT NULL,ICATEGORY  VARCHAR(100),ITYPE  VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_CATEGORY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_HC_AGREEMENT_TYPE(ICATEGORYID NUMERIC(19) NOT NULL,IAGREEMENTID NUMERIC(19),ICATEGORY NUMERIC(19),IVALUE VARCHAR(100),ITYPE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_TYPE PRIMARY KEY (ICATEGORYID));	
		CREATE TABLE IF NOT EXISTS IEAI_HC_AGREEMENT_DBTYYE(IID NUMERIC(19) NOT NULL,DBNAME VARCHAR(100),DBTYPE VARCHAR(100),ITYPE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_DBTYYE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_HC_AGREEMENT_CONFIGURE(IID NUMERIC(19) NOT NULL,IVENDOR VARCHAR(100),IMODEL VARCHAR(100),ICATEGORY VARCHAR(100),ITYPE VARCHAR(100), IVALUE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_CONFIGURE PRIMARY KEY (IID));
		
		IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING' AND COLUMN_NAME = 'PRGNAME') THEN
			ALTER TABLE IEAI_HC_ACTWARNING modify PRGNAME VARCHAR(100);
		END IF;

		IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING_HIS' AND COLUMN_NAME = 'PRGNAME') THEN
			ALTER TABLE IEAI_HC_ACTWARNING_HIS modify PRGNAME VARCHAR(100);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	
	
	-- v8.1.1
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_SCRIPT' AND COLUMN_NAME = 'PROTOCOL') THEN
		ALTER TABLE IEAI_CHECK_SCRIPT ADD PROTOCOL VARCHAR(10)  DEFAULT 'Entegor'  ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_SCRIPT' AND COLUMN_NAME = 'AGREEMENTCATEGORYID') THEN
			ALTER TABLE IEAI_CHECK_SCRIPT ADD AGREEMENTCATEGORYID NUMERIC(19)  DEFAULT -1 ;
		END IF;


	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_CMD' AND COLUMN_NAME = 'CMDDESC') THEN
		ALTER TABLE IEAI_CHECK_CMD ADD CMDDESC VARCHAR(200) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'CHKPOINTDESC') THEN
			ALTER TABLE IEAI_CHKPOINT ADD CHKPOINTDESC VARCHAR(2000) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_CONFIG' AND COLUMN_NAME = 'CHKPOINTDESC') THEN
			ALTER TABLE IEAI_CHKPOINT_CONFIG ADD CHKPOINTDESC VARCHAR(200) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_SYS' AND COLUMN_NAME = 'CHKPOINTDESC') THEN
			ALTER TABLE IEAI_CHKPOINT_SYS ADD CHKPOINTDESC VARCHAR(200) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT_GROUP' AND COLUMN_NAME = 'CHKPOINTDESC') THEN
			ALTER TABLE IEAI_CHKPOINT_GROUP ADD CHKPOINTDESC VARCHAR(200) ;
		END IF;
		CREATE TABLE IF NOT EXISTS IEAI_CHECK_HANDWORK_PENGDING (IID NUMERIC(19) NOT NULL ,SYSTEMID NUMERIC(19) ,COMPUTERID NUMERIC(19) ,CHKITEMID NUMERIC(19) ,CHKPOINTID NUMERIC(19) NOT NULL ,CHKPOINTNAME VARCHAR(150) ,CHKPOINTDESC VARCHAR(500) ,WDATE NUMERIC(19) ,LDATE NUMERIC(19) ,PUSHCOUNT NUMERIC(3) DEFAULT 1 ,CONSTRAINT PK_IEAI_HANDWORK_PENGDING PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CHECK_HANDWORK_HIS (IID NUMERIC(19) NOT NULL ,SYSTEMID NUMERIC(19) ,SYSTEMNAME VARCHAR(300) ,COMPUTERID NUMERIC(19) ,COMPUTERIP VARCHAR(150) ,COMPUTERNAME VARCHAR(150) ,CHKITEMID NUMERIC(19) ,CHKITEMNAME VARCHAR(300) ,CHKPOINTID NUMERIC(19) NOT NULL ,CHKPOINTNAME VARCHAR(150) ,CHKPOINTDESC VARCHAR(500) ,CHKCMDID NUMERIC(19) ,CMDDESC VARCHAR(500) ,CMDNAME VARCHAR(200) ,WDATE NUMERIC(19) ,LDATE NUMERIC(19) ,HANDLEDATE NUMERIC(19) ,PUSHCOUNT NUMERIC(3) DEFAULT 1 ,HANDLEUSER VARCHAR(150) ,HANDLEMSG VARCHAR(500) ,EXCUTERESULT VARCHAR(50) );

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- V8.2.0 version HC patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_HD_DATAOVERVIEW;;
	CREATE PROCEDURE PROC_HD_DATAOVERVIEW(IN AN_USERID NUMERIC(19,0), IN AN_OBJ_ID NUMERIC(19,0), IN AI_OBJ_TYPE SMALLINT, IN AI_PAGE_ID INTEGER, IN AI_PAGE_SIZE INTEGER,IN AI_DATA_CENTER VARCHAR(255),IN AN_STATUS NUMERIC(19,0), OUT AN_OPT_ID NUMERIC(19,0), OUT AI_REC_AMOUNT INTEGER)
	LABEL_PRO:BEGIN
		DECLARE	CN_MENU_ID			SMALLINT DEFAULT 72;
		DECLARE	V_COUNT				INTEGER;
		DECLARE	LI_PAGE_FROM		INTEGER;
		DECLARE	LI_PAGE_TO			INTEGER;
		DECLARE	LI_PAGE_COUNT		INTEGER;

		IF	AI_OBJ_TYPE < 0 OR AI_OBJ_TYPE > 4	THEN
			LEAVE LABEL_PRO;
		END	IF;
		
		CALL	PROC_GET_NEXT_ID ('TMP_DATASHOW', AN_OPT_ID);
		
		IF	AN_OPT_ID IS NULL	THEN
			LEAVE LABEL_PRO;
		END	IF;
		
		IF AI_OBJ_TYPE BETWEEN 0 AND 1 THEN
			SELECT
				COUNT(P.IID) 
			INTO V_COUNT 
			FROM
				IEAI_USERINHERIT U,
				IEAI_SYS_PERMISSION S,
				IEAI_PROJECT P 
			WHERE
				U.IUSERID = AN_USERID 
				AND S.IROLEID = U.IROLEID
				AND S.IPERMISSION = 1 
				AND S.IPROID = -7
				AND P.IID = S.IPROID 
				AND (P.PROTYPE = -7 OR P.PROTYPE = 7)
				;
		END IF;
		
		
		IF	AI_OBJ_TYPE = 0	THEN
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,						
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S, IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.IID > 0
								AND S.IID=SS.IID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND S.IID=SS.IID
								AND		S.IID >= 0
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID	
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
										
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 1	THEN
		
			
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
							SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
							SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
							SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
							SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
							SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
							SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, CONCAT(RIGHT(CONCAT('000',L.APPLVLID ),3), '.', L.APPLVL) AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
										
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		S.IID >= 0
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B,(SELECT @rownum1:=0) rr
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 2	THEN
			IF LENGTH(AI_DATA_CENTER)>0 THEN 
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							(@rownum1:=@rownum1+1) AS rownum 
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
											SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
											SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
											SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
											SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
											SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
											SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								AND A.NDCNAME = AI_DATA_CENTER
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							) AA,(SELECT @rownum1:=0) rr
				ORDER	BY	NDCNAME, NIP;
			ELSE
			
				IF	AN_STATUS = 2	THEN
						INSERT	INTO	TMP_DATASHOW
								(
									OPT_ID,
									DATA_CENTER,
									OID,
									POID,
									OBJ_NAME,
									OBJ_TYPE,
									AMOUNT,
									CL0,	
									CL1,	
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									CHECK_TIME,
									CHECK_RESULT,
									ROW_ID
								)
						SELECT	AN_OPT_ID,
									NDCNAME,
									OID,
									AN_OBJ_ID,
									OBJ_NAME,
									(AI_OBJ_TYPE + 1),
									AMOUNT,
									CL0,
									CL1,
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									NULL,
									NULL,
									(@rownum1:=@rownum1+1) AS rownum 
						FROM		(
									 SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
												SELECT	A.NDCNAME,
															A.OID,
															A.OBJ_NAME,
															IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
															SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
															SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
															SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
															SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
															SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
															SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
															SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
															A.NIP
												FROM		(
																SELECT	R.CENTERNAME,
																			R.SDID AS OID,
																			L.IP AS OBJ_NAME,
																			FUN_GET_IP_NUMBER(L.IP) AS NIP,
																			N.NDCNAME
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COMPUTER_LIST L,
																			(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		L.CPID = R.COMPUTERID
																AND		N.CENTERNAME = R.CENTERNAME
															) A,
															(
																SELECT	R.CENTERNAME,
																			R.SDID,
																			D.CPSTATUS,
																			COUNT(*) AS DAT_CNT
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COM_CHK C,
																			IEAI_CHKPOINT K,
																			HD_CHECK_RESULT_DATA_LAST D
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		C.CPID = R.COMPUTERID
																AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
																AND		C.STARTLOGO = 1
																AND		K.SCID = C.SCID
																AND		K.STARTLOGO = 1
																AND		D.CPID = K.CPID
																GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
															) B
												WHERE		B.SDID = A.OID
												AND		B.CENTERNAME = A.CENTERNAME
												GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP ) VG
											) AA,(SELECT @rownum1:=0) rr
								ORDER	BY	NDCNAME, NIP ;
					 ELSE 
              IF	AN_STATUS = 1	THEN
						INSERT	INTO	TMP_DATASHOW
								(
									OPT_ID,
									DATA_CENTER,
									OID,
									POID,
									OBJ_NAME,
									OBJ_TYPE,
									AMOUNT,
									CL0,	
									CL1,	
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									CHECK_TIME,
									CHECK_RESULT,
									ROW_ID
								)
						SELECT	AN_OPT_ID,
									NDCNAME,
									OID,
									AN_OBJ_ID,
									OBJ_NAME,
									(AI_OBJ_TYPE + 1),
									AMOUNT,
									CL0,
									CL1,
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									NULL,
									NULL,
									(@rownum1:=@rownum1+1) AS rownum 
						FROM		(
									 SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
												SELECT	A.NDCNAME,
															A.OID,
															A.OBJ_NAME,
															IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
															SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
															SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
															SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
															SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
															SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
															SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
															SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
															A.NIP
												FROM		(
																SELECT	R.CENTERNAME,
																			R.SDID AS OID,
																			L.IP AS OBJ_NAME,
																			FUN_GET_IP_NUMBER(L.IP) AS NIP,
																			N.NDCNAME
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COMPUTER_LIST L,
																			(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		L.CPID = R.COMPUTERID
																AND		N.CENTERNAME = R.CENTERNAME
															) A,
															(
																SELECT	R.CENTERNAME,
																			R.SDID,
																			D.CPSTATUS,
																			COUNT(*) AS DAT_CNT
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COM_CHK C,
																			IEAI_CHKPOINT K,
																			HD_CHECK_RESULT_DATA_LAST D
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		C.CPID = R.COMPUTERID
																AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
																AND		C.STARTLOGO = 1
																AND		K.SCID = C.SCID
																AND		K.STARTLOGO = 1
																AND		D.CPID = K.CPID
																GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
															) B
												WHERE		B.SDID = A.OID
												AND		B.CENTERNAME = A.CENTERNAME
												GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP) VG
											) AA,(SELECT @rownum1:=0) rr where AA.ISTATUS > 0
								ORDER	BY	NDCNAME, NIP ;
						ELSE
							INSERT	INTO	TMP_DATASHOW
								(
									OPT_ID,
									DATA_CENTER,
									OID,
									POID,
									OBJ_NAME,
									OBJ_TYPE,
									AMOUNT,
									CL0,	
									CL1,	
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									CHECK_TIME,
									CHECK_RESULT,
									ROW_ID
								)
						SELECT	AN_OPT_ID,
									NDCNAME,
									OID,
									AN_OBJ_ID,
									OBJ_NAME,
									(AI_OBJ_TYPE + 1),
									AMOUNT,
									CL0,
									CL1,
									CL2,
									CL3,
									CL4,
									CL5,
									CHECKING,
									NULL,
									NULL,
									(@rownum1:=@rownum1+1) AS rownum 
						FROM		(
									 SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
												SELECT	A.NDCNAME,
															A.OID,
															A.OBJ_NAME,
															IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
															SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
															SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
															SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
															SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
															SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
															SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
															SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
															A.NIP
												FROM		(
																SELECT	R.CENTERNAME,
																			R.SDID AS OID,
																			L.IP AS OBJ_NAME,
																			FUN_GET_IP_NUMBER(L.IP) AS NIP,
																			N.NDCNAME
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COMPUTER_LIST L,
																			(SELECT CONCAT(RIGHT(CONCAT('00',(@rownum:=@rownum+1)),2),'.',CENTERNAME) AS NDCNAME, CENTERNAME FROM IEAI_DC,(SELECT @rownum:=0) rrr ORDER BY ISMAIN DESC, CENTERNAME) N
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		L.CPID = R.COMPUTERID
																AND		N.CENTERNAME = R.CENTERNAME
															) A,
															(
																SELECT	R.CENTERNAME,
																			R.SDID,
																			D.CPSTATUS,
																			COUNT(*) AS DAT_CNT
																FROM		IEAI_SYS_RELATION R,
																			IEAI_COM_CHK C,
																			IEAI_CHKPOINT K,
																			HD_CHECK_RESULT_DATA_LAST D
																WHERE		R.SYSTEMID = AN_OBJ_ID
																AND		C.CPID = R.COMPUTERID
																AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
																AND		C.STARTLOGO = 1
																AND		K.SCID = C.SCID
																AND		K.STARTLOGO = 1
																AND		D.CPID = K.CPID
																GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
															) B
												WHERE		B.SDID = A.OID
												AND		B.CENTERNAME = A.CENTERNAME
												GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP) VG
											) AA,(SELECT @rownum1:=0) rr where AA.ISTATUS = 0
								ORDER	BY	NDCNAME, NIP ;
					 END IF;
			END IF;
		END IF;
		ELSEIF	AI_OBJ_TYPE = 3	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						A.CENTERNAME,
						A.OID,
						AN_OBJ_ID AS POID,
						A.OBJ_NAME,
						(AI_OBJ_TYPE + 1),
						IFNULL(SUM(B.DAT_CNT), 0) AS AMOUNT,
						SUM(case when B.CPSTATUS = 0 then B.DAT_CNT else 0 end) AS CL0,
						SUM(case when B.CPSTATUS = 1 then B.DAT_CNT else 0 end) AS CL1,
						SUM(case when B.CPSTATUS = 2 then B.DAT_CNT else 0 end) AS CL2,
						SUM(case when B.CPSTATUS = 3 then B.DAT_CNT else 0 end) AS CL3,
						SUM(case when B.CPSTATUS = 4 then B.DAT_CNT else 0 end) AS CL4,
						SUM(case when B.CPSTATUS = 5 then B.DAT_CNT else 0 end) AS CL5,
						SUM(case when B.CPSTATUS = -1 then B.DAT_CNT else 0 end) AS CHECKING,	
						NULL,
						NULL,
						(@rownum:=@rownum+1) AS rownum 
			FROM		(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID AS OID,
										T.ICHKITEMNAME AS OBJ_NAME
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKITEM T
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		T.ICHKITEMID = C.CHKITEMID
						) A,
						(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID,
										D.CPSTATUS,
										COUNT(*) AS DAT_CNT
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKPOINT P,
										HD_CHECK_RESULT_DATA_LAST D
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		P.SCID = C.SCID
							AND		P.STARTLOGO = 1
							AND		D.CPID = P.CPID
							GROUP BY R.CENTERNAME, C.SCID, D.CPSTATUS
						) B,(SELECT @rownum:=0) rr
			WHERE		B.CENTERNAME = A.CENTERNAME
			AND		B.SCID = A.OID
			GROUP BY A.CENTERNAME, A.OID, A.OBJ_NAME
			ORDER	BY	A.OID;
		ELSEIF	AI_OBJ_TYPE = 4	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						NULL,
						D.RSDID,
						AN_OBJ_ID AS POID,
						D.CPTEXT,
						(AI_OBJ_TYPE + 1),
						1 AS AMOUNT,
						(case when D.CPSTATUS = 0 then 1 else 0 end) AS CL0,
						(case when D.CPSTATUS = 1 then 1 else 0 end) AS CL1,
						(case when D.CPSTATUS = 2 then 1 else 0 end) AS CL2,
						(case when D.CPSTATUS = 3 then 1 else 0 end) AS CL3,
						(case when D.CPSTATUS = 4 then 1 else 0 end) AS CL4,
						(case when D.CPSTATUS = 5 then 1 else 0 end) AS CL5,
						(case when D.CPSTATUS = -1 then 1 else 0 end) AS CHECKING,
						FUN_GET_TIMESTAMP(D.CPTIME, 0),
						D.CPSTATUS,
						(@rownum:=@rownum+1) AS rownum 
			FROM		IEAI_CHKPOINT P,
						HD_CHECK_RESULT_DATA_LAST D
						,(SELECT @rownum:=0) rr
			WHERE		P.SCID = AN_OBJ_ID
			AND		P.STARTLOGO = 1
			AND		D.CPID = P.CPID;
		END	IF;
		
		
		SELECT	COUNT(*)
		INTO		AI_REC_AMOUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID;
		
		
		SET	LI_PAGE_COUNT = AI_REC_AMOUNT / AI_PAGE_SIZE;
		
		IF MOD(AI_REC_AMOUNT, AI_PAGE_SIZE) > 0 THEN
			SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
		END	IF;
		
		IF	AI_PAGE_ID < 1 THEN
			SET	AI_PAGE_ID = 1;
		END	IF;
		
		IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
			SET	AI_PAGE_ID = LI_PAGE_COUNT;
		END	IF;
		
		SET	LI_PAGE_FROM = (AI_PAGE_ID - 1) * AI_PAGE_SIZE + 1;
		SET	LI_PAGE_TO = AI_PAGE_ID * AI_PAGE_SIZE;
		
		SELECT	SIGN(COUNT(*))
		INTO		V_COUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID
		AND		(ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		
		IF V_COUNT > 0 THEN
			DELETE	FROM	TMP_DATASHOW WHERE OPT_ID = AN_OPT_ID AND (ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		END IF;
	END ;;
	DELIMITER ;
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_A_MAIL_FOR_PINGAN;;
	CREATE PROCEDURE PROC_GET_A_MAIL_FOR_PINGAN(IN AI_TIMER INTEGER, OUT AN_MAIL_ID NUMERIC(19,0), OUT AN_SYS_ID NUMERIC(19,0), OUT AV_SYS_NAME VARCHAR(200),OUT AV_MAILS VARCHAR(1000), OUT AV_SUMMARY VARCHAR(200))
	BEGIN
		DECLARE	LN_MAIL_ID	NUMERIC(19,0);
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
		DECLARE	LT_NOW		TIME;
		DECLARE	LI_REDO		SMALLINT	DEFAULT	0;
		DECLARE	LI_CNT		SMALLINT;
		DECLARE	LI_MaxRedo	SMALLINT	DEFAULT	10;
		DECLARE	LI_TIMERS	INTEGER;
		DECLARE	LI_HOURS		SMALLINT;
		DECLARE	LI_MINUTES	SMALLINT;
		DECLARE	LI_POWERS	SMALLINT	DEFAULT 5;
		DECLARE	LI_WEEK_DAY	SMALLINT;
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;
		
		SET	LT_NOW = CURRENT_TIME;
		SET	AN_MAIL_ID = -1;
		SET	LI_HOURS	= AI_TIMER * LI_POWERS / 3600;
		SET	LI_MINUTES = AI_TIMER * LI_POWERS / 60;
		
		SET	LI_TIMERS = LI_HOURS * 10000 + LI_MINUTES * 100 + MOD(AI_TIMER * 5, 60);
		
		
		SELECT	COUNT(*)
		INTO		LI_CNT
		FROM		IEAI_SENDMAIL
		WHERE		STATUS = 10
		AND		((LT_NOW - GET_TIME) + (CASE WHEN LT_NOW < GET_TIME THEN 240000 ELSE 0 END)) > LI_TIMERS;
		
		IF	LI_CNT > 0	THEN
			UPDATE	IEAI_SENDMAIL
			SET		GET_TIME = TIME('00:00:00'),
						STATUS = 1
			WHERE		STATUS = 10
			AND		((LT_NOW - GET_TIME) + (CASE WHEN LT_NOW < GET_TIME THEN 240000 ELSE 0 END)) > LI_TIMERS;
			
			COMMIT WORK;
		END IF;
		
		
		
		SET	LI_WEEK_DAY = 70 + DAYOFWEEK(CURRENT_DATE);
		
		WHILE	(LI_REDO < LI_MaxRedo)
		DO
			SELECT	MIN(A.IID)
			INTO		LN_MAIL_ID
			FROM		IEAI_SENDMAIL A
			WHERE		A.STATUS = 1
			AND		A.MSWITCH = 1
			and (case when A.DAY_TYPE = 0 then 1 when A.DAY_TYPE = LI_WEEK_DAY then 1 else 0 end) > 0
			AND		A.PLANNING_TIME =
							(
								SELECT	MAX(B1.PLANNING_TIME)
								FROM		IEAI_SENDMAIL B1
								WHERE		B1.SYSTEMID = A.SYSTEMID
								AND		B1.MSWITCH = 1
								and (case when B1.DAY_TYPE = 0 then 1 when B1.DAY_TYPE = LI_WEEK_DAY then 1 else 0 end) > 0
								AND		B1.PLANNING_TIME <= LT_NOW
							);
			
			
			
			UPDATE IEAI_SENDMAIL M,IEAI_SENDMAIL BB
			 SET    M.STATUS = 10,
					M.GET_TIME = LT_NOW
			 WHERE  M.DAY_TYPE=BB.DAY_TYPE
			   AND  M.MSWITCH=BB.MSWITCH
			   AND  M.PLANNING_TIME=BB.PLANNING_TIME
			   AND  M.MSWITCH=1
			   AND  M.STATUS=1			   
			   AND  BB.IID=LN_MAIL_ID
			   AND  BB.STATUS=1;
			
			IF	RETSQLCODE = 0 THEN
				COMMIT WORK;
				
				SELECT	IID,
							SYSTEMID,
							SYSNAME,
							MAILS,
							IFNULL(SUMMARY, '')
				INTO		AN_MAIL_ID,
							AN_SYS_ID,
							AV_SYS_NAME,
							AV_MAILS,
							AV_SUMMARY
				FROM		IEAI_SENDMAIL
				WHERE		IID = LN_MAIL_ID;
				
				SET	LI_REDO = LI_MaxRedo;
			ELSE
				ROLLBACK WORK;
			END IF;
			
			SET	LI_REDO = LI_REDO + 1;
		END WHILE;
	END ;;
	DELIMITER ;
	
		-- v8.3.0
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKITEM' AND COLUMN_NAME = 'IBASIC') THEN
    	ALTER TABLE IEAI_CHKITEM ADD IBASIC NUMERIC(2) DEFAULT 0 ;
		END IF;



	END;;
	DELIMITER ;
	CALL UPPRDE();
	
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_INSPECT_TASK' AND COLUMN_NAME = 'TASKDESC') THEN 
			ALTER TABLE IEAI_HC_INSPECT_TASK ADD TASKDESC VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_ACTWARNING' AND COLUMN_NAME = 'DELUSER') THEN 
			ALTER TABLE IEAI_HC_ACTWARNING ADD DELUSER VARCHAR(30) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_HANDWORK_HIS' AND COLUMN_NAME = 'TASKNAME') THEN 
			ALTER TABLE IEAI_CHECK_HANDWORK_HIS ADD TASKNAME VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();


-- v8.5.0 version HC patch is as follows

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_HC_CONFIRM (IID NUMERIC (19) NOT NULL,RSDID NUMERIC (19) ,CRID NUMERIC (19) ,IUSERID NUMERIC (19) ,IFULLNAME VARCHAR (255) ,ICONFIRMDATE NUMERIC (19) ,IWCOUNT NUMERIC (19) ,IWCODE NUMERIC (19) , CONSTRAINT PK_IEAI_HC_CONFIRM PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_CHECK_RESULT_DATA_CACHE' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE HD_CHECK_RESULT_DATA_CACHE ADD STARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_HANDWORK_HIS' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE IEAI_CHECK_HANDWORK_HIS ADD STARTUSER VARCHAR(50) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHECK_HANDWORK_PENGDING' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE IEAI_CHECK_HANDWORK_PENGDING ADD STARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_CHECK_RESULT_DATA_HIS' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE HD_CHECK_RESULT_DATA_HIS ADD STARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_CHECK_RESULT_DATA_LAST' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE HD_CHECK_RESULT_DATA_LAST ADD STARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'STARTUSER') THEN 
			ALTER TABLE IEAI_CHKPOINT ADD STARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'STOPUSER') THEN 
			ALTER TABLE IEAI_CHKPOINT ADD STOPUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IMMESTARTUSER') THEN 
			ALTER TABLE IEAI_CHKPOINT ADD IMMESTARTUSER VARCHAR(30) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CHKPOINT' AND COLUMN_NAME = 'IMMESTARTTIME') THEN 
			ALTER TABLE IEAI_CHKPOINT ADD IMMESTARTTIME NUMERIC(19) DEFAULT 0; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_CHECK_POINTS;;
	CREATE PROCEDURE PROC_GET_CHECK_POINTS(IN AV_HOSTIP VARCHAR(15), OUT AI_DO INTEGER)
	BEGIN
		DECLARE	LI_NOW			INTEGER;
		DECLARE	LTM_NOW			TIME;
		DECLARE	LD_TODAY			DATE;
		DECLARE	LI_DayOfWeek	SMALLINT;
		DECLARE	LI_DAY			SMALLINT;
		
		
		SET	LTM_NOW = CURRENT_TIME;
		SET	LI_NOW = HOUR(LTM_NOW) * 60 + MINUTE(LTM_NOW);
		
		SET	LD_TODAY = CURRENT_DATE;
		SET	LI_DayOfWeek = DATE_FORMAT(LD_TODAY,'%W') + 70;
		SET	LI_DAY = DAY(LD_TODAY);
		
		SELECT	IFNULL(SIGN(COUNT(*)), 0)
		INTO		AI_DO
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B LEFT JOIN IEAI_SYS_RELATION F ON F.SYSTEMID = B.APPLOGO, 
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E
		WHERE		A.IP = AV_HOSTIP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		B.IHIDDEN = 0
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN IFNULL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN IFNULL(B1.START_DAY, 0) AND IFNULL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					);
	END ;;
	DELIMITER ;
	
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_DEAL_ACT_WARNING_MESSAGE;
	CREATE PROCEDURE PROC_DEAL_ACT_WARNING_MESSAGE(IN AN_PK NUMERIC(19,0),IN AV_USER VARCHAR(50), IN AV_SUGGESTION VARCHAR(200))
	BEGIN
		DECLARE	LI_EXISTS	SMALLINT;
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
		DECLARE	LI_COUNT		INTEGER;
		DECLARE	LN_KEY		NUMERIC(19,0);
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;
		
		INSERT INTO IEAI_HC_ACTWARNING_HIS
				(
					IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					DELUSER,
					DELTIME,
					DISPOSETYPE,
					SUGGESTION
				)
		SELECT	IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					AV_USER,
					CURRENT_TIMESTAMP,
					2,
					AV_SUGGESTION
		FROM		IEAI_HC_ACTWARNING
		WHERE		IID = AN_PK;
		
		DELETE	FROM	IEAI_HC_ACTWARNING WHERE IID = AN_PK;
		
		IF	RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
			COMMIT WORK;
		ELSE
			ROLLBACK WORK;
		END IF;
	END ;;
	DELIMITER ;
	
	DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_HC_CONFIRM' AND COLUMN_NAME = 'IFLAG') THEN 
			ALTER TABLE IEAI_HC_CONFIRM ADD IFLAG NUMERIC(19) DEFAULT 0; 
		END IF;  
	  END;;
	DELIMITER ;
	CALL UPPRDE();
	DELIMITER ;



DELIMITER ;;
DROP PROCEDURE IF EXISTS PROC_SAVE_WARNING_MESSAGE;;
CREATE PROCEDURE PROC_SAVE_WARNING_MESSAGE 
	(
		IN AI_WCODE SMALLINT,
		IN AV_WDATE VARCHAR(23),
		IN AV_WSOURCE VARCHAR(20),
		IN AV_PRGNAME VARCHAR(50),
		IN AN_ERRCODE NUMERIC(12,0),
		IN AV_HOST VARCHAR(40),
		IN AV_IP VARCHAR(15),
		IN AV_CVALUE VARCHAR(128),
		IN AV_THREADHOLD VARCHAR(128),
		IN AV_AMSG VARCHAR(128),
		IN AN_CPID NUMERIC(19,0),
		IN AV_WARN_CODE_LIST VARCHAR(50),
		OUT AN_KEY NUMERIC(19,0),
		OUT AV_RETURN VARCHAR(128)
	)
	LANGUAGE SQL
BEGIN
	DECLARE	LI_EXISTS	 SMALLINT;
	DECLARE	SQLCODE		INTEGER	DEFAULT	0;
	DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
	DECLARE	LI_COUNT		INTEGER;
	DECLARE	LN_KEY		NUMERIC(19,0);
	DECLARE	LI_LOOP		INTEGER;
	DECLARE	LI_LENGTH	INTEGER;
	DECLARE	LV_DATA		VARCHAR(200);
	DECLARE	LI_DATA		INTEGER;
	DECLARE	LI_SWITCH	SMALLINT;
	
	DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
	SET	RETSQLCODE = SQLCODE;
	
	SET		AN_KEY = -1;
	
	SELECT	IFNULL(MAX(IID), -1)
	INTO		LN_KEY
	FROM		IEAI_HC_ACTWARNING
	WHERE		IP = AV_IP
	AND		ERRCODE = AN_ERRCODE
	AND		PRGNAME = AV_PRGNAME
	AND		WCODE = AI_WCODE
	AND		THREADHOLD = AV_THREADHOLD
	AND		AMESSAGE = AV_AMSG;
	
	
	IF LN_KEY < 0 THEN
      INSERT INTO IEAI_HC_ACTWARNING_HIS
              (
                IID,
                CPID,
                WTYPE,
                WCODE,
                WDATE,
                LDATE,
                WSOURCE,
                PRGNAME,
                ERRCODE,
                HOSTNAME,
                IP,
                CHKVALUE,
                THREADHOLD,
                AMESSAGE,
                WCOUNT,
                TFLAG,
                WMESSAGE,
                SYSNAME,
                DELUSER,
                DELTIME,
                SUGGESTION,
                DISPOSETYPE
              )
          SELECT  IID,
                  CPID,
                WTYPE,
                WCODE,
                WDATE,
                LDATE,
                WSOURCE,
                PRGNAME,
                ERRCODE,
                HOSTNAME,
                IP,
                CHKVALUE,
                THREADHOLD,
                AMESSAGE,
                WCOUNT,
                TFLAG,
                WMESSAGE,
                SYSNAME,
                'Auto change Close',
                CURRENT_TIMESTAMP,
                '告警改变或恢复',
                '2'
          FROM   IEAI_HC_ACTWARNING W
          WHERE	 W.IP = AV_IP
            AND	 W.AMESSAGE = AV_AMSG
            AND  W.CPID = AN_CPID;
            
            
          DELETE	FROM	IEAI_HC_ACTWARNING 
          WHERE	 IP = AV_IP
            AND	 AMESSAGE = AV_AMSG
            AND  CPID = AN_CPID;
            
    END IF;
	
	
	IF	LN_KEY >= 0 THEN
		UPDATE	IEAI_HC_ACTWARNING
		SET		WCOUNT = WCOUNT + 1,
					LDATE = TIMESTAMP(AV_WDATE),
					CHKVALUE = AV_CVALUE
		WHERE		IID = LN_KEY;
	ELSE
		CALL PROC_GET_NEXT_PK('IEAI_HC_ACTWARNING', LN_KEY);
		INSERT INTO IEAI_HC_ACTWARNING
				(
					IID,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					CPID
				)
		SELECT	LN_KEY,
					AI_WCODE,
					TIMESTAMP(AV_WDATE),
					TIMESTAMP(AV_WDATE),
					AV_WSOURCE,
					AV_PRGNAME,
					AN_ERRCODE,
					AV_HOST,
					AV_IP,
					AV_CVALUE,
					AV_THREADHOLD,
					AV_AMSG,
					1,
					0,
					NULL,
					FUN_GET_APPS_NAME(AN_CPID, '运维服务自动化系统'),
					AN_CPID
		FROM		IDUAL;
		
		SELECT	WSWITCH
		INTO		LI_SWITCH
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
		
		IF LI_SWITCH = 1 THEN
			SET		LI_LOOP = 1;
			SET		LI_DATA = 0;
			
			WHILE	LI_DATA IS NOT NULL
			DO
				SET	LI_DATA = FUN_GET_STRING_NUMBER(AV_WARN_CODE_LIST, LI_LOOP);
				
				IF	AI_WCODE >= LI_DATA THEN
					SET	AN_KEY = LN_KEY;
					SET	LI_DATA = NULL;
				END IF;
				
				SET	LI_LOOP = LI_LOOP + 1;
			END WHILE;
		END IF;
	END IF;
	
	IF	RETSQLCODE = 0 THEN
		SET	AV_RETURN = 'SCUCCESS';
		COMMIT WORK;
	ELSE
		SET	AV_RETURN = 'FAILURE';
		ROLLBACK WORK;
	END IF;
END ;;
DELIMITER ;


-- PROC_SET_CHK_DATA_LAST_STATUS remove to v8.9.0 version change

-- V8.7.0 version 	
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HCIMME_CAMA_BATCH (HCBATCHID NUMERIC (19) NOT NULL,POINTID NUMERIC (19) NOT NULL,STARTPARAM VARCHAR (100) ,STARTUSER VARCHAR (30) ,STARTTIME NUMERIC (19) , CONSTRAINT PK_IEAI_HCIMME_CAMA_BATCH PRIMARY KEY (HCBATCHID, POINTID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;

-- V8.8.0 version pacth
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_GF_HC_XMDB_SYS (IID NUMERIC (19) ,IINSTID VARCHAR (255) ,IFIRSTSYS VARCHAR (255) ,ISECONDAPP VARCHAR (255) ,ICISTATUS VARCHAR (255) ,IUSEFOR VARCHAR (255) ,IAROLE VARCHAR (255) ,IBROLE VARCHAR (255) , CONSTRAINT PK_IEAI_GF_HC_XMDB_SYS PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();

-- v8.8.0 version pacth ALL_REPORT
DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_RESULT_ALL_REPORT;;
	CREATE  PROCEDURE PROC_GET_RESULT_ALL_REPORT(IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	BEGIN
	DECLARE	LN_CPID	NUMERIC(19,0);
	DECLARE	LN_SYSID	NUMERIC(19,0);
	DECLARE	LN_NUM	INTEGER;		
 	SELECT	IID
 	INTO		LN_SYSID
 	FROM		IEAI_PROJECT AA
 	WHERE		INAME = AV_SYS_NAME
	AND		PROTYPE = 7;
	
	SET		LN_NUM = AV_END - AV_START;
	
	IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN
		
		SELECT COUNT(*)
		INTO		RECORDCOUNT
		FROM(
				SELECT t1.CPID,t1.RSDID ,t1.MEID
				FROM HD_CHECK_RESULT_DATA_CACHE t1
				WHERE EXISTS
				(
					SELECT * 
					FROM
									IEAI_SYS_RELATION t2,
									IEAI_COMPUTER_LIST t3,
									IEAI_PROJECT t4
								WHERE
									t3.CPID = t2.COMPUTERID 
									AND t2.SYSTEMID = t4.IID 
									AND t4.INAME = AV_SYS_NAME 
									AND t4.IID = LN_SYSID 
									AND t4.PROTYPE = 7 
									AND t4.IPKGCONTENTID = 0
									AND t1.MEID = t2.COMPUTERID
				)
				AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
				AND t1.CIID = AV_CHECK_ITEM_ID
		)t6,HD_CHECK_STATUS_CACHE t5
		WHERE t6.RSDID = t5.RSDID
		AND t6.CPID = t5.CPID
		 ;
	
		
		INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
	  SELECT 						
			t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
			t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
			t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
		FROM (		
		SELECT
				t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
		FROM    HD_CHECK_RESULT_DATA_CACHE t1
		WHERE EXISTS
		(
				SELECT 1 
				FROM
								IEAI_SYS_RELATION t2,
								IEAI_COMPUTER_LIST t3,
								IEAI_PROJECT t4
							WHERE
								t3.CPID = t2.COMPUTERID 
								AND t2.SYSTEMID = t4.IID 
								AND t4.INAME = AV_SYS_NAME 
								AND t4.IID = LN_SYSID 
								AND t4.PROTYPE = 7 
								AND t4.IPKGCONTENTID = 0
								AND t1.MEID = t2.COMPUTERID
		)
		AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
		AND t1.CIID = AV_CHECK_ITEM_ID
		ORDER BY t1.RSDID DESC ,t1.CPTIME DESC ,t1.MEID DESC
		)t5,HD_CHECK_STATUS_CACHE t6
		WHERE    	t5.RSDID=t6.RSDID
		AND   	t5.CPID=t6.CPID
		ORDER BY t5.CPTIME DESC ,t5.MEID DESC
		LIMIT AV_START,LN_NUM;


	ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
		SELECT COUNT(1)
		INTO		RECORDCOUNT
		FROM(
				SELECT t1.CPID,t1.RSDID ,t1.MEID
				FROM HD_CHECK_RESULT_DATA_CACHE t1
				WHERE EXISTS
				(
					SELECT 1 
					FROM
									IEAI_SYS_RELATION t2,
									IEAI_COMPUTER_LIST t3,
									IEAI_PROJECT t4
								WHERE
									t3.CPID = t2.COMPUTERID 
									AND t2.SYSTEMID = t4.IID 
									AND t4.INAME = AV_SYS_NAME 
									AND t4.IID = LN_SYSID 
									AND t4.PROTYPE = 7 
									AND t4.IPKGCONTENTID = 0
									AND t1.MEID = t2.COMPUTERID
				)
				AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
				AND t1.CIID = AV_CHECK_ITEM_ID
				AND	  t1.CPTIME <= AV_TIME_END
			 )t6,HD_CHECK_STATUS_CACHE t5
		WHERE t6.RSDID = t5.RSDID
		AND t6.CPID = t5.CPID
		 ;
		
		INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
		SELECT 						
			t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
			t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
			t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
		FROM (		
		SELECT
				t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
		FROM    HD_CHECK_RESULT_DATA_CACHE t1
		WHERE EXISTS
		(
				SELECT 1 
				FROM
								IEAI_SYS_RELATION t2,
								IEAI_COMPUTER_LIST t3,
								IEAI_PROJECT t4
							WHERE
								t3.CPID = t2.COMPUTERID 
								AND t2.SYSTEMID = t4.IID 
								AND t4.INAME = AV_SYS_NAME 
								AND t4.IID = LN_SYSID 
								AND t4.PROTYPE = 7 
								AND t4.IPKGCONTENTID = 0
								AND t1.MEID = t2.COMPUTERID
		)
		AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
		AND t1.CIID = AV_CHECK_ITEM_ID
		AND	t1.CPTIME <= AV_TIME_END
		ORDER BY t1.RSDID DESC ,t1.CPTIME DESC ,t1.MEID DESC
		)t5,HD_CHECK_STATUS_CACHE t6
		WHERE    	t5.RSDID=t6.RSDID
		AND   	t5.CPID=t6.CPID
		ORDER BY t5.CPTIME DESC ,t5.MEID DESC
		LIMIT AV_START,LN_NUM
		;


	ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
	
		SELECT COUNT(1)
		INTO		RECORDCOUNT
		FROM(
				SELECT t1.CPID,t1.RSDID ,t1.MEID
				FROM HD_CHECK_RESULT_DATA_CACHE t1
				WHERE EXISTS
				(
					SELECT 1 
					FROM
									IEAI_SYS_RELATION t2,
									IEAI_COMPUTER_LIST t3,
									IEAI_PROJECT t4
								WHERE
									t3.CPID = t2.COMPUTERID 
									AND t2.SYSTEMID = t4.IID 
									AND t4.INAME = AV_SYS_NAME 
									AND t4.IID = LN_SYSID 
									AND t4.PROTYPE = 7 
									AND t4.IPKGCONTENTID = 0
									AND t1.MEID = t2.COMPUTERID
				)
				AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
				AND t1.CIID = AV_CHECK_ITEM_ID
				AND	  t1.CPTIME >= AV_TIME_START
				)t6,HD_CHECK_STATUS_CACHE t5
		WHERE t6.RSDID = t5.RSDID
		AND t6.CPID = t5.CPID
		 ;
		
		INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
		SELECT 						
			t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
			t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
			t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
		FROM (		
		SELECT
				t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
		FROM    HD_CHECK_RESULT_DATA_CACHE t1
		WHERE EXISTS
		(
				SELECT 1 
				FROM
								IEAI_SYS_RELATION t2,
								IEAI_COMPUTER_LIST t3,
								IEAI_PROJECT t4
							WHERE
								t3.CPID = t2.COMPUTERID 
								AND t2.SYSTEMID = t4.IID 
								AND t4.INAME = AV_SYS_NAME 
								AND t4.IID = LN_SYSID 
								AND t4.PROTYPE = 7 
								AND t4.IPKGCONTENTID = 0
								AND t1.MEID = t2.COMPUTERID
		)
		AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
		AND t1.CIID = AV_CHECK_ITEM_ID
		AND	t1.CPTIME >= AV_TIME_START
		ORDER BY t1.RSDID DESC ,t1.CPTIME DESC ,t1.MEID DESC
		)t5,HD_CHECK_STATUS_CACHE t6
		WHERE    	t5.RSDID=t6.RSDID
		AND   	t5.CPID=t6.CPID
		ORDER BY t5.CPTIME DESC ,t5.MEID DESC
		LIMIT AV_START,LN_NUM
		;
		
		
	ELSE
		SELECT COUNT(1)
		INTO		RECORDCOUNT
		FROM(
				SELECT t1.CPID,t1.RSDID ,t1.MEID
				FROM HD_CHECK_RESULT_DATA_CACHE t1
				WHERE EXISTS
				(
					SELECT 1 
					FROM
									IEAI_SYS_RELATION t2,
									IEAI_COMPUTER_LIST t3,
									IEAI_PROJECT t4
								WHERE
									t3.CPID = t2.COMPUTERID 
									AND t2.SYSTEMID = t4.IID 
									AND t4.INAME = AV_SYS_NAME 
									AND t4.IID = LN_SYSID 
									AND t4.PROTYPE = 7 
									AND t4.IPKGCONTENTID = 0
									AND t1.MEID = t2.COMPUTERID
				)
				AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
				AND t1.CIID = AV_CHECK_ITEM_ID
				AND	  (t1.CPTIME  BETWEEN AV_TIME_START AND AV_TIME_END)
        )t6,HD_CHECK_STATUS_CACHE t5
		WHERE t6.RSDID = t5.RSDID
		AND t6.CPID = t5.CPID
		 ;
		
		INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
		SELECT 						
			t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
			t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
			t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
		FROM (		
		SELECT
				t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
		FROM    HD_CHECK_RESULT_DATA_CACHE t1
		WHERE EXISTS
		(
				SELECT 1 
				FROM
								IEAI_SYS_RELATION t2,
								IEAI_COMPUTER_LIST t3,
								IEAI_PROJECT t4
							WHERE
								t3.CPID = t2.COMPUTERID 
								AND t2.SYSTEMID = t4.IID 
								AND t4.INAME = AV_SYS_NAME 
								AND t4.IID = LN_SYSID 
								AND t4.PROTYPE = 7 
								AND t4.IPKGCONTENTID = 0
								AND t1.MEID = t2.COMPUTERID
		)
		AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
		AND t1.CIID = AV_CHECK_ITEM_ID
		AND	  (t1.CPTIME  BETWEEN AV_TIME_START AND AV_TIME_END)
		ORDER BY t1.RSDID DESC ,t1.CPTIME DESC ,t1.MEID DESC
		)t5,HD_CHECK_STATUS_CACHE t6
		WHERE    	t5.RSDID=t6.RSDID
		AND   	t5.CPID=t6.CPID
		ORDER BY t5.CPTIME DESC ,t5.MEID DESC
		LIMIT AV_START,LN_NUM
		;
	END	IF;
	END;;
DELIMITER ;


-- v8.8.0 version pacth grid
DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_GET_RESULT_REPORT_GRID;;
	CREATE  PROCEDURE PROC_GET_RESULT_REPORT_GRID(IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	BEGIN
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
		DECLARE	LN_NUM	INTEGER;	

		SELECT		CPID 
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST SAS
		WHERE		IP = AV_IP;
			
		SELECT		IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT AA
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;
		
		SET		LN_NUM = AV_END - AV_START;
		
		IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN
		
			SELECT  COUNT(1)
			INTO		RECORDCOUNT
			FROM
			(
					SELECT t1.CPID,t1.RSDID ,t1.MEID
					FROM HD_CHECK_RESULT_DATA_CACHE t1
					WHERE	t1.MEID = LN_CPID
					AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
					AND t1.CIID = AV_CHECK_ITEM_ID
			)t6,HD_CHECK_STATUS_CACHE t5
			WHERE t6.RSDID = t5.RSDID
			AND t6.CPID = t5.CPID 
								;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT 						
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
			FROM (		
						SELECT
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
						FROM  HD_CHECK_RESULT_DATA_CACHE t1
						WHERE t1.MEID = LN_CPID
						AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
						AND t1.CIID = AV_CHECK_ITEM_ID
						ORDER BY t1.RSDID DESC ,t1.CPTIME DESC
						)t5,HD_CHECK_STATUS_CACHE t6
			WHERE    	t5.RSDID=t6.RSDID
			AND   	  t5.CPID=t6.CPID
			ORDER BY t5.CPTIME DESC
			LIMIT AV_START,LN_NUM
			;
			
			
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
		
			SELECT  COUNT(1)
			INTO		RECORDCOUNT
			FROM
			(
					SELECT t1.CPID,t1.RSDID ,t1.MEID
					FROM HD_CHECK_RESULT_DATA_CACHE t1
					WHERE	t1.MEID = LN_CPID
					AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
					AND t1.CIID = AV_CHECK_ITEM_ID
					AND	t1.CPTIME <= AV_TIME_END
					)t6,HD_CHECK_STATUS_CACHE t5
			WHERE t6.RSDID = t5.RSDID
			AND t6.CPID = t5.CPID 
								;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT 						
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
			FROM (		
							SELECT
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM  HD_CHECK_RESULT_DATA_CACHE t1
							WHERE t1.MEID = LN_CPID
							AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
							AND t1.CIID = AV_CHECK_ITEM_ID
							AND	t1.CPTIME <= AV_TIME_END
							ORDER BY t1.RSDID DESC ,t1.CPTIME DESC
						)t5,HD_CHECK_STATUS_CACHE t6
			WHERE    	t5.RSDID=t6.RSDID
			AND   	  t5.CPID=t6.CPID
			ORDER BY t5.CPTIME DESC
			LIMIT AV_START,LN_NUM
			;
			
			
		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
		
			SELECT  COUNT(1)
			INTO		RECORDCOUNT
			FROM
			(
					SELECT t1.CPID,t1.RSDID ,t1.MEID
					FROM HD_CHECK_RESULT_DATA_CACHE t1
					WHERE	t1.MEID = LN_CPID
					AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
					AND t1.CIID = AV_CHECK_ITEM_ID
					AND	t1.CPTIME >= AV_TIME_START
			)t6,HD_CHECK_STATUS_CACHE t5
			WHERE t6.RSDID = t5.RSDID
			AND t6.CPID = t5.CPID 
								;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT 						
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
			FROM (		
							SELECT
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM  HD_CHECK_RESULT_DATA_CACHE t1
							WHERE t1.MEID = LN_CPID
							AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
							AND t1.CIID = AV_CHECK_ITEM_ID
							AND	t1.CPTIME >= AV_TIME_START
							ORDER BY t1.RSDID DESC ,t1.CPTIME DESC
						)t5,HD_CHECK_STATUS_CACHE t6
			WHERE    	t5.RSDID=t6.RSDID
			AND   	  t5.CPID=t6.CPID
			ORDER BY t5.CPTIME DESC
			LIMIT AV_START,LN_NUM
			;
			
			
		ELSE
		
			SELECT  COUNT(1)
			INTO		RECORDCOUNT
			FROM
			(
					SELECT t1.CPID,t1.RSDID ,t1.MEID
					FROM HD_CHECK_RESULT_DATA_CACHE t1
					WHERE	t1.MEID = LN_CPID
					AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
					AND t1.CIID = AV_CHECK_ITEM_ID
					AND	(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
			)t6,HD_CHECK_STATUS_CACHE t5
			WHERE t6.RSDID = t5.RSDID
			AND t6.CPID = t5.CPID 
								;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT 						
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
			FROM (		
							SELECT
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM  HD_CHECK_RESULT_DATA_CACHE t1
							WHERE t1.MEID = LN_CPID
							AND ( t1.ISYSID = LN_SYSID OR t1.ISYSID = - 1 ) 
							AND t1.CIID = AV_CHECK_ITEM_ID
							AND	(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
							ORDER BY t1.RSDID DESC ,t1.CPTIME DESC
						)t5,HD_CHECK_STATUS_CACHE t6
			WHERE    	t5.RSDID=t6.RSDID
			AND   	  t5.CPID=t6.CPID
			ORDER BY t5.CPTIME DESC
			LIMIT AV_START,LN_NUM
			;
		END	IF;
	END;;
DELIMITER ;

-- v8.9.0 version pacth 
DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_SET_CHK_DATA_LAST_STATUS;;
	CREATE PROCEDURE PROC_SET_CHK_DATA_LAST_STATUS  (IN AV_IP VARCHAR(25))
			LANGUAGE SQL
		BEGIN
			DECLARE	LD_NOW	NUMERIC(19,0);
			DECLARE	LI_CNT	INTEGER;
			
			-- 1 get the number of NOW
			SET		LD_NOW = FUN_GET_DATE_NUMBER_NEW(CURRENT_TIMESTAMP, 8);
			
			-- 2 set check point status
			-- 2.1 Update CPSTATUS from The max of HD_CHECK_STATUS_LAST.CPSTATUS
			UPDATE	HD_CHECK_RESULT_DATA_LAST
			SET		CPSTATUS =	(
											SELECT	IFNULL(MAX(CAST(B1.I1 AS SIGNED INTEGER)), -1)
											FROM		HD_CHECK_STATUS_LAST B1
											WHERE	HD_CHECK_RESULT_DATA_LAST.IP = AV_IP 
											AND     B1.RSDID = HD_CHECK_RESULT_DATA_LAST.RSDID
										),
						CPTIME = LD_NOW
			WHERE		IP = AV_IP
			AND		CPSTATUS = -1
			AND     CISTATUS = 1;
			COMMIT WORK;
			-- 2.2 Update CPSTATUS from the max of TMP_EXCEPTION_CHECK_POINT.ALARMCODE which it could identity by CIID and CPID
			UPDATE	HD_CHECK_RESULT_DATA_LAST
			SET		CPSTATUS =	(
											SELECT	IFNULL(MAX(B1.ALARMCODE), -1)
											FROM		TMP_EXCEPTION_CHECK_POINT B1
											WHERE		B1.IP = AV_IP
											AND		B1.CHKITEMID = HD_CHECK_RESULT_DATA_LAST.CIID
											AND		B1.CHKPOINTID = HD_CHECK_RESULT_DATA_LAST.CPID
										),
						CPTIME = LD_NOW
			WHERE		IP = AV_IP
			AND		CPSTATUS = -1
			AND     CISTATUS = 1;
			COMMIT WORK;
			-- 2.3 Update CPSTATUS 
			UPDATE	HD_CHECK_RESULT_DATA_LAST
			SET		CPSTATUS =	FUN_GET_ALARM_CODE (CPTEXT),
						CPTIME = LD_NOW
			WHERE		IP = AV_IP
			AND		CPSTATUS = -1
			AND     CISTATUS=1;
			COMMIT WORK;
			-- 3 write data into CACHE table
			-- 3.1 write HD_CHECK_RESULT_DATA_CACHE
			INSERT	INTO	HD_CHECK_RESULT_DATA_CACHE
					(
						RSDID,
						ISYSID,
						ISYSNAME,
						CIID,
						CINAME,
						CIPARSRULE,
						CISTATUS,
						MEID,
						IP,
						PORT,
						CPID,
						CPTEXT,
						CPTIME,
						COMCHKID,
						CPSTATUS,
						CPTIMESTAMP
					)
			SELECT	RSDID,
						ISYSID,
						ISYSNAME,
						CIID,
						CINAME,
						CIPARSRULE,
						CISTATUS,
						MEID,
						IP,
						PORT,
						CPID,
						CPTEXT,
						CPTIME,
						COMCHKID,
						CPSTATUS,
						CPTIMESTAMP
			FROM		HD_CHECK_RESULT_DATA_LAST
			WHERE		IP = AV_IP
			AND		CISTATUS = 1
			AND		RSDID NOT IN (
						SELECT RSDID FROM (
							SELECT L.RSDID 
							FROM   HD_CHECK_RESULT_DATA_LAST L,
								   HD_CHECK_RESULT_DATA_CACHE CACHE
							WHERE  L.IP=AV_IP
							AND	   L.CISTATUS=1
							AND    L.RSDID=CACHE.RSDID
						) AA1
					);
			COMMIT WORK;
			-- 3.2 write HD_CHECK_STATUS_CACHE
			INSERT	INTO	HD_CHECK_STATUS_CACHE
					(
						CRID,
						CPID,
						RSDID,
						I1,
						I2,
						I3,
						I4,
						I5,
						I6,
						I7,
						I8,
						I9,
						I10,
						I11,
						I12,
						I13,
						I14,
						I15,
						I16,
						I17,
						I18,
						I19,
						I20
					)
			SELECT	B.CRID,
						B.CPID,
						B.RSDID,
						B.I1,
						B.I2,
						B.I3,
						B.I4,
						B.I5,
						B.I6,
						B.I7,
						B.I8,
						B.I9,
						B.I10,
						B.I11,
						B.I12,
						B.I13,
						B.I14,
						B.I15,
						B.I16,
						B.I17,
						B.I18,
						B.I19,
						B.I20
			FROM		HD_CHECK_RESULT_DATA_LAST A,
						HD_CHECK_STATUS_LAST B
			WHERE		A.IP = AV_IP
			AND		A.CISTATUS = 1
			AND		B.RSDID = A.RSDID
			AND		CRID NOT IN (
						SELECT CRID FROM (
							SELECT B11.CRID 
							FROM   HD_CHECK_RESULT_DATA_LAST L1,
								   HD_CHECK_STATUS_LAST B11,
								   HD_CHECK_STATUS_CACHE CACHE1
							WHERE  L1.IP=AV_IP
							AND	   L1.CISTATUS=1
							AND    L1.RSDID=B11.RSDID
							AND    B11.CRID=CACHE1.CRID
						) AA2
					);
			COMMIT WORK;
			-- 4 try to auto close warning event
			-- 4.1 to calculate the flag of auto closing
			SELECT	COUNT(DID)
			INTO		LI_CNT
			FROM		IEAI_SYS_DICTIONARY
			WHERE		DID = 100
			AND		DNAME = 'AutoCloseWarningEvent'
			AND		DINTVALUE = '1';
			COMMIT WORK;
			IF LI_CNT > 0 THEN
				
				-- 4.2 get auto to close warning event list
				INSERT INTO TMP_AUTO_CLOSE_WARNING_EVENT
						(
							RID
						) 
				
				SELECT DISTINCT  W.IID
				  FROM IEAI_HC_ACTWARNING W
				 WHERE NOT EXISTS
				 (SELECT 1
						  FROM HD_CHECK_RESULT_DATA_LAST D LEFT JOIN HD_CHECK_STATUS_LAST S
						   ON   D.RSDID = S.RSDID
						   AND  D.CPID  = S.CPID
						 WHERE  W.CPID  =  D.CPID
						   AND  W.IP    = D.IP
						   AND  W.IP    = AV_IP
						   AND  (S.I1 > 2 OR (S.I1 IS NULL  AND D.CPSTATUS>2) )
						)
					AND     W.IP = AV_IP;
				
				
				COMMIT WORK;
				-- 4.3 write warning history
				INSERT INTO IEAI_HC_ACTWARNING_HIS
						(
							IID,
							CPID,
							WTYPE,
							WCODE,
							WDATE,
							LDATE,
							WSOURCE,
							PRGNAME,
							ERRCODE,
							HOSTNAME,
							IP,
							CHKVALUE,
							THREADHOLD,
							AMESSAGE,
							WCOUNT,
							TFLAG,
							WMESSAGE,
							SYSNAME,
							DELUSER,
							DELTIME
						)
				SELECT	IID,
							CPID,
							WTYPE,
							WCODE,
							WDATE,
							LDATE,
							WSOURCE,
							PRGNAME,
							ERRCODE,
							HOSTNAME,
							IP,
							CHKVALUE,
							THREADHOLD,
							AMESSAGE,
							WCOUNT,
							TFLAG,
							WMESSAGE,
							SYSNAME,
							'Auto Close',
							CURRENT_TIMESTAMP
				FROM		IEAI_HC_ACTWARNING W,
							TMP_AUTO_CLOSE_WARNING_EVENT A
				WHERE		W.IID = A.RID;
				COMMIT WORK;
				-- 4.4 clean warning information
				DELETE
				FROM		IEAI_HC_ACTWARNING 
				WHERE		EXISTS (SELECT 1 FROM TMP_AUTO_CLOSE_WARNING_EVENT A WHERE A.RID = IID);
				COMMIT WORK;
				DELETE FROM TMP_AUTO_CLOSE_WARNING_EVENT;
				COMMIT WORK;
			END IF;
		END ;;
	DELIMITER ;
	
	
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HC_WARN_SEQ (ICURRENTDATE VARCHAR (8) ,ITYPE VARCHAR (10) ,ISEQNO NUMERIC (19) , CONSTRAINT PK_IEAI_HC_WARN_SEQ PRIMARY KEY (ICURRENTDATE,ITYPE));
	  END;;
DELIMITER ;
CALL UPPRDE();

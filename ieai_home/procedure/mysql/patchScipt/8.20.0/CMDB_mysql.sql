	-- 4.7.17 version does not have patches for cmdb  
	-- 4.7.18 version does not have patches for cmdb
	-- 4.7.19 version does not have patches for cmdb
	-- 4.7.20 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_WARNINFO' AND COLUMN_NAME = 'ISTATE') THEN
			ALTER TABLE IEAI_CMDB_WARNINFO ADD ISTATE NUMERIC(1);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ISOURCECODE') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ISOURCECODE VARCHAR(255);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ITARGETCODE') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ITARGETCODE VARCHAR(255);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_INFO_TEMP (IID NUMERIC(19) NOT NULL,ICMDBINFOID NUMERIC(19),IWARNINFOID NUMERIC(19),ITYPEID NUMERIC(19),ICONTENT LONGTEXT,CONSTRAINT PK_IEAI_CMDB_INFO_TEMP PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.21 version does not have patches for cmdb
	-- 4.7.22 version does not have patches for cmdb
	-- 4.7.23 version does not have patches for cmdb
	-- 4.7.24 version does not have patches for cmdb
	-- 4.7.25 version does not have patches for cmdb
	-- 4.7.26 version does not have patches for cmdb
	-- 4.7.27 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO_RELATION' AND COLUMN_NAME = 'IRELATIONID') THEN
			ALTER TABLE IEAI_CMDB_INFO_RELATION ADD IRELATIONID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO_RELATION' AND COLUMN_NAME = 'ISUPPERID') THEN
			ALTER TABLE IEAI_CMDB_INFO_RELATION ADD ISUPPERID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ISOURCEID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ISOURCEID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ITARGETID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ITARGETID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'IPARENTID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD IPARENTID NUMERIC(19) DEFAULT 0;
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.28 version does not have patches for cmdb
	
	-- 4.7.29 version cmdb patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN

			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_DATARELATION' AND COLUMN_NAME = 'IGIVECMDBTYPE') THEN
				ALTER TABLE IEAI_CMDB_DATARELATION ADD IGIVECMDBTYPE NUMERIC(19);
			END IF;
			
			CREATE TABLE IF NOT EXISTS IEAI_CMDB_TOPO_INFO (IID NUMERIC(19) NOT NULL,ICMDBINFOID NUMERIC(19),ITYPEID NUMERIC(19),IPARENTCMDBINFOID NUMERIC(19),IRELATIONID NUMERIC(19),ITOPOXML LONGTEXT,IUPDATETIME NUMERIC(19),CONSTRAINT PK_IEAI_CMDB_TOPO_INFO PRIMARY KEY (IID));

		END;;
		DELIMITER ;
		CALL UPPRDE();
	-- 4.8.2 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_cmdb_type' AND COLUMN_NAME = 'iipcode') THEN
			ALTER TABLE ieai_cmdb_type add iipcode VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_cmdb_type' AND COLUMN_NAME = 'iport') THEN
			ALTER TABLE ieai_cmdb_type add iport VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_cmdb_type' AND COLUMN_NAME = 'iscriptid') THEN
			ALTER TABLE ieai_cmdb_type add iscriptid VARCHAR(255);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_BH_MABUALENTRY (IID NUMERIC(19) NOT NULL, INAME VARCHAR(255),IIP VARCHAR(255) , ISERIALNUMBER VARCHAR(255) , ICFOCODE VARCHAR(255) , IASSETCODE VARCHAR(255) ,IBUYDATA VARCHAR(255) ,IMICROVER VARCHAR(255) ,ICONFIG VARCHAR(255) ,IZONETYPE VARCHAR(255) ,ISUPPLY VARCHAR(255) ,IMAINTAIN VARCHAR(255) ,IUSEPORT VARCHAR(255),IACTIVATEPORT VARCHAR(255) ,IOTN VARCHAR(255) ,IOTHERIP VARCHAR(255) ,IRACKSITE VARCHAR(255) ,IROOMSITE VARCHAR(255) ,IOSTYPE VARCHAR(255) ,IOSVERSION VARCHAR(255) ,IMIDDLETYPE VARCHAR(255) ,IMIDDLEVERSION VARCHAR(255) ,IDBTYPE VARCHAR(255) ,IDBVERSION VARCHAR(255) ,INOTE VARCHAR(255) ,CONSTRAINT PK_IEAI_CMDB_BH_MABUALENTRY PRIMARY KEY (IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_BH_RELATION (IID NUMERIC(19) NOT NULL, IMABUALENTRYID NUMERIC(19), ITYPEIDS VARCHAR(1000),IPROTOCOLTYPE VARCHAR(255),IOOBIP VARCHAR(255),IJUMPIP VARCHAR(255),IGROUPNAME VARCHAR(255),IUSERNAME VARCHAR(255),IPASSWORD VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_BH_RELATION PRIMARY KEY (IID));
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.8.3 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_ATTRIBUTE' AND COLUMN_NAME = 'IISDROPLIST') THEN
			ALTER TABLE IEAI_CMDB_TYPE_ATTRIBUTE ADD IISDROPLIST NUMERIC(1) default 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TOPO_INFO' AND COLUMN_NAME = 'BCSSTYLEDATA') THEN
			ALTER TABLE IEAI_CMDB_TOPO_INFO ADD BCSSTYLEDATA LONGTEXT;
		END IF;
			
	END;;
	DELIMITER ;
	CALL UPPRDE();
	-- 4.8.5 version cmdb patch is as follows
    DELIMITER ;;
          DROP PROCEDURE IF EXISTS UPPRDE;;
          CREATE PROCEDURE UPPRDE()
          BEGIN

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO' AND COLUMN_NAME = 'IMARK') THEN
                ALTER TABLE IEAI_CMDB_INFO ADD IMARK VARCHAR(20) ;
            END IF;
          END;;
    DELIMITER ;
    CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		CREATE TABLE  IF NOT EXISTS IEAI_CMDB_TYPE_WARNINFO (IID NUMERIC(19) NOT NULL, ITYPID NUMERIC(19,0),ICMDBINFOID NUMERIC(19,0), ISTRJSON LONGTEXT,ITYPENAME VARCHAR(255), ITYPEPROPNAME VARCHAR(255),IPOINTNAME VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_TYPE_WARN PRIMARY KEY (IID));
	END;;
	DELIMITER ;
	CALL UPPRDE();

	-- 8.18.0 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_cmdb_type' AND COLUMN_NAME = 'SYNCFLAG') THEN
			ALTER TABLE ieai_cmdb_type add SYNCFLAG NUMERIC(1) DEFAULT 1;
		END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();

-- 结束
	-- 4.7.17 version does not have patches for cmdb  
	-- 4.7.18 version does not have patches for cmdb
	-- 4.7.19 version does not have patches for cmdb
	-- 4.7.20 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_WARNINFO' AND COLUMN_NAME = 'ISTATE') THEN
			ALTER TABLE IEAI_CMDB_WARNINFO ADD ISTATE NUMERIC(1);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ISOURCECODE') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ISOURCECODE VARCHAR(255);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ITARGETCODE') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ITARGETCODE VARCHAR(255);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_INFO_TEMP (IID NUMERIC(19) NOT NULL,ICMDBINFOID NUMERIC(19),IWARNINFOID NUMERIC(19),ITYPEID NUMERIC(19),ICONTENT LONGTEXT,CONSTRAINT PK_IEAI_CMDB_INFO_TEMP PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.21 version does not have patches for cmdb
	-- 4.7.22 version does not have patches for cmdb
	-- 4.7.23 version does not have patches for cmdb
	-- 4.7.24 version does not have patches for cmdb
	-- 4.7.25 version does not have patches for cmdb
	-- 4.7.26 version does not have patches for cmdb
	-- 4.7.27 version cmdb patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO_RELATION' AND COLUMN_NAME = 'IRELATIONID') THEN
			ALTER TABLE IEAI_CMDB_INFO_RELATION ADD IRELATIONID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO_RELATION' AND COLUMN_NAME = 'ISUPPERID') THEN
			ALTER TABLE IEAI_CMDB_INFO_RELATION ADD ISUPPERID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ISOURCEID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ISOURCEID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'ITARGETID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD ITARGETID NUMERIC(19) DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_RELATION' AND COLUMN_NAME = 'IPARENTID') THEN
			ALTER TABLE IEAI_CMDB_TYPE_RELATION ADD IPARENTID NUMERIC(19) DEFAULT 0;
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
package com.ideal.ieai;

import cn.com.antcloud.api.antcloud.rest.AntCloudRestClient;
import cn.com.antcloud.api.antcloud.rest.AntCloudRestClientRequest;
import cn.com.antcloud.api.antcloud.rest.AntCloudRestClientResponse;
import cn.com.antcloud.repack.com.alibaba.fastjson.JSON;
import cn.com.antcloud.repack.rest.RestHttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class StartSoFaTask {

    private final static Logger LOGGER = LoggerFactory.getLogger("StartSoFaTask");

    public static void main(String[] args) {

        String endpoint = args[0];
        String ak = args[1];
        String sk = args[2];
        int job_id = Integer.parseInt(args[3]);
        String inst_id = args[4];

        AntCloudRestClientResponse startScheduleResponse = betaTriggerJobTask(ScheduleRequestClient.buildProducer(endpoint, ak, sk), job_id, "", inst_id);
        System.out.println(JSON.toJSONString(JSON.parseObject(startScheduleResponse.getData()), true));
        System.out.println(startScheduleResponse.isSuccess());

        LOGGER.info("Start Task: {}", startScheduleResponse.getData());

    }

    /**
     *
     * 任务 触发
     */
    public static AntCloudRestClientResponse betaTriggerJobTask(AntCloudRestClient client, int job_id, String regionName, String inst_id) {
        AntCloudRestClientRequest request = new AntCloudRestClientRequest();
        request.setUrlPath("/sofa/ts/job/trigger");
        request.setApiVersion("1.0");
        request.setHttpMethod(RestHttpMethod.PUT);
        request.setRegionName("");
        request.setProductInstanceId("MiddleWareCluster-TS");
        Map<String, Object> param = new HashMap<String,Object>();
        param.put("instance_id", inst_id);
        param.put("job_id", job_id);
        param.put("zone", "docker");

        request.setPostBody(JSON.toJSONString(param));
        AntCloudRestClientResponse response = client.execute(request);
        LOGGER.info("Start Task Response: {}", response.getData());
        return response;
    }

}

/*
 * Created on 2005-6-17
 */
package test.com.ideal.ieai.server.repository;

import java.util.ArrayList; 
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import junit.textui.TestRunner;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.RoleInfo;
import com.ideal.ieai.commons.UserBasicInfo;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.commons.task.QueryTaskCriteria;
import com.ideal.ieai.commons.task.TaskDelegationRecord;
import com.ideal.ieai.commons.task.TaskItem;
import com.ideal.ieai.commons.task.TaskOperationInCore;
import com.ideal.ieai.commons.task.TaskProperty;
import com.ideal.ieai.commons.task.WarningSetting;
import com.ideal.ieai.core.data.DataTypeNames;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.task.TaskData;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.task.UserTaskRepository;
import com.ideal.ieai.server.repository.user.IUserManager;
import com.ideal.ieai.server.repository.user.UserManager;

/**
 * TODO this unit test is unsafe,it will change the datas in db
 * 
 * <AUTHOR> Shi
 */
public class UserTaskRepositoryTestCase extends RepositoryTestCase
{
    private UserTaskRepository _repository;

    private TaskData           _source;

    private IUserManager       _userManager;
    Date                       current = new Date();

    /*
     * @see RepositoryTestCase#setUp()
     */
    protected void setUp () throws Exception
    {
        super.setUp();
        _userManager = UserManager.getInstance();
        _repository = UserTaskRepository.getInstance();

        _source = new TaskData();
        _source.setAcquired(true);
        _source.setAcquireTime(current);
        _source.setActivityName("abcActivity");
        _source.setAllowSkip(true);

        // set assigneeGroups
        List assigneeGroups = new ArrayList();
        RoleInfo role1 = new RoleInfo();
        role1.setRoleName("role1" + System.currentTimeMillis());
        RoleInfo role2 = new RoleInfo();
        role2.setRoleName("role2" + System.currentTimeMillis());
        _userManager.createRole(role1);
        _userManager.createRole(role2);
        assigneeGroups.add(role1);
        assigneeGroups.add(role2);
        _source.setAssigneeGroups(assigneeGroups);

        // set assignee
        UserBasicInfo user1 = new UserBasicInfo();
        user1.setFullName("user1" + System.currentTimeMillis());
        UserBasicInfo user2 = new UserBasicInfo();
        user2.setFullName("user2" + System.currentTimeMillis());
        _userManager.createUser(user1, null);
        _userManager.createUser(user2, null);
        List assignees = new ArrayList();
        assignees.add(user1);
        assignees.add(user2);
        _source.setAssignees(assignees);

        _source.setDeadLine(current);
        _source.setDelegateTime(current);
        _source.setDelegateToAssigneeOnly(true);
        _source.setDelegateUser(user1);
        _source.setDelegationRecord(new TaskDelegationRecord(user2, current, "abcState", "currentState", current, 3333));
        _source.setDesc("desccccccccccccccccccccccccccccccccccccccccc");
        _source.setFlowId(new Long(1111111));
        _source.setForwardTime(current);
        _source.setForwardToAssigneeOnly(true);
        _source.setForwardUser(user1);
        _source.setId(current.getTime());
        _source.setLastAlertTime(current);
        _source.setLastOp("operateName");
        _source.setLastOpTime(current);
        _source.setOldForwardTime(current);
        _source.setOldForwardUser(user1);

        // set operations
        Map operations = new HashMap();
        TaskOperationInCore op1 = new TaskOperationInCore(1, "operateName", "desc1", 2, "what?");
        TaskOperationInCore op2 = new TaskOperationInCore(2, "operateName2", "desc2", 3, "It's a pig");
        operations.put(new Long(op1.getId()), op1);
        operations.put(new Long(op2.getId()), op2);
        _source.setOperations(operations);

        _source.setPriority(2);
        _source.setStartTime(current);
        _source.setState("state");
        _source.setTaskEnd(false);
        _source.setTaskName("greate name");
        _source.setTaskOwner(user1);
        // _source.setTimeLimit(new TimeLimitInCore(1, "abc", "123", "222", "333", "444"));
        _source.setUsingFlowParticipant(true);

        // warningSetting
        WarningSetting setting = new WarningSetting(3, "aaa", "abcd");
        setting.setTimeOutEmailFlag(2);
        setting.setWarningEmailFlag(3);
        // _source.setWarningSetting(setting);

        // task items
        TaskItem item1 = new TaskItem("the TaskItem");
        TaskItem item2 = new TaskItem("the task Item 2");
        List taskItems = new LinkedList();
        taskItems.add(item1);
        taskItems.add(item2);
        _source.setItems(taskItems);

        // task properties
        TaskProperty property1 = new TaskProperty();
        property1.setDescription("desctiption dddd");
        property1.setName("paramName");
        property1.setType(DataTypeNames.TYPE_INT);
        TaskProperty property2 = new TaskProperty();
        BeanUtils.copyProperties(property1, property2);
        List properties = new LinkedList();
        properties.add(property1);
        properties.add(property2);
        _source.setProperties(properties);
    }

    /*
     * @see RepositoryTestCase#tearDown()
     */
    protected void tearDown () throws Exception
    {
        _source = null;

        super.tearDown();

    }

    /**
     * Constructor for UserTaskRepositoryTestCase.
     * 
     * @param arg0
     */
    public UserTaskRepositoryTestCase(String arg0)
    {
        super(arg0);
    }

    public void testSaveNewTask () throws RepositoryException
    {
        _repository.saveNewTask(_source);
        TaskData stored = _repository.getTask(_source.getId(), Constants.IEAI_IEAI_BASIC);

        _source.setDelegationRecord(null);

        assertTrue(_source.getAssigneeGroups().containsAll(stored.getAssigneeGroups()));
        assertTrue(_source.getAssignees().containsAll(stored.getAssignees()));
        assertTrue(_source.getOperations().values().containsAll(stored.getOperations().values()));
        assertTrue(stored.getOperations().values().containsAll(_source.getOperations().values()));
        assertTrue(stored.getItems().size() == 2);
        assertTrue(stored.getProperties().size() == 2);
        assertEquals(_source, stored);
    }

    public void testUpdateTask () throws RepositoryException
    {
        _source.setId(_source.getId() + 2);
        _repository.saveNewTask(_source);

        TaskDelegationRecord newRecord = new TaskDelegationRecord(new UserInfo(new Long(3335), "dfdfd"), new Date(), "oldState", "curState", new Date(), 3);
        _source.setDelegationRecord(newRecord);
        _repository.updateTask(_source, Constants.IEAI_IEAI_BASIC);

        TaskData stored = _repository.getTask(_source.getId(), Constants.IEAI_IEAI_BASIC);
        assertEquals(_source, stored);

    }

    public void testGetFlowTasksOnlyWithAssigneeWithoutFinishedSkiped () throws ServerException, RepositoryException
    {
        _repository.getUserAccessibleUnfinishedTaskIdsInFlow(new Long(1), new Long(3),Constants.IEAI_IEAI_BASIC);
    }

    public void testGetOnlyTaskOwnerWithoutFinishedSkipped () throws ServerException, RepositoryException
    {
        _repository.getOnlyTaskOwnerWithoutFinishedSkipped(new Long(1), Constants.IEAI_IEAI_BASIC);
    }

    public void testQueryTask () throws ServerException, RepositoryException
    {
        /*
         * this test case can only make sure to go over every road in the query method but not
         * promise to make sure that the logic is right
         */
        _source.setId(_source.getId() + 33);
        _repository.saveNewTask(_source);
        QueryTaskCriteria criteria = new QueryTaskCriteria();
        criteria.setDeadline1(_source.getDeadLine());
        criteria.setDeadline2(_source.getDeadLine());
        criteria.setFinishTime1(_source.getLastOpTime());
        criteria.setFinishTime2(_source.getLastOpTime());
        criteria.setFlowId(_source.getFlowId());
        criteria.setFlowInstanceName("flowInstanceName");
        criteria.setProjName("projName");
        criteria.setStartTime1(_source.getStartTime());
        criteria.setStartTime2(_source.getStartTime());
        criteria.setStatus(QueryTaskCriteria.STATE_ACQUIRED | QueryTaskCriteria.STATE_FINISHED | QueryTaskCriteria.STATE_OVERDUED);
        criteria.setTaskId(_source.getId());
        criteria.setTaskName(_source.getTaskName());
        criteria.setTaskOwner(_source.getTaskOwner().getFullName());
        _repository.queryTask(criteria, Constants.IEAI_IEAI_BASIC);

    }

    public void testGetTasksOfFlow () throws RepositoryException
    {
        _source.setId(_source.getId() + 10);
        _repository.saveNewTask(_source);
        List result = _repository.getTasksOfFlow(_source.getFlowId(), Constants.IEAI_IEAI_BASIC);
        assertFalse(result.isEmpty());
        assertTrue(result.get(0) instanceof TaskData);
    }

    public static void main ( String[] args )
    {
        TestRunner.run(UserTaskRepositoryTestCase.class);
    }

}

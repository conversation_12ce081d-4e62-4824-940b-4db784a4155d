package com.ideal.ieai.server.repository.topo.acttimecalculate;

import com.ideal.ieai.server.repository.topo.sequence.GanttBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * ????ActTimeCalculateManager????????????????
 */
@RunWith(MockitoJUnitRunner.class)
public class ActTimeCalculateManagerTest {

    private ActTimeCalculateManager manager;
    
    @Before
    public void setUp() {
        manager = new ActTimeCalculateManager();
    }

    /**
     * ????calculateMaxParentEndTime????
     */
    @Test
    public void testCalculateMaxParentEndTime() throws Exception {
        // ???????????
        List<String> listParent = Arrays.asList("parent1", "parent2", "parent3");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        // ?????????
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("1500");
        mapAct.put("parent2", parent2);
        
        GanttBean parent3 = new GanttBean();
        parent3.setId("parent3");
        parent3.setExpEndTime("800");
        mapAct.put("parent3", parent3);
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "calculateMaxParentEndTime", List.class, Map.class);
        method.setAccessible(true);
        
        long result = (Long) method.invoke(manager, listParent, mapAct);
        
        // ?????????????????????
        assertEquals(1500L, result);
    }

    /**
     * ????validateAndFixStartTime????
     */
    @Test
    public void testValidateAndFixStartTime() throws Exception {
        // ???????????
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");  // ??????С????????????
        ganttBean.setExpEndTime("800");    // ???????300
        
        List<String> listParent = Arrays.asList("parent1");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");  // ??????????????????????
        mapAct.put("parent1", parent1);
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // ??????????????????????????
        assertEquals("1000", ganttBean.getExpStartTime());
        // ????????????????????????????????
        assertEquals("1300", ganttBean.getExpEndTime());
    }

    /**
     * ????????????????????????????????????е???
     */
    @Test
    public void testValidateAndFixStartTimeNoAdjustmentNeeded() throws Exception {
        // ???????????
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("1200");  // ???????????????????
        ganttBean.setExpEndTime("1500");
        
        List<String> listParent = Arrays.asList("parent1");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("1000");
        mapAct.put("parent1", parent1);
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // ????????б?????
        assertEquals("1200", ganttBean.getExpStartTime());
        assertEquals("1500", ganttBean.getExpEndTime());
    }

    /**
     * ???????????????
     */
    @Test
    public void testValidateAndFixStartTimeMultipleParents() throws Exception {
        // ???????????
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = Arrays.asList("parent1", "parent2", "parent3");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime("900");
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("1200");  // ??????????
        mapAct.put("parent2", parent2);
        
        GanttBean parent3 = new GanttBean();
        parent3.setId("parent3");
        parent3.setExpEndTime("700");
        mapAct.put("parent3", parent3);
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // ????????????????????????????
        assertEquals("1200", ganttBean.getExpStartTime());
        assertEquals("1500", ganttBean.getExpEndTime());
    }

    /**
     * ??????????б?????
     */
    @Test
    public void testValidateAndFixStartTimeEmptyParents() throws Exception {
        // ???????????
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = new ArrayList<>();
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // ????????б?????
        assertEquals("500", ganttBean.getExpStartTime());
        assertEquals("800", ganttBean.getExpEndTime());
    }

    /**
     * ???????????????null?????????????
     */
    @Test
    public void testValidateAndFixStartTimeNullParentEndTime() throws Exception {
        // ???????????
        GanttBean ganttBean = new GanttBean();
        ganttBean.setId("child1");
        ganttBean.setExpStartTime("500");
        ganttBean.setExpEndTime("800");
        
        List<String> listParent = Arrays.asList("parent1", "parent2");
        Map<String, GanttBean> mapAct = new HashMap<>();
        
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpEndTime(null);  // null???????
        mapAct.put("parent1", parent1);
        
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpEndTime("");    // ??????????????
        mapAct.put("parent2", parent2);
        
        // ??÷????????з???
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);
        
        method.invoke(manager, ganttBean, listParent, mapAct);
        
        // 验证时间没有被调整
        assertEquals("500", ganttBean.getExpStartTime());
        assertEquals("800", ganttBean.getExpEndTime());
    }

    /**
     * 测试跨系统依赖的时间计算
     */
    @Test
    public void testCrossSystemDependencyTimeCalculation() throws Exception {
        // 准备测试数据 - 模拟跨系统依赖场景
        GanttBean childNode = new GanttBean();
        childNode.setId("child_system_A");
        childNode.setExpStartTime("500");  // 开始时间小于跨系统父节点结束时间
        childNode.setExpEndTime("800");
        childNode.setIsProOut("0");  // 非跨系统节点

        List<String> listParent = Arrays.asList("parent_system_B", "parent_system_C");
        Map<String, GanttBean> mapAct = new HashMap<>();

        // 跨系统父节点B
        GanttBean parentB = new GanttBean();
        parentB.setId("parent_system_B");
        parentB.setExpStartTime("100");
        parentB.setExpEndTime("1000");  // 结束时间大于子节点开始时间
        parentB.setIsProOut("1");  // 跨系统节点
        mapAct.put("parent_system_B", parentB);

        // 跨系统父节点C
        GanttBean parentC = new GanttBean();
        parentC.setId("parent_system_C");
        parentC.setExpStartTime("200");
        parentC.setExpEndTime("1200");  // 最大的结束时间
        parentC.setIsProOut("1");  // 跨系统节点
        mapAct.put("parent_system_C", parentC);

        // 使用反射调用私有方法
        java.lang.reflect.Method method = ActTimeCalculateManager.class.getDeclaredMethod(
            "validateAndFixStartTime", GanttBean.class, List.class, Map.class);
        method.setAccessible(true);

        method.invoke(manager, childNode, listParent, mapAct);

        // 验证子节点的开始时间被调整为最大的父节点结束时间
        assertEquals("1200", childNode.getExpStartTime());
        // 验证结束时间也相应调整，保持持续时间不变 (原持续时间300)
        assertEquals("1500", childNode.getExpEndTime());
    }

    /**
     * 测试增强版isExsitAct方法对跨系统节点的检查
     */
    @Test
    public void testEnhancedIsExsitActForCrossSystemNodes() throws Exception {
        ActTimeCalculateManager manager = new ActTimeCalculateManager();

        List<String> listParent = Arrays.asList("parent1", "parent2", "parent3");
        Map<String, GanttBean> mapAct = new HashMap<>();

        // 正常父节点
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpStartTime("100");
        parent1.setExpEndTime("500");
        parent1.setIsProOut("0");
        mapAct.put("parent1", parent1);

        // 跨系统父节点，有有效时间
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpStartTime("200");
        parent2.setExpEndTime("600");
        parent2.setIsProOut("1");  // 跨系统节点
        mapAct.put("parent2", parent2);

        // 跨系统父节点，但没有有效的开始时间
        GanttBean parent3 = new GanttBean();
        parent3.setId("parent3");
        parent3.setExpStartTime("");  // 无效的开始时间
        parent3.setExpEndTime("700");
        parent3.setIsProOut("1");  // 跨系统节点
        mapAct.put("parent3", parent3);

        // 调用isExsitAct方法
        boolean result = manager.isExsitAct(listParent, mapAct);

        // 验证结果应该为false，因为parent3没有有效的开始时间
        assertFalse("Should return false when cross-system parent has invalid start time", result);
    }

    /**
     * 测试所有跨系统父节点都有有效时间的情况
     */
    @Test
    public void testEnhancedIsExsitActAllValidCrossSystemNodes() throws Exception {
        ActTimeCalculateManager manager = new ActTimeCalculateManager();

        List<String> listParent = Arrays.asList("parent1", "parent2");
        Map<String, GanttBean> mapAct = new HashMap<>();

        // 正常父节点
        GanttBean parent1 = new GanttBean();
        parent1.setId("parent1");
        parent1.setExpStartTime("100");
        parent1.setExpEndTime("500");
        parent1.setIsProOut("0");
        mapAct.put("parent1", parent1);

        // 跨系统父节点，有有效时间
        GanttBean parent2 = new GanttBean();
        parent2.setId("parent2");
        parent2.setExpStartTime("200");
        parent2.setExpEndTime("600");
        parent2.setIsProOut("1");  // 跨系统节点
        mapAct.put("parent2", parent2);

        // 调用isExsitAct方法
        boolean result = manager.isExsitAct(listParent, mapAct);

        // 验证结果应该为true，因为所有父节点都有有效时间
        assertTrue("Should return true when all parents have valid times", result);
    }
}

package com.ideal.ieai.server.repository.hd.actExcelToRun;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ebanksmdb.SmdbAcceptServiceNew;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.core.ExcelModelExecManager;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.commons.mail.SendEmailForApmManager;
import com.ideal.ieai.server.repository.commons.mail.SendEmailForAzManager;
import com.ideal.ieai.server.repository.commons.mail.SendEmailManager;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.hd.infoCollection.InfoCollectionManager;
import com.ideal.ieai.server.repository.hd.switchMonitor.SwitchMonitorManage;
import com.ideal.ieai.server.repository.sus.testcenter.SusTestFlowInfoManager;
import org.apache.log4j.Logger;

import java.net.UnknownHostException;
import java.rmi.ServerException;
import java.sql.*;
import java.util.*;

public class ActExcelFromRunToFinishManager
{
    private static ActExcelFromRunToFinishManager intance = new ActExcelFromRunToFinishManager();
    private Logger                                log     = Logger.getLogger(ActExcelFromRunToFinishManager.class);
    

    public static ActExcelFromRunToFinishManager getInstance ()
    {
        if (intance == null)
        {
            intance = new ActExcelFromRunToFinishManager();
        }
        return intance;
    }

    public List<Long> updateExcelInsToFinish ( String serverIp ) throws RepositoryException
    {
        List<Long> instanceIDs = new ArrayList<Long>();
        for (int i = 0;; i++)
        {
            try
            {
                updateExcelInsToFinishPartOne(serverIp, instanceIDs);
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        
        //查出应急系统的完成状态调用itsm的接口发送给itsm
        
        // 应用维护流程结束发送邮件
        SendEmailForApmManager.getInstance().sendFlowEndEmail(instanceIDs, true);
        // 变更流程结束发送邮件
        SendEmailManager.getInstance().sendFlowEndEmail(instanceIDs, true);
        // AZ流程
        SendEmailForAzManager.getInstance().sendFlowEndEmail(instanceIDs, true);
        if (Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_TEST_SEND_MESSAGE, false))
        {
            // 浦发 外部接口测试流程结束发送执行结果信息
            SusTestFlowInfoManager.getInstance().sendTestFlowInfo(instanceIDs, 1);
        }

        //信息采集类型的模板流程，信息采集步骤结果处理开关
        if (ServerEnv.getInstance().infoCollectStepResultSwitch() && null != instanceIDs&&!instanceIDs.isEmpty())
        {
            log.info("info.collect.step.result.smdb.switch : "
                    + ServerEnv.getInstance().infoCollectStepResultSmdbSwitch());
            // 信息采集步骤结果处理，smdb集成处理方式。
            if (ServerEnv.getInstance().infoCollectStepResultSmdbSwitch())
            {
                SmdbAcceptServiceNew sendSmdbResult = new SmdbAcceptServiceNew(instanceIDs);
                sendSmdbResult.start();
            }
        }
        
        if (PersonalityEnv.isInfoCollectionSendSwitchValue() && PersonalityEnv.isInfoCollectionNsSwitchValue())
        {
            // 计算是否
            InfoCollectionManager.getInstance().sendInfoCollecionResultToPlatform(instanceIDs,
                Constants.IEAI_INFO_COLLECT);
        }
      
        if(Environment.getInstance().getSwitchSendFinishFlag()){
            this.sendFinishMsg(instanceIDs);
        }
        return instanceIDs;
    }

    

    private Map getType ( long instanceid )
    {
        Map resultMap = new HashMap();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        String sql = "SELECT IID,ISYSTYPE FROM IEAI_RUN_INSTANCE WHERE IID = ?";

        List<Long> list = new ArrayList<Long>();
        try
        {
            conn = DBResource.getConnection("getType", log, Constants.IEAI_EMERGENCY_OPER);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                resultMap.put("isystype", rs.getString("ISYSTYPE"));
                resultMap.put("iid", rs.getString("IID"));
            }
        } catch (Exception e)
        {
            log.error("getType() is error at ActExcelFromRunToFinishManager ",e);
            
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getType()", log);
        }
        return resultMap;
    }

    public void updateExcelInsToFinishPartOne ( String serverIp, List<Long> instanceIDs ) throws RepositoryException
    {
        Connection conn = null;
        CallableStatement call = null;
        PreparedStatement actStat = null;
        PreparedStatement actStatUpdate = null;
        ResultSet actRS = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

            String sql = this.getEndSelectInfo();

            String sqlFlow = "UPDATE IEAI_WORKFLOWINSTANCE SET ISTATUS=4 ,IENDTIME=FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8) WHERE ISTATUS=0 AND IFLOWID=?";
            actStat = conn.prepareStatement(sql);
            actStatUpdate = conn.prepareStatement(sqlFlow);
            actStat.setString(1, serverIp);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                long flowid = actRS.getLong("IFLOWID");
                actStatUpdate.setLong(1, flowid);
                actStatUpdate.addBatch();

                long instanceID = actRS.getLong("IRUNINSID");
                instanceIDs.add(instanceID);

            }
            
            String sqlwhere = "SELECT T.IFLOWID AS IFLOWID FROM IEAI_RUNINFO_INSTANCE T WHERE T.IRUNINSID IN "
                    + "(SELECT T.ICHILDINSTANCEID FROM IEAI_RUNINFO_INSTANCE T WHERE T.ICHILDINSTANCEID IN "
                    + "(SELECT A.IID FROM IEAI_RUN_INSTANCE A , IEAI_RUNINFO_INSTANCE B WHERE A.IID=B.IRUNINSID AND A.ISTATE=0  AND  "
                    + "A.ISERVERIP=? GROUP BY A.IID HAVING  COUNT(IF(B.ISTATE=0,1,IF(B.ISTATE=1,1,NULL)))=0 ) )";

            if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.DB2) || DBManager.Orcl_Faimily())
            {
                sqlwhere = "SELECT T.IFLOWID AS IFLOWID FROM IEAI_RUNINFO_INSTANCE T WHERE T.IRUNINSID IN "
                        + "(SELECT T.ICHILDINSTANCEID FROM IEAI_RUNINFO_INSTANCE T WHERE T.ICHILDINSTANCEID IN "
                        + "(SELECT A.IID FROM IEAI_RUN_INSTANCE A , IEAI_RUNINFO_INSTANCE B WHERE A.IID=B.IRUNINSID AND A.ISTATE=0  AND  "
                        + "A.ISERVERIP=? GROUP BY A.IID HAVING  COUNT(DECODE(B.ISTATE,0,1,DECODE(B.ISTATE,1,1,NULL)))=0 ) )";
            }
            ps = conn.prepareStatement(sqlwhere);
            ps.setString(1, serverIp);
            rs = ps.executeQuery();
            while (rs.next())
            {
                long flowid = rs.getLong("IFLOWID");
                actStatUpdate.setLong(1, flowid);
                actStatUpdate.addBatch();
            }
            
            actStatUpdate.executeBatch();
            //查询当前所有步骤是否完成
            String callStr = "{call PROC_UPDATE_RUN_INSSTATE(?,?)}";
            try {
                String bankFlag = Environment.getInstance().getBankSwitch();
                if (Constants.BANK_FJNX.equals(bankFlag)){
                    callStr= "{call proc_update_run_insstate(?,?)}";
                }

                if (!Constants.BANK_FJNX.equals(bankFlag)){
                    if (PersonalityEnv.isPF_SUS_TIMINGSTART_VALUE()||ServerEnv.getServerEnv().isGDDownloadLocalPath())
                    {

                        callStr = "{call PROC_UPDATE_RUN_INSSTATE_NEW(?,?)}";
                    }
                }

            } catch (UnknownHostException e) {
                e.printStackTrace();
            }



            call = conn.prepareCall(callStr);
            call.setString(1, serverIp);
            call.registerOutParameter(2, java.sql.Types.NUMERIC); // 设定返回值类型
            call.execute();
            conn.commit();
        } catch (DBException e)
        {
            log.error("updateExcelInsToFinish() is error DBException", e);
            throw new RepositoryException(ServerError.ERR_DB_INIT);
        } catch (SQLException e)
        {
            log.error("updateExcelInsToFinish() is error SQLException", e);
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e, "updateExcelInsToFinish", log);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeCallableStatement(call, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePreparedStatement(actStatUpdate, Thread.currentThread().getStackTrace()[1].getMethodName(),
                log);
            DBResource.closeConn(conn, actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }

    /**
     * 
     * @Title: getEndSelectInfo   
     * @Description: 获取流程结束信息查询语句   
     * @return      
     * @author: hao_niu 
     * @date:   2019年1月25日 下午2:06:47
     */
    private String getEndSelectInfo ()
    {
        String sql = "SELECT IFLOWID,IRUNINSID  FROM IEAI_RUNINFO_INSTANCE WHERE "
                + "IRUNINSID IN (  SELECT  A.IID FROM IEAI_RUN_INSTANCE A , IEAI_RUNINFO_INSTANCE B "
                + "WHERE A.IID=B.IRUNINSID AND A.ISTATE=0 AND A.ISERVERIP=? GROUP BY A.IID "
                + "HAVING COUNT(IF(B.ISTATE=0,1,IF(B.ISTATE=1,1,NULL)))=0)";

        if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.DB2) || DBManager.Orcl_Faimily())
        {
            sql = "SELECT IFLOWID,IRUNINSID FROM IEAI_RUNINFO_INSTANCE WHERE "
                    + "IRUNINSID IN (  SELECT A.IID FROM IEAI_RUN_INSTANCE A , "
                    + "IEAI_RUNINFO_INSTANCE B WHERE A.IID=B.IRUNINSID AND A.ISTATE=0 AND "
                    + "A.ISERVERIP=? GROUP BY A.IID HAVING "
                    + "COUNT(DECODE(B.ISTATE,0,1,DECODE(B.ISTATE,1,1,NULL)))=0)";
        }
        return sql;
    }

    public List<Long> getSysIDByInstaceID ( String instanceIDs, int sysType ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        
        String sqlPart = getSqlPart(instanceIDs);
        String sql = " select b.iid iieaisysid from ieai_run_instance a,ieai_project b " + " where a.isysname=b.iname"
                + " and "+sqlPart;
        List<Long> list = new ArrayList<Long>();
        try
        {
            conn = DBResource.getConnection("getCheckRet", log, sysType);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                list.add(rs.getLong("iieaisysid"));
            }
        } catch (Exception e)
        {
            log.error("getExcelFinishInsMsg() is error at ActExcelFromRunToFinishManager " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getExcelFinishInsMsg()", log);
        }
        return list;
    }
    
    public String getSqlPart ( String instanceIDs )
    {
        StringBuilder sqlpart = new StringBuilder();
        String[] instanceID = instanceIDs.split(",");
        List instanceIDList = Arrays.asList(instanceID);
        int count = 0;
        if(instanceIDList.size()>1000) {
            sqlpart.append(" ( ");
            while((instanceIDList.size()-count*1000)>0) {
                int maxLength = (instanceIDList.size()-count*1000)>1000?(count+1)*1000:instanceIDList.size();
                if(count==0) {
                    sqlpart.append(" a.iid in (" + org.apache.commons.lang.StringUtils.join(instanceIDList.subList(count*1000, maxLength), ",")  + ") \r\n");
                }else {
                    sqlpart.append(" or a.iid in (" + org.apache.commons.lang.StringUtils.join(instanceIDList.subList(count*1000, maxLength), ",") + ") \r\n");
                }
                count ++;
            }
            sqlpart.append(" ) ");
        }else {
            sqlpart.append("a.iid in (" + instanceIDs + ")");
        }
        return sqlpart.toString();
    }

    public List<Map<String, String>> startResStateCheckFlow ( List<Map<String, String>> dbList, String userName,
            long userId ) throws ServerException
    {

        UserInfo userInfo = new UserInfo();
        userInfo.setFullName(userName);
        userInfo.setId(userId);
        long flowId = 0;
        StringBuilder cmd = new StringBuilder();

        List<Map<String, String>> flowList = new ArrayList<Map<String, String>>();
        try
        {
            for (Map<String, String> map : dbList)
            {
                int itype = Integer.parseInt(map.get("itype"));
                if (itype == 0)
                {
                    cmd.append(map.get("iscriptpath"));
                    List args = new ArrayList();
                    args.add(map.get("iagent_ip") + ":" + map.get("iagent_port"));
                    args.add(cmd.toString());
                    flowId = Engine.getInstance().startFlow(userInfo, "ExcelActExecModelDR", "ActExec_Check", args,
                        new HashMap(), null, map.get("iip"), null, false, null, null, Constants.IEAI_IEAI_BASIC,
                        Constants.IEAI_EMERGENCY_SWITCH,false);
                    map.put("iflowid", String.valueOf(flowId));
                    map.put("type", "3");
                    flowList.add(map);
                } else
                {
                    cmd.append(map.get("iscriptpath"));
                    String ip = map.get("irealip");
                    String iuser = map.get("iuser");
                    String ipasswd = map.get("ipasswd");
                    cmd.append(" " + ip + " " + iuser + " " + ipasswd);
                    List args = new ArrayList();
                    args.add(map.get("iagent_ip") + ":" + map.get("iagent_port"));
                    args.add(cmd.toString());
                    flowId = Engine.getInstance().startFlow(userInfo, "ExcelActExecModelDR", "ActExec_Check", args,
                        new HashMap(), null, map.get("iip"), null, false, null, null, Constants.IEAI_IEAI_BASIC,
                        Constants.IEAI_EMERGENCY_SWITCH,false);
                    map.put("iflowid", String.valueOf(flowId));
                    map.put("type", "3");
                    flowList.add(map);
                }
            }

        } catch (Exception e)
        {
            throw new ServerException("启动流程失败!" + e.getMessage());
        }
        return flowList;
    }
    
    private void sendFinishMsg(List<Long> instanceIDs){
        Connection conn = null;
        try{
            conn = DBResource.getConnection("sendFinishMsg", log, Constants.IEAI_IEAI_BASIC);
            for(int i = 0; i < instanceIDs.size(); i++){
                long iruninsid = instanceIDs.get(i);
                List endStepInfo = ExcelModelExecManager.getInstance().getEndStepInfo(iruninsid,conn);
                for(int j = 0; j<endStepInfo.size();j++ ){
                    Map map = (Map) endStepInfo.get(j);
                    Long iconner = Long.parseLong(String.valueOf(map.get("iconner")));
                    Long iserner = Long.parseLong(String.valueOf(map.get("iserner")));
                    String iactname = String.valueOf(map.get("iactname"));
                    log.info("----------------iactname:"+iactname);
                    SwitchMonitorManage.getInstance().sendFininshMsgToThridPart(iruninsid,iconner,iserner,iactname,conn);
                }
                if(!endStepInfo.isEmpty()){
                    ExcelModelExecManager.getInstance().updateRunInsAllSend(iruninsid,conn);
                }
            }
        }catch (Exception e) {
            log.error("sendFinishMsg is error",e);
        }finally{
            DBResource.closeConnection(conn, "sendFinishMsg", log);
        }
    }

    public Map<String, Object> getgetRunInfo ( long mainId ){
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resMap = new HashMap<String, Object>();
        try
        {
            con = DBResource.getConnection(methodName, log, Constants.IEAI_IEAI_BASIC);
            String sqlAppend=" LISTAGG(B.IFILENAME||':'||B.IVERSION, ',') WITHIN GROUP(ORDER BY B.IFILENAME) ";
            if (DBManager.Mysql_Faimily()) {
                sqlAppend = " GROUP_CONCAT(concat(B.IFILENAME,':',B.IVERSION) ORDER BY B.IFILENAME, ',') ";
            }
            String sql = "SELECT A.IRUNINSNAME,A.ISTARTUSER,C.ICICDSYS_CODE, "+sqlAppend+" AS IFILENAME "
                    +"FROM IEAI_RUN_INSTANCE A, IEAI_CICD_TASK_RELEASE B,IEAI_PROJECT C  "
                    +"WHERE A.ITASKID = B.IFLOWTASKRUNID AND A.IBUSNES_SYS_IID=C.IID AND A.IID = ? GROUP BY IRUNINSNAME,A.ISTARTUSER,C.ICICDSYS_CODE";
            ps = con.prepareStatement(sql);
            ps.setLong(1, mainId);
            rs = ps.executeQuery();
            while (rs.next()) {
                resMap.put("itaskname", rs.getString("IRUNINSNAME"));
                resMap.put("iname", rs.getString("IFILENAME"));
                resMap.put("username", rs.getString("ISTARTUSER"));
                resMap.put("icicdsyscode", rs.getString("ICICDSYS_CODE"));
            }
        } catch (Exception e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(con, rs, ps, methodName, log);
        }
        return resMap;
    }
    
}

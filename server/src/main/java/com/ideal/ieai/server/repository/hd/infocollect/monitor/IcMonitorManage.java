package com.ideal.ieai.server.repository.hd.infocollect.monitor;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;
import com.ideal.ieai.server.repository.hd.infocollect.bean.IcQueryParamBean;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.PageParamBean;
import com.ideal.ieai.server.util.SqlLogUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName:  IcMonitorManage
 * @Description: 信息采集 流程监控页面DB层操作类(补充注释)
 * @author: haijiao_dong
 * @date:   2019-09-08 4:51:19 PM
 *
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
 *
 */
public class IcMonitorManage
{

    private static final Logger _log               = Logger.getLogger(IcMonitorManage.class);
    private static final String CLASSNAME          = new Throwable().getStackTrace()[1].getClassName();

    private static final String LEFTSTATECOLORKEY  = "leftStateColor";
    private static final String RIGHTSTATECOLORKEY = "rightStateColor";
    private static final String GREENCOLOR         = "green";
    private static final String GRAYCOLOR          = "gray";
    private static final String REDCOLOR           = "red";

    private IcMonitorManage()
    {
    }

    private static IcMonitorManage intance = new IcMonitorManage();

    public static IcMonitorManage getInstance ()
    {
        if (intance == null)
        {
            intance = new IcMonitorManage();
        }
        return intance;
    }

    /**
     *
     * <li>Description:获取实例列表</li>
     * <AUTHOR>
     * 2016年1月20日
     * @return
     * @throws Exception
     * return List
     */
    public List<Map<String, Object>> getSwitchMonitorSys ( IcQueryParamBean queryBean, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List<ProjectBean> busSysNameList = DataCenterOperationManage.getInstance().getProject(conn,
                Long.valueOf(queryBean.getUserid()), sysType);
            String sysnames = getSysNames(busSysNameList);
            if ("".equals(sysnames))
            {
                return resultList;
            }

            /**
             * SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
             * String sql =
             * "select c.*,d.istartuser,dp.IBATCHDATE from (select a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.isystype, a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.IVERSION,count(decode(b.ISTATE,2,1,3,1,null)) as finishcount,count(b.IID) as count,a.istate,count(decode(b.IISFAIL,0,null,null,null,-1,null,1)) as iisfail,FUN_GET_DATE_NUMBER_NEW("
             * + Constants.getCurrentSysDate()
             * +
             * ",8) as mysysdate from ieai_run_instance a left outer join IEAI_RUNINFO_INSTANCE b on a.IID=b.IRUNINSID group by a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.ISYSTYPE,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE,a.IVERSION order by a.ISTARTTIME desc) c ,ieai_run_instance d,"
             * +
             * "(select * from (select IRUNINSTANCEID,IBATCHDATE from ieai_dnps union all select IRUNINSTANCEID,IBATCHDATE from ieai_dnps_mid)) dp where 1=1 "
             * + sqlWhere + " and c.iid = d.iid and dp.IRUNINSTANCEID = d.iid  ";
             */

            // V4.7.26 信息采集-信息采集监控页面系统列表查询条件sql
            String sqlWhere = getgetSwitchMonitorSysWhereSql(sysnames, queryBean, sysType);

            // V4.7.26 信息采集-信息采集监控页面系统列表查询sql
            String sql = getgetSwitchMonitorSysSql(sqlWhere);

            PageParamBean pageBean = PageParamBean.getInstance().getParamPageBean(sql, queryBean.getStart(),
                queryBean.getLimit());

            ps = conn.prepareStatement(pageBean.getSql());
            ps.setInt(1, pageBean.getPagePara1());
            ps.setInt(2, pageBean.getPagePara2());

            SqlLogUtil.debugSqlLog(CLASSNAME, method, pageBean.getSql(),
                new Object[] { pageBean.getPagePara1(), pageBean.getPagePara2() }, sysType);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("iid", rset.getLong("iid"));
                map.put("isysid", rset.getLong("isysid"));
                map.put("isysname", rset.getString("isysname"));
                map.put("iruninsname", rset.getString("iruninsname"));
                map.put("iinsdes", rset.getString("iinsdes"));
                map.put("istartuser", rset.getString("ISTARTUSER"));
                map.put("istarttime",
                    rset.getLong("istarttime") == 0 ? "" : sdf.format(new Date(rset.getLong("istarttime"))));
                map.put("iendtime",
                    rset.getLong("iendtime") == 0 ? "" : sdf.format(new Date(rset.getLong("iendtime"))));
                map.put("istate", rset.getLong("istate"));
                if (rset.getLong("istate") == 0)
                {
                    map.put("iruntime", mSec2HMS((rset.getLong("mysysdate") - rset.getLong("istarttime"))));
                } else
                {
                    map.put("iruntime", mSec2HMS((rset.getLong("iendtime") - rset.getLong("istarttime"))));
                }
                map.put("finishcount", rset.getLong("finishcount"));
                map.put("count", rset.getLong("count"));
                map.put("iisfail", rset.getString("iisfail"));
                String bankFlag = "";
                List stepInfo = null;
                int resCount = 0;
                try
                {
                    bankFlag = Environment.getInstance().getBankSwitch();
                } catch (Exception e)
                {
                }

                if ((Constants.BANK_CMB).equals(bankFlag))
                {
                    stepInfo = this.getSwitchMonitorStepStatisticForZS(rset.getLong("iid"), sysType);
                } else
                {

                    if (sysType == 3)// 应用自动化变更
                    {
                        stepInfo = this.getSwitchMonitorStepStatisticForSUS(rset.getLong("iid"), rset.getLong("istate"),
                            sysType);
                        // stepInfo = this.getSwitchMonitorStepStatisticForSUS(rset.getLong("iid"));
                        for (int i = 0; i < stepInfo.size(); i++)
                        {
                            Map stepMap = (Map) stepInfo.get(i);
                            List resList = (List) stepMap.get("resList");
                            if (resList.size() > resCount)
                            {
                                resCount = resList.size();
                            }
                        }
                    } else
                    {
                        stepInfo = this.getSwitchMonitorStepStatistic(rset.getLong("iid"), sysType);
                    }
                }

                map.put("stepInfo", stepInfo);
                int circleCount = 2 + stepInfo.size();
                if (sysType == 3)// 应用自动化变更
                {
                    circleCount = 2 + resCount;
                }
                int arrowCount = circleCount - 1;
                int width = 90 * circleCount + arrowCount * 69 + 10;
                map.put("graphWidth", width);

                if (rset.getLong("finishcount") == 0)
                {
                    map.put(LEFTSTATECOLORKEY, GREENCOLOR);
                    map.put(RIGHTSTATECOLORKEY, GRAYCOLOR);
                } else if (rset.getLong("finishcount") > 0 && rset.getLong("finishcount") < rset.getLong("count"))
                {
                    map.put(LEFTSTATECOLORKEY, GRAYCOLOR);
                    map.put(RIGHTSTATECOLORKEY, GRAYCOLOR);
                } else if (rset.getLong("finishcount") >= rset.getLong("count"))
                {
                    map.put(LEFTSTATECOLORKEY, GRAYCOLOR);
                    map.put(RIGHTSTATECOLORKEY, GREENCOLOR);
                } else
                {
                    map.put(LEFTSTATECOLORKEY, REDCOLOR);
                    map.put(RIGHTSTATECOLORKEY, REDCOLOR);
                }
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("getSwitchMonitorSys Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorSys", _log);
        }
        return resultList;
    }

    /**
     * @Title: getgetSwitchMonitorSysCount
     * @Description: V4.7.26 信息采集-信息采集监控页面分页记录总数查询方法
     * @param queryBean
     * @param sysType
     * @return
     * @throws RepositoryException
     * @author: yue_sun
     * @date:   2020-04-03 10:35:54
     */
    public int getgetSwitchMonitorSysCount ( IcQueryParamBean queryBean, int sysType ) throws RepositoryException
    {
        int count = 0;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            Connection con = null;
            try
            {
                con = DBResource.getJDBCConnection(method, _log, sysType);
                // V4.7.22 根据ID获取所有要出的服务信息配置的相关信息
                count = getgetSwitchMonitorSysCount(con, queryBean, sysType);

                break;
            } catch (RepositoryException ex)
            {
                _log.error(method, ex);
                DBRetryUtil.waitForNextTry(i, ex);
            } finally
            {
                DBResource.closeConnection(con, method, _log);
            }
        }
        return count;
    }

    /**
     * @Title: getgetSwitchMonitorSysCount
     * @Description:  V4.7.26 信息采集-信息采集监控页面分页记录总数查询方法
     * @param conn
     * @param queryBean
     * @param sysType
     * @return
     * @throws RepositoryException
     * @author: yue_sun
     * @date:   2020-04-03 10:36:35
     */
    public int getgetSwitchMonitorSysCount ( Connection conn, IcQueryParamBean queryBean, int sysType )
            throws RepositoryException
    {
        int count = 0;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            List<ProjectBean> busSysNameList = DataCenterOperationManage.getInstance().getProject(conn,
                Long.valueOf(queryBean.getUserid()), sysType);
            String sysnames = getSysNames(busSysNameList);
            if ("".equals(sysnames))
            {
                return count;
            }
            // V4.7.26 信息采集-信息采集监控页面系统列表查询条件sql
            String sqlWhere = getgetSwitchMonitorSysWhereSql(sysnames, queryBean, sysType);

            // V4.7.26 信息采集-信息采集监控页面系统列表查询sql
            String sql = getgetSwitchMonitorSysSql(sqlWhere);
            String countSql = "select count(1) as sysCount from ( " + sql + " ) t";

            ps = conn.prepareStatement(countSql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("sysCount");
            }
        } catch (SQLException e)
        {
            _log.error(method, e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, _log);
        }

        return count;
    }

    /**
     * @Title: getSysNames
     * @Description: V4.7.26 信息采集-信息采集监控页面系统权限获取方法
     * @param busSysNameList
     * @return
     * @author: yue_sun
     * @date:   2020-04-03 10:36:43
     */
    public String getSysNames ( List<ProjectBean> busSysNameList )
    {
        StringBuilder sysnames = new StringBuilder();
        if (busSysNameList != null && !busSysNameList.isEmpty())
        {
            for (int i = 0; i < busSysNameList.size(); i++)
            {
                ProjectBean bean = busSysNameList.get(i);
                if (bean.getLastId() < 0)
                {
                    sysnames.append("All");
                    break;
                } else
                {
                    if (i == 0)
                    {
                        sysnames.append("'" + bean.getiName() + "'");
                    } else
                    {
                        sysnames.append("," + "'" + bean.getiName() + "'");
                    }
                }

            }
        }
        return sysnames.toString();
    }

    /**
     * @Title: getgetSwitchMonitorSysSql
     * @Description: V4.7.26 信息采集-信息采集监控页面系统列表查询sql
     * @param sqlWhere
     * @return
     * @author: yue_sun
     * @date:   2020-04-03 10:38:38
     */
    public String getgetSwitchMonitorSysSql ( String sqlWhere )
    {
        String sql = "select c.*,d.istartuser from (select a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.isystype, a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.IVERSION,count(decode(b.ISTATE,2,1,3,1,null)) as finishcount,count(b.IID) as count,a.istate,count(decode(b.IISFAIL,0,null,null,null,-1,null,1)) as iisfail,FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8) as mysysdate from ieai_run_instance a left outer join IEAI_RUNINFO_INSTANCE b on a.IID=b.IRUNINSID group by a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.ISYSTYPE,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE,a.IVERSION order by a.ISTARTTIME desc) c ,ieai_run_instance d where 1=1 "
                + sqlWhere + " and c.iid = d.iid ";
        if (3 == JudgeDB.IEAI_DB_TYPE)
        {
            // mysql
            sql = "select c.*,d.istartuser from (select a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.isystype, a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.IVERSION,count(if(b.ISTATE=2,1,if(b.ISTATE=3,1,null))) as finishcount,count(b.IID) as count,a.istate,count(if(b.IISFAIL=0,null,if(b.IISFAIL=null,null,if(b.IISFAIL=-1,null,1)))) as iisfail,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8) as mysysdate from ieai_run_instance a left outer join IEAI_RUNINFO_INSTANCE b on a.IID=b.IRUNINSID group by a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.ISYSTYPE,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE,a.IVERSION order by a.ISTARTTIME desc) c ,ieai_run_instance d where 1=1 "
                    + sqlWhere + " and c.iid = d.iid ";

        }
        return sql;
    }

    /**
     * @Title: getgetSwitchMonitorSysWhereSql
     * @Description: V4.7.26 信息采集-信息采集监控页面系统列表查询条件sql
     * @param sysnames
     * @param queryBean
     * @param sysType
     * @return
     * @author: yue_sun
     * @date:   2020-04-03 10:38:58
     */
    public String getgetSwitchMonitorSysWhereSql ( String sysnames, IcQueryParamBean queryBean, int sysType )
    {
        StringBuilder sqlWhere = new StringBuilder();
        if ("All".equals(sysnames))
        {
            sqlWhere.append(" AND c.ISYSTYPE=" + sysType + " ");
        } else
        {
            sqlWhere.append(" AND c.ISYSTYPE=" + sysType + " AND c.ISYSNAME in (" + sysnames + ")");
        }
        if (StringUtils.isNotBlank(queryBean.getIbatchdatename()))
        {
            sqlWhere.append(" and dp.IBATCHDATE like '%" + queryBean.getIbatchdatename().trim() + "%'");
        }
        if (StringUtils.isNotBlank(queryBean.getIruninsname()))
        {
            sqlWhere.append(" and c.IRUNINSNAME like '%" + queryBean.getIruninsname().trim() + "%'");
        }
        if (StringUtils.isNotBlank(queryBean.getIinsdes()))
        {
            sqlWhere.append(" and c.IINSDES like '%" + queryBean.getIinsdes().trim() + "%'");
        }
        if (StringUtils.isNotBlank(queryBean.getVersion()))
        {
            sqlWhere.append(" and c.IVERSION like '%" + queryBean.getVersion().trim() + "%'");
        }
        if (StringUtils.isNotBlank(queryBean.getIsysclassid()))
        {
            sqlWhere.append(" and c.ISYSCLASSID = " + queryBean.getIsysclassid().trim());
        }
        if (StringUtils.isNotBlank(queryBean.getIsysname()))
        {
            sqlWhere.append(" and c.ISYSNAME like '%" + queryBean.getIsysname().trim() + "%'");
        }
        if (StringUtils.isNotBlank(queryBean.getIstate()))
        {
            sqlWhere.append(" and c.istate = " + queryBean.getIstate().trim());
        }
        return sqlWhere.toString();
    }

    public List getSwitchMonitorInstanceConcurrent ( Long instanceId, int sysType ) throws Exception
    {

        List resultList = new ArrayList();
        if (null == instanceId || instanceId <= 0)
        {
            return resultList;
        }

        String bankFlag = "";
        try
        {
            bankFlag = Environment.getInstance().getBankSwitch();
            if ((Constants.BANK_CMB).equals(bankFlag))
            {
                resultList = this.getSwitchMonitorStepStatisticForZS(instanceId, sysType);
            } else
            {
                resultList = this.getSwitchMonitorStepStatistic(instanceId, sysType);
            }
        } catch (Exception e)
        {
        }
        return resultList;
    }

    public List getSwitchMonitorSysInstanceList ( String sysnames, Integer sysType ) throws Exception
    {

        List resultList = new ArrayList();
        String sqlWhere = "";

        if (!"".equals(sysnames))
        {
            sqlWhere = sqlWhere + " and ISYSNAME in (" + sysnames + ")";
        } else
        {
            return resultList;
        }
        String whereSql = " WHERE A.ISYSTYPE= " + Constants.IEAI_EMERGENCY_SWITCH;// 灾备切换

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "select * from (select a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.IVERSION,count(decode(b.ISTATE,2,1,3,1,null)) as finishcount,count(b.IID) as count,a.istate,count(decode(b.IISFAIL,0,null,null,null,-1,null,1)) as iisfail,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8) as mysysdate from ieai_run_instance a left outer join IEAI_RUNINFO_INSTANCE b on a.IID=b.IRUNINSID "
                    + whereSql
                    + " group by a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE,a.IVERSION order by a.ISTARTTIME desc) where 1=1 "
                    + sqlWhere;
            ps = conn.prepareStatement(sql);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put("isysid", rset.getLong("isysid"));
                map.put("isysname", rset.getString("isysname"));
                map.put("iruninsname", rset.getString("iruninsname"));
                map.put("iinsdes", rset.getString("iinsdes"));
                map.put("istate", rset.getLong("istate"));
                map.put("finishcount", rset.getLong("finishcount"));
                map.put("count", rset.getLong("count"));
                map.put("iisfail", rset.getInt("iisfail"));
                if (rset.getInt("iisfail") > 0)
                {
                    map.put("stateColor", REDCOLOR);
                } else if (rset.getLong("istate") == 0)
                {
                    map.put("stateColor", GREENCOLOR);
                } else
                {
                    map.put("stateColor", GRAYCOLOR);
                }
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("getSwitchMonitorSysInstanceList Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorSysInstanceList", _log);
        }
        return resultList;
    }

    /**
     *
     * <li>Description:获取大系统列表</li>
     * <AUTHOR>
     * 2016年1月20日
     * @return
     * @throws Exception
     * return List
     */
    public List getSysType ( int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "select a.IID,a.IBUSNAME from IEAI_BUSINESS_TYPE a";
            ps = conn.prepareStatement(sql);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql, new Object[] {}, sysType);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put("ibusname", rset.getString("ibusname"));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.getSysType Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSysType", _log);
        }
        return resultList;
    }

    /**
     *
     * <li>Description:将毫秒转换为时分秒，不足一秒按一秒算</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param msec
     * @return
     * return List
     */
    private String mSec2HMS ( long msec )
    {
        long time = msec;
        if (time < 1000)
        {
            return "0小时0分1秒";
        }
        long mSec = time % 1000;
        time /= 1000;
        long hour = time / 3600;
        time = time % 3600;
        long min = time / 60;
        time = time % 60;
        long sec = time;
        if (mSec > 0)
        {
            sec = sec + 1;
        }
        return hour + "小时" + min + "分" + sec + "秒";
    }

    /**
     *
     * <li>Description:获取步骤列表</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninsid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStep ( String iruninsid, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "select b.IID,b.icenter,b.iconnername,b.iflowid,a.ISYSNAME,a.IRUNINSNAME,b.ISERNER,b.iprener,a.ISWITCHTO,b.IIP,b.ICONNER,b.IACTNAME,b.IACTDES,b.ISHELLSCRIPT,b.ISHELLPATH,b.iparameter,b.ISTARTTIME,b.IENDTIME,b.ISTATE,b.IISFAIL,b.IACTTYPE,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8) as mysysdate,b.irerunflag from  IEAI_RUN_INSTANCE a,IEAI_RUNINFO_INSTANCE b where a.IID=b.IRUNINSID and a.IID=? order by b.ISERNER,b.IRERUNFLAG";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(iruninsid));
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql, new Object[] { Long.valueOf(iruninsid) }, sysType);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put("iflowid", rset.getLong("iflowid"));
                map.put("isysname", rset.getString("isysname"));
                map.put("iruninsname", rset.getString("iruninsname"));
                map.put("iip", rset.getString("iip"));
                map.put("iserner", rset.getDouble("iserner"));
                map.put("iprener", rset.getString("iprener"));
                map.put("iswitchto", rset.getString("iswitchto"));
                map.put("iconner", rset.getLong("iconner"));
                map.put("iactname", rset.getString("iactname"));
                map.put("iactdes", rset.getString("iactdes"));
                // map.put("ishellpath", rset.getString("ishellpath")+"
                // "+(rset.getString("iparameter")==null?"":rset.getString("iparameter")));
                // map.put("ishellpath", rset.getString("ISHELLSCRIPT")+"
                // "+rset.getString("ishellpath"));
                String shellpath = (rset.getString("ISHELLSCRIPT") == null ? "" : rset.getString("ISHELLSCRIPT")) + " "
                        + (rset.getString("ishellpath") == null ? "" : rset.getString("ishellpath"));
                map.put("ishellpath", shellpath);
                map.put("istarttime",
                    rset.getLong("istarttime") == 0 ? "" : sdf.format(new Date(rset.getLong("istarttime"))));
                map.put("iendtime",
                    rset.getLong("iendtime") == 0 ? "" : sdf.format(new Date(rset.getLong("iendtime"))));
                if (rset.getLong("istate") == 0 || rset.getLong("istate") == 4)
                {
                    if (rset.getLong("istarttime") > 0)
                    {
                        map.put("iruntime", mSec2HMS((rset.getLong("mysysdate") - rset.getLong("istarttime"))));
                    } else
                    {
                        map.put("iruntime", "");
                    }
                } else
                {
                    if (rset.getLong("istarttime") > 0)
                    {
                        map.put("iruntime", mSec2HMS((rset.getLong("iendtime") - rset.getLong("istarttime"))));
                    } else
                    {
                        map.put("iruntime", "");
                    }
                }
                map.put("istate", rset.getLong("istate"));
                map.put("iisfail", rset.getLong("iisfail"));
                map.put("iacttype", rset.getLong("iacttype"));
                map.put("irerunflag", rset.getLong("irerunflag"));
                map.put("icenter", rset.getString("icenter"));
                map.put("iconnername", rset.getString("iconnername"));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.getSysType Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStep", _log);
        }
        return resultList;
    }

    /**
     *
     * <li>Description:获取步骤</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninfoinsid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStepByStepId ( String iruninfoinsid, String flag, int sysType ) throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "";
            if ("1".equals(flag))
            {
                sql = "select b.IID,b.iflowid,b.ISTATE,b.IISFAIL,b.iacttype,b.IREMINFO from  IEAI_RUNINFO_INSTANCE_HIS b where b.IID=?";
            } else
            {
                sql = "select b.IID,b.iflowid,b.ISTATE,b.IISFAIL,b.iacttype,b.IREMINFO from  IEAI_RUNINFO_INSTANCE b where b.IID=?";
            }
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(iruninfoinsid));
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put("iflowid", rset.getLong("iflowid"));
                map.put("istate", rset.getLong("istate"));
                map.put("iisfail", rset.getLong("iisfail"));
                map.put("iacttype", rset.getLong("iacttype"));
                map.put("ireminfo", rset.getString("ireminfo"));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.getSwitchMonitorStepByStepId Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepByStepId", _log);
        }
        return resultList;
    }

    /**
     *
     * <li>Description:实例终止</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninsid
     * @param istate
     * @throws Exception
     * return void
     */
    public void sysStop ( String iruninsid, Long istate, Connection conn ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUN_INSTANCE SET ISTATE=?,IENDTIME=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8) WHERE IID=? and ISTATE=0";
        try
        {
            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, istate);
            actStat.setLong(2, Long.valueOf(iruninsid));
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql_update, new Object[] { istate, Long.valueOf(iruninsid) },
                Constants.IEAI_INFO_COLLECT);
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.sysStop Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "sysStop", _log);
        }
    }

    /**
     *
     * <li>Description:实例全部终止</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninsid
     * @param istate
     * @throws Exception
     * return void
     */
    public void sysStopAll ( Long istate, Connection conn ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUN_INSTANCE SET ISTATE=?,IENDTIME=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8) WHERE istate=0";
        try
        {
            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, istate);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql_update, new Object[] {}, Constants.IEAI_INFO_COLLECT);
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("sysStopAll Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "sysStopAll", _log);
        }
    }

    /**
     * @Title: sysStopBatch
     * @Description: V4.7.26 批量实例终止
     * @param irunInsIds
     * @param istate
     * @param conn
     * @throws Exception
     * @author: yue_sun
     * @date:   2020-04-02 15:55:19
     */
    public void sysStopBatch ( String irunInsIds, Long istate, Connection conn ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement actStat = null;
        String sqlUpdate = "UPDATE IEAI_RUN_INSTANCE SET ISTATE=?,IENDTIME=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8) WHERE iid in (" + irunInsIds + ") and istate=0";
        try
        {
            actStat = conn.prepareStatement(sqlUpdate);
            actStat.setLong(1, istate);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sqlUpdate, new Object[] { istate }, Constants.IEAI_INFO_COLLECT);
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("sysStopAll Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "sysStopAll", _log);
        }
    }

    /**
     *
     * <li>Description:实例下所有运行步骤终止</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninfoinsid
     * @param istate
     * @throws Exception
     * return void
     */
    public void stepStopByruninsid ( String iruninsid, Long istate, Connection conn ) throws Exception
    {
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET IENDTIME=decode(istarttime,null,null,FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)),ISTATE=? WHERE iruninsid=? and istate=0";
        if (3 == JudgeDB.IEAI_DB_TYPE)
        {
            // mysql
            sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET IENDTIME=if(istarttime=null,null,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8)),ISTATE=? WHERE iruninsid=? and istate=0";
        }
        try
        {
            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, istate);
            actStat.setLong(2, Long.valueOf(iruninsid));
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.stepStopByruninsid Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "stepStopByruninsid", _log);
        }
    }

    /**
     *
     * <li>Description:全部实例下所有运行步骤终止</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninfoinsid
     * @param istate
     * @throws Exception
     * return void
     */
    public void stepStopByruninsidAll ( Long istate, Connection conn ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET IENDTIME=decode(istarttime,null,null,FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8)),ISTATE=? WHERE iruninsid in (select iid from ieai_run_instance where istate=0) and istate=0";
        try
        {
            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, istate);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql_update, new Object[] { istate }, Constants.IEAI_INFO_COLLECT);
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.stepStopByruninsidAll Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "stepStopByruninsidAll", _log);
        }
    }

    /**
     * @Title: stepStopByruninsidBatch
     * @Description: V4.7.26 步骤批量终止
     * @param irunInsIds
     * @param istate
     * @param conn
     * @throws Exception
     * @author: yue_sun
     * @date:   2020-04-02 15:51:21
     */
    public void stepStopByruninsidBatch ( String irunInsIds, Long istate, Connection conn ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement actStat = null;
        String sqlUpdate = "UPDATE IEAI_RUNINFO_INSTANCE SET IENDTIME=decode(istarttime,null,null,FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8)),ISTATE=? WHERE iruninsid in ((select iid from ieai_run_instance where iid in (" + irunInsIds
                + ") and istate=0)) and istate=0";
        try
        {
            actStat = conn.prepareStatement(sqlUpdate);
            actStat.setLong(1, istate);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sqlUpdate, new Object[] { istate }, Constants.IEAI_INFO_COLLECT);
            actStat.executeUpdate();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.stepStopByruninsidAll Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "stepStopByruninsidAll", _log);
        }
    }

    /**
     *
     * <li>Description:步骤终止</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iruninfoinsid
     * @param istate
     * @throws Exception
     * return void
     */
    public void stepStop ( String iruninfoinsid, Long istate, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET IENDTIME=decode(istarttime,null,null,FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)),ISTATE=? WHERE IID=? AND (ISTATE=0 OR ISTATE=1 OR ISTATE=4)";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);

            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, istate);
            actStat.setLong(2, Long.valueOf(iruninfoinsid));
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql_update, new Object[] { istate, Long.valueOf(iruninfoinsid) },
                sysType);
            actStat.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.stepStop Error", e);
            DBResource.rollback(conn, sysType, e, method, _log);
            throw new Exception(e);
        } finally
        {
            DBResource.closePSConn(conn, actStat, "stepStop", _log);
        }
    }

    /**
     * <li>Description:暂停步骤</li>
     * <AUTHOR>
     * 2016年4月7日
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepPause ( String iruninfoinsid, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement actStat = null;
        String sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET ISTATE=? WHERE IID=? AND (ISTATE=0 OR ISTATE=1)";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            actStat = conn.prepareStatement(sql_update);
            actStat.setLong(1, 4);
            actStat.setLong(2, Long.valueOf(iruninfoinsid));
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql_update, new Object[] { 4, Long.valueOf(iruninfoinsid) },
                sysType);
            actStat.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            _log.error("stepPause Error", e);
            DBResource.rollback(conn, sysType, e, "stepPause", _log);
            throw new Exception(e);
        } finally
        {
            DBResource.closePSConn(conn, actStat, "stepPause", _log);
        }
    }

    /**
     * <li>Description:步骤恢复</li>
     * <AUTHOR>
     * 2016年4月7日
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepResume ( String iruninfoinsid, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet rset = null;
        boolean hasFlow = false;
        long stepState = 2;
        String sql = " select A.IFLOWID, B.ISTATUS from IEAI_RUNINFO_INSTANCE A, IEAI_WORKFLOWINSTANCE B WHERE IID=? and A.IFLOWID=B.IFLOWID";
        String sql_update = "UPDATE IEAI_RUNINFO_INSTANCE SET ISTATE=? WHERE IID=? AND ISTATE=4 ";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            actStat = conn.prepareStatement(sql);
            actStat.setLong(1, Long.valueOf(iruninfoinsid));
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql, new Object[] { Long.valueOf(iruninfoinsid) }, sysType);
            rset = actStat.executeQuery();
            while (rset.next())
            {
                long flowId = rset.getLong("IFLOWID");
                long flowStatus = rset.getLong("ISTATUS");
                if (flowId > 0)
                {
                    hasFlow = true;
                    if (flowStatus == 0 || flowStatus == 8)
                    {
                        stepState = 0;
                    }
                }
            }

            actStat = conn.prepareStatement(sql_update);
            if (hasFlow)
            {
                actStat.setLong(1, stepState);
            } else
            {
                actStat.setLong(1, 1); // 没有启动流程，设置为未开始
            }
            actStat.setLong(2, Long.valueOf(iruninfoinsid));
            actStat.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            DBResource.rollback(conn, sysType, e, "stepResume", _log);
            _log.error("SwitchMonitorManage.stepResume Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, actStat, "stepResume", _log);
        }
    }

    /**
     *
     * <li>Description:获取UT执行信息</li>
     * <AUTHOR>
     * 2016年1月20日
     * @param iflowid
     * @return
     * @throws Exception
     * return Map
     */
    public Map getUTMess ( Long iflowid ) throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        Map map = null;
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_INFO_COLLECT);
            String sql = "select a.IPROJECTNAME,a.IFLOWNAME,a.IFLOWINSNAME,b.IACTID,b.IACTNAME,b.ITASKID from ieai_workflowinstance a,IEAI_ACTRUNTIME b where a.IFLOWID=b.IFLOWID and IACTDEFNAME='UserTask' and a.IFLOWID=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iflowid);
            rset = ps.executeQuery();
            while (rset.next())
            {
                map = new HashMap();
                map.put("iprojectname", rset.getString("iprojectname"));
                map.put("iflowname", rset.getString("iflowname"));
                map.put("iflowinsname", rset.getString("iflowinsname"));
                map.put("iactid", rset.getString("iactid"));
                map.put("iactname", rset.getString("iactname"));
                map.put("itaskid", rset.getString("itaskid"));
            }
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.getUTMess Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getUTMess", _log);
        }
        return map;
    }

    /**
     * <li>Description:统计步骤状态 中信版</li>
     * <AUTHOR>
     * 2016年3月9日
     * @param iruninsid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStepStatistic ( Long iruninsid, int sysType ) throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List res = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String concatFunc = "";
            if (DBManager.Orcl_Faimily())
            { // oracle
                concatFunc = " WM_CONCAT(IACTNAME) ";
            } else if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                // mysql
                concatFunc = " group_concat(IACTNAME) ";
            } else
            {
                concatFunc = " LISTAGG(IACTNAME,',') ";
            }
            String sql = "select " + "A.ICONNER, A.ICENTER," + concatFunc + " as ACTNAMES, "
                    + "(SELECT ICONNERNAME  FROM (SELECT ROW_NUMBER() OVER(ORDER BY ICONNERNAME) AS ROW_NUMBER, A1.ICONNERNAME,A1.ICONNER  FROM IEAI_RUNINFO_INSTANCE A1 WHERE  A1.IRUNINSID="
                    + iruninsid + ") WHERE ICONNER=A.ICONNER AND ROW_NUMBER < 2)  AS ICONNERNAME,"
                    + "(select count(decode(IPARAMETER,'主',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                    + iruninsid + " and ICONNER=A.ICONNER ) as MAINDC, "
                    + "(select count(decode(IPARAMETER,'备',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                    + iruninsid + " and ICONNER=A.ICONNER ) as PREDC,"
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=1 and ICONNER=A.ICONNER) as NOTRUNCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=0 and ICONNER=A.ICONNER) as RUNNINGCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=4 and ICONNER=A.ICONNER) as PAUSINGCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE in (2,3) and ICONNER=A.ICONNER) as FINISHCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and (ISTATE=0 or ISTATE=4) and IISFAIL=1 and ICONNER=A.ICONNER) as EXECCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and (ISTATE=0 or ISTATE=4) and IISFAIL=3 and ICONNER=A.ICONNER) as TIMEOUTCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=0 and IACTTYPE=2 and ICONNER=A.ICONNER) as UTCOUNT "
                    + "from (select ICONNER, IACTNAME,ICENTER from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " group by ICONNER, IACTNAME,ICENTER) A group by A.ICONNER,ICENTER";
            if (3 == JudgeDB.IEAI_DB_TYPE)
            {
                // mysql
                sql = "select " + "A.ICONNER, A.ICENTER," + concatFunc + " as ACTNAMES, "
                // + "(SELECT ICONNERNAME FROM (SELECT ROW_NUMBER() OVER(ORDER BY ICONNERNAME) AS
                // ROW_NUMBER, A1.ICONNERNAME,A1.ICONNER FROM IEAI_RUNINFO_INSTANCE A1 WHERE
                // A1.IRUNINSID="
                // + iruninsid + ") WHERE ICONNER=A.ICONNER AND ROW_NUMBER < 2) AS ICONNERNAME,"
                        + "(select ICONNERNAME from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + "   AND ICONNER=A.ICONNER limit 1)  AS ICONNERNAME,"
                        + "(select count(if(IPARAMETER='主',1,NULL)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER ) as MAINDC, "
                        + "(select count(if(IPARAMETER='备',1,NULL)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER ) as PREDC,"
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=1 and ICONNER=A.ICONNER) as NOTRUNCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and ICONNER=A.ICONNER) as RUNNINGCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=4 and ICONNER=A.ICONNER) as PAUSINGCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE in (2,3) and ICONNER=A.ICONNER) as FINISHCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and (ISTATE=0 or ISTATE=4) and IISFAIL=1 and ICONNER=A.ICONNER) as EXECCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and (ISTATE=0 or ISTATE=4) and IISFAIL=3 and ICONNER=A.ICONNER) as TIMEOUTCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and IACTTYPE=2 and ICONNER=A.ICONNER) as UTCOUNT "
                        + "from (select ICONNER, IACTNAME,ICENTER from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " group by ICONNER, IACTNAME,ICENTER) A group by A.ICONNER,ICENTER";
            }
            ps = conn.prepareStatement(sql);
            rset = ps.executeQuery();
            while (rset.next())
            {
                int allSer = rset.getInt("NOTRUNCOUNT") + rset.getInt("RUNNINGCOUNT") + rset.getInt("PAUSINGCOUNT")
                        + rset.getInt("FINISHCOUNT");
                Map map = new HashMap();
                map.put("icenter", rset.getString("ICENTER"));
                map.put("conner", rset.getLong("ICONNER"));
                map.put("serverCount", allSer);
                // 兼容旧数据 如果没有顺序步骤名称则按照旧规则显示
                String iconnerNameString = rset.getString("ICONNERNAME");
                if (iconnerNameString == null || "".equals(iconnerNameString))
                {
                    iconnerNameString = rset.getString("ACTNAMES");
                }
                map.put("actNames", iconnerNameString);
                map.put("notRunCount", rset.getInt("NOTRUNCOUNT"));
                map.put("runningCount", rset.getInt("RUNNINGCOUNT"));
                map.put("finishCount", rset.getInt("FINISHCOUNT"));
                map.put("execCount", rset.getInt("EXECCOUNT"));
                map.put("timeoutCount", rset.getInt("TIMEOUTCOUNT"));
                map.put("utCount", rset.getInt("UTCOUNT"));
                if (rset.getInt("MAINDC") > 0 && rset.getInt("PREDC") == 0)
                {
                    map.put("dc", "maindc");
                } else if (rset.getInt("MAINDC") == 0 && rset.getInt("PREDC") > 0)
                {
                    map.put("dc", "predc");
                } else
                {
                    map.put("dc", "");
                }
                if (rset.getString("ICENTER") == null || "".equals(rset.getString("ICENTER")))
                {
                    map.put("existcenter", 3);
                    map.put("centertype", "Point");
                } else
                {
                    map.put("existcenter", 2);
                    map.put("centertype", "Circle");
                }
                if (rset.getInt("EXECCOUNT") > 0)
                {
                    map.put("stateColor", REDCOLOR);
                    map.put("stateColorCapitalize", REDCOLOR);
                    if (rset.getInt("RUNNINGCOUNT") > 0)
                    {
                        map.put("colorstate", 1);
                    }
                } else if (rset.getInt("TIMEOUTCOUNT") > 0)
                {
                    map.put("stateColor", "purple");
                    map.put("stateColorCapitalize", "Purple");
                } else if (rset.getInt("UTCOUNT") > 0)
                {
                    map.put("stateColor", "orange");
                    map.put("stateColorCapitalize", "Yellow");
                } else if (rset.getInt("RUNNINGCOUNT") > 0)
                {
                    map.put("stateColor", "blue");
                    map.put("stateColorCapitalize", "Blue");
                } else
                {
                    if (rset.getInt("NOTRUNCOUNT") == 0)
                    {
                        map.put("stateColor", GREENCOLOR); // finished
                        map.put("stateColorCapitalize", GREENCOLOR);
                    } else
                    {
                        if (rset.getInt("FINISHCOUNT") == 0)
                        {
                            map.put("stateColor", GRAYCOLOR);
                            map.put("stateColorCapitalize", GRAYCOLOR);
                        } else
                        {
                            map.put("stateColor", "blue");
                            map.put("stateColorCapitalize", "Blue");
                        }
                    }
                }
                res.add(map);
            }
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.getSwitchMonitorStepStatistic Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepStatistic", _log);
        }
        return res;
    }

    public List getSwitchMonitorStepStatisticForSUS ( Long iruninsid, long state, int sysType ) throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resBypkgName = new ArrayList();
        String leftStateColor = REDCOLOR;// 第一个图颜色
        String rightStateColor = REDCOLOR;// 最后一个图颜色
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List<String> pkgNameList = this.getSwitchMonitorStepPkgName(conn, iruninsid);
            for (String pkgName : pkgNameList)
            {
                Map returnMap = new HashMap();
                List res = new ArrayList();
                returnMap.put("pkgName", pkgName);
                String concatFunc = "";
                if (DBManager.Orcl_Faimily())
                { // oracle
                    concatFunc = " WM_CONCAT(IACTNAME) ";
                } else
                {
                    concatFunc = " LISTAGG(IACTNAME,',') ";
                }
                String sql = "select " + "A.ICONNER, " + concatFunc + " as ACTNAMES, "
                        + "(select ICONNERNAME from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + "   AND ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "' fetch first 1 rows only)  AS ICONNERNAME,"
                        + "(select count(decode(IPARAMETER,'主',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as MAINDC, "
                        + "(select count(decode(IPARAMETER,'备',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as PREDC,"
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=1 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as NOTRUNCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as RUNNINGCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE in (2,3) and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as FINISHCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + "  AND IPKGNAME='" + pkgName + "') as ALLCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE in (2,3)  AND IPKGNAME='" + pkgName + "') as ALLFINISHCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and IISFAIL=1 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as EXECCOUNT, " + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ISTATE=0 and IISFAIL=3 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as TIMEOUTCOUNT, " + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ISTATE=0 and IACTTYPE=2 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as UTCOUNT "
                        + "from (select ICONNER, IACTNAME  from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " AND IPKGNAME='" + pkgName + "' group by ICONNER, IACTNAME ) A group by A.ICONNER";
                ps = conn.prepareStatement(sql);
                rset = ps.executeQuery();
                while (rset.next())
                {
                    int allSer = rset.getInt("NOTRUNCOUNT") + rset.getInt("RUNNINGCOUNT") + rset.getInt("FINISHCOUNT");
                    Map map = new HashMap();
                    map.put("conner", rset.getLong("ICONNER"));
                    map.put("serverCount", allSer);
                    // 兼容旧数据 如果没有顺序步骤名称则按照旧规则显示
                    String iconnerNameString = rset.getString("ICONNERNAME");
                    if (iconnerNameString == null || "".equals(iconnerNameString))
                    {
                        iconnerNameString = rset.getString("ACTNAMES");
                    }
                    map.put("actNames", iconnerNameString);
                    map.put("notRunCount", rset.getInt("NOTRUNCOUNT"));
                    map.put("runningCount", rset.getInt("RUNNINGCOUNT"));
                    map.put("finishCount", rset.getInt("FINISHCOUNT"));
                    map.put("execCount", rset.getInt("EXECCOUNT"));
                    map.put("timeoutCount", rset.getInt("TIMEOUTCOUNT"));
                    map.put("utCount", rset.getInt("UTCOUNT"));
                    if (rset.getInt("MAINDC") > 0 && rset.getInt("PREDC") == 0)
                    {
                        map.put("dc", "maindc");
                    } else if (rset.getInt("MAINDC") == 0 && rset.getInt("PREDC") > 0)
                    {
                        map.put("dc", "predc");
                    } else
                    {
                        map.put("dc", "");
                    }
                    if (rset.getInt("EXECCOUNT") > 0)
                    {
                        map.put("stateColor", REDCOLOR);
                    } else if (rset.getInt("TIMEOUTCOUNT") > 0)
                    {
                        map.put("stateColor", "purple");
                    } else if (rset.getInt("UTCOUNT") > 0)
                    {
                        map.put("stateColor", "orange");
                    } else if (rset.getInt("RUNNINGCOUNT") > 0)
                    {
                        map.put("stateColor", "blue");
                    } else
                    {
                        if (rset.getInt("NOTRUNCOUNT") == 0)
                        {
                            map.put("stateColor", GREENCOLOR); // finished
                        } else
                        {
                            if (rset.getInt("FINISHCOUNT") == 0)
                            {
                                map.put("stateColor", GRAYCOLOR);
                            } else
                            {
                                map.put("stateColor", "blue");
                            }
                        }
                    }
                    res.add(map);
                    if (state == 3)
                    {
                        leftStateColor = "pause";
                        if (rset.getLong("ALLFINISHCOUNT") == 0)
                        {
                            rightStateColor = GRAYCOLOR;
                        } else if (rset.getLong("ALLFINISHCOUNT") > 0
                                && rset.getLong("ALLFINISHCOUNT") < rset.getLong("ALLCOUNT"))
                        {
                            rightStateColor = GRAYCOLOR;
                        } else if (rset.getLong("ALLFINISHCOUNT") >= rset.getLong("ALLCOUNT"))
                        {
                            rightStateColor = GREENCOLOR;
                        }
                    } else
                    {
                        if (rset.getLong("ALLFINISHCOUNT") == 0)
                        {
                            leftStateColor = GREENCOLOR;
                            rightStateColor = GRAYCOLOR;
                        } else if (rset.getLong("ALLFINISHCOUNT") > 0
                                && rset.getLong("ALLFINISHCOUNT") < rset.getLong("ALLCOUNT"))
                        {
                            leftStateColor = GRAYCOLOR;
                            rightStateColor = GRAYCOLOR;
                        } else if (rset.getLong("ALLFINISHCOUNT") >= rset.getLong("ALLCOUNT"))
                        {
                            leftStateColor = GRAYCOLOR;
                            rightStateColor = GREENCOLOR;
                        }
                    }
                }
                returnMap.put("resList", res);
                returnMap.put("leftStateColor", leftStateColor);
                returnMap.put("rightStateColor", rightStateColor);
                resBypkgName.add(returnMap);
            }

        } catch (Exception e)
        {
            _log.error("getSwitchMonitorStepStatisticForSUS Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepStatisticForSUS", _log);
        }
        return resBypkgName;
    }

    public List getSwitchMonitorStepStatisticForSUS ( Long iruninsid ) throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resBypkgName = new ArrayList();
        String leftStateColor = REDCOLOR;// 第一个图颜色
        String rightStateColor = REDCOLOR;// 最后一个图颜色
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMERGENCY_SWITCH);
            List<String> pkgNameList = this.getSwitchMonitorStepPkgName(conn, iruninsid);
            for (String pkgName : pkgNameList)
            {
                Map returnMap = new HashMap();
                List res = new ArrayList();
                returnMap.put("pkgName", pkgName);
                String concatFunc = "";
                if (DBManager.Orcl_Faimily())
                { // oracle
                    concatFunc = " WM_CONCAT(IACTNAME) ";
                } else
                {
                    concatFunc = " LISTAGG(IACTNAME,',') ";
                }
                String sql = "select " + "A.ICONNER, " + concatFunc + " as ACTNAMES, "
                        + "(select ICONNERNAME from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + "   AND ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "' fetch first 1 rows only)  AS ICONNERNAME,"
                        + "(select count(decode(IPARAMETER,'主',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as MAINDC, "
                        + "(select count(decode(IPARAMETER,'备',1,null)) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as PREDC,"
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=1 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as NOTRUNCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as RUNNINGCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE in (2,3) and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName + "') as FINISHCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + "  AND IPKGNAME='" + pkgName + "') as ALLCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE in (2,3)  AND IPKGNAME='" + pkgName + "') as ALLFINISHCOUNT, "
                        + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " and ISTATE=0 and IISFAIL=1 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as EXECCOUNT, " + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ISTATE=0 and IISFAIL=3 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as TIMEOUTCOUNT, " + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID="
                        + iruninsid + " and ISTATE=0 and IACTTYPE=2 and ICONNER=A.ICONNER AND IPKGNAME='" + pkgName
                        + "') as UTCOUNT "
                        + "from (select ICONNER, IACTNAME  from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                        + " AND IPKGNAME='" + pkgName + "' group by ICONNER, IACTNAME ) A group by A.ICONNER";
                ps = conn.prepareStatement(sql);
                rset = ps.executeQuery();
                while (rset.next())
                {
                    int allSer = rset.getInt("NOTRUNCOUNT") + rset.getInt("RUNNINGCOUNT") + rset.getInt("FINISHCOUNT");
                    Map map = new HashMap();
                    map.put("conner", rset.getLong("ICONNER"));
                    map.put("serverCount", allSer);
                    // 兼容旧数据 如果没有顺序步骤名称则按照旧规则显示
                    String iconnerNameString = rset.getString("ICONNERNAME");
                    if (iconnerNameString == null || "".equals(iconnerNameString))
                    {
                        iconnerNameString = rset.getString("ACTNAMES");
                    }
                    map.put("actNames", iconnerNameString);
                    map.put("notRunCount", rset.getInt("NOTRUNCOUNT"));
                    map.put("runningCount", rset.getInt("RUNNINGCOUNT"));
                    map.put("finishCount", rset.getInt("FINISHCOUNT"));
                    map.put("execCount", rset.getInt("EXECCOUNT"));
                    map.put("timeoutCount", rset.getInt("TIMEOUTCOUNT"));
                    map.put("utCount", rset.getInt("UTCOUNT"));
                    if (rset.getInt("MAINDC") > 0 && rset.getInt("PREDC") == 0)
                    {
                        map.put("dc", "maindc");
                    } else if (rset.getInt("MAINDC") == 0 && rset.getInt("PREDC") > 0)
                    {
                        map.put("dc", "predc");
                    } else
                    {
                        map.put("dc", "");
                    }
                    if (rset.getInt("EXECCOUNT") > 0)
                    {
                        map.put("stateColor", REDCOLOR);
                    } else if (rset.getInt("TIMEOUTCOUNT") > 0)
                    {
                        map.put("stateColor", "purple");
                    } else if (rset.getInt("UTCOUNT") > 0)
                    {
                        map.put("stateColor", "orange");
                    } else if (rset.getInt("RUNNINGCOUNT") > 0)
                    {
                        map.put("stateColor", "blue");
                    } else
                    {
                        if (rset.getInt("NOTRUNCOUNT") == 0)
                        {
                            map.put("stateColor", GREENCOLOR); // finished
                        } else
                        {
                            if (rset.getInt("FINISHCOUNT") == 0)
                            {
                                map.put("stateColor", GRAYCOLOR);
                            } else
                            {
                                map.put("stateColor", "blue");
                            }
                        }
                    }
                    res.add(map);
                    if (rset.getLong("ALLFINISHCOUNT") == 0)
                    {
                        leftStateColor = GREENCOLOR;
                        rightStateColor = GRAYCOLOR;
                    } else if (rset.getLong("ALLFINISHCOUNT") > 0
                            && rset.getLong("ALLFINISHCOUNT") < rset.getLong("ALLCOUNT"))
                    {
                        leftStateColor = GRAYCOLOR;
                        rightStateColor = GRAYCOLOR;
                    } else if (rset.getLong("ALLFINISHCOUNT") >= rset.getLong("ALLCOUNT"))
                    {
                        leftStateColor = GRAYCOLOR;
                        rightStateColor = GREENCOLOR;
                    }
                }
                returnMap.put("resList", res);
                returnMap.put("leftStateColor", leftStateColor);
                returnMap.put("rightStateColor", rightStateColor);
                resBypkgName.add(returnMap);
            }

        } catch (Exception e)
        {
            _log.error("getSwitchMonitorStepStatisticForSUS Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepStatisticForSUS", _log);
        }
        return resBypkgName;
    }

    /**
     * <li>Description:统计步骤状态 招商版</li>
     * <AUTHOR>
     * 2016年3月9日
     * @param iruninsid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStepStatisticForZS ( Long iruninsid, int sysType ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List res = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String concatFunc = "";
            if (DBManager.Orcl_Faimily())
            { // oracle
                concatFunc = " WM_CONCAT(IACTNAME) ";
            } else
            {
                concatFunc = " LISTAGG(IACTNAME,',') ";
            }
            String sql = "select " + "A.ICONNER,A.ICENTER, " + concatFunc + " as ACTNAMES, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=1 and ICONNER=A.ICONNER) as NOTRUNCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=0 and ICONNER=A.ICONNER) as RUNNINGCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE in (2,3) and ICONNER=A.ICONNER) as FINISHCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and IISFAIL in (1,2) and ICONNER=A.ICONNER) as EXECCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and IISFAIL in (3,4) and ICONNER=A.ICONNER) as TIMEOUTCOUNT, "
                    + "(select count(*) from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " and ISTATE=0 and IACTTYPE=2 and ICONNER=A.ICONNER) as UTCOUNT "
                    + "from (select ICONNER, IACTNAME,ICENTER from IEAI_RUNINFO_INSTANCE where IRUNINSID=" + iruninsid
                    + " group by ICONNER, IACTNAME, ICENTER) A group by A.ICONNER,A.ICENTER";
            ps = conn.prepareStatement(sql);
            SqlLogUtil.debugSqlLog(CLASSNAME, method, sql, new Object[] {}, sysType);
            rset = ps.executeQuery();
            while (rset.next())
            {
                int allSer = rset.getInt("NOTRUNCOUNT") + rset.getInt("RUNNINGCOUNT") + rset.getInt("FINISHCOUNT");
                Map map = new HashMap();
                map.put("icenter", rset.getString("ICENTER") == null ? "" : rset.getString("ICENTER"));
                map.put("conner", rset.getLong("ICONNER"));
                map.put("serverCount", allSer);
                map.put("actNames", rset.getString("ACTNAMES"));
                map.put("notRunCount", rset.getInt("NOTRUNCOUNT"));
                map.put("runningCount", rset.getInt("RUNNINGCOUNT"));
                map.put("finishCount", rset.getInt("FINISHCOUNT"));
                map.put("execCount", rset.getInt("EXECCOUNT"));
                map.put("timeoutCount", rset.getInt("TIMEOUTCOUNT"));
                map.put("utCount", rset.getInt("UTCOUNT"));
                if (rset.getString("ICENTER") == null || "".equals(rset.getString("ICENTER")))
                {
                    map.put("existcenter", 3);
                    map.put("centertype", "Point");
                } else
                {
                    map.put("existcenter", 2);
                    map.put("centertype", "Circle");
                }
                if (rset.getInt("EXECCOUNT") > 0)
                {
                    map.put("stateColor", REDCOLOR);
                    if (rset.getInt("RUNNINGCOUNT") > 0)
                    {
                        map.put("colorstate", 1);
                    }
                } else if (rset.getInt("TIMEOUTCOUNT") > 0)
                {
                    map.put("stateColor", "purple");
                } else if (rset.getInt("UTCOUNT") > 0)
                {
                    map.put("stateColor", "orange");
                } else if (rset.getInt("RUNNINGCOUNT") > 0)
                {
                    map.put("stateColor", "blue");
                } else
                {
                    if (rset.getInt("NOTRUNCOUNT") == 0)
                    {
                        map.put("stateColor", GREENCOLOR); // finished
                    } else
                    {
                        if (rset.getInt("FINISHCOUNT") == 0)
                        {
                            map.put("stateColor", GRAYCOLOR);
                        } else
                        {
                            map.put("stateColor", "blue");
                        }
                    }
                }
                res.add(map);
            }
        } catch (Exception e)
        {
            _log.error("getSwitchMonitorStepStatisticForZS Error", e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepStatisticForZS", _log);
        }
        return res;
    }

    /**
     * <li>Description:</li>
     * <AUTHOR>
     * 2016年1月26日
     * @param instanceId
     * @param step
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStepForGraph ( Long instanceId, Integer step, int sysType ) throws Exception
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {

            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "select b.IID,b.iflowid,a.ISYSNAME,a.IRUNINSNAME,b.iip,b.ISERNER,b.iprener,a.ISWITCHTO,b.ICONNER,b.IACTNAME,b.IACTTYPE,b.IACTDES,b.ISHELLPATH,b.iparameter,b.ISTARTTIME,b.IENDTIME,b.ISTATE,b.IISFAIL,b.IACTTYPE,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8) as mysysdate,b.irerunflag,b.ICENTER,b.ICONNERNAME from  IEAI_RUN_INSTANCE a,IEAI_RUNINFO_INSTANCE b where a.IID=b.IRUNINSID and a.IID=? AND ICONNER=? order by b.ISERNER,b.IRERUNFLAG";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceId);
            ps.setInt(2, step);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put("iflowid", rset.getLong("iflowid"));
                map.put("isysname", rset.getString("isysname"));
                map.put("iruninsname", rset.getString("iruninsname"));
                map.put("iip", rset.getString("iip"));
                map.put("iprener", rset.getString("iprener"));
                map.put("iserner", rset.getDouble("iserner"));
                map.put("iswitchto", rset.getString("iswitchto"));
                map.put("iconner", rset.getLong("iconner"));
                map.put("iactname", rset.getString("iactname"));
                map.put("iactdes", rset.getString("iactdes"));
                map.put("ishellpath", rset.getString("ishellpath") + " "
                        + (rset.getString("iparameter") == null ? "" : rset.getString("iparameter")));
                map.put("istarttime",
                    rset.getLong("istarttime") == 0 ? "" : sdf.format(new Date(rset.getLong("istarttime"))));
                map.put("iendtime",
                    rset.getLong("iendtime") == 0 ? "" : sdf.format(new Date(rset.getLong("iendtime"))));
                if (rset.getLong("istate") == 0)
                {
                    map.put("iruntime", (rset.getLong("mysysdate") - rset.getLong("istarttime")) / 1000);
                } else
                {
                    map.put("iruntime", (rset.getLong("iendtime") - rset.getLong("istarttime")) / 1000);
                }
                map.put("istate", rset.getLong("istate"));
                map.put("iisfail", rset.getLong("iisfail"));
                map.put("iacttype", rset.getLong("iacttype"));
                map.put("irerunflag", rset.getLong("irerunflag"));
                map.put("icenter", rset.getString("ICENTER") == null ? "" : rset.getString("ICENTER"));
                map.put("iconnername", rset.getString("ICONNERNAME"));
                String stateColor = GRAYCOLOR;
                if (rset.getLong("iisfail") == 0 || rset.getLong("iisfail") == -1)
                {
                    if (rset.getLong("istate") == 0)
                    {
                        if (rset.getLong("iacttype") == 2)
                        { // 人工类型 显示黄色
                            stateColor = "Yellow";
                        } else
                        {
                            stateColor = "Blue";
                        }
                    } else if (rset.getLong("istate") == 1)
                    {
                        stateColor = GRAYCOLOR;
                    } else if (rset.getLong("istate") == 2)
                    {
                        stateColor = GREENCOLOR;
                    } else if (rset.getLong("istate") == 3)
                    {
                        stateColor = GRAYCOLOR;
                    }
                } else if (rset.getLong("iisfail") == 1 || rset.getLong("iisfail") == 2)
                {
                    stateColor = REDCOLOR;
                } else if (rset.getLong("iisfail") == 3 || rset.getLong("iisfail") == 4)
                {
                    stateColor = "Purple";
                }
                map.put("stateColor", stateColor);

                String execState = "";
                if (rset.getLong("istate") == 0)
                {
                    execState = "运行中";
                } else if (rset.getLong("istate") == 1)
                {
                    execState = "未开始";
                } else if (rset.getLong("istate") == 2)
                {
                    execState = "已完成";
                } else if (rset.getLong("istate") == 3)
                {
                    execState = "已跳过";
                } else if (rset.getLong("istate") == 4)
                {
                    execState = "暂停";
                }
                map.put("execState", execState); // 执行状态

                long istate = rset.getLong("istate");
                long isfail = rset.getLong("iisfail");
                String execResult = "";
                if (istate == 2 || istate == 3)
                {
                    if (isfail == 1 || isfail == 2)
                    {
                        execResult = "错误";
                    } else if (isfail == 3 || isfail == 4)
                    {
                        execResult = "超时";
                    } else
                    {
                        if (istate == 2)
                        {
                            execResult = "成功";
                        }
                    }
                } else if (istate == 0)
                {
                    if (isfail == 1 || isfail == 2)
                    {
                        execResult = "错误";
                    } else if (isfail == 3 || isfail == 4)
                    {
                        execResult = "超时";
                    }
                } else if (istate == 4)
                { // 暂停
                    if (isfail == 1 || isfail == 2)
                    {
                        execResult = "错误";
                    } else if (isfail == 3 || isfail == 4)
                    {
                        execResult = "超时";
                    }
                } else
                {
                    execResult = "";
                }
                map.put("execResult", execResult); // 执行结果
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("getSwitchMonitorStepForGraph is error : " + e.getMessage(), e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getSwitchMonitorStepForGraph", _log);
        }
        return resultList;
    }

    public void setTimeOut () throws Exception
    {
        Connection conn = null;
        PreparedStatement actStat = null;
        String sql = "UPDATE ieai_runinfo_instance ar SET ar.istate=3,ar.iisfail=3,ar.iendtime=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8) WHERE ar.IRUNINSID in (select a.iid from ieai_run_instance a where a.ISTATE=0) and ar.istate=0 AND ar.itimeout!=0 AND ar.itimeout is not null AND (FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)-ar.istarttime)>ar.itimeout*1000";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMERGENCY_SWITCH);
            actStat = conn.prepareStatement(sql);
            actStat.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            _log.error("setTimeOut Error : " + e.getMessage(), e);
            DBResource.rollback(conn, Constants.IEAI_EMERGENCY_SWITCH, e, "setTimeOut", _log);
        } finally
        {
            DBResource.closePSConn(conn, actStat, "setTimeOut", _log);
        }
    }

    public void instanceTimeOut () throws Exception
    {
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement psTimeout = null;
        PreparedStatement instanceStat = null;
        PreparedStatement actStat = null;
        ResultSet rs = null;
        ResultSet rsTimeout = null;
        long timeout = 0;
        String timeoutparamsql = "select IPARAVALUE from ieai_parameter_config where iid=3";
        String timeoutidsql = "select a.iid from ieai_run_instance a where a.istate = 0 and (FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ", 8) - a.istarttime) > ? for update";
        String sql = "UPDATE ieai_run_instance run SET run.istate=2, run.iendtime=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8) WHERE run.iid = ?";
        String sqlact = "UPDATE ieai_runinfo_instance ar SET ar.istate=3, ar.iisfail=3, ar.iendtime=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ", 8) WHERE ar.IRUNINSID = ? AND ar.istate=0";

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMERGENCY_SWITCH);
            psTimeout = conn.prepareStatement(timeoutparamsql);
            rsTimeout = psTimeout.executeQuery();
            while (rsTimeout.next())
            {
                timeout = rsTimeout.getLong("IPARAVALUE") * 1000;
            }

            if (timeout > 0)
            {
                ps = conn.prepareStatement(timeoutidsql);
                ps.setLong(1, timeout);
                rs = ps.executeQuery();
                while (rs.next())
                {

                    instanceStat = conn.prepareStatement(sql);
                    instanceStat.setLong(1, rs.getLong("iid"));
                    instanceStat.executeUpdate();

                    actStat = conn.prepareStatement(sqlact);
                    actStat.setLong(1, rs.getLong("iid"));
                    actStat.executeUpdate();
                }
            }

            conn.commit();
        } catch (Exception e)
        {
            _log.error("SwitchMonitorManage.instanceTimeOut Error", e);
            conn.rollback();
        } finally
        {
            try
            {
                if (rsTimeout != null)
                {
                    rsTimeout.close();
                }
                if (rs != null)
                {
                    rs.close();
                }
                if (psTimeout != null)
                {
                    psTimeout.close();
                }
                if (ps != null)
                {
                    ps.close();
                }
                if (instanceStat != null)
                {
                    instanceStat.close();
                }
                if (actStat != null)
                {
                    actStat.close();
                }
                if (conn != null)
                {
                    conn.close();
                }
            } catch (SQLException e)
            {
                _log.error("Close Connection Of SwitchMonitorManage.instanceTimeOut Error", e);
            }
        }
    }

    /**
     *
     * @Title: getSwitchMonitorStepPkgName
     * @Description: 【变更管理】获取报名列表
     * @param conn
     * @param iruninsid
     * @return
     * @throws Exception
     * @return List 返回类型
     * @throws
     * @变更记录 2016年4月26日 yunpeng_zhang
     */
    public List<String> getSwitchMonitorStepPkgName ( Connection conn, Long iruninsid ) throws Exception
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> res = new ArrayList<String>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "SELECT  MIN(ICONNER) AS IC ,T.IPKGNAME FROM IEAI_RUNINFO_INSTANCE T WHERE T.IRUNINSID="
                            + iruninsid + " GROUP BY T.IPKGNAME  ORDER BY IC ASC";
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        res.add(rs.getString("IPKGNAME"));
                    }
                } catch (SQLException e)
                {
                    _log.error("getSwitchMonitorStepPkgName method of SwitchMonitorManage.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getSwitchMonitorStepPkgName", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getSwitchMonitorStepPkgName method of SwitchMonitorManage.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return res;
    }

    /**
     * <li>Description:急应单号状态</li>
     * <AUTHOR>
     * 2017年4月27日
     * @param conn
     * @param taskId
     * @return
     * @throws Exception
     * return boolean  true： 未审核/上传  . false:已审核  /未上传
     */
    public boolean getIsExamineEm ( Connection conn, String taskId, int taskstatus ) throws Exception
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "SELECT COUNT(ID) AS CNUM FROM IEAI_ITSM_EM WHERE TASKID=? AND TASKSTATUS =?";
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, taskId);
                    ps.setInt(2, taskstatus);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {

                        count = rs.getInt("CNUM");
                    }
                    if (count > 0)
                    {
                        flag = true;
                    }
                } catch (SQLException e)
                {
                    _log.error("getIsExamineEm method of SwitchMonitorManage.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getIsExamineEm", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getIsExamineEm method of IcMonitorManage.class :" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return flag;
    }

    /**
     * <li>Description:</li>
     * <AUTHOR>
     * 2017年7月5日
     * @param conn
     * @param taskId
     * @param taskstatus
     * @param crtUser
     * @param userId
     * @param remark
     * @param sysName
     * @param taskFrom 1 itsm  2 平台上传
     * @return
     * @throws Exception
     * return boolean
     */
    public boolean saveExamineEm ( Connection conn, String taskId, int taskstatus, String crtUser, Long userId,
            String remark, String sysName, int taskFrom ) throws Exception
    {

        PreparedStatement ps = null;
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "  INSERT INTO   IEAI_ITSM_EM (ID,TASKID ,TASKSTATUS,CRTUSER,CRTUSERID ,REMARK,ISYSNAME ,TASKFROM)  VALUES(?,?,?,?,?,?,?,?)";
                    Long id = IdGenerator.createId("IEAI_ITSM_EM", conn);
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, id);
                    ps.setString(2, taskId);
                    ps.setInt(3, taskstatus);

                    ps.setString(4, crtUser);
                    ps.setLong(5, userId);
                    ps.setString(6, remark);
                    ps.setString(7, sysName);
                    ps.setInt(8, taskFrom);
                    ps.executeUpdate();
                    flag = true;
                } catch (SQLException e)
                {
                    _log.error("saveExamineEm method of IcMonitorManage.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePreparedStatement(ps, "saveExamineEm", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("saveExamineEm method of IcMonitorManage.class :" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return flag;
    }

    /**
     * <li>Description: 修改应急操作 ITSM 审核状态</li>
     * <AUTHOR>
     * 2017年4月27日
     * @param conn
     * @param taskId
     * @return
     * @throws RepositoryException
     * @throws Exception
     * return boolean
     */
    public boolean updateExamineEm ( Connection conn, String taskId, int taskstatus ) throws RepositoryException
    {

        PreparedStatement ps = null;
        boolean flag = false;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = " UPDATE IEAI_ITSM_EM SET TASKSTATUS =? WHERE  TASKID =?";
                    ps = conn.prepareStatement(sql);
                    ps.setInt(1, taskstatus);
                    ps.setString(2, taskId);
                    int result = ps.executeUpdate();
                    if (result > 0)
                    {
                        flag = true;
                    }
                } catch (SQLException e)
                {
                    _log.error("saveExamineEm method of IcMonitorManage.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePreparedStatement(ps, "saveExamineEm", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("saveExamineEm method of IcMonitorManage.class :" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return flag;
    }

    /*
     * public boolean deleteExamineEm ( Connection conn, String taskId ) throws Exception
     * {
     * 
     * PreparedStatement ps = null;
     * // PreparedStatement ps1 = null;
     * // PreparedStatement ps2 = null;
     * // PreparedStatement ps3 = null;
     * boolean flag = false;
     * for (int i = 0; i < 10; i++)
     * {
     * try
     * {
     * try
     * {
     * // conn=DBResource.getConnection("asdfasdf", _log, 0);
     * String sql = "DELETE FROM IEAI_ITSM_EM  WHERE TASKID=?";
     * 
     * String
     * sql_del_pro=" DELETE  FROM IEAI_PROJECT WHERE INAME IN ( SELECT   DISTINCT  ISYSNAME FROM IEAI_ITSM_EM WHERE TASKID=? ) AND PROTYPE=6 "
     * ;
     * //-- delete IEAI_INSTANCE_VERSION
     * String
     * sql_del_instverion=" DELETE FROM IEAI_INSTANCE_VERSION WHERE  IUPPERID IN((SELECT  DISTINCT IUPPERID FROM IEAI_PROJECT WHERE INAME IN(SELECT   DISTINCT  ISYSNAME FROM IEAI_ITSM_EM WHERE TASKID=?)))"
     * ;
     * //-- delete IEAI_INSTANCEINFO
     * String
     * sql_del_insinfo=" DELETE FROM IEAI_INSTANCEINFO WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION WHERE IUPPERID IN (SELECT DISTINCT IUPPERID FROM IEAI_PROJECT WHERE INAME IN (SELECT   DISTINCT  ISYSNAME FROM IEAI_ITSM_EM WHERE TASKID=?) AND PROTYPE=6))"
     * ;
     *
     * ps3 = conn.prepareStatement(sql_del_insinfo);
     * ps3.setString(1, taskId);
     * ps3.executeUpdate();
     *
     * ps2 = conn.prepareStatement(sql_del_instverion);
     * ps2.setString(1, taskId);
     * ps2.executeUpdate();
     *
     * ps1 = conn.prepareStatement(sql_del_pro);
     * ps1.setString(1, taskId);
     * ps1.executeUpdate();
     * 
     * //
     * //
     * 
     * ps = conn.prepareStatement(sql);
     * ps.setString(1, taskId);
     * ps.executeUpdate();
     * flag = true;
     * } catch (SQLException e)
     * {
     * _log.error("deleteExamineEm method of IcMonitorManage.class SQLException:" + e.getMessage());
     * throw new RepositoryException(ServerError.ERR_DB_QUERY);
     * } finally
     * {
     * DBResource.closePreparedStatement(ps, "deleteExamineEm", _log);
     * 
     * DBResource.closePreparedStatement(ps1, "deleteExamineEm", _log);
     * DBResource.closePreparedStatement(ps2, "deleteExamineEm", _log);
     * DBResource.closePreparedStatement(ps3, "deleteExamineEm", _log);
     * 
     * }
     * break;
     * } catch (RepositoryException ex)
     * {
     * _log.error("deleteExamineEm method of IcMonitorManage.class :" + ex.getMessage());
     * DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
     * }
     * }
     * 
     * return flag;
     * }
     */

    /**
     * <li>Description:获取未审核的 应急操作系统列表</li>
     * <AUTHOR>
     * 2017年4月28日
     * @param conn
     * @param taskId
     * @param taskstatus
     * @return
     * @throws Exception
     * return boolean
     */
    public List<String> getExamineedEmList ( Connection conn, int taskstatus ) throws Exception
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> emList = new ArrayList<String>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "SELECT TASKID,ISYSNAME FROM IEAI_ITSM_EM WHERE TASKSTATUS=?";
                    ps = conn.prepareStatement(sql);
                    ps.setInt(1, taskstatus);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        emList.add(rs.getString("ISYSNAME"));
                    }
                } catch (SQLException e)
                {
                    _log.error("getExamineedEmList method of IcMonitorManage.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getExamineedEmList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getExamineedEmList method of IcMonitorManage.class :" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return emList;
    }

    public List<String> getEmDoublePerson ( Connection conn, int type ) throws Exception
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> emList = new ArrayList<String>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "SELECT DISTINCT T2.ICOLVALUE AS ISYSNAME  FROM IEAI_DOUBLECHECK_WORKITEM T , IEAI_DOUBLECHECK_COLVALUE T2 WHERE  (( T.ISTATE in (0,2) OR   ( T.ISTATE IN (1,4))) AND T.IITEMTYPE=?)"
                            + " AND T.IID=T2.IWORKITEMID AND T2.ICOLHEADER='isysName'";
                    ps = conn.prepareStatement(sql);
                    ps.setInt(1, type);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        emList.add(rs.getString("ISYSNAME"));
                    }
                } catch (SQLException e)
                {
                    _log.error("getEmDoublePerson method of IcMonitorManage.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getEmDoublePerson", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getEmDoublePerson method of IcMonitorManage.class :" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return emList;
    }
}

package com.ideal.ieai.server.ieaikernel.kernel.registry;

import org.apache.log4j.Logger;

import java.util.List;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.calendar.CalendarInfo;
import com.ideal.ieai.commons.calendar.HolidayInfo;
import com.ideal.ieai.commons.calendar.SpecialWorkDayInfo;
import com.ideal.ieai.commons.calendar.WeeklyDayInfo;
import com.ideal.ieai.commons.calendar.WorkingTimeInfo;
import com.ideal.ieai.communication.marshall.Request;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.communication.marshall.transfer.interfaces.APIService;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ParameterHelper;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.PermissionCheckHelper;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ResponseHelper;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.calendar.CalendarManager;
import com.ideal.ieai.server.repository.calendar.ICalendarManager;

/**
 * <p>
 * Title:IEAI SERVER
 * </p>
 * <p>
 * Description: for calendar provider
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003, 2004
 * </p>
 * <p>
 * Company: Ideal Technologies Inc.
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */
public class CalendarProvider extends Provider
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger.getLogger(CalendarProvider.class);

    public CalendarProvider(String ServName, ICalendarManager Mgr)
    {
        super(ServName);
        _mgr = Mgr;
    }

    /**
     * execute request service and return a response include object or error
     * 
     * @param req
     * @return
     */
    public Response executeService ( Request req ) throws ServerException
    {
        /**
         * to check user's permission of calling calendar api
         */
        try
        {
            PermissionCheckHelper.checkUserType(req.getSessionId(), PermissionCheckHelper.UA_USER);
        } catch (ServerException e)
        {
            try
            {
                PermissionCheckHelper.checkUserType(req.getSessionId(), PermissionCheckHelper.COMMON_USER);
            } catch (ServerException ex)
            {
                return ResponseHelper.getErrorResponse(ex.getErrCode());
            }
        }

        try
        {
            String reqName = req.getRequestName();
            // create calendar
            if (reqName.equals(APIService.S_CREATE_CAL))
            {
                return this.createCalendar(req);
            }
            // create holiday
            else if (reqName.equals(APIService.S_CREATE_HOLIDAY))
            {
                return this.createHoliday(req);
            }
            // create special workday
            else if (reqName.equals(APIService.S_CREATE_SPECIALDAY))
            {
                return this.createSpecialWorkday(req);
            }
            // create weelyday
            else if (reqName.equals(APIService.S_CREATE_WEEKLYDAY))
            {
                return this.createWeeklyday(req);
            }
            // create worktime
            else if (reqName.equals(APIService.S_CREATE_WORKTIME))
            {
                return this.createWorktime(req);
            } else if (reqName.equals(APIService.S_GET_ALL_CALENDAR))
            {
                return this.getAllCalendar(req);
            }
            // get default calendar
            else if (reqName.equals(APIService.S_GET_DEFAULT_CAL))
            {
                return this.getDefaultCalendar(req);
            }
            // get a calendar
            else if (reqName.equals(APIService.S_GET_CALENDAR))
            {
                return this.getCalendar(req);
            }
            // get calendar by calendar name
            else if (reqName.equals(APIService.S_GET_CALENDAR_BY_NAME))
            {
                return this.getCalendarByName(req);
            }
            // get holiday
            else if (reqName.equals(APIService.S_GET_HOLIDAY))
            {
                return this.getHoliday(req);
            } else if (reqName.equals(APIService.S_GET_SPECIALDAY))
            {
                return this.getSpecialWorkDay(req);
            }
            // get weeekly day
            else if (reqName.equals(APIService.S_GET_WEEKLYDAY))
            {
                return this.getWeeklyDay(req);
            } else if (reqName.equals(APIService.S_GET_WORKTIME))
            {
                return this.getWorkingTime(req);
            }
            // remove calendar
            else if (reqName.equals(APIService.S_REMOVE_CAL))
            {
                return this.removeCalendar(req);
            }
            // remove calendars
            else if (reqName.equals(APIService.S_REMOVE_CALS))
            {
                return this.removeCalendars(req);
            } else if (reqName.equals(APIService.S_REMOVE_HOLIDAY))
            {
                return this.removeHoliday(req);
            } else if (reqName.equals(APIService.S_REMOVE_HOLIDAYS))
            {
                return this.removeHolidays(req);
            }
            // remove special work day
            else if (reqName.equals(APIService.S_REMOVE_SPECIALDAY))
            {
                return this.removeSpecialWorkDay(req);
            } else if (reqName.equals(APIService.S_REMOVE_SPECIALDAYS))
            {
                return this.removeSpecialWorkDays(req);
            } else if (reqName.equals(APIService.S_REMOVE_WEEKLYDAY))
            {
                return this.removeWeeklyDay(req);
            } else if (reqName.equals(APIService.S_REMOVE_WEEKLYDAYS))
            {
                return this.removeWeeklyDays(req);
            } else if (reqName.equals(APIService.S_REMOVE_WORKTIME))
            {
                return this.removeWorkingTime(req);
            } else if (reqName.equals(APIService.S_REMOVE_WORKTIMES))
            {
                return this.removeWorkingTimes(req);
            }
            // update calendar
            else if (reqName.equals(APIService.S_UPDATE_CAL))
            {
                return this.updateCalendar(req);
            }
            // update holiday
            else if (reqName.equals(APIService.S_UPDATE_HOLIDAY))
            {
                return this.updateHoliday(req);
            }
            // update special workday
            else if (reqName.equals(APIService.S_UPDATE_SPECIALDAY))
            {
                return this.updateSpecialWorkDay(req);
            }
            // update weekly day
            else if (reqName.equals(APIService.S_UPDATE_WEEKLYDAY))
            {
                return this.updateWeeklyDay(req);
            }
            // update work time
            else if (reqName.equals(APIService.S_UPDATE_WORKTIME))
            {
                return this.updateWorkingTime(req);
            } else
            {
                return ResponseHelper.getErrorResponse(new ServerError(ServerError.ERR_NOT_SURPPORT_METHOD));
            }
        } catch (ClassCastException Ex)
        {
            // must be using an invalid class casting when getting parameters
            // report error here
            _log.info(Ex.getMessage(), Ex);
            return new Response(ServerError.ERR_INV_PARAM_TYPE);
        }
    }

    /**
     * create calenar
     * 
     * @param req Request
     * @throws ServerException
     * @return Response calendar id
     */
    private Response createCalendar ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("service of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }

        Object obj = ParameterHelper.getObjectParameter(req, APIService.P_CAL_CAL);
        CalendarInfo calendar = null == obj ? null : (CalendarInfo) obj;
        int type = ParameterHelper.getIntParameter(req, APIService.P_IS_PROJ_ADP_EXISTED_TYPE);// 前台传过来的数据库类型
        try
        {
            _log.info("create a new calendar");

            // user.getPassword()
            return ResponseHelper.getReturnValueResponse(new Long(_mgr.createCalendar(calendar, type)));
        } catch (RepositoryException re)
        {
            _log.info("create a new calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(re.getServerError());
        }
    }

    /**
     * create holiday info
     * 
     * @param req Request
     * @throws ServerException
     * @return Response holiday info id
     */
    private Response createHoliday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("service of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }

        Object obj = ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAY);
        HolidayInfo holiday = null == obj ? null : (HolidayInfo) obj;
        int type = ParameterHelper.getIntParameter(req, APIService.P_IS_PROJ_ADP_EXISTED_TYPE);// 前台传过来的数据库类型
        try
        {
            _log.info("create a new calendar");

            long id = _mgr.createHoliday(holiday, type);
            return ResponseHelper.getReturnValueResponse(new Long(id));
        } catch (RepositoryException re)
        {
            _log.info("create a new calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(re.getServerError());
        }
    }

    /**
     * create special work day info
     * 
     * @param req Request
     * @throws ServerException
     * @return Response specialworkdayinfo id
     */
    private Response createSpecialWorkday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("service of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }

        Object obj = ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIAL);
        SpecialWorkDayInfo info = null == obj ? null : (SpecialWorkDayInfo) obj;
        try
        {
            _log.info("create a new calendar");

            long id = _mgr.createSpecialWorkDay(info);
            return ResponseHelper.getReturnValueResponse(new Long(id));
        } catch (RepositoryException re)
        {
            _log.info("create a new calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(re.getServerError());
        }
    }

    /**
     * create weelydayinfo
     * 
     * @param req Request
     * @throws ServerException
     * @return Response weeklyinfo id
     */
    private Response createWeeklyday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("service of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }

        Object obj = ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAY);
        WeeklyDayInfo info = null == obj ? null : (WeeklyDayInfo) obj;
        try
        {
            _log.info("create a new calendar");

            long id = _mgr.createWeeklyDay(info);
            return ResponseHelper.getReturnValueResponse(new Long(id));
        } catch (RepositoryException re)
        {
            _log.info("create a new calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(re.getServerError());
        }
    }

    /**
     * create worktimeinfo
     * 
     * @param req Request
     * @throws ServerException
     * @return Response worktimeinfo id
     */
    private Response createWorktime ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("service of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }

        Object obj = ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIME);
        WorkingTimeInfo info = null == obj ? null : (WorkingTimeInfo) obj;
        try
        {
            _log.info("create a new calendar");

            long id = _mgr.createWorkingTime(info);
            return ResponseHelper.getReturnValueResponse(new Long(id));
        } catch (RepositoryException re)
        {
            _log.info("create a new calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(re.getServerError());
        }
    }

    /**
     * get all calendar
     * 
     * @param req Request
     * @throws ServerException
     * @return Response list every element is calendarinfo
     */
    private Response getAllCalendar ( Request req ) throws ServerException
    {
        // if (req.getParamNum() != 1)
        // {
        // _log.error("server of " + req.getRequestName() + " params number error !");
        // return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        // }
        try
        {
            String curPage = (String) (req.getParameterValue("curPage"));
            String pageSize = (String) (req.getParameterValue("pageSize"));
            if ("-1" == curPage || null == curPage || "".equals(curPage))
            {
                return ResponseHelper.getReturnValueResponse(_mgr.getAllCalendar());

            } else
            {
                return ResponseHelper.getReturnValueResponse(CalendarManager.getInstance().getAllCalendarForPaging(curPage, pageSize));
            }
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get a calendar
     * 
     * @param req Request
     * @throws ServerException
     * @return Response a calendarinfo
     */
    private Response getCalendar ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_CALID);
        try
        {
            return ResponseHelper.getReturnValueResponse(_mgr.getCalendar(id.longValue()));
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get defaultCalendar
     * 
     * @param req Request
     * @throws ServerException
     * @return Response defaultcalendarinfo ,if not have ,return null
     */
    private Response getDefaultCalendar ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 1)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        try
        {
            return ResponseHelper.getReturnValueResponse(_mgr.getDefaultCalendar());
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get calendar by name
     * 
     * @param req Request
     * @throws ServerException
     * @return Response a calendarinfo
     */
    private Response getCalendarByName ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        String name = (String) ParameterHelper.getObjectParameter(req, APIService.P_CAL_NAME);
        try
        {
            return ResponseHelper.getReturnValueResponse(_mgr.getCalendar(name));
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get holiday
     * 
     * @param req Request
     * @throws ServerException
     * @return Response a holidayinfo
     */
    private Response getHoliday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAYID);
        try
        {
            HolidayInfo info = _mgr.getHoliday(id.longValue());
            return ResponseHelper.getReturnValueResponse(info);
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get specialWorkday
     * 
     * @param req Request
     * @throws ServerException
     * @return Response specialWorkDayInfo
     */
    private Response getSpecialWorkDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIALID);
        try
        {
            SpecialWorkDayInfo info = _mgr.getSpecialWorkDay(id.longValue());
            return ResponseHelper.getReturnValueResponse(info);
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    /**
     * get weeklyDay
     * 
     * @param req Request
     * @throws ServerException
     * @return Response weeklyDayInfo
     */
    private Response getWeeklyDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAYID);
        try
        {
            WeeklyDayInfo info = _mgr.getWeeklyDay(id.longValue());
            return ResponseHelper.getReturnValueResponse(info);
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response getWorkingTime ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIMEID);
        try
        {
            WorkingTimeInfo info = _mgr.getWorkingTime(id.longValue());
            return ResponseHelper.getReturnValueResponse(info);
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeCalendar ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_CALID);
        try
        {
            _mgr.removeCalendar(id.longValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeCalendars ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        List ids = (List) ParameterHelper.getObjectParameter(req, APIService.P_CAL_CALIDS);
        try
        {
            _mgr.removeCalendars(ids);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeHoliday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAYID);
        try
        {
            _mgr.removeHoliday(id.longValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeHolidays ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        List ids = (List) ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAYIDS);
        try
        {
            _mgr.removeHolidays(ids);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeSpecialWorkDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIALID);
        try
        {
            _mgr.removeSpecialWorkDay(id.longValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeSpecialWorkDays ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        List ids = (List) ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIALIDS);
        try
        {
            _mgr.removeSpecialWorkDays(ids);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeWeeklyDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAYID);
        try
        {
            _mgr.removeWeeklyDay(id.longValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeWeeklyDays ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        List ids = (List) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAYIDS);
        try
        {
            _mgr.removeWeeklyDays(ids);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeWorkingTime ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIMEID);
        try
        {
            _mgr.removeWorkingTime(id.longValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response removeWorkingTimes ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 2)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        List ids = (List) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIMEIDS);
        try
        {
            _mgr.removeWorkingTimes(ids);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response updateCalendar ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_CALID);
        CalendarInfo cal = (CalendarInfo) ParameterHelper.getObjectParameter(req, APIService.P_CAL_CAL);
        try
        {
            // update the calendar
            _mgr.updateCalendar(id.longValue(), cal);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("remove calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }

    }

    private Response updateHoliday ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAYID);
        HolidayInfo info = (HolidayInfo) ParameterHelper.getObjectParameter(req, APIService.P_CAL_HOLIDAY);
        try
        {
            _mgr.updateHoliday(id.longValue(), info);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response updateSpecialWorkDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIALID);
        SpecialWorkDayInfo info = (SpecialWorkDayInfo) ParameterHelper.getObjectParameter(req, APIService.P_CAL_SPECIAL);
        try
        {
            _mgr.updateSpecialWorkDay(id.longValue(), info);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response updateWeeklyDay ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 3)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAYID);
        Boolean isWorkDay = (Boolean) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WEEKLYDAY);
        try
        {
            _mgr.updateWeeklyDay(id.longValue(), isWorkDay.booleanValue());
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    private Response updateWorkingTime ( Request req ) throws ServerException
    {
        if (req.getParamNum() != 4)
        {
            _log.error("server of " + req.getRequestName() + " params number error !");
            return ResponseHelper.getErrorResponse(ServerError.ERR_ILLEGAL_ARGU);
        }
        Long id = (Long) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIMEID);
        WorkingTimeInfo info = (WorkingTimeInfo) ParameterHelper.getObjectParameter(req, APIService.P_CAL_WORKTIME);
        int type = ParameterHelper.getIntParameter(req, APIService.P_IS_PROJ_ADP_EXISTED_TYPE);// 前台传过来的数据库类型
        try
        {
            _mgr.updateWorkingTime(id.longValue(), info, type);
            return ResponseHelper.getNoErrorResponse();
        } catch (RepositoryException ex)
        {
            _log.info("get all calendar failed");
            // error when save failed
            return ResponseHelper.getErrorResponse(ex.getServerError());
        }
    }

    ICalendarManager _mgr = CalendarManager.getInstance();
}

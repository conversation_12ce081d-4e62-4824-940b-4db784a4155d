package com.ideal.ieai.server.platform.repository.equipchar;

import java.util.List;

public class EquipCharModel
{
    private long                     iid;
    // 厂商
    private String                   vendor;
    // 设备类型
    private String                   equipType;
    // 成功结束符
    private String                   successChar;
    // 可忽略提示符
    private String                   ignoreChar;
    // 特殊操作
    private String                   specialChar;
    // 结束符变更命令 / 结束字符
    private String                   endChar;
    // 非配置命令
    private String                   noConfigChar;
    // 错误命令
    private String                   errorChar;

    private int                      type;
    private boolean                  isForm = false;

    private List<EquipCharFormModel> noConfigList;
    private String successList;
    private List<EquipCharFormModel> endList;
    private List<EquipCharFormModel> specialList;
    private List<EquipCharFormModel> errorList;

    public List<EquipCharFormModel> getSpecialList() {
        return specialList;
    }

    public void setSpecialList(List<EquipCharFormModel> specialList) {
        this.specialList = specialList;
    }

    public List<EquipCharFormModel> getNoConfigList ()
    {
        return noConfigList;
    }

    public void setNoConfigList ( List<EquipCharFormModel> noConfigList )
    {
        this.noConfigList = noConfigList;
    }

  
    public String getSuccessList ()
    {
        return successList;
    }

    public void setSuccessList ( String successList )
    {
        this.successList = successList;
    }

    public List<EquipCharFormModel> getEndList ()
    {
        return endList;
    }

    public void setEndList ( List<EquipCharFormModel> endList )
    {
        this.endList = endList;
    }

    public int getType ()
    {
        return type;
    }

    public void setType ( int type )
    {
        this.type = type;
    }

    public boolean isForm ()
    {
        return isForm;
    }

    public void setForm ( boolean isForm )
    {
        this.isForm = isForm;
    }

    public long getIid ()
    {
        return iid;
    }

    public void setIid ( long iid )
    {
        this.iid = iid;
    }

    public String getVendor ()
    {
        return vendor;
    }

    public void setVendor ( String vendor )
    {
        this.vendor = vendor;
    }

    public String getEquipType ()
    {
        return equipType;
    }

    public void setEquipType ( String equipType )
    {
        this.equipType = equipType;
    }

    public String getSuccessChar ()
    {
        return successChar;
    }

    public void setSuccessChar ( String successChar )
    {
        this.successChar = successChar;
    }

    public String getIgnoreChar ()
    {
        return ignoreChar;
    }

    public void setIgnoreChar ( String ignoreChar )
    {
        this.ignoreChar = ignoreChar;
    }

    public String getSpecialChar ()
    {
        return specialChar;
    }

    public void setSpecialChar ( String specialChar )
    {
        this.specialChar = specialChar;
    }

    public String getEndChar ()
    {
        return endChar;
    }

    public void setEndChar ( String endChar )
    {
        this.endChar = endChar;
    }

    public String getNoConfigChar ()
    {
        return noConfigChar;
    }

    public void setNoConfigChar ( String noConfigChar )
    {
        this.noConfigChar = noConfigChar;
    }

    public String getErrorChar ()
    {
        return errorChar;
    }

    public void setErrorChar ( String errorChar )
    {
        this.errorChar = errorChar;
    }

    public List<EquipCharFormModel> getErrorList ()
    {
        return errorList;
    }

    public void setErrorList ( List<EquipCharFormModel> errorList )
    {
        this.errorList = errorList;
    }
    
}
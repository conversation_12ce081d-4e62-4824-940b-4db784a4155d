package com.ideal.ieai.server.repository.hd.ic.hcstrategy.strategyEquipmentHcHis;

public class StrategyEquipmentHcHisModel {

    private String iid;

    private String centername;

    private String sysname;

    private String applvl;

    private String cpid;

    private String cpname;

    private String manufactoryname;

    private String equipmentmodelname;

    private String idate;

    private String istatus;

    private String mainTenanceVender;
    private String bigModel;
    private String contractNo;
    private String mainTenanceFlag;
    private String usingCategories;

    public String getIid() {
        return iid;
    }

    public void setIid(String iid) {
        this.iid = iid;
    }

    public String getCentername() {
        return centername;
    }

    public void setCentername(String centername) {
        this.centername = centername;
    }

    public String getSysname() {
        return sysname;
    }

    public void setSysname(String sysname) {
        this.sysname = sysname;
    }

    public String getApplvl() {
        return applvl;
    }

    public void setApplvl(String applvl) {
        this.applvl = applvl;
    }

    public String getCpid() {
        return cpid;
    }

    public void setCpid(String cpid) {
        this.cpid = cpid;
    }

    public String getCpname() {
        return cpname;
    }

    public void setCpname(String cpname) {
        this.cpname = cpname;
    }

    public String getManufactoryname() {
        return manufactoryname;
    }

    public void setManufactoryname(String manufactoryname) {
        this.manufactoryname = manufactoryname;
    }

    public String getEquipmentmodelname() {
        return equipmentmodelname;
    }

    public void setEquipmentmodelname(String equipmentmodelname) {
        this.equipmentmodelname = equipmentmodelname;
    }

    public String getIdate() {
        return idate;
    }

    public void setIdate(String idate) {
        this.idate = idate;
    }

    public String getIstatus() {
        return istatus;
    }

    public void setIstatus(String istatus) {
        this.istatus = istatus;
    }

    public String getMainTenanceVender() {
        return mainTenanceVender;
    }

    public void setMainTenanceVender(String mainTenanceVender) {
        this.mainTenanceVender = mainTenanceVender;
    }

    public String getBigModel() {
        return bigModel;
    }

    public void setBigModel(String bigModel) {
        this.bigModel = bigModel;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getMainTenanceFlag() {
        return mainTenanceFlag;
    }

    public void setMainTenanceFlag(String mainTenanceFlag) {
        this.mainTenanceFlag = mainTenanceFlag;
    }

    public String getUsingCategories() {
        return usingCategories;
    }

    public void setUsingCategories(String usingCategories) {
        this.usingCategories = usingCategories;
    }
}

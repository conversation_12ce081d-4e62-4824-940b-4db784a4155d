package com.ideal.ieai.server.repository.hd.ic.businessMonitor;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.servlet.jsp.JspException;
import java.io.*;
import java.sql.Blob;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

public class BusinessMonitorNxyService
{
    private static final Logger              _log     = Logger.getLogger(BusinessMonitorNxyService.class);
    private static BusinessMonitorNxyService intance = null;
    private CellStyle defaultCellStyle;
    
    public static BusinessMonitorNxyService getInstance ()
    {
        if (intance == null)
        {
            intance = new BusinessMonitorNxyService();
        }
        return intance;
    }

    private BusinessMonitorNxyService()
    {

    }

    /**
     * 
     * @Title: getAllSysLev   
     * @Description: 获取所有 
     * @param sysId
     * @return      
     * @author: Administrator 
     * @throws RepositoryException 
     * @date:   2018年11月3日 下午1:41:08
     */
    public List getAllSysLev () throws RepositoryException
    {
       /** List allSysLevList=BusinessMonitorNxyManager.getInstance().getAllSysLev();*/
        
        /**List esscList=BusinessMonitorNxyManager.getInstance().getESSCId();
        if(esscList!=null&&!esscList.isEmpty()){
            allSysLevList.add(esscList.get(0));
        }*/
        return BusinessMonitorNxyManager.getInstance().getAllSysLev();
    }


    /**
     * <li>Description:巡键监控 报表导出</li>
     * 
     * <AUTHOR> 2016年4月7日
     * @param request
     * @param out
     * @param list
     * @param sysId
     * @param ExportTime
     * @param prjName
     * @throws JspException return void
     */
    public void doExport ( OutputStream out, List<BusinessMonitorNxyModel> klist, String userId )
    {
        BufferedReader br = null;
        InputStream in = null;
        try
        {
            // 获取当前系统时间
            long current = System.currentTimeMillis();
            long configtime = ServerEnv.getServerEnv().getHcOverLastInspectTime();

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            HSSFWorkbook wb = new HSSFWorkbook();
            setDefaultStyle(wb);
            HSSFSheet sheet = null;
            if (Environment.getInstance().getBooleanConfig(Environment.HC_INSPECT_MONITOR_EXPORT_TRADE_STATISTIC,
                false))
            {
                List<BusinessBlob> blist = BusinessMonitorNxyManager.getInstance().doExportOther(userId);

                if (null != blist && !blist.isEmpty())
                {
                    for (int h = 0; h < blist.size(); h++)
                    {
                        BusinessBlob bean = blist.get(h);
                        String sheetName = bean.getDataType();
                        if (null != sheetName && !"".equals(sheetName))
                        {
                            sheet = wb.createSheet(sheetName);
                            sheet.setDisplayGridlines(false);
                            sheet.setDisplayGridlines(false);
                            Blob datas = bean.getDatas();
                            // 开始读入文件
                            in = datas.getBinaryStream();
                            br = new BufferedReader(new InputStreamReader(in, "UTF-8"));
                            String lineTxt = null;
                            int i = 0;
                            while ((lineTxt = br.readLine()) != null)
                            {
                                HSSFRow row = sheet.createRow(i);
                                HSSFCell cellindex = row.createCell(0);
                                cellindex.setCellValue(lineTxt);
                                i++;
                            }

                        }
                    }
                }
            }

            if (null != klist && !klist.isEmpty())
            {
                int count = 0;
                for (int k = 0; k < klist.size(); k++)
                {

                    BusinessMonitorNxyModel bModel = klist.get(k);
                    String levelName = bModel.getApplvl();// 业务系统级别名
                    // 根据APPLVLID判断系统分类下是否有具有权限的业务系统，如果没有则不创建Sheet页，但是ESSC排除
                    if (!levelName.trim().contains("ESSC"))
                    {
                        boolean flag = BusinessMonitorNxyManager.getInstance().getCount(bModel.getApplvlid(),
                            bModel.getPrjName(), userId);// 获取需要生成excel表格的全部数据。
                        if (!flag)
                        {
                            count++;
                            continue;
                        }
                    } else
                    {
                        boolean flag = BusinessMonitorNxyManager.getInstance().getCountEssc(bModel.getApplvlid(),
                            bModel.getPrjName(), userId);// 获取需要生成excel表格的全部数据。
                        if (!flag)
                        {
                            count++;
                            continue;
                        }
                    }

                    sheet = wb.createSheet(levelName);
                    bModel.getApplvlid();

                    sheet.setDisplayGridlines(false);
                    sheet.setDisplayGridlines(false);
                    HSSFCellStyle styleMain = wb.createCellStyle();
                    HSSFFont font = wb.createFont();
                    styleMain.setVerticalAlignment(VerticalAlignment.CENTER);
                    styleMain.setWrapText(true);
                    styleMain.setAlignment(HorizontalAlignment.CENTER);
                    styleMain.setBorderBottom(BorderStyle.THIN);
                    styleMain.setBorderLeft(BorderStyle.THIN);
                    styleMain.setBorderRight(BorderStyle.THIN);
                    styleMain.setBorderTop(BorderStyle.THIN);
                    font.setFontName("Courier New");
                    font.setFontHeightInPoints((short) 35);
                    font.setBold(true);
                    styleMain.setFont(font);
                    styleMain.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
                    styleMain.setFillPattern(SOLID_FOREGROUND);

                    HSSFPalette customPalette = wb.getCustomPalette();

                    // 正常 RGB(171,255,171)
                    HSSFCellStyle cellStyleG = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.GREEN.getIndex(), (byte) 171, (byte) 255, (byte) 171);
                    cellStyleG.setFillForegroundColor(IndexedColors.GREEN.getIndex());
                    cellStyleG.setFillPattern(SOLID_FOREGROUND);
                    cellStyleG.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleG.setBorderBottom(BorderStyle.THIN);
                    cellStyleG.setBorderTop(BorderStyle.THIN);
                    cellStyleG.setBorderLeft(BorderStyle.THIN);
                    cellStyleG.setBorderRight(BorderStyle.THIN);

                    // 定义报警级别颜色
                    // 一级别(关注)RGB(153,255,204)
                    HSSFCellStyle cellStyleOne = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.LIME.getIndex(), (byte) 153, (byte) 255, (byte) 204);
                    cellStyleOne.setFillForegroundColor(IndexedColors.LIME.getIndex());
                    cellStyleOne.setFillPattern(SOLID_FOREGROUND);
                    cellStyleOne.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleOne.setBorderBottom(BorderStyle.THIN);
                    cellStyleOne.setBorderTop(BorderStyle.THIN);
                    cellStyleOne.setBorderLeft(BorderStyle.THIN);
                    cellStyleOne.setBorderRight(BorderStyle.THIN);
                    
                    
                    // 二级别(一般)RGB(255,255,102)
                    HSSFCellStyle cellStyleSecond = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.GOLD.getIndex(), (byte) 255, (byte) 255, (byte) 102);
                    cellStyleSecond.setFillForegroundColor(IndexedColors.GOLD.getIndex());
                    cellStyleSecond.setFillPattern(SOLID_FOREGROUND);
                    cellStyleSecond.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleSecond.setBorderBottom(BorderStyle.THIN);
                    cellStyleSecond.setBorderTop(BorderStyle.THIN);
                    cellStyleSecond.setBorderLeft(BorderStyle.THIN);
                    cellStyleSecond.setBorderRight(BorderStyle.THIN);
                    
                    
                    // 三级别(次要)RGB(255,192,0)
                    HSSFCellStyle cellStyleThird = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.LIGHT_ORANGE.getIndex(), (byte) 255, (byte) 192, (byte) 0);
                    cellStyleThird.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                    cellStyleThird.setFillPattern(SOLID_FOREGROUND);
                    cellStyleThird.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleThird.setBorderBottom(BorderStyle.THIN);
                    cellStyleThird.setBorderTop(BorderStyle.THIN);
                    cellStyleThird.setBorderLeft(BorderStyle.THIN);
                    cellStyleThird.setBorderRight(BorderStyle.THIN);
                    
                    // 浅黄色
                    // 三级别(次要)RGB(255,250,204)
                    HSSFCellStyle cellStylegg = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.TEAL.getIndex(), (byte) 255, (byte) 255, (byte) 204);
                    cellStylegg.setFillForegroundColor(IndexedColors.TEAL.getIndex());
                    cellStylegg.setFillPattern(SOLID_FOREGROUND);
                    cellStylegg.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStylegg.setBorderBottom(BorderStyle.THIN);
                    cellStylegg.setBorderTop(BorderStyle.THIN);
                    cellStylegg.setBorderLeft(BorderStyle.THIN);
                    cellStylegg.setBorderRight(BorderStyle.THIN);

                    // 四级别(警告)RGB(238,114,40)
                    HSSFCellStyle cellStyleFourth = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.ORCHID.getIndex(), (byte) 238, (byte) 114, (byte) 40);
                    cellStyleFourth.setFillForegroundColor(IndexedColors.ORCHID.getIndex());
                    cellStyleFourth.setFillPattern(SOLID_FOREGROUND);
                    cellStyleFourth.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleFourth.setBorderBottom(BorderStyle.THIN);
                    cellStyleFourth.setBorderTop(BorderStyle.THIN);
                    cellStyleFourth.setBorderLeft(BorderStyle.THIN);
                    cellStyleFourth.setBorderRight(BorderStyle.THIN);
                    
                    
                    // 5级别(严重)RGB(195,77,7)
                    HSSFCellStyle cellStyleFive = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.TAN.getIndex(), (byte) 195, (byte) 77, (byte) 7);
                    cellStyleFive.setFillForegroundColor(IndexedColors.TAN.getIndex());
                    cellStyleFive.setFillPattern(SOLID_FOREGROUND);
                    cellStyleFive.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleFive.setBorderBottom(BorderStyle.THIN);
                    cellStyleFive.setBorderTop(BorderStyle.THIN);
                    cellStyleFive.setBorderLeft(BorderStyle.THIN);
                    cellStyleFive.setBorderRight(BorderStyle.THIN);

                    HSSFCellStyle cellStyleO = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.ORANGE.getIndex(), (byte) 217, (byte) 169, (byte) 37);
                    cellStyleO.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
                    cellStyleO.setFillPattern(SOLID_FOREGROUND);
                    cellStyleO.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleO.setBorderBottom(BorderStyle.THIN);
                    cellStyleO.setBorderTop(BorderStyle.THIN);
                    cellStyleO.setBorderLeft(BorderStyle.THIN);
                    cellStyleO.setBorderRight(BorderStyle.THIN);

                    HSSFCellStyle cellStyleGray = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.GREY_25_PERCENT.getIndex(), (byte) 192, (byte) 192, (byte) 192);
                    cellStyleGray.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    cellStyleGray.setFillPattern(SOLID_FOREGROUND);
                    cellStyleGray.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleGray.setBorderBottom(BorderStyle.THIN);
                    cellStyleGray.setBorderTop(BorderStyle.THIN);
                    cellStyleGray.setBorderLeft(BorderStyle.THIN);
                    cellStyleGray.setBorderRight(BorderStyle.THIN);

                    HSSFCellStyle cellStyleR = wb.createCellStyle();
                    cellStyleR.setFillForegroundColor(IndexedColors.RED.getIndex());
                    cellStyleR.setFillPattern(SOLID_FOREGROUND);
                    cellStyleR.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleR.setBorderBottom(BorderStyle.THIN);
                    cellStyleR.setBorderTop(BorderStyle.THIN);
                    cellStyleR.setBorderLeft(BorderStyle.THIN);
                    cellStyleR.setBorderRight(BorderStyle.THIN);

                    HSSFCellStyle cellStyleBlue = wb.createCellStyle();
                    customPalette.setColorAtIndex(IndexedColors.BLUE.getIndex(), (byte) 37, (byte) 109, (byte) 183);
                    cellStyleBlue.setFillForegroundColor(IndexedColors.BLUE.getIndex());
                    cellStyleBlue.setFillPattern(SOLID_FOREGROUND);
                    cellStyleBlue.setAlignment(HorizontalAlignment.CENTER);// 设置居中
                    cellStyleBlue.setBorderBottom(BorderStyle.THIN);
                    cellStyleBlue.setBorderTop(BorderStyle.THIN);
                    cellStyleBlue.setBorderLeft(BorderStyle.THIN);
                    cellStyleBlue.setBorderRight(BorderStyle.THIN);
                    HSSFFont font1 = wb.createFont();
                    font1.setColor(IndexedColors.WHITE.getIndex());
                    cellStyleBlue.setFont(font1);


                    HSSFCellStyle style = wb.createCellStyle();
                    style.setAlignment(HorizontalAlignment.CENTER);
                    style.setBorderBottom(BorderStyle.THIN);
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderLeft(BorderStyle.THIN);
                    style.setBorderRight(BorderStyle.THIN);

                    HSSFRow xlsRowMain = sheet.createRow(0);
                    HSSFCell cellMain = xlsRowMain.createCell(0);
                    cellMain.setCellValue(levelName + "日间巡检报告");
                    this.mergecell(sheet, 0, 0, 0, 12);
                    cellMain.setCellStyle(cellStyleGray);


                    HSSFRow xlsRowMainV = sheet.createRow(1);
                    HSSFCell cellMainV = xlsRowMainV.createCell(0);
                    cellMainV.setCellValue("监控系统 ");
                    cellMainV.setCellStyle(cellStyleO);

                    HSSFCell cellMainUser = xlsRowMainV.createCell(1);
                    cellMainUser.setCellValue("监控主机");
                    cellMainUser.setCellStyle(cellStyleO);

                    HSSFCell cellhh = xlsRowMainV.createCell(2);
                    cellhh.setCellValue("主机名称");
                    cellhh.setCellStyle(cellStyleO);

                    HSSFCell jiankongxiang = xlsRowMainV.createCell(3);
                    jiankongxiang.setCellValue("监控项");
                    jiankongxiang.setCellStyle(cellStyleO);

                    HSSFCell jianchadian = xlsRowMainV.createCell(4);
                    jianchadian.setCellValue("检查点");
                    jianchadian.setCellStyle(cellStyleO);

                    HSSFCell jcdx = xlsRowMainV.createCell(5);
                    jcdx.setCellValue("检查对象");
                    jcdx.setCellStyle(cellStyleO);
                     /**HSSFCell hanzi = xlsRowMainV.createCell(5);
                     hanzi.setCellValue("状态");
                     hanzi.setCellStyle(cellStyleO);*/

                    HSSFCell jianchadianzt = xlsRowMainV.createCell(6);
                    jianchadianzt.setCellValue("状态");
                    jianchadianzt.setCellStyle(cellStyleO);

                    HSSFCell baocuoxinxi = xlsRowMainV.createCell(7);
                    baocuoxinxi.setCellValue("报错信息");
                    baocuoxinxi.setCellStyle(cellStyleO);

                    HSSFCell shuoming = xlsRowMainV.createCell(8);
                    shuoming.setCellValue("说明");
                    shuoming.setCellStyle(cellStyleO);

                    HSSFCell jianchezhi = xlsRowMainV.createCell(9);
                    jianchezhi.setCellValue("检测值");
                    jianchezhi.setCellStyle(cellStyleO);

                    HSSFCell jixian = xlsRowMainV.createCell(10);
                    jixian.setCellValue("基线");
                    jixian.setCellStyle(cellStyleO);

                     /**HSSFCell chulizhuangtai = xlsRowMainV.createCell(11);
                     chulizhuangtai.setCellValue("处理状态");
                     chulizhuangtai.setCellStyle(cellStyleO);*/

                    HSSFCell chulijieguo = xlsRowMainV.createCell(11);
                    chulijieguo.setCellValue("处理结果");
                    chulijieguo.setCellStyle(cellStyleO);

                    HSSFCell xunjianshijian = xlsRowMainV.createCell(12);
                    xunjianshijian.setCellValue("巡检时间");
                    xunjianshijian.setCellStyle(cellStyleO);

                    List list = new ArrayList(); //结果值

                    Map retmap = BusinessMonitorNxyManager.getInstance()
                            .doExport(bModel.getApplvlid(), bModel.getPrjName(), userId);// 获取需要生成excel表格的全部数据。

                    if (null != retmap && retmap.containsKey("dataList"))
                    {
                        list = (List) retmap.get("dataList");
                    }

                    Map cm2 = (Map) retmap.get("computerMaps2");

                    if (null != list && !list.isEmpty())
                    {
                        HSSFRow rowcount = sheet.createRow(2); 
                        HSSFCell ccellindex = rowcount.createCell(0);
                        BusinessMonitorNxyModel beanm = (BusinessMonitorNxyModel) list.get(0);
                        ccellindex.setCellValue(beanm.getPrjName());
                        ccellindex.setCellStyle(cellStyleBlue);

                        HSSFCell c1 = rowcount.createCell(1);

                        c1.setCellValue("共计" + cm2.get(beanm.getPrjName()) + "台");
                        c1.setCellStyle(cellStyleBlue);
                        HSSFCell c2 = rowcount.createCell(2);
                        c2.setCellValue(beanm.getPrjName());
                        /** this.mergecell(sheet, 2, 2, 2, 10);*/
                        this.mergecell(sheet, 2, 2, 2, 11);
                        c2.setCellStyle(cellStyleBlue);
                        HSSFCell c12 = rowcount.createCell(12);
                        c12.setCellValue("最后巡检时间");
                        c12.setCellStyle(cellStyleBlue);

                    }

                    String sysName = "";// 系统名称，根据系统名称判断是否需要合并列和行
                    int jj = 0;// 计数循环到第几个业务系统
                    int mergercellStart = 0;// 记录合并的开始行

                    /** Map computerNameMap = (Map) retmap.get("computerNameMap");*/

                    if (null != list && !list.isEmpty())
                    {
                        // 该map的key为 systemid+"@@"+ip ，value为记录数
                        Map<String, Integer> mergeCellMapCou = new LinkedHashMap<String, Integer>();
                        // 记录当前shett页有几个系统
                        int listSizelist = 0;
                        for (int i = 0; i < list.size(); i++)
                        {
                            // 当前循环的bean
                            BusinessMonitorNxyModel bean = (BusinessMonitorNxyModel) list.get(i);
                            // 上条循环的bean
                            BusinessMonitorNxyModel beforeBean = null;
                            if (i == 0)
                            {
                                mergercellStart = i + jj + 3;
                                listSizelist++;
                            } else
                            {
                                beforeBean = (BusinessMonitorNxyModel) list.get(i - 1);
                                // 当前循环的bean和上条循环的bean不是同一个系统，那么才累加一次
                                if (!beforeBean.getSystemId().equals(bean.getSystemId()))
                                {
                                    listSizelist++;
                                }
                            }


                            if (!sysName.equals(bean.getPrjName()) && i != 0)
                            {
                                HSSFRow row = sheet.createRow(i + jj + 3);
                                HSSFCell ccellindex = row.createCell(0);
                                BusinessMonitorNxyModel beanm = (BusinessMonitorNxyModel) list.get(i);
                                ccellindex.setCellValue(beanm.getPrjName());
                                ccellindex.setCellStyle(cellStyleBlue);

                                HSSFCell c1 = row.createCell(1);

                                c1.setCellValue("共计" + cm2.get(beanm.getPrjName()) + "台");
                                c1.setCellStyle(cellStyleBlue);
                                HSSFCell c2 = row.createCell(2);
                                c2.setCellValue(beanm.getPrjName());
                                this.mergecell(sheet, i + jj + 3, i + jj + 3, 2, 11);
                                c2.setCellStyle(cellStyleBlue);
                                HSSFCell c12 = row.createCell(12);
                                c12.setCellValue("最后巡检时间");
                                c12.setCellStyle(cellStyleBlue);


                                if (jj == 0)
                                {
                                    Integer tempCellStart = mergercellStart;
                                    /** this.mergecell(sheet, mergercellStart, i + jj + 2, 1, 1);*/
                                    // 遍历MAP 进行合并单元格
                                    for (Map.Entry<String, Integer> entry : mergeCellMapCou.entrySet())
                                    {
                                        Integer intV = entry.getValue();
                                        this.mergecell(sheet, tempCellStart, tempCellStart + intV - 1, 1, 1);
                                        tempCellStart += intV;
                                    }
                                } else
                                {
                                    /** this.mergecell(sheet, mergercellStart + 1, i + jj + 2, 1, 1);*/
                                    Integer tempCellStart = mergercellStart;
                                    // 遍历MAP 进行合并单元格
                                    for (Map.Entry<String, Integer> entry : mergeCellMapCou.entrySet())
                                    {
                                        Integer intV = entry.getValue();
                                        this.mergecell(sheet, tempCellStart + 1, tempCellStart + intV, 1, 1);
                                        tempCellStart += intV;
                                    }
                                }

                                jj = jj + 1;
                                mergercellStart = i + jj + 2;
                                mergeCellMapCou.clear();
                            }

                            // ***********************************************
                            String key = bean.getSystemId() + "@@" + bean.getServerIp();
                            if (mergeCellMapCou.containsKey(key))
                            {
                                int num = mergeCellMapCou.get(key);
                                mergeCellMapCou.put(key, num + 1);
                            } else
                            {
                                mergeCellMapCou.put(key, 1);
                            }
                            // ***********************************************

                            sysName = bean.getPrjName();
                            // 遍历allCount
                            HSSFRow row = sheet.createRow(i + jj + 3);

                            // 第一列业务系统名
                            HSSFCell cellindex = row.createCell( 0);
                            cellindex.setCellValue(bean.getPrjName());
                            cellindex.setCellStyle(style);

                            // 设备IP
                            HSSFCell cell = row.createCell( 1);
                            cell.setCellValue(bean.getCpName());
                            cell.setCellStyle(style);

                            // 设备IP
                            HSSFCell cellh = row.createCell( 2);
                            cellh.setCellValue(bean.getCpName());
                            cellh.setCellStyle(style);

                            // 检查项

                            HSSFCell cell2 = row.createCell( 3);
                            cell2.setCellValue(bean.getCiName());
                            cell2.setCellStyle(style);

                            // 检查点
                            HSSFCell cell3 = row.createCell( 4);
                            cell3.setCellValue(bean.getCptext());
                            cell3.setCellStyle(style);

                            HSSFCell celljxdx = row.createCell( 5);
                            celljxdx.setCellValue(bean.getJcdx());
                            celljxdx.setCellStyle(style);
                            // 状态汉字
                             /**HSSFCell cell4 = row.createCell((short) 5);
                             cell4.setCellValue("状态");
                             cell4.setCellStyle(style);*/

                            // 异常信息
                            HSSFCell cell5 = row.createCell( 6);
                            /** if (bean.getXjstatus().equals("正常"))
                             {
                             cell5.setCellValue(bean.getXjstatus());
                             cell5.setCellStyle(cellStyleG);
                             } else
                             {
                             cell5.setCellValue(bean.getXjstatus());
                             cell5.setCellStyle(cellStyleR);
                             }
*/                            // 农信银行12-20 修改不同级别，不同颜色显示
                            if (bean.getXjstatus().equals("正常"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleG);
                            }
                            if (bean.getXjstatus().equals("关注"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleOne);
                            }
                            if (bean.getXjstatus().equals("一般"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleSecond);
                            }
                            if (bean.getXjstatus().equals("次要"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleThird);
                            }
                            if (bean.getXjstatus().equals("警告"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleFourth);
                            }
                            if (bean.getXjstatus().equals("严重"))
                            {
                                cell5.setCellValue(bean.getXjstatus());
                                cell5.setCellStyle(cellStyleFive);
                            }


                            // 报错信息
                            HSSFCell cell6 = row.createCell( 7);
                            if (null != bean.getCheckName() && !"".equals(bean.getCheckName())
                                    && !"null".equals(bean.getCheckName())
                                    &&!"NULL".equals(bean.getCheckName()))
                            {
                                cell6.setCellValue("["+bean.getCpName()+"] : "+bean.getCheckName());
                            }
                            cell6.setCellStyle(style);

                            // 说明
                            HSSFCell cell7 = row.createCell( 8);
                            if (null != bean.getCheckResultValue() && !"".equals(bean.getCheckResultValue())
                                    && !"null".equals(bean.getCheckResultValue())
                                    &&!"NULL".equals(bean.getCheckResultValue()))
                            {
                                cell7.setCellValue("["+bean.getCpName()+"] : "+bean.getCheckResultValue());
                            }
                           
                            cell7.setCellStyle(style);

                            // 检测值
                            HSSFCell cell8 = row.createCell( 9);
                            cell8.setCellValue(bean.getCheckResult());
                            cell8.setCellStyle(style);

                            // 阀值
                            HSSFCell cell9 = row.createCell( 10);
                            cell9.setCellValue(bean.getBaseLine());
                            cell9.setCellStyle(style);

                            // 处理状态
                            /** HSSFCell cell10 = row.createCell((short) 11);
                             cell10.setCellValue("处理状态");
                             cell10.setCellStyle(style);*/

                            // 处理结果
                            HSSFCell cell11 = row.createCell( 11);
                            if (bean.getCpstatus().equals("0"))
                            {
                                cell11.setCellValue("无需处理");
                            } else
                            {
                                cell11.setCellValue("");
                            }
                            cell11.setCellStyle(style);

                            // 巡检时间
                            HSSFCell cell12 = row.createCell( 12);
                            cell12.setCellValue(bean.getCheckTime());


                            Date date = formatter.parse(bean.getCheckTime());
                            long startTime = date.getTime();
                            if (current > configtime + startTime)
                            {
                                cell12.setCellStyle(cellStylegg);
                            } else
                            {
                                cell12.setCellStyle(style);
                            }

                            if (i > 65000)
                            {
                                break;
                            }

                        }

                        // 合并最后一个业务系统的设备单元格
                        /** 
                         if (mergercellStart == 3)
                         {
                         this.mergecell(sheet, mergercellStart, list.size() + cm2.size() + 1, 1,
                         1);
                         } else
                         {
                         this.mergecell(sheet, mergercellStart + 1, list.size() + cm2.size() + 1,
                         1, 1);
                         }
                         this.mergecell(sheet, mergercellStart + 1, i + jj + 2, 1, 1);*/

                        // 合并最后一个业务系统的设备单元格
                        if (!mergeCellMapCou.isEmpty())
                        {
                            Integer tempCellStart = mergercellStart;
                            for (Map.Entry<String, Integer> entry : mergeCellMapCou.entrySet())
                            {
                                // 通过 listSizelist 判断 ，当前sheet页是否存在多个系统，只有一个系统和多个系统传参不同
                                Integer intV = entry.getValue();
                                this.mergecell(sheet, listSizelist == 1 ? tempCellStart : tempCellStart + 1,
                                    listSizelist == 1 ? tempCellStart + intV - 1 : tempCellStart + intV, 1, 1);
                                tempCellStart += intV;
                            }
                        }

                    }

                    sheet.setColumnWidth( 0,  5000);
                    sheet.setColumnWidth( 1,  5000);
                    sheet.setColumnWidth( 2,  7000);
                    sheet.setColumnWidth( 3,  5000);
                    sheet.setColumnWidth( 4,  3000);
                    sheet.setColumnWidth( 5,  3000);
                    sheet.setColumnWidth( 6,  3000);
                    sheet.setColumnWidth( 7,  3000);
                    sheet.setColumnWidth( 8,  3000);
                    sheet.setColumnWidth( 9,  5000);
                    sheet.setColumnWidth( 10,  5000);
                    sheet.setColumnWidth( 11,  5000);
                    sheet.setColumnWidth( 12,  5000);
                    /** sheet.setColumnWidth((short) 12, (short) 5000);
                     sheet.setColumnWidth((short) 13, (short) 5000);*/
                }
                if (count == klist.size())
                {
                    sheet = wb.createSheet("业务系统没有分类");
                    HSSFRow xlsRowMain = sheet.createRow(0);
                    HSSFCell cellMain = xlsRowMain.createCell(0);
                    cellMain.setCellValue("业务系统没有分类！");
                    this.mergecell(sheet, 0, 0, 0, 11);
                    wb.write(out);

                } else
                {
                    wb.write(out);
                }

            }
            wb.write(out);
        } catch (Exception e)
        {
            _log.error(" BusinessMonitorNxyService ", e);
        } finally
        {
            try
            {
                if (null != br)
                {
                    br.close();
                }
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                _log.error(" BusinessMonitorNxyService out close is error ! ", e);
            }
        }
    }

    public Row getRow ( Sheet sheet, int ridx )
    {
        Row row = sheet.getRow(ridx);
        if (row == null)
        {
            row = sheet.createRow(ridx);
        }

        return row;
    }

    public Cell getCell ( Row row, int idx )
    {
        Cell cell = row.getCell(idx);
        if (cell == null)
        {
            cell = row.createCell(idx);
            cell.setCellStyle(defaultCellStyle);
            cell.setCellType(CellType.STRING);

        }

        return cell;
    }

    private void setDefaultStyle ( HSSFWorkbook wb )
    {
        defaultCellStyle = wb.createCellStyle();
        defaultCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        defaultCellStyle.setAlignment(HorizontalAlignment.CENTER);
        defaultCellStyle.setBorderBottom(BorderStyle.THIN);
        defaultCellStyle.setBorderLeft(BorderStyle.THIN);
        defaultCellStyle.setBorderRight(BorderStyle.THIN);
        defaultCellStyle.setBorderTop(BorderStyle.THIN);
        defaultCellStyle.setWrapText(true);
        Font font = wb.createFont();
        font.setFontName("Courier New");
        defaultCellStyle.setFont(font);

    }

    public void mergecell ( Sheet sheet, int rowfrom, int rowto, int colfrom, int colto )
    {
        if(!(rowfrom==rowto && colfrom==colto)) {
            sheet.addMergedRegion(new CellRangeAddress(rowfrom, rowto, colfrom, colto));
        }
        for (int r = rowfrom; r <= rowto; ++r)
        {
            Row row = this.getRow(sheet, r);
            for (int c = colfrom; c <= colto; ++c)
            {
                Cell cell = this.getCell(row, c);
                cell.setCellStyle(defaultCellStyle);
            }
        }
    }
}

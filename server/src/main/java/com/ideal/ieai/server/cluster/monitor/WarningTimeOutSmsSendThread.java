package com.ideal.ieai.server.cluster.monitor;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.log4j.Logger;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.hd.warningmailsend.WarningMailSendManager;

public class WarningTimeOutSmsSendThread extends Thread
{
    private static final Logger log            = Logger.getLogger(WarningTimeOutSmsSendThread.class);
    private long cycle;
    
    public WarningTimeOutSmsSendThread()
    {
        this.cycle =getWaringTimeOutSmsCycle ()*1000;
    }

    private  long getWaringTimeOutSmsCycle ()
    {
        long cycleproperties;
        String smsCycle = ServerEnv.getInstance().getSysConfig(Environment.SEND_HC_WARING_TIMEOUT_SMS_CYCLE, "600");
        try
        {
            cycleproperties = Long.parseLong(smsCycle);
        } catch (java.lang.NumberFormatException numEX)
        {
            cycleproperties = 600;
        }
        return cycleproperties;
    }
    
    @Override
    public void run () {
        SimpleDateFormat start = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startD = new Date();
        log.info("线程启动时间："+start.format(startD));
        
        long currentTime = 0;  
        Date currentD = null;
        while(true) {
            try
            {
                Thread.sleep(this.cycle);
            } catch (Exception ex)
            {
                log.error("巡检告警超时未处理告警短信发送线程，等轮询过程出现异常！", ex);
            }
            boolean startSwich = ServerEnv.getInstance().getBooleanConfig(Environment.SEND_HC_WARING_TIMEOUT_SMS_SWITCH, false);
            boolean sendplatform = ServerEnv.getInstance().sendHcWarningTimeoutUnifiedPlatformSwitch();
            
            currentTime = System.currentTimeMillis();
            currentD = new Date(currentTime);
            log.info("当前发送时间："+start.format(currentD));
            
            
            if(startSwich) {
                WarningMailSendManager manager = null;
                try {
                    manager = WarningMailSendManager.getInstance();
                    
                    //如果是开启发送告警平台开关，执行发送告警平台逻辑
                    if(sendplatform) {
                        manager.sendSmsNew();
                    }
                    //否则走告警超时还是内蒙原来告警操作时发送逻辑
                    else {
                        manager.sendSms();
                    }
                    
                }catch (Exception e) {
                    log.error("超时未处理报警短信提醒发送失败",e);
                }
            }else {
                log.info("超时未处理告警短信开关未开启，不进行短信发送");
                break;
            }
           
            
            
        }
    }
    
    
   
    
}

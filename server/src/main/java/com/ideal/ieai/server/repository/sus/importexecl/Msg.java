package com.ideal.ieai.server.repository.sus.importexecl;

import java.util.List;
import java.util.Map;


public interface Msg
{
    public boolean isSucss ();
    public String getVesion();
    public String getChangeNumber();
    public String getChangeDesc();
    public String getMessage();
    public List getSheetsObj();
    public String getSysname ();
    public void setSheetsObj(List sheets);
    public void setPkgName(String pkgName);
    public String getPkgName();
}

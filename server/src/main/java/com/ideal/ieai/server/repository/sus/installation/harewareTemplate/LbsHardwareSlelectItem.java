package com.ideal.ieai.server.repository.sus.installation.harewareTemplate;

public class LbsHardwareSlelectItem
{
    //默认硬件配置模板下拉类表展示
    private String name;
    private String state;//是否选中 true false
    private String script;
    public String getName ()
    {
        return name;
    }
    public void setName ( String name )
    {
        this.name = name;
    }
    public String getState ()
    {
        return state;
    }
    public void setState ( String state )
    {
        this.state = state;
    }
    
    public String getScript ()
    {
        return script;
    }
    public void setScript ( String script )
    {
        this.script = script;
    }
    public LbsHardwareSlelectItem(String name ,String state,String script)
    {
        this.name=name;
        this.state=state;
        this.script=script;
    }
    
}

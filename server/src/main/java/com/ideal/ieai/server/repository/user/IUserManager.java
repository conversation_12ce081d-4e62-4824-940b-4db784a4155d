    package com.ideal.ieai.server.repository.user;

import com.ideal.dubbo.models.product.ScriptUserBean;
import com.ideal.ieai.commons.RoleInfo;
import com.ideal.ieai.commons.UserBasicInfo;
import com.ideal.ieai.server.repository.RepositoryException;

import java.sql.Connection;
import java.util.List;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.0
 */

public interface IUserManager
{

    /**
     * check if user with the id userId is exists in db
     * 
     * @param userId
     * @return
     * @throws RepositoryException
     */
    public boolean isUserExists ( Long userId ) throws RepositoryException;

    /**
     * check if role with the id is exists in db
     * 
     * @param roleId
     * @return
     * @throws RepositoryException
     */
    public boolean isRoleExists ( Long roleId ) throws RepositoryException;

    public boolean isUserExists ( String userLoginName ) throws RepositoryException;

    // public boolean isRoleExists ( String roleName ) throws RepositoryException;

    /**
     * save a role to db,and the roleInfo.setRoleId will be called the set the id of this role
     * 
     * @param roleInfo the role to save
     * @return the id of the role
     * @exception RepositoryException error saving the roleInfo.
     */
    public Long createRole ( RoleInfo roleInfo ) throws RepositoryException;

    /**
     * save a user to db and the user.setId will be called to set the id of this user
     * 
     * @param user the user to save
     * @param roleIds Long instance of ids of roles of the user
     * @return the id of the user
     */
    public Long createUser ( UserBasicInfo user, List roleIds ) throws RepositoryException;

    /**
     * to list all the RoleInfos in the database
     * 
     * @return:all RoleInfos List of RoleInfo instances
     * @throws RepositoryException:exception when load group
     */
    public List getAllRoles () throws RepositoryException;

    /**
     * to list all the users in the database
     * 
     * @return: List of UserBasicInfo instances
     * @throws ReposioryException:exception when load user
     */
    public List getAllUsers () throws RepositoryException;

    /**
     * to get a RoleInfo from database
     * 
     * @param roleId the role's ID
     * @return:a role,null if the role not exist
     * @throws RepositoryException:exception when load
     */
    public RoleInfo getRole ( Long roleId ) throws RepositoryException;

    /**
     * to get a RoleInfo according to its name
     * 
     * @param roleName String
     * @throws RepositoryException
     * @return the RoleInfo
     */
    public RoleInfo getRole ( String roleName ) throws RepositoryException;

    /**
     * get all the roles then the user own
     * 
     * @param userId the id of the user
     * @return list of RoleInfo instancess
     * @throws RepositoryException
     */
    public List getRolesOfUser ( Long userId ) throws RepositoryException;

    //
    // /**
    // * to get one role's sub roles
    // *
    // * @param roleId id of the role
    // * @return list of RoleInfo
    // */
    // public List getSubRoles ( Long roleId ) throws RepositoryException;

    // /**
    // * to get super roles
    // *
    // * @param roleId
    // * @return list of RoleInfo instances
    // * @throws RepositoryException
    // */
    // public List getSuperRoles ( Long roleId ) throws RepositoryException;

    /**
     * to get a UserInfo instance from database
     * 
     * @param userId:user's id
     * @return:a UserInfo,null if the user not exist
     * @throws RepositoryException:exception when load
     */
    // public UserInfo getUser ( Long userId ) throws RepositoryException;
    public UserBasicInfo getBasicUser ( Long userId ) throws RepositoryException;

    //
    // /**
    // * to get a userInfo according to user's login Name
    // *
    // * @param loginName user's login id
    // * @return instance of UserInfo if exists,otherwise return null
    // * @throws RepositoryException
    // */
    // public UserInfo getUser ( String loginName ) throws RepositoryException;
    //
    public UserBasicInfo getBasicUser ( String loginName ) throws RepositoryException;

    /**
     * get all UserBasicInfos that own the RoleInfo
     * 
     * @param roleId roleInfo's id
     * @return List of UserInfo instance
     * @throws RepositoryException
     */
    public List getUsersOfRole ( Long roleId ) throws RepositoryException;

    /**
     * get all userInfos of this roleInfo
     * 
     * @param roleName name of the roleInfo
     * @return List of UserInfo instance
     * @throws RepositoryException
     */
    // public List getUsersOfRole ( String roleName ) throws RepositoryException;
    /**
     * to remove a roleInfo from database removing a role includes :remove users of it,remove role
     * inheritence relations,remove permissions,remove it's info.
     * 
     * @param roleId:id of the roleInfo
     * @throws RepositoryException ERR_ROLE_NOT_EXIST: when the role to move is not exists
     */
    public void removeRole ( Long roleId ) throws RepositoryException;

    /**
     * remove list of roles: removing a role includes :remove users of it,remove role inheritence
     * relations,remove permissions,remove it's info.
     * 
     * 
     * @param roleIds list of Long type role ids
     * @throws RepositoryException ERR_ROLE_NOT_EXIST when any role with the id in roleIds does not
     *             exists
     */
    public void removeRoles ( List roleIds ) throws RepositoryException;

    /**
     * remove a user from database. including remove roles of user ,remove users permission, remove
     * the user
     * 
     * @param userId:id of the user to be removed
     * @throws RepositoryException:when delete failed ERR_USER_NOT_EXIST when the user is not exists
     *             in db
     */
    public void removeUser ( Long userId ) throws RepositoryException;

    /**
     * remove a list of users when removing a user ,including remove roles of user ,remove users
     * permission, remove the user
     * 
     * @param userIds a List of java.lang.Long type instance there value is the id of the user to
     *            remove Long of user's id list
     * @throws RepositoryException ERR_USER_NOT_EXIST when any of the users is not exists in db
     */
    public void removeUsers ( List userIds ) throws RepositoryException;

    /**
     * set the roles of the user when thr roleIds is null or empty list,then the user will own none
     * roles after the method is called.
     * 
     * @param userId the id of the user to set
     * @param roleIds ids of the roles to set, list of Long type
     * @throws RepositoryException <ul>
     *             <li>ERR_USER_NOT_EXIST if the user with the userId is not exists in db</li>
     *             <li>ERR_ROLE_NOT_EXIST any of the role with the id list in roleIds is not exists
     *             in db</li>
     *             </ul>
     */
    public void setRolesOfUser ( Long userId, List roleIds ) throws RepositoryException;

    /**
     * to set system administrator's password
     * 
     * @param pwd:sa's password to set
     * @exception RepositoryException ERR_USER_NOT_EXIST when the sa user is not exists
     */
    public void setSAPassword ( String pwd ) throws RepositoryException;

    /**
     * set sub roles. when subRoleIds is null or an empty list.then will make the role owns none any
     * sub roles
     * 
     * @param roleId the superRole to set
     * @param subRoleIds ids of subRoles, List of Long
     * @throws RepositoryException <ul>
     *             <li>ERR_ROLE_INHERIT_RECURSION when id of one of the subRoles is the equals to
     *             roleId</li>
     *             <li>ERR_ROLE_NOT_EXIST when the id of any of roles refer in param is not exists</li>
     *             </ul>
     */
    // public void setSubRoles ( Long roleId, List subRoleIds ) throws RepositoryException;
    // /**
    // * set super roles. when superRoleIds is null or an empty list.then will make the role owns
    // none
    // * any supers roles
    // *
    // * @param roleId the subRole to set
    // * @param subRoleIds ids of superRoles, List of Long
    // * @throws RepositoryException
    // * <ul>
    // * <li>ERR_ROLE_INHERIT_RECURSION when id of one of the superRoles is the equals to
    // * roleId</li>
    // * <li>ERR_ROLE_NOT_EXIST when the id of any of roles refer in param is not exists</li>
    // * </ul>
    // */
    // public void setSuperRoles ( Long roleId, List superRoleIds ) throws RepositoryException;
    /**
     * to set user administrator's password
     * 
     * @param password to set
     * @throws RepositoryException ERR_USER_NOT_EXIST ua is not exists
     */
    public void setUAPassword ( String pwd ) throws RepositoryException;

    /**
     * set users of the role,if the userIds is null or empty list,will set the role to own no user
     * 
     * @param roleId id of the roles
     * @param userIds id of the users list of Long type
     * @throws RepositoryException <ul>
     *             <li>ERR_ROLE_NOT_EXIST is the role is not exists</li>
     *             <li>ERR_USER_NOT_EXIST is any of the user is not exists</li>
     *             </ul>
     */
    public void setUsersOfRole ( Long roleId, List userIds ) throws RepositoryException;

    /**
     * update the info of a role
     * 
     * @param role RoleInfo to updated
     * @throws RepositoryException <ul>
     *             <li>ERR_ROLE_NOT_EXIST if the role is not exists in db</li>
     *             <li>ERR_ROLE_ALREADY_EXIST if the role with the new name is exists in db</li>
     *             </ul>
     */
    public void updateRole ( RoleInfo role ) throws RepositoryException;

    /**
     * to update role's basic info and all related information including: <ur> <li>role's users</li>
     * <li>role's super roles</li> <li>role's basic info</li> </ur>
     * 
     * @param role role's information
     * @throws RepositoryException <ul>
     *             <li>ERR_ROLE_ID_ILLEGAL if the length of role name &gt; 32</li>
     *             <li>ERR_ROLE_NOT_EXIST if the role to update is not exists,or one the super roles
     *             of it is not exists</li>
     *             <li>ERR_ROLE_ALREADY_EXIST if the new name of the role is already the same with a
     *             nother existing role</li>
     *             <li>ERR_USER_NOT_EXIST if the user of this role is not exists.</li>
     *             </ul>
     * 
     */
    // public void updateRoleRelatedInfo ( RoleInfo role ) throws RepositoryException;
    /**
     * update the user's info
     * 
     * @param userInfo the userInfo to update
     * @throws RepositoryException <UL>
     *             <li>ERR_USER_NOT_EXIST if the userInfo to update is not exists</li>
     *             <li>ERR_USER_ALREADY_EXIST if the user with new loginName is aleady exists</li>
     *             </UL>
     */
    public void updateUser ( UserBasicInfo userBasicInfo ) throws RepositoryException;

    /**
     * to update a user's info and all related info including :<ur> <li>user's basic info</li> <li>
     * user's roles</li> </ur>
     * 
     * @param roleIds the ids of roles of the user
     * @param user the userInfo to update
     * 
     * @throws RepositoryException <ul>
     *             <li>ERR_USER_NOT_EXIST if the user with the userId is not exists in db</li>
     *             <li>ERR_ROLE_NOT_EXIST any of the role with the id list in roleIds is not exists
     *             in db</li>
     *             <li>ERR_USER_ALREADY_EXIST if the user with new loginName is aleady exists</li>
     *             </ul>
     */
    public void updateUserRelatedInfo ( UserBasicInfo userDetailInfo, List roleIds ) throws RepositoryException;

    /**
     * get super roles id of the role
     * 
     * @param roleId the subrole's id
     * @param storer
     * @return list of Long type
     * @throws RepositoryException
     */
    // public List getSuperRoleIds ( Long roleId, TransactionStorer storer )
    // throws RepositoryException;
    /**
     * get roles of the user
     * 
     * @param userId the user's id
     * @param storer
     * @return list of Long type
     * @throws RepositoryException
     */
    public List getRoleIds ( Long userId ) throws RepositoryException;

    /**
     * get roles that there id in roleIdss
     * 
     * @param roleIds list of Long type, an item is a role id
     * @param storer
     * @return List of RoleInfo instances
     * @throws RepositoryException
     */
    // public List getRoles ( List roleIds, TransactionStorer storer ) throws RepositoryException;
    /**
     * get system define password length
     * 
     * @return password length
     * @throws RepositoryException
     */
    public Integer getSysDefPwdLength () throws RepositoryException;

    /**
     * invalidate users
     * 
     * @param users list of Long instances of user Id,null or empty list will perform nothing
     * @throws RepositoryException happens when any database error happened
     */
    public void invalidateUsers ( List userIds ) throws RepositoryException;

    /**
     * validate users
     * 
     * @param users list of Long instances of user Id,null or empty list will perform nothing
     * @throws RepositoryException happens when any database error happened
     */
    public void validateUsers ( List userIds ) throws RepositoryException;

    /**
     * get system define password format
     * 
     * @return
     * @throws RepositoryException
     */
    public String getSysPwdFormat () throws RepositoryException;

    /**
     * check if the password is the password of the user
     * 
     * @param userLoginName the login name of the user
     * @param password password to check
     * @return true, if the password is for the username,else return false
     */
    public boolean checkUserPassword ( String userLoginName, String password ) throws RepositoryException;

    /**
     * @return all users that is not locked. every item is a instances of UserBasicInfo
     * @throws RepositoryException
     */
    public List getAllValidUser () throws RepositoryException;

    /**
     * to list the users in the database
     * 
     * @return: List of UserBasicInfo instances
     * @throws ReposioryException:exception when load user
     */

    public List getUsers ( String titUserName, String titLogId ) throws RepositoryException;

    /**
     * to list the usersbyname in the database
     * 
     * @return: List of UserBasicInfo instances
     * @throws ReposioryException:exception when load user
     */
    public List getUsersByName ( String titUserName ) throws RepositoryException;

    /**
     * to list the usersbyid in the database
     * 
     * @return: List of UserBasicInfo instances
     * @throws ReposioryException:exception when load user
     */
    public List getUsersById ( String titLogId ) throws RepositoryException;

    /**
     * 对用户工程关系表进行维护 add by tao_ding on 2009-04-02
     * 
     * @param prjName
     * @param loginName
     * @throws RepositoryException
     */
    public void saveUserProject ( List prjName, String loginName, int type ) throws RepositoryException;

    /**
     * 通过登陆名查询表是否有数据 add by tao_ding on 2009-04-02
     * 
     * @param loginName
     * @param type
     * @return
     * @throws RepositoryException
     */
    public List queryUsername ( String loginName, int type ) throws RepositoryException;

    /**
     * 通过登陆ID查询表IEAI_USER信息 add by tao_ding on 2009-04-09
     * 
     * @param userId
     * @return
     * @throws RepositoryException
     */
    public List queryUserInfo ( Long userId ) throws RepositoryException;

    /**
     * @param loginName 通过用户的登陆名称查询IID add by tao_ding on 2009-04-02
     * @return
     * @throws RepositoryException
     */
    public List getLoginName ( String loginName ) throws RepositoryException;

    List<ScriptUserBean> getGroupUserByIid (Long userIid, Connection conn );

    /**
     * 如果开启单点登录，用户没有退出的情况下，直接停止服务，会导致数据未清除
     * 未清除的情况下，用户登陆成功，但是无法进入页面
     * @return
     */
    public void delOldSessionUser();
}

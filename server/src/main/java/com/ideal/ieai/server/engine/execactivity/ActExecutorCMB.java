package com.ideal.ieai.server.engine.execactivity;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Hashtable;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.adaptors.commadaptors.delayer.DelayerAct;
import com.ideal.ieai.adaptors.commadaptors.delayer.Helper;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.task.TimeLimitInCore;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.AgentCommunicationException;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.activity.IActivity;
import com.ideal.ieai.core.activity.IUserTaskActivity;
import com.ideal.ieai.core.activity.OtherException;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.agent.ActivityRExecHelper;
import com.ideal.ieai.server.engine.core.ActivityContextManager;
import com.ideal.ieai.server.engine.core.ResourceManager;
import com.ideal.ieai.server.engine.core.Scheduler;
import com.ideal.ieai.server.engine.expreval.Evaluate;
import com.ideal.ieai.server.engine.util.DBRexecRequestWrapperRepository;
import com.ideal.ieai.server.engine.util.IExecContext;
import com.ideal.ieai.server.engine.util.WorkflowInstance;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.activity.RepActivityRuntime;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.engine.DbOpScheduler;
import com.ideal.ieai.server.repository.engine.EngineRepository;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.engine.RepWorkflowInstance;
import com.ideal.ieai.server.repository.workflow.RepActRunInfo;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;

/**
 * <li>Title: ActExecutor.java</li> <li>Project: server</li> <li>Package:
 * com.ideal.ieai.server.engine.execactivity</li> <li>Description: This class is the executor of
 * activity. <li> <li>Copyright: Copyright (c) 2006</li> <li>Company: IdealTechnologies</li> <li>
 * Created on 07-Jun-2006, 15:12:40</li>
 * 
 * <AUTHOR> liancheng_gu
 * @version iEAI v3.5
 */
public class ActExecutorCMB extends AbstractActExecutor
{

    static protected final int  CONTINUE = 0;

    static protected final int  RETURN   = 1;

    static private final Logger _log     = Logger.getLogger(ActExecutorCMB.class);

    public ActExecutorCMB(ExecAct execAct, IExecContext execCtx, BasicActElement actElem)
    {
        super(execAct, actElem, execCtx);
    }

    protected void localExec () throws ServerException, ActivityException
    {
        // get Workflow instance infomation with workflow id
        ResourceManager resMgr = Engine.getInstance().getResourceManager();
        ActivityContextManager actCtxMgr = Engine.getInstance().getActCtxManager();
        Scheduler scheduler = Engine.getInstance().getScheduler();
        switch (_execAct.getRuningPosition())
        {
            case ExecAct.POSITION_BEGIN:
                long beginTime1 = System.currentTimeMillis();
                if (_actElem.getID() == Workflow.getStartId())
                {
                    _execAct.setRuningPosition(ExecAct.POSITION_ACT_END);
                    processPositionActEnd(actCtxMgr, resMgr, scheduler);
                    return;
                }
            case ExecAct.POSITION_CAL_VALID_TIME:
            case ExecAct.POSITION_EXECUTE_ACT:
                WorkflowManager.getInstance().saveActInfo(RepActRunInfo.newActInfo(_execAct.getFlowId(), _execCtx, _actElem));
                try
                {
                    executeAct(scheduler, resMgr, actCtxMgr);
                } catch (RepositoryException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ActExecutorCMB ", e);
                }
                return;
            case ExecAct.POSITION_ACT_END:
                processPositionActEnd(actCtxMgr, resMgr, scheduler);
                return;
        }

    }

    private RepActRunInfo newActinfo ( RepActRunInfo info )
    {
        String defname = info.getActDefName();
        if (null != defname && !"".equals(defname))
        {
            if (defname.equalsIgnoreCase("shellcmd") || defname.equalsIgnoreCase("External Function") || defname.equalsIgnoreCase("Receive Mail")
                    || defname.equalsIgnoreCase("Send Mail") || defname.equalsIgnoreCase("Create File") || defname.equalsIgnoreCase("Write File")
                    || defname.equalsIgnoreCase("Rename File") || defname.equalsIgnoreCase("Read File") || defname.equalsIgnoreCase("Get File List")
                    || defname.equalsIgnoreCase("Detect File Change") || defname.equalsIgnoreCase("Delete File") || defname.equalsIgnoreCase("Usertask")
                    || defname.equalsIgnoreCase("Ftp Remove File") || defname.equalsIgnoreCase("Ftp Rename File") || defname.equalsIgnoreCase("Ftp Make Dir")
                    || defname.equalsIgnoreCase("Ftp Upload File") || defname.equalsIgnoreCase("Ftp Retrieve File") || defname.equalsIgnoreCase("Ftp Dir")
                    || defname.equalsIgnoreCase("Ftp Get Serverinfo"))
            {
                DbOpScheduler opScheduler = new DbOpScheduler();
                try
                {
                    WorkflowInstance in = opScheduler.getPrjAndFlowName(_execAct.getFlowId());
                    info.setPrjName(in.getProjectName());
                    info.setFlowName(in.getFlowName());
                } catch (RepositoryException e)
                {
                    _log.info("set newActinfo is error");
                }
            }
        }

        return info;
    }

    protected int caculateValidTime ( ActivityContextManager actCtxMgr, Scheduler scheduler ) throws ServerException, ActivityException
    {

        int ret = actCtxMgr.calValidTime(_execAct.getFlowId(), _execAct.getActId());
        if (ret == Constants.REPEAT)
        {
            scheduler.updateExecActAndMakeItPendingForIp(_execAct);
            return RETURN;
        }
        int result = actCtxMgr.getCalValidTimeResult(_execAct.getFlowId(), _execAct.getActId());
        if (result == Constants.OUTOF_VALID_TIME)
        {
            _execAct.setRuningPosition(ExecAct.POSITION_ACT_END);
            scheduler.updateExecActAndMakeItPendingForIp(_execAct);
            return RETURN;
        }
        return CONTINUE;
    }

    protected int caculateValidTimeMy ( ActivityContextManager actCtxMgr, Scheduler scheduler ) throws ServerException, ActivityException
    {

        int ret = actCtxMgr.calValidTime(_execAct.getFlowId(), _execAct.getActId());
        if (ret == Constants.REPEAT)
        {
            return RETURN;
        }
        int result = actCtxMgr.getCalValidTimeResult(_execAct.getFlowId(), _execAct.getActId());
        if (result == Constants.OUTOF_VALID_TIME)
        {
            return RETURN;
        }
        return CONTINUE;
    }

    protected void processPositionActEnd ( ActivityContextManager actCtxMgr, ResourceManager resMgr, Scheduler scheduler ) throws ServerException,
            ActivityException
    {
        BasicActElement sucElm = Evaluate.evalNextBranch(_actElem);
        if (null == sucElm)
        {
            Engine.getInstance().getBranchManager().endBranch(null, _execCtx, _execAct);
            try
            {
                long execactId = EngineRepository.getInstance().queryPendingExecActForCallflow(_execAct.getFlowId());
                if (execactId > 0)
                {
                    EngineRepository.getInstance().updateExecActState(execactId, ExecAct.PENDING);
                }
            } catch (Throwable ee)
            {
                throw new ActivityException(ee.getMessage());
            }
            return;
        }
        // 查询一下现在的execact活动是否为server所属，否则不执行下面代码
        boolean isContorl = EngineRepository.getInstance().isContorlExec(_execAct.getSerialNo());
        if (isContorl)
        {
            ExecAct sucExecAct = new ExecAct(_execAct, sucElm);
            scheduler.appendExecAct(sucExecAct, _execAct);
        }
    }

    protected int beforeExecAct ( ActivityContextManager actCtxMgr ) throws ServerException, ActivityException
    {
        return CONTINUE;
    }

    private Map getAndCheckInput ( IActivity actInst ) throws OtherException
    {
        Map input = null;
        try
        {
            if (actInst.getInputDef().size() == 0)
            {
                _log.info("actInst.getInputDef() size is 0!  " + _execAct.getActName());
            }
        } catch (Exception ee)
        {
            _log.error("Error when get actInst info !");
        }
        input = Evaluate.evalActInput(_actElem, actInst.getInputDef());
        try
        {
            Evaluate.checkAndConvertInput(input, actInst.getInputDef());
        } catch (OtherException ae)
        {
            ae.setLocation(ActivityException.OUTPUT_EXCEPTION);
            ae.setMessage("Check and Convert Input Error!");
            ae.setActivityName(_actElem.getName());
            ae.setTime(new Date());
            throw ae;
        }
        return input;
    }

    private void executeAct ( Scheduler scheduler, ResourceManager resMgr, ActivityContextManager actCtxMgr )
            throws ActivityException, ServerException, RepositoryException
    {
        IActivity actInst;
        try
        {
            actInst = resMgr.getActivityWithCache(_execAct.getFlowId(), _actElem, Constants.IEAI_IEAI_BASIC);
        } catch (ServerException e)
        {
            ActivityException actExp = new ActivityException(e);
            actExp.setActivityName(_actElem.getName());
            actExp.setLocation(ActivityException.CONFIGURATION_EXCEPTION);
            actExp.setMessage("Load Act Error!");
            actExp.setTime(new Date());
            throw actExp;
        }

        Map input = null;

        try
        {
            if (actInst.getInputDef().size() == 0)
            {
                _log.info("actInst.getInputDef() size is 0!  " + _execAct.getActName());
            }
        } catch (Exception ee)
        {
            _log.error("Error when get actInst info !");
        }

        input = Evaluate.evalActInput(_actElem, actInst.getInputDef());
        try
        {
            Evaluate.checkAndConvertInput(input, actInst.getInputDef());
        } catch (OtherException ae)
        {
            ae.setLocation(ActivityException.OUTPUT_EXCEPTION);
            ae.setMessage("Check and Convert Input Error!");
            ae.setActivityName(_actElem.getName());
            ae.setTime(new Date());
            throw ae;
        }
        Map output = new Hashtable();
        if (!_execAct.containsFlag(ExecAct.FLAG_ACT_START))
        {
            if (!(actInst instanceof IUserTaskActivity))
            {
                actCtxMgr.setLatestState(_execAct.getFlowId(), _execAct.getActId(), Constants.ACT_STATE_RUNNING, Constants.IEAI_IEAI_BASIC);
                actCtxMgr.setBeginExcTime(_execAct.getFlowId(), _execAct.getActId(), new Date(), Constants.IEAI_IEAI_BASIC);
            }
            input.put("actTempName", _actElem.getName());
            _execAct.addFlag(ExecAct.FLAG_ACT_START);

            _execAct.setActInput(input);
            if (_actElem.isRemoteExec())
            {

                for (int i = 0;; i++)
                {
                    try
                    {
                        Connection con = null;

                        try
                        {
                            con = DBResource.getSchedulerConnection(
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                                Constants.IEAI_IEAI_BASIC);
                            String requestId = ActivityRExecHelper.createRExecRequest(_execAct.getFlowId(), _execCtx,
                                (ActivityElement) _actElem, input, con);

                            _execAct.setRexecRequestId(requestId);
                            scheduler.updateExecActForIp(_execAct, con);
                            con.commit();
                            _log.info("create RExecRequest. id=" + requestId + " ,actname=" + _actElem.getName()
                                    + " ,flowid=" + _execAct.getFlowId());

                        } catch (Exception e)
                        {
                            DBResource.rollback(con, ServerError.ERR_DB_COMMIT, e, "createRExecRequest", _log);
                        } finally
                        {
                            DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(),
                                _log);
                        }
                    } catch (RepositoryException ex)
                    {
                        DBRetryUtil.waitForNextTry(i, ex);
                    }
                }
            }
        }

        int ret = Constants.END;
        if (_actElem.isRemoteExec())
        {
            if (!_execAct.containsFlag(ExecAct.FLAG_REMOTE_ACT_CHECK_OUTPUT))
            {
                ret = ActivityRExecHelper.exec(_execAct, _execAct.getRexecRequestId());
                if (ret == Constants.END)
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveActFlag(ExecAct.FLAG_REMOTE_ACT_CHECK_OUTPUT_FINALL,
                            _execAct.getSerialNo());
                    } catch (RepositoryException e)
                    {
                        _log.error("actexcutor saveactflag error! ", e);
                    }
                    return;
                }
            } else
            {
                try
                {
                    ret = ActivityRExecHelper.checkOutput(_execAct, _execAct.getRexecRequestId(), output);
                } catch (ServerException e)
                {
                    AgentCommunicationException agentExp = new AgentCommunicationException(e);
                    agentExp.setActivityName(_actElem.getName());
                    agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                    agentExp.setMessage("Communication Agent Error!");
                    agentExp.setTime(new Date());
                    agentExp.setAgentHost(((ActivityElement) _actElem).getRemoteHost());
                    agentExp.setAgentPort(String.valueOf(((ActivityElement) _actElem).getRemotePort()));
                    agentExp.setConnectionType(((ActivityElement) _actElem).getConnectType());
                    ActivityException actExp = agentExp;
                    throw actExp;
                }
            }
        } else
        {
            if (actInst instanceof DelayerAct)
            {
                long shouldendtime = execDelay(input, _execAct.getFlowId());
                input.put("shouldendtime", new Long(shouldendtime));
            }
            ret = actInst.execute(_execAct.getActStateData(), input, output);
        }
        if (ret == Constants.REPEAT)
        {
            if (!_actElem.isRemoteExec())
            {
                if (actInst instanceof IUserTaskActivity)
                {
                    if (0 == _execAct.getActStateData().getIntData())
                    {
                        scheduler.updateExecActAndMakeItPendingForIp(_execAct);
                    } else
                    {
                        try
                        {
                            EngineRepository.getInstance().updateExecAct(_execAct);
                        } catch (RepositoryException e)
                        {
                            throw new ServerException(e.getServerError().getErrCode());
                        }
                    }
                } else
                {
                    scheduler.updateExecActAndMakeItPendingForIp(_execAct);
                }
            }
            return;
        }

        try
        {
            Evaluate.checkOutput(output, actInst.getOutputDef());
        } catch (OtherException otherExp)
        {
            otherExp.setActivityName(_actElem.getName());
            throw otherExp;
        }

        _execCtx.saveActOuput(output);
        // Evaluate post condition.
        Evaluate.evalPostCondition(_actElem);

        _execAct.setRuningPosition(ExecAct.POSITION_ACT_END);
        if (!(actInst instanceof IUserTaskActivity))
        {

            DbOpScheduler opScheduler = new DbOpScheduler();
            try
            {
                opScheduler.setLatestState(_execAct.getFlowId(), _execAct.getActId(), Constants.ACT_STATE_FINISH, Constants.IEAI_IEAI_BASIC);
                opScheduler.setEndTime(_execAct.getFlowId(), _execAct.getActId(), DBUtil.getTime(new Date()));
            } catch (RepositoryException e)
            {
                _log.error("error while setting the state of activity runtime", e);
                throw new ServerException(e.getServerError());
            }
        }
        processPositionActEnd(actCtxMgr, resMgr, scheduler);
        if (_actElem.isRemoteExec())
        {
            DBRexecRequestWrapperRepository dbre = new DBRexecRequestWrapperRepository();
            dbre.removeRExecReqWrapper(_execAct.getRexecRequestId());
        }
    }

    private int doRemoteAct ( int ret, Map output ) throws ActivityException, ServerException
    {

        if (!_execAct.containsFlag(ExecAct.FLAG_REMOTE_ACT_CHECK_OUTPUT))
        {
            ret = ActivityRExecHelper.exec(_execAct, _execAct.getRexecRequestId());
            if (ret == Constants.END)
            {
                _execAct.addFlag(ExecAct.FLAG_REMOTE_ACT_CHECK_OUTPUT);
                ret = Constants.REPEAT;
                // 添加报存活动信息
                try
                {
                    EngineRepository.getInstance().updateExecAct(_execAct);
                } catch (RepositoryException e)
                {

                }
                return ret;
            }
        } else
        {
            try
            {
                ret = ActivityRExecHelper.checkOutput(_execAct, _execAct.getRexecRequestId(), output);
            } catch (ServerException e)
            {
                AgentCommunicationException agentExp = new AgentCommunicationException(e);
                agentExp.setActivityName(_actElem.getName());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                _log.info("_execAct.getFlag():" + _execAct.getFlag());
                if (_execAct.getFlag() == 5)
                {
                    // 业务异常
                    agentExp.setMessage("Business exceptions!");
                } else
                {
                    agentExp.setMessage("Communication Agent Error!");
                }
                //
                agentExp.setTime(new Date());
                agentExp.setAgentHost(((ActivityElement) _actElem).getRemoteHost());
                agentExp.setAgentPort(String.valueOf(((ActivityElement) _actElem).getRemotePort()));
                agentExp.setConnectionType(((ActivityElement) _actElem).getConnectType());
                ActivityException actExp = agentExp;
                throw actExp;
            }
        }
        return ret;

    }

    private long execDelay ( Map input, long flowId )
    {
        DbOpScheduler opScheduler = new DbOpScheduler();

        DefaultConfig dc = (DefaultConfig) ((ActivityElement) _actElem).getActConfig();
        int type = Integer.valueOf((String) dc.get(Helper.SAVE_DELAY_TYPE)).intValue();
        Long year = null, month = null, day = null, hour = null, minute = null, second = null;
        if (null != dc.get(Helper.USE_DYN) && ((Boolean) dc.get(Helper.USE_DYN)).booleanValue())
        {
            year = (Long) input.get(Helper.YEAR);
            month = (Long) input.get(Helper.MONTH);
            day = (Long) input.get(Helper.DAY);
            hour = (Long) input.get(Helper.HOUR);
            minute = (Long) input.get(Helper.MINUTE);
            second = (Long) input.get(Helper.SECOND);
            if (null != input.get(Helper.ISABS))
            {
                boolean timeType = Boolean.parseBoolean(input.get(Helper.ISABS) + "");
                if (timeType)
                {
                    type = Helper.ABS_TIME_DELAY;
                } else
                {
                    type = Helper.TIME_DELAY;
                }
            }
        } else
        {
            String sTimes[] = TimeLimitInCore.parseTimeStr((String) dc.get(Helper.SAVE_TIME_STR));
            if (!"".equals(sTimes[0]))
            {
                year = Long.valueOf(sTimes[0]);
            }
            if (!"".equals(sTimes[1]))
            {
                month = Long.valueOf(sTimes[1]);
            }
            if (!"".equals(sTimes[2]))
            {
                day = Long.valueOf(sTimes[2]);
            }
            if (!"".equals(sTimes[3]))
            {
                hour = Long.valueOf(sTimes[3]);
            }
            if (!"".equals(sTimes[4]))
            {
                minute = Long.valueOf(sTimes[4]);
            }
            if (!"".equals(sTimes[5]))
            {
                second = Long.valueOf(sTimes[5]);
            }
        }
        Calendar calendar = new GregorianCalendar();
        Integer fieldPushed = null;
        if (type == Helper.ABS_TIME_DELAY)
        {
            if (null != year)
            {
                calendar.set(Calendar.YEAR, year.intValue());
            } else
            {
                fieldPushed = new Integer(Calendar.YEAR);
            }
            if (null != month)
            {
                calendar.set(Calendar.MONTH, month.intValue() - 1);
            } else
            {
                fieldPushed = new Integer(Calendar.MONTH);
            }
            if (null != day)
            {
                if (day.intValue() > calendar.getActualMaximum(Calendar.DATE))
                {
                    calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
                } else
                {
                    calendar.set(Calendar.DATE, day.intValue());
                }
            } else
            {
                fieldPushed = new Integer(Calendar.DATE);
            }
            if (null != hour)
            {
                calendar.set(Calendar.HOUR_OF_DAY, hour.intValue());
            } else
            {
                fieldPushed = new Integer(Calendar.HOUR_OF_DAY);
            }
            if (null != minute)
            {
                calendar.set(Calendar.MINUTE, minute.intValue());
            } else
            {
                fieldPushed = new Integer(Calendar.MINUTE);
            }
            if (null != second)
            {
                calendar.set(Calendar.SECOND, second.intValue());
            } else
            {
                fieldPushed = new Integer(Calendar.SECOND);
            }
        } else if (type == Helper.TIME_DELAY)
        {
            if (null != year)
            {
                calendar.add(Calendar.YEAR, year.intValue());
            }
            if (null != month)
            {
                calendar.add(Calendar.MONTH, month.intValue());
            }
            if (null != day)
            {
                calendar.add(Calendar.DATE, day.intValue());
            }
            if (null != hour)
            {
                calendar.add(Calendar.HOUR_OF_DAY, hour.intValue());
            }
            if (null != minute)
            {
                calendar.add(Calendar.MINUTE, minute.intValue());
            }
            if (null != second)
            {
                calendar.add(Calendar.SECOND, second.intValue());
            }
        }
        long targetMillSec = calendar.getTimeInMillis();

        targetMillSec = getShouldEndTime(targetMillSec, fieldPushed, flowId, calendar, type);
        try
        {
            opScheduler.updateShouldEndTime(_execAct.getFlowId(), _execAct.getActId(), targetMillSec);
        } catch (RepositoryException e)
        {
            _log.error("updateShouldEndTime time err");
        }
        return targetMillSec;
    }

    public long getShouldEndTime ( long targetMillSec, Integer fieldPushed, long flowId, Calendar calendar, int type )
    {
        long curMillSec = System.currentTimeMillis();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        // 延迟时间小于当前时间，则进入
        if (type == Helper.ABS_TIME_DELAY && curMillSec >= targetMillSec && fieldPushed != null)
        {
            // 当延迟器应该结束的时间正好在工作流的暂停开始时间及暂停结束时间之间，则延迟器直接运行结束
            boolean skip = false;
            RepWorkflowInstance flowInstance = null;
            try
            {
                flowInstance = EngineRepositotyJdbc.getInstance().getFlowInstanceEndPauseTime(flowId);
            } catch (RepositoryException e)
            {
                _log.error("getFlowInstancePause time err");
            }
            // 判断是否暂停过，如果没有暂停过，则正常运行。
            if (null != flowInstance && 0 != flowInstance.getPauseEndTime() && 0 != flowInstance.getPauseTime())
            {
                DbOpScheduler dbOpScheduler = new DbOpScheduler();
                RepActivityRuntime actRuntimeInfo = null;
                try
                {
                    actRuntimeInfo = dbOpScheduler.getActRuntimeAndState(flowId, _actElem.getID());
                } catch (RepositoryException e1)
                {
                    _log.error("getFlowInstancePause actRuntimeInfo err");
                }
                Calendar pauseTime = new GregorianCalendar();
                pauseTime.setTimeInMillis(flowInstance.getPauseTime());
                Calendar pauseEndTime = new GregorianCalendar();
                pauseEndTime.setTimeInMillis(flowInstance.getPauseEndTime());

                // 如果跨天
                if (!format.format(pauseTime.getTime()).equals(format.format(pauseEndTime.getTime())))
                {
                    // 当延迟器预计结束时间在暂停区间内时，可以略过
                    if (targetMillSec > flowInstance.getPauseTime() && targetMillSec < flowInstance.getPauseEndTime())
                    {
                        // 当活动的结束时间为空时，证明该活动为第一次执行，延迟器直接略过。如果有结束时间，并且结束时间小于暂停的开始时间，也直接结束。
                        if (null != actRuntimeInfo && (0 == actRuntimeInfo.getEndTime() || actRuntimeInfo.getEndTime() < flowInstance.getPauseTime()))
                        {
                            skip = true;
                        }
                    } else
                    {
                        Calendar shouldEndTime = new GregorianCalendar();
                        shouldEndTime.setTimeInMillis(targetMillSec);
                        shouldEndTime.set(Calendar.DATE, pauseTime.get(Calendar.DATE));
                        shouldEndTime.set(Calendar.MONTH, pauseTime.get(Calendar.MONTH));
                        shouldEndTime.set(Calendar.YEAR, pauseTime.get(Calendar.YEAR));
                        // 将延迟器预计结束时间回拨到暂停开始时间的日期内
                        if (shouldEndTime.getTimeInMillis() > flowInstance.getPauseTime() && shouldEndTime.getTimeInMillis() < flowInstance.getPauseEndTime())
                        {
                            // 当活动的结束时间为空时，证明该活动为第一次执行，延迟器直接略过。如果有结束时间，并且结束时间小于暂停的开始时间，也直接结束。
                            if (null != actRuntimeInfo && (0 == actRuntimeInfo.getEndTime() || actRuntimeInfo.getEndTime() < flowInstance.getPauseTime()))
                            {
                                targetMillSec = shouldEndTime.getTimeInMillis();
                                skip = true;
                                _log.info("DelayerAct way out 1");
                            }
                        }
                    }
                } else
                {
                    // 当延迟器预计结束时间在暂停区间内时，可以略过
                    if (targetMillSec > flowInstance.getPauseTime() && targetMillSec < flowInstance.getPauseEndTime())
                    {
                        // 当活动的结束时间为空时，证明该活动为第一次执行，延迟器直接略过。如果有结束时间，并且结束时间小于暂停的开始时间，也直接结束。
                        if (null != actRuntimeInfo && (0 == actRuntimeInfo.getEndTime() || actRuntimeInfo.getEndTime() < flowInstance.getPauseTime()))
                        {
                            skip = true;
                            _log.info("DelayerAct way out 2");
                        }
                    }
                }
            }
            if (skip)
            {
                _log.info("delay end ! shouldEndTime : " + targetMillSec + "  flowPauseTime: " + flowInstance.getPauseTime() + "  flowPauseEndTime: "
                        + flowInstance.getPauseEndTime());
            } else
            {
                calendar.add(fieldPushed.intValue(), 1);
                targetMillSec = calendar.getTimeInMillis();
            }
            // end
        } else if (type == Helper.ABS_TIME_DELAY && curMillSec < targetMillSec && fieldPushed != null)
        {
            // 延迟时间大于当前时间，则进入
            RepWorkflowInstance flowInstance = null;
            try
            {
                flowInstance = EngineRepositotyJdbc.getInstance().getFlowInstanceEndPauseTime(flowId);
            } catch (RepositoryException e)
            {
                _log.error("getFlowInstancePause time err");
            }
            // 判断是否暂停过，如果没有暂停过，则正常运行。
            if (null != flowInstance && 0 != flowInstance.getPauseEndTime() && 0 != flowInstance.getPauseTime())
            {
                DbOpScheduler dbOpScheduler = new DbOpScheduler();
                RepActivityRuntime actRuntimeInfo = null;
                try
                {
                    actRuntimeInfo = dbOpScheduler.getActRuntimeAndState(flowId, _actElem.getID());
                } catch (RepositoryException e1)
                {
                    _log.error("getFlowInstancePause actRuntimeInfo err");
                }
                Calendar pauseTime = new GregorianCalendar();
                pauseTime.setTimeInMillis(flowInstance.getPauseTime());
                Calendar pauseEndTime = new GregorianCalendar();
                pauseEndTime.setTimeInMillis(flowInstance.getPauseEndTime());

                // 判断是否跨天
                if (!format.format(pauseTime.getTime()).equals(format.format(pauseEndTime.getTime())))
                {
                    // 当延迟器预计结束时间在暂停区间内时，可以略过
                    if (targetMillSec > flowInstance.getPauseTime() && targetMillSec < flowInstance.getPauseEndTime())
                    {
                        // 当活动的结束时间为空时，证明该活动为第一次执行，延迟器直接略过。如果有结束时间，并且结束时间小于暂停的开始时间，也直接结束。
                        if (null != actRuntimeInfo && (0 == actRuntimeInfo.getEndTime() || actRuntimeInfo.getEndTime() < flowInstance.getPauseTime()))
                        {
                            _log.info("DelayerAct way out 3");
                            _log.info("delay end ! shouldEndTime : " + targetMillSec + "  flowPauseTime: " + flowInstance.getPauseTime()
                                    + "  flowPauseEndTime: " + flowInstance.getPauseEndTime());
                        }
                    } else
                    {
                        Calendar shouldEndTime = new GregorianCalendar();
                        shouldEndTime.setTimeInMillis(targetMillSec);
                        shouldEndTime.set(Calendar.DATE, pauseTime.get(Calendar.DATE));
                        shouldEndTime.set(Calendar.MONTH, pauseTime.get(Calendar.MONTH));
                        shouldEndTime.set(Calendar.YEAR, pauseTime.get(Calendar.YEAR));
                        // 将延迟器预计结束时间回拨到暂停开始时间的日期内
                        if (shouldEndTime.getTimeInMillis() > flowInstance.getPauseTime() && shouldEndTime.getTimeInMillis() < flowInstance.getPauseEndTime())
                        {
                            // 当活动的结束时间为空时，证明该活动为第一次执行，延迟器直接略过。如果有结束时间，并且结束时间小于暂停的开始时间，也直接结束。
                            if (null != actRuntimeInfo && (0 == actRuntimeInfo.getEndTime() || actRuntimeInfo.getEndTime() < flowInstance.getPauseTime()))
                            {
                                targetMillSec = shouldEndTime.getTimeInMillis();
                                _log.info("DelayerAct way out 4");
                                _log.info("delay end ! shouldEndTime : " + targetMillSec + "  flowPauseTime: " + flowInstance.getPauseTime()
                                        + "  flowPauseEndTime: " + flowInstance.getPauseEndTime());
                            }
                        }
                    }
                }
            }
        }
        return targetMillSec;
    }

    /**
     * 延时器结束时赋予状态
     * 
     * @param dbOpScheduler
     * @param resMgr
     * @throws ActivityException
     * @throws ServerException
     */
    private void endDelayAct ( DbOpScheduler dbOpScheduler, ResourceManager resMgr ) throws ActivityException, ServerException
    {
        IActivity actInst;
        try
        {
            actInst = resMgr.getActivityWithCache(_execAct.getFlowId(), _actElem, Constants.IEAI_IEAI_BASIC);
        } catch (ServerException e)
        {
            ActivityException actExp = new ActivityException(e);
            actExp.setActivityName(_actElem.getName());
            actExp.setLocation(ActivityException.CONFIGURATION_EXCEPTION);
            actExp.setMessage("Load Act Error!");
            actExp.setTime(new Date());
            throw actExp;
        }
        if (!(actInst instanceof DelayerAct))
        {
            return;
        } else
        {
            try
            {
                dbOpScheduler.setLatestState(_execAct.getFlowId(), _execAct.getActId(), Constants.ACT_STATE_FINISH, Constants.IEAI_IEAI_BASIC);
                dbOpScheduler.setEndTime(_execAct.getFlowId(), _execAct.getActId(), DBUtil.getTime(new Date()));
            } catch (RepositoryException e)
            {
                _log.error("error while setting the state of activity runtime for delay act", e);
                throw new ServerException(e.getServerError());
            }
        }
    }
}

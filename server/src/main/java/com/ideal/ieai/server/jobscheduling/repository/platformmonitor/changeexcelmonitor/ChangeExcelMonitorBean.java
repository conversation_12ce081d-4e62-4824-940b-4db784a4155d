/**
 * All rights Reserved, Designed By www.idealinfo.com
 * 
 * @Title: ChangeExcelMonitorBean.java
 * @Package com.ideal.ieai.server.jobScheduling.repository.platformMonitor.changeExcelMonitor
 * @Description: 变更过程监控 Bean
 * @author: 理想科技
 * @date: 2018年2月9日 下午1:22:44
 * @version V1.0
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved.
 * 
 */
package com.ideal.ieai.server.jobscheduling.repository.platformmonitor.changeexcelmonitor;

import java.sql.Blob;

import com.ideal.ieai.server.jobscheduling.repository.BaseBean;

/**
 * @ClassName: ChangeExcelMonitorBean
 * @Description:变更过程监控 Bean
 * @author: zhen_sui
 * @date: 2018年2月9日 下午1:22:44
 * 
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved.
 * 
 */
public class ChangeExcelMonitorBean extends BaseBean
{
    private String id;
    private String systemName;
    private String projectName;
    private String fileName;
    private String updateUser;
    private String updateTime;
    private String updateTimeqs;
    private String updateTimeqe;
    private Blob fileContent;
    

    public Blob getFileContent ()
    {
        return fileContent;
    }

    public void setFileContent ( Blob fileContent )
    {
        this.fileContent = fileContent;
    }

    public String getSystemName ()
    {
        return systemName;
    }

    public void setSystemName ( String systemName )
    {
        this.systemName = systemName;
    }

    public String getProjectName ()
    {
        return projectName;
    }

    public void setProjectName ( String projectName )
    {
        this.projectName = projectName;
    }

    public String getFileName ()
    {
        return fileName;
    }

    public void setFileName ( String fileName )
    {
        this.fileName = fileName;
    }

    public String getUpdateUser ()
    {
        return updateUser;
    }

    public void setUpdateUser ( String updateUser )
    {
        this.updateUser = updateUser;
    }

    public String getUpdateTime ()
    {
        return updateTime;
    }

    public void setUpdateTime ( String updateTime )
    {
        this.updateTime = updateTime;
    }

    public String getUpdateTimeqs ()
    {
        return updateTimeqs;
    }

    public void setUpdateTimeqs ( String updateTimeqs )
    {
        this.updateTimeqs = updateTimeqs;
    }

    public String getUpdateTimeqe ()
    {
        return updateTimeqe;
    }

    public void setUpdateTimeqe ( String updateTimeqe )
    {
        this.updateTimeqe = updateTimeqe;
    }

    

    public String getId ()
    {
        return id;
    }

    public void setId ( String id )
    {
        this.id = id;
    }
}

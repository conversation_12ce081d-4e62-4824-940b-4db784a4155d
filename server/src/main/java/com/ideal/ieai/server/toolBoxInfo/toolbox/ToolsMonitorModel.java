package com.ideal.ieai.server.toolBoxInfo.toolbox;

public class ToolsMonitorModel {
    private Long iid;
    private Long itoolsId;
    private String itoolName;
    private Long ideliveryAuditor;
    private Long ireceptionAuditor;
    private String itoolScriptType;
    private String itoolScriptName;
    private String itoolScriptUuid;
    private Long itoolAuditorId;
    private String executor;
    private String neventId;
    private Long workitemId;
    private Long taskId;
    private Long runStatus;
    private Long iexcStartTime;
    private Long iexcEndTime;
    private Long iprojectId;

    public Long getIid() {
        return iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    public Long getItoolsId() {
        return itoolsId;
    }

    public void setItoolsId(Long itoolsId) {
        this.itoolsId = itoolsId;
    }

    public String getItoolName() {
        return itoolName;
    }

    public void setItoolName(String itoolName) {
        this.itoolName = itoolName;
    }

    public Long getIdeliveryAuditor() {
        return ideliveryAuditor;
    }

    public void setIdeliveryAuditor(Long ideliveryAuditor) {
        this.ideliveryAuditor = ideliveryAuditor;
    }

    public Long getIreceptionAuditor() {
        return ireceptionAuditor;
    }

    public void setIreceptionAuditor(Long ireceptionAuditor) {
        this.ireceptionAuditor = ireceptionAuditor;
    }

    public String getItoolScriptType() {
        return itoolScriptType;
    }

    public void setItoolScriptType(String itoolScriptType) {
        this.itoolScriptType = itoolScriptType;
    }

    public String getItoolScriptName() {
        return itoolScriptName;
    }

    public void setItoolScriptName(String itoolScriptName) {
        this.itoolScriptName = itoolScriptName;
    }

    public String getItoolScriptUuid() {
        return itoolScriptUuid;
    }

    public void setItoolScriptUuid(String itoolScriptUuid) {
        this.itoolScriptUuid = itoolScriptUuid;
    }

    public Long getItoolAuditorId() {
        return itoolAuditorId;
    }

    public void setItoolAuditorId(Long itoolAuditorId) {
        this.itoolAuditorId = itoolAuditorId;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public String getNeventId() {
        return neventId;
    }

    public void setNeventId(String neventId) {
        this.neventId = neventId;
    }

    public Long getWorkitemId() {
        return workitemId;
    }

    public void setWorkitemId(Long workitemId) {
        this.workitemId = workitemId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(Long runStatus) {
        this.runStatus = runStatus;
    }

    public Long getIexcStartTime() {
        return iexcStartTime;
    }

    public void setIexcStartTime(Long iexcStartTime) {
        this.iexcStartTime = iexcStartTime;
    }

    public Long getIexcEndTime() {
        return iexcEndTime;
    }

    public void setIexcEndTime(Long iexcEndTime) {
        this.iexcEndTime = iexcEndTime;
    }

    public Long getIprojectId() {
        return iprojectId;
    }

    public void setIprojectId(Long iprojectId) {
        this.iprojectId = iprojectId;
    }

    @Override
    public String toString() {
        return "ToolsMonitorModel{" +
                "iid=" + iid +
                ", itoolsId=" + itoolsId +
                ", itoolName='" + itoolName + '\'' +
                ", ideliveryAuditor=" + ideliveryAuditor +
                ", ireceptionAuditor=" + ireceptionAuditor +
                ", itoolScriptType='" + itoolScriptType + '\'' +
                ", itoolScriptName='" + itoolScriptName + '\'' +
                ", itoolScriptUuid='" + itoolScriptUuid + '\'' +
                ", itoolAuditorId=" + itoolAuditorId +
                ", executor='" + executor + '\'' +
                ", neventId='" + neventId + '\'' +
                ", workitemId=" + workitemId +
                ", taskId=" + taskId +
                ", runStatus=" + runStatus +
                ", iexcStartTime=" + iexcStartTime +
                ", iexcEndTime=" + iexcEndTime +
                ", iprojectId=" + iprojectId +
                '}';
    }
}

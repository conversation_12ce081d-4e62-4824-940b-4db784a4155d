package com.ideal.ieai.server.cqsendcompare;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.sus.cmdb.CMDB_MAINService;
import com.ideal.ieai.server.repository.sus.cmdb.CMDB_IPService;
import com.ideal.ieai.server.repository.sus.cmdb.CMDB_OPSService;
import com.ideal.ieai.server.repository.sus.cmdb.CMDB_SYSService;


/**
 * 
 * <ul>
 * <li>Title: SendApolloCloudThread.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2021年7月6日
 */
public class SaveCMDBCompareServiceThread implements Runnable{
    private static final Logger   _log  = Logger.getLogger(SaveCMDBCompareServiceThread.class);
    
   
    public String cmdbinfo="";
    public String busniss="";
    
    

    private static Connection conn;
    
    public SaveCMDBCompareServiceThread( String cmd,String busniss_system){ 
        cmdbinfo=cmd;
        busniss=busniss_system;        
    }
    @Override
    public void run (){
        try{
            saveCMDBInfo(cmdbinfo,busniss);
        } catch (Exception e){
            e.printStackTrace();
        }
    }
   
    public static void saveCMDBInfo ( String cmdbinfo2, String busniss2 ) throws RepositoryException, SQLException{
        JSONObject json = JSONArray.parseObject(cmdbinfo2);
        int type =Constants.IEAI_SUS;
        
        String method =Thread.currentThread().getStackTrace()[1].getMethodName();
        conn=DBResource.getConnection(method, _log, type);
        
        CMDB_IPService cMDB_IPService =new CMDB_IPService();
        CMDB_MAINService cMDB_MAINService = new CMDB_MAINService();
        CMDB_OPSService cMDB_OPSService = new CMDB_OPSService();
        CMDB_SYSService cMDB_SYSService = new CMDB_SYSService();
        
        JSONArray jArrResults = json.getJSONObject("data").getJSONArray("results"); 
        JSONObject temp_jsonOb = new JSONObject();
        HttpServletRequest request = null;
        List<Map<String, Object>> dataList = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> mainList = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> opsList = new ArrayList<Map<String,Object>>();
        List<Map<String, Object>> sysList = new ArrayList<Map<String,Object>>();
        
        for (int i = 0; i < jArrResults.size(); i++){
            temp_jsonOb = (JSONObject) jArrResults.get(i);

            Map<String, Object> maininfoMap = new HashMap<String, Object>();
            
            
            maininfoMap.put("name", temp_jsonOb.get("name"));
            maininfoMap.put("no", temp_jsonOb.get("no"));
            
            maininfoMap.put("opsflag", temp_jsonOb.get("opsflag"));
            maininfoMap.put("org", temp_jsonOb.get("org"));
            maininfoMap.put("parent_name", temp_jsonOb.get("parent_name"));
            maininfoMap.put("simple_name", temp_jsonOb.get("simple_name"));
            maininfoMap.put("sys_leader", temp_jsonOb.get("sys_leader"));
            
            mainList.add(maininfoMap);
            
            JSONArray jArrIPArr = (JSONArray) temp_jsonOb.get("ip_address");
            JSONObject jObj_IP = null;
            for (int j = 0; j < jArrIPArr.size(); j++) {
                jObj_IP = new JSONObject();
                jObj_IP = (JSONObject) jArrIPArr.get(j);
                
                
                Map<String, Object> ipinfoMap = new HashMap<String, Object>();
                Object obj=jArrIPArr.get(j);
                JSONObject jsonObject2= null;
                if (null!=obj && obj instanceof JSONObject)
                {
                    jsonObject2= (JSONObject) obj;
                }else {
                    continue;
                }
                ipinfoMap.put("businessip", jObj_IP.get("businessip"));
                ipinfoMap.put("env", jObj_IP.get("env"));
                ipinfoMap.put("facility_use_state", jObj_IP.get("facility_use_state"));
                ipinfoMap.put("server_type", jObj_IP.get("server_type"));
                
                dataList.add(ipinfoMap);

               
                
                
                
            }
            
            
            JSONArray jArrOpsArr = (JSONArray) temp_jsonOb.get("ops_persons");
            JSONObject jObj_Ops = null;
            for (int j = 0; j < jArrOpsArr.size(); j++) {
                jObj_Ops = new JSONObject();
                jObj_Ops = (JSONObject) jArrOpsArr.get(j);

                Map<String, Object> opsinfoMap = new HashMap<String, Object>();
                JSONObject jsonObject2 = (JSONObject) jArrOpsArr.get(j);
                opsinfoMap.put("administrative_department", jObj_Ops.get("administrative_department"));
                opsinfoMap.put("email", jObj_Ops.get("email"));
                opsinfoMap.put("fullname", jObj_Ops.get("fullname"));
                opsinfoMap.put("job_number", jObj_Ops.get("job_number"));
                opsinfoMap.put("phone", jObj_Ops.get("phone"));
                opsList.add(opsinfoMap);
                
                
                
            }
            JSONArray jArrSysArr = (JSONArray) temp_jsonOb.get("sys_leaders");
            JSONObject jObj_Sys = null;
            for (int j = 0; j < jArrSysArr.size(); j++) {
                jObj_Sys = new JSONObject();
                jObj_Sys = (JSONObject) jArrSysArr.get(j);
 
                Map<String, Object> sysinfoMap = new HashMap<String, Object>();
                
                Object obj=jArrSysArr.get(j);
                JSONObject jsonObject2= null;
                if (null!=obj && obj instanceof JSONObject)
                {
                    jsonObject2= (JSONObject) obj;
                }else {
                    continue;
                }

                sysinfoMap.put("administrative_department", jObj_Sys.get("administrative_department"));
                sysinfoMap.put("email", jObj_Sys.get("email"));
                sysinfoMap.put("fullname", jObj_Sys.get("fullname"));
                sysinfoMap.put("job_number", jObj_Sys.get("job_number"));
                sysinfoMap.put("phone", jObj_Sys.get("phone"));
                sysList.add(sysinfoMap);
                
               
                
            }
            
            
            try
            {
                long iiid = cMDB_MAINService.saveCMDB_MAIN(mainList, type, conn);
                cMDB_IPService.saveCMDB_IP(dataList, type ,conn , iiid);
                cMDB_OPSService.saveCMDB_OPS(opsList, type , conn , iiid);
                cMDB_SYSService.saveCMDB_SYS(sysList, type, conn , iiid);
                conn.commit();
            } catch (Exception e)
            {
                conn.rollback();
                e.printStackTrace();
            } finally {
                DBResource.closeConnection(conn, method, _log);
            }
            
        }
        
        
        
        
    }  
    
}

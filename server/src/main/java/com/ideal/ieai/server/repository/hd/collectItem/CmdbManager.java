package com.ideal.ieai.server.repository.hd.collectItem;

import com.ideal.ieai.server.poc.DBUtilsNew;

public class CmdbManager
{
    static private CmdbManager _intance = new CmdbManager();

    static public CmdbManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new CmdbManager();
        }
        return _intance;
    }
    
    public CmdbBean getItemTagByItemid ( int modelType, Long colitemid )
    {
        String sql = "SELECT CR.COLRESULT AS ITEMTAG FROM IEAI_COLLECT_RESULT CR WHERE CR.COLITEMID = ?";
        Object[] insertdata = { colitemid };
        DBUtilsNew db = new DBUtilsNew(modelType);
        return db.get(sql, insertdata, new CmdbBean());
    }
    
    public int updateCollectResult(int modelType,Long cpid ,String key ,String value)
    {
        String sql = "UPDATE IEAI_CMDB_LIST SET " + key + " = ? WHERE CPID = ?";
        Object[] insertdata = { value ,  cpid};
        DBUtilsNew db = new DBUtilsNew(modelType);
        return db.insert(sql, insertdata);
    }
}

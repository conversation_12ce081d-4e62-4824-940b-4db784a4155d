package com.ideal.ieai.server.repository.sus.evnbuild;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;

import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.engine.JsonBuilder;

/**
 * <ul>
 * <li>Title: EnvBuildSvc.java</li>
 * <li>Description:环境构建服务层</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2017-5-12
 */
public class EnvBuildSvc
{
    private static final Logger _log           = Logger.getLogger(EnvBuildSvc.class);
    public EnvBuildSvc()
    {
    }

    public Map queryEnvBuildList ( long userid,int start, int limit ) throws RepositoryException
    {
        return EnvBuildMainMgr.getInstance().queryEnvBuildList(start, limit);
    }
    
    /**
     * <li>Description:根据页面的操作，进行数据库更新操作或者向客户端发送报文</li> 
     * <AUTHOR>
     * 2017-5-22 
     * @param opt
     * @param iid
     * @param taskid
     * @param imessage_send
     * @return
     * return Map
     * @throws Exception 
     */
    public Map opt(int opt,long iid,String taskid,String mes) throws Exception{
        Map<String, Object> result = new LinkedHashMap<String, Object>();
        if (opt ==Constants.ENV_BUILD_OPT_BEGINE)   //环境构建开始
        {
            //更新数据库状态
            EnvBuildMain ebMain= new EnvBuildMain();
            ebMain =EnvBuildMainMgr.getInstance().queryEnvBuildBean(iid);
            ebMain.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_RUNNING));
            EnvBuildMainMgr.getInstance().updateEnvBuildMain(ebMain);
        }else if (opt ==Constants.ENV_BUILD_OPT_BACK)//打回
        {
            // 更新数据库状态
            EnvBuildMain ebMain= new EnvBuildMain();
            ebMain =EnvBuildMainMgr.getInstance().queryEnvBuildBean(iid);
            ebMain.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_BACK));
            ebMain.setIfinishedtime(String.valueOf(System.currentTimeMillis()));
            EnvBuildMainMgr.getInstance().updateEnvBuildMain(ebMain);
            sendMessage4EnvBuild( opt, iid, taskid, mes,ebMain);
        }else if (opt ==Constants.ENV_BUILD_OPT_FISHESH_SEND)//完成
        {
            EnvBuildMain ebMain= new EnvBuildMain();
            ebMain =EnvBuildMainMgr.getInstance().queryEnvBuildBean(iid);
            ebMain.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_FISHESH));
            ebMain.setIfinishedtime(String.valueOf(System.currentTimeMillis()));
            EnvBuildMainMgr.getInstance().updateEnvBuildMain(ebMain);
            sendMessage4EnvBuild( opt, iid, taskid, mes,ebMain);
        }else if (opt ==Constants.ENV_BUILD_OPT_FAIL_SEND) //失败并完成
        {
            EnvBuildMain ebMain= new EnvBuildMain();
            ebMain =EnvBuildMainMgr.getInstance().queryEnvBuildBean(iid);
            ebMain.setIfinishedtime(String.valueOf(System.currentTimeMillis()));
            ebMain.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_FAIL));
            EnvBuildMainMgr.getInstance().updateEnvBuildMain(ebMain);
            sendMessage4EnvBuild( opt, iid, taskid, mes,ebMain);
        }
        return result;
    }
    
    
    /**
     * <li>Description:根据页面的操作，进行数据库更新操作或者向客户端发送报文</li> 
     * <AUTHOR>
     * 2017-5-22 
     * @param opt
     * @param iid
     * @param taskid
     * @param imessage_send
     * @return
     * return Map
     * @throws Exception 
     */
    public Map optItem(int opt,long iid) throws Exception{
        Map<String, Object> result = new LinkedHashMap<String, Object>();
        EnvBuildItems item= new EnvBuildItems();
        item.setIid(iid);
        if (opt ==Constants.ENV_BUILD_OPT_BEGINE)   //环境构建开始
        {
            item.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_RUNNING));
        }else if (opt ==Constants.ENV_BUILD_OPT_FISHESH_SEND)//完成
        {
            item.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_FISHESH));
        }else if (opt ==Constants.ENV_BUILD_OPT_FAIL_SEND) //失败
        {
            item.setIstate(String.valueOf(Constants.ENV_BUILD_STATE_FAIL));
        }
        EnvBuildMainMgr.getInstance().updateEnvBuildItem(item);
        return result;
    }
    public void sendMessage4EnvBuild(int opt,long iid,String taskid,String mes,EnvBuildMain ebMain) throws Exception{
        _log.info("sendMessage()");
         String ieai_home = System.getProperty("IEAI_HOME");
         String configHome = ieai_home + File.separator + "config" + File.separator
                 + "sendDeployInfo.config";
         Map<String, String> configurationMap=this.getConfigurationMap(configHome);
         String sendPackagePath=configurationMap.get("sendEnvBuildPath");
         String getTokenPath=configurationMap.get("getEnvBuildPath");
         int timeOutValue=Integer.parseInt(configurationMap.get("timeOutValue"));
         buildSendJson4EnvBuild(sendPackagePath,getTokenPath,timeOutValue,taskid,mes, opt, iid,ebMain);
    }
    
    /**
     * 
     * @Title: buildSendJson4EnvBuild
     * @Description: (构建json以及发送)
     * @return
     * @throws Exception
     * @return String 返回类型
     * @throws
     * @变更记录 2017-4-22 tiejun_fan
     * @变更记录 2015-10-8 liyang
     */
    private String buildSendJson4EnvBuild (String sendPackagePath,String getTokenPath,int timeOutValue,String taskid
            ,String mes, int opt, long iid,EnvBuildMain ebMain) throws Exception
    {
        _log.info("buildSendJson()");
        String tokenstr = null;
        String result = null;
        String sendMesResonDesc ="环境构建";
        try
        {
            try
            {
                _log.info("part1:"+sendMesResonDesc+",获得令牌_地址："+getTokenPath);
                tokenstr = EngineRepositotyJdbc.getInstance().send("", "application/json", getTokenPath,timeOutValue);
                _log.info("part2:"+sendMesResonDesc+",获得令牌值："+tokenstr);
            } catch (Exception e)
            {
                _log.error("part3:"+sendMesResonDesc+",获得令牌失败"+e.toString());
                _log.error(sendMesResonDesc+"获得令牌失败,该地址是:"+getTokenPath);
                throw e;
            }
            String token = "";
            if (tokenstr != null&&!"".equals(tokenstr))
            {
                JSONObject json;
                try
                {
                    json = new JSONObject(tokenstr);
                    token = json.getString("access_token");
                } catch (JSONException e)
                {
                    _log.error("part4:"+sendMesResonDesc+"获得令牌转换成json对象时出错"+e.toString());
                }
            }else
            {
            }

            JSONArray  jsonArr = new  JSONArray();
            JSONObject jsonObj = new JSONObject();
            if (null!=ebMain)
            {
                List<EnvBuildItems>  itemsList=ebMain.getItems();
                List list= new ArrayList();
                if (null!=itemsList)
                {
                    EnvBuildItems item=null;
                    Map<String ,String > map= new LinkedHashMap<String, String>();
                    for (int i = 0; i < itemsList.size(); i++)
                    {
                        item =itemsList.get(i); 
                        String msg = "1".equals(item.getIstate())?"构建成功":"构建失败";
                        map.put("envid",item.getEnvname());
                        map.put("state",item.getIstate());
                        map.put("msg",msg);
                        list.add(map);
                    }
                }
                jsonArr =JSONArray.fromObject(list);
            }
            JsonBuilder.addJsonAttribt(jsonObj,"action","buildEvnResult");
            JsonBuilder.addJsonAttribt(jsonObj,"taskid",taskid);
            JsonBuilder.addJsonAttribt(jsonObj,"demand",jsonArr);
            
            

            
            StringBuffer jsonBuf=new StringBuffer(jsonObj.toString());
            _log.info("part5:"+sendMesResonDesc+",准备发送");
            result = EngineRepositotyJdbc.getInstance().send(URLEncoder.encode(jsonBuf.toString()), "application/json", sendPackagePath + token,timeOutValue);
            _log.info("环境构建："+sendPackagePath + token);
            _log.info("part6:"+sendMesResonDesc+",未进行URLEncoder.encode的报文："+jsonBuf.toString());
            _log.info("part6:"+sendMesResonDesc+",URLEncoder.encode后的报文："+URLEncoder.encode(jsonBuf.toString()));
        } catch (Exception e)
        {
            _log.error("part7:"+sendMesResonDesc+",发送失败,"+e.toString());
        }
        return result;
    }
    
    /**
     * 
     * @Title: getConfigurationMap
     * @Description: (获取指定目录Properties文件内容)
     * @param file
     * @return
     * @throws IOException
     * @return Map<String,String> 返回类型
     * @throws
     * @变更记录 2015-9-17 yunpeng_zhang
     */
    private Map<String, String> getConfigurationMap ( String file ) throws IOException
    {
        Map<String, String> res = new HashMap<String, String>();
        File f = new File(file);
        if (f.exists())
        {
            FileReader    fileReader =new FileReader(f);
            BufferedReader br = new BufferedReader(fileReader);
            String line = "";

            while ((line = br.readLine()) != null)
            {
                try
                {
                    if ("".equals(line)) // 如果为空
                    {
                        continue;
                    }
                    if (line.startsWith("#")) // 如果为注释
                    {
                        continue;
                    }

                    String[] keyandvalue = line.split("=", 2);
                    res.put(keyandvalue[0].toString(), keyandvalue[1].toString());
                    continue;
                } catch (Exception e)
                {
                    e.printStackTrace();
                    _log.error(e.getMessage());
                    _log.error("Read a line from sendDeployInfo.config :'" +line+"'");
                }
            }
            
            try{ if (null !=fileReader){fileReader.close();}} catch (IOException e) {e.printStackTrace();_log.error(e.toString());}
            try{ if (null !=br){br.close();}} catch (IOException e) {e.printStackTrace();_log.error(e.toString());}  
        }
        return res;

    }
    
    
    //--------------------- detai ------------------
    public Map queryEnvBuildDetailList ( long iid ,int start, int limit ) throws RepositoryException{
        return EnvBuildMainMgr.getInstance().queryEnvBuildDetailList(iid,start, limit);
    }
}

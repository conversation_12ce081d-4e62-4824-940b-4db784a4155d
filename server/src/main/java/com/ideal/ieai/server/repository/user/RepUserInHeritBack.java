package com.ideal.ieai.server.repository.user;

public class RepUserInHeritBack
{

    private long roleId;
    
    private long userId;
    
    private long groupId;
    
    private String sql;
    
    private int dbType;
    
    private String flagtype;
    
    private int iisextend;

    public int getIisextend ()
    {
        return iisextend;
    }

    public void setIisextend ( int iisextend )
    {
        this.iisextend = iisextend;
    }

    public long getRoleId ()
    {
        return roleId;
    }

    public void setRoleId ( long roleId )
    {
        this.roleId = roleId;
    }

    public long getUserId ()
    {
        return userId;
    }

    public void setUserId ( long userId )
    {
        this.userId = userId;
    }

    public long getGroupId ()
    {
        return groupId;
    }

    public void setGroupId ( long groupId )
    {
        this.groupId = groupId;
    }

    public String getSql ()
    {
        return sql;
    }

    public void setSql ( String sql )
    {
        this.sql = sql;
    }

    public int getDbType ()
    {
        return dbType;
    }

    public void setDbType ( int dbType )
    {
        this.dbType = dbType;
    }

    public String getFlagtype ()
    {
        return flagtype;
    }

    public void setFlagtype ( String flagtype )
    {
        this.flagtype = flagtype;
    }
    
}

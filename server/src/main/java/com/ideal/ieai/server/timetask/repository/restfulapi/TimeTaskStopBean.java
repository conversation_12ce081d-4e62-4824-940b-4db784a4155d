package com.ideal.ieai.server.timetask.repository.restfulapi;

import java.util.List;

import com.ideal.ieai.server.timetask.repository.TimetaskInfoBean;

public class TimeTaskStopBean
{
    // 提交参数
    private String stopType;
    private List<TimetaskInfoBean> timeTaskList;
    public String getStopType ()
    {
        return stopType;
    }

    public void setStopType ( String stopType )
    {
        this.stopType = stopType;
    }


    public List<TimetaskInfoBean> getTimeTaskList ()
    {
        return timeTaskList;
    }

    public void setTimeTaskList ( List<TimetaskInfoBean> timeTaskList )
    {
        this.timeTaskList = timeTaskList;
    }

}

package com.ideal.ieai.server.repository.hd.agentMaintain.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

@ExcelIgnoreUnannotated
public class AgentActModel implements Serializable {
    private long agentId;

    private String agentName;

    private long agentPort;

    @ExcelProperty(value = "AgentIp")
    private String agentIp;

    @ExcelProperty(value = "Agent组")
    private String agentGroupName;

    private int proType;

    @ExcelProperty(value = "工程名称")
    private String prjName;

    @ExcelProperty(value = "工作流名称")
    private String flowName;

    @ExcelProperty(value = "作业名称")
    private String actName;

    @ExcelProperty(value = "模块类型")
    private String modelType;


    public long getAgentId() {
        return agentId;
    }

    public void setAgentId(long agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public long getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(long agentPort) {
        this.agentPort = agentPort;
    }

    public String getPrjName() {
        return prjName;
    }

    public void setPrjName(String prjName) {
        this.prjName = prjName;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getAgentGroupName() {
        return agentGroupName;
    }

    public void setAgentGroupName(String agentGroupName) {
        this.agentGroupName = agentGroupName;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public int getProType() {
        return proType;
    }

    public void setProType(int proType) {
        this.proType = proType;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }
}

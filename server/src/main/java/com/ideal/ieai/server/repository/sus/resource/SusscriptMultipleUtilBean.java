package com.ideal.ieai.server.repository.sus.resource;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * 名称: SusscriptMultipleUtilBean.java<br>
 * 描述: 脚本库维护多写工具类<br>
 * 类型: JAVA<br>
 */
public class SusscriptMultipleUtilBean extends AbsMultipleUtilBean
{

    private List<SusscriptB>   updateSusscriptBList     = new ArrayList<SusscriptB>();   // 脚本执行类
    private List<SusscriptB>   rollbackSusscriptBList   = new ArrayList<SusscriptB>();   // 脚本执行回滚
    private List<ScriptparamB> updateScriptparamBList   = new ArrayList<ScriptparamB>(); // 参数执行集合
    private List<ScriptparamB> rollbackScriptparamBList = new ArrayList<ScriptparamB>(); // 参数自行回滚

    public List<SusscriptB> getUpdateSusscriptBList ()
    {
        return updateSusscriptBList;
    }

    public List<ScriptparamB> getUpdateScriptparamBList ()
    {
        return updateScriptparamBList;
    }

    public void setUpdateScriptparamBList ( List<ScriptparamB> updateScriptparamBList )
    {
        this.updateScriptparamBList = updateScriptparamBList;
    }

    public List<ScriptparamB> getRollbackScriptparamBList ()
    {
        return rollbackScriptparamBList;
    }

    public void setRollbackScriptparamBList ( List<ScriptparamB> rollbackScriptparamBList )
    {
        this.rollbackScriptparamBList = rollbackScriptparamBList;
    }

    public void setUpdateSusscriptBList ( List<SusscriptB> updateSusscriptBList )
    {
        this.updateSusscriptBList = updateSusscriptBList;
    }

    public List<SusscriptB> getRollbackSusscriptBList ()
    {
        return rollbackSusscriptBList;
    }

    public void setRollbackSusscriptBList ( List<SusscriptB> rollbackSusscriptBList )
    {
        this.rollbackSusscriptBList = rollbackSusscriptBList;
    }
}

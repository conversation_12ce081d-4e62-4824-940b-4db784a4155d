package com.ideal.ieai.server.repository.hd.login.dg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.ParserConfig;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.hd.login.dg.exception.DrcbclUempException;
import com.ideal.ieai.server.repository.hd.login.dg.head.AppHeadREQ;
import com.ideal.ieai.server.repository.hd.login.dg.head.SidecarRequest;
import com.ideal.ieai.server.repository.hd.login.dg.head.SidecarResponse;
import com.ideal.ieai.server.repository.hd.login.dg.head.SysHeadREQ;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 
 * <ul>
 * <li>Title: DrcbclUempDefault.java</li>
 * <li>Description:东莞农村商业银行-边车请求添加默认参数</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2024年7月1日
 */
public class SidecarDefault<Q,S> {
	
	private static final Logger log = Logger.getLogger(SidecarDefault.class);

	/** 统一登录URL */
	private final String urlStr;
	
	public SidecarDefault() throws DrcbclUempException {
		this.urlStr = Environment.getInstance().getDRCBUnifiedUrl();
		if (this.urlStr == null || this.urlStr.isEmpty()) throw new DrcbclUempException("0001", "东莞农商边车URL未配置，请联系管理员进行配置！");
	}
	
	/**
	 * 
	 * <li>Description:创建带默认信息的请求封装类：默认信息为系统号、时间等有固定规则信息</li>
	 * 
	 * <AUTHOR> 2024年7月1日
	 * @return RequestUEMP<T>
	 */
	public SidecarRequest<Q> defaultRequestUEMP() {
		SidecarRequest<Q> requestUEMP = new SidecarRequest<Q>();
		// 创建公共报文-系统头(流水号、时间、系统号等固定信息)
		SysHeadREQ sysHead = new SysHeadREQ();
		sysHead.setServiceVersion("00");
		sysHead.setConsumerSeqNo(generateConsumerSeqNo());
		sysHead.setConsumerId(Environment.getInstance().getConsumerId());
		// 创建公共报文-应用头
		AppHeadREQ appHead = new AppHeadREQ();
		appHead.setBusSeqNo(generateConsumerSeqNo());

		requestUEMP.setAppHead(appHead);
		requestUEMP.setSysHead(sysHead);
		return requestUEMP;
	}
	/**
	 * 生成唯一流水号
	 * @return
	 */
	private String generateConsumerSeqNo() {
		// 组成规则:源系统标识号（6位）+ 源系统交易日期（8位）+ 批次号（2位）+ 交易流水号（10位）
		Random random = new Random();
		String result = Environment.getInstance().getConsumerId();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		result += sdf.format(new Date());
		result += random.nextInt(100);
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < 10; i++) {
			int digit = random.nextInt(10);
			sb.append(digit);
		}
		result += sb.toString();
		return result;
	}
	/**
	 * 
	 * <li>Description:默认请求封装方法：自动转换为指定对象</li>
	 * 
	 * <AUTHOR> 2024年7月2日
	 * @param requestUEMP
	 * @return SidecarResponse<S>
	 * @throws DrcbclUempException 
	 */
	public SidecarResponse<S> defaultPostResponseUEMP(SidecarRequest<Q> requestUEMP, Class<S> clazz) throws DrcbclUempException {
		log.info("==========东莞农商银行：请求参数==========" + requestUEMP.toString());
		String jsonString = JSONObject.toJSONString(requestUEMP, true);
		String retJsonString = doComm(jsonString);

		if (retJsonString == null || retJsonString.isEmpty()) {
			log.error("==========东莞农商银行：东莞农商边车服务调用失败！URL==========" + urlStr);
			throw new DrcbclUempException("0002", "东莞农商边车服务调用失败！");
		}
		
		ParserConfig parserConfig = new ParserConfig();
        parserConfig.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        TypeReference<SidecarResponse<S>> typeReference = new TypeReference<SidecarResponse<S>>(clazz) {};
        SidecarResponse<S> parseObject = JSONObject.parseObject(retJsonString, typeReference.getType(), parserConfig, JSON.DEFAULT_PARSER_FEATURE);
//		log.info("==========东莞农商银行：返回参数==========" + JSONObject.toJSONString(parseObject, true));
        return parseObject;
	}

	/**
	 * 
	 * <li>Description:发生边车请求</li>
	 * 
	 * <AUTHOR> 2024年7月2日
	 * @param str
	 * @return String
	 */
	private String doComm(String str) {
		byte[] retStr = doComm(str.getBytes());
		if (retStr == null) return null;
		return new String(retStr, StandardCharsets.UTF_8);
	}
	
	/**
	 * 
	 * <li>Description:发生边车请求</li>
	 * 
	 * <AUTHOR> 2024年7月2日
	 * @param jsonByte
	 * @return byte[]
	 */
	private byte[] doComm(byte[] jsonByte) {
		byte[] repBytes = null;
		OutputStream os = null;
		InputStream is = null;
		ByteArrayOutputStream byteArrayOutputStream = null;
		try {
			URL url = new URL(urlStr);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("POST");
			conn.setConnectTimeout(0);
			conn.setReadTimeout(0);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			os = conn.getOutputStream();
			os.write(jsonByte);
			byte[] bytes = new byte[1024];
			is = conn.getInputStream();
			byteArrayOutputStream = new ByteArrayOutputStream();
			int len = 0;
			while((len = is.read(bytes)) != -1) {
				byteArrayOutputStream.write(bytes, 0, len);
			}
			repBytes = byteArrayOutputStream.toByteArray();
		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (byteArrayOutputStream != null) {
				try {
					byteArrayOutputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
				}
			}
		}
		return repBytes;
	}
}
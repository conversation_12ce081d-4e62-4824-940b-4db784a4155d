package com.ideal.ieai.server.repository.actRelation;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.MonitorActModel;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.topo.dataimport.ImportModel;
import com.ideal.ieai.server.repository.topo.dataimport.TopoImport;
import com.ideal.ieai.server.repository.topo.sequence.GantManager;
import com.ideal.ieai.server.repository.topo.sequence.GantModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ProjectAutoAnalyzeActMonitor {
    private static final Logger _log          = Logger.getLogger(ProjectAutoAnalyzeActMonitor.class);

    static private ProjectAutoAnalyzeActMonitor _ins = new ProjectAutoAnalyzeActMonitor();

    static public ProjectAutoAnalyzeActMonitor getInstance ()
    {
        if (_ins == null)
        {
            _ins = new ProjectAutoAnalyzeActMonitor();
        }
        return _ins;
    }

    public void startAnalyzeMonitor(String prjUuid,String prjName) {
        _log.info("ProjectAutoAnalyzeActMonitor startAnalyzeMonitor");
        try {
            Project project = Engine.getInstance().getResourceManager().getProjectByUuid(prjUuid);
            analyzeMonitor(project, prjName);
        } catch (Exception e) {
            _log.error("ProjectAutoAnalyzeActMonitor startAnalyzeMonitor error",e);
        }
    }

    public void analyzeMonitor(@NotNull Project project, String prjName) {
        Collection flows = project.getWorkflows();
        List servers = new ArrayList();
        for (Iterator j = flows.iterator(); j.hasNext();)
        {
            Workflow flow = (Workflow) j.next();
            List activities  = flow.getActivities();

            for (Iterator k = activities.iterator(); k.hasNext();)
            {
                try{
                    Object obj = k.next();
                    if(obj instanceof ActivityElement) {
                        ActivityElement ele = (ActivityElement)obj;
                        if (null != ele && null != ele.getActDef() && null != ele.getActDef().getAdaptorName()) {
                            if (ele.getActDef().getName().equals("Act Monitor")) {
                                ele.getActConfig();
                                DefaultConfig config = (DefaultConfig) ele.getActConfig();
                                if (!(config.get("monitorActs") instanceof List)) {
                                    continue;
                                }
                                for (Object singleConfig : (List) config.get("monitorActs")) {
                                    // 获取环境信息
                                    MonitorActModel model = (MonitorActModel) singleConfig;
                                    String modelPrjName = model.getPrjName();
                                    String modelFlowname = model.getFlwName();
                                    ImportModel server = getMonitorActModel();
                                    server.setSystemname(prjName);
                                    server.setTaskname(flow.getName());
                                    server.setLastsysname(modelPrjName);
                                    server.setLasttaskname(modelFlowname);
                                    //查询工程对应的组
                                    String group = getGroup(prjName);
                                    if(StringUtils.isNotBlank(group)){
                                        server.setGroup(group);
                                    }
                                    servers.add(server);
                                    _log.info("monitorActs : prjname : " + prjName + " flowname : " + flow.getName() + " modelPrjName : " + modelPrjName + " modelFlowname : " + modelFlowname);
                                }
                            } // 获取环境信息)
                        }
                    }
                }catch(Exception e){
                    _log.error("ProjectAutoAnalyzeActMonitor analyzeMonitor error : "+e.getMessage(),e);
                }

            }


        }
        if(servers.size()>0){

            TopoImport di1 = new TopoImport();
            try {
                delImportTopo(prjName);
                di1.excelImportdb(servers, 16, 0);
                //保存系统内上下游关系,获取时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String idataDate =  sdf.format(new Date());
                //删除原系统间间依赖关系
                deleteUpDownInfo(idataDate);
                //调用发布方法,自动将保存的依赖关系发布
                topoPublish(idataDate);
//                saveUpInfo(idataDate,prjName);
//                saveDownInfo(idataDate,prjName);

            } catch (Exception e) {
                _log.error("ProjectAutoAnalyzeActMonitor analyzeMonitor error : "+e.getMessage(),e);
            }
        }

        _log.info("ProjectAutoAnalyzeActMonitor analyzeMonitor end");
    }

    private String getGroup(String modelPrjName) {

        String sql = "select iname,DEPARTMENT from ieai_project where protype = 1 and iname = ? order by iid desc";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String group = "";

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setString(1 ,modelPrjName);
            rs = ps.executeQuery();
            while (rs.next()) {
                group =  rs.getString("DEPARTMENT");
                if(StringUtils.isNotBlank(group)){
                    return group;
                }
            }
        } catch (DBException | SQLException e) {
            _log.error("getGroup is error2"+e.getMessage(),e);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getGroup", _log);
        }
        return group;
    }

    private void topoPublish(String idataDate) throws RepositoryException {
        Map map = new HashMap();
        map.put("success", false);
        map.put("message", "发布失败");
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        ResultSet rs = null;
        ResultSet rs2 = null;
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            //ieai_topoinfo_pre
            String sql0 = "select systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro,gjtask,idate,iopendatetype,iarrivedatetype,ishowtype from ieai_topoinfo_pre";
            String sql1 = "delete from ieai_topoinfo";
            String sql4 = "insert into ieai_topoinfo_version_his(iid,systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro," +
                    "gjtask,idate,iopendatetype,iarrivedatetype,ISHOWTYPE,ITOPOVERSIONID) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            String sql5 = "insert into ieai_topoinfo(iid,systemname,taskname,taskdes,lastsysname,lasttaskname,lasttaskdes,opentime,arrivetime,information,remark,gro," +
                    "gjtask,idate,iopendatetype,iarrivedatetype,ishowtype,iversionname) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            String sql2 = "select max(iorder) from ieai_topoinfo_version";
            ps1 = conn.prepareStatement(sql1);
            ps1.executeUpdate();
            ps2 = conn.prepareStatement(sql2);
            rs2 = ps2.executeQuery();
            long iorder = 1;
            if (rs2.next()) {
                iorder = rs2.getLong(1) + 1;
            }
            String ordername = "V"+iorder;
            long vid = IdGenerator.createIdFor("ieai_topoinfo_version", conn);
            String sql3 = "insert into ieai_topoinfo_version(iid,itopoversion,iorder,ipublisher,icreatetime)" +
                    " values('"+vid+"','"+ordername+"','"+iorder+"','"+4+"',FUN_GET_DATE_NUMBER_NEW(" + Constants.getCurrentSysDate() + ",8))";
            ps3 = conn.prepareStatement(sql3);
            ps3.executeUpdate();
            ps = conn.prepareStatement(sql0);
            rs = ps.executeQuery();
            ps5 = conn.prepareStatement(sql5);
            ps4 = conn.prepareStatement(sql4);
            Map<String,String> cellmap = new HashMap();
            int incount = 0;
            while (rs.next()) {
                long topoid = IdGenerator.createIdFor("ieai_topoinfo", conn);
                int index1 = 0;
                incount++;
                String group = rs.getString("gro");
                ps5.setLong(++index1, topoid);
                ps5.setString(++index1,rs.getString("systemname"));
                ps5.setString(++index1,rs.getString("taskname"));
                ps5.setString(++index1,rs.getString("taskdes"));
                ps5.setString(++index1,rs.getString("lastsysname"));
                ps5.setString(++index1,rs.getString("lasttaskname"));
                ps5.setString(++index1,rs.getString("lasttaskdes"));
                ps5.setString(++index1,rs.getString("opentime"));
                ps5.setString(++index1,rs.getString("arrivetime"));
                ps5.setString(++index1,rs.getString("information"));
                ps5.setString(++index1,rs.getString("remark"));
                ps5.setString(++index1,group);
                ps5.setInt(++index1,rs.getInt("gjtask"));
                ps5.setString(++index1,rs.getString("idate"));
                ps5.setString(++index1,rs.getString("iopendatetype"));
                ps5.setString(++index1,rs.getString("iarrivedatetype"));
                ps5.setInt(++index1,rs.getInt("ishowtype"));
                ps5.setString(++index1, ordername);
                ps5.addBatch();
                index1 = 0;
                long vhisid = IdGenerator.createIdFor("ieai_topoinfo_version_his", conn);
                ps4.setLong(++index1, vhisid);
                ps4.setString(++index1,rs.getString("systemname"));
                ps4.setString(++index1,rs.getString("taskname"));
                ps4.setString(++index1,rs.getString("taskdes"));
                ps4.setString(++index1,rs.getString("lastsysname"));
                ps4.setString(++index1,rs.getString("lasttaskname"));
                ps4.setString(++index1,rs.getString("lasttaskdes"));
                ps4.setString(++index1,rs.getString("opentime"));
                ps4.setString(++index1,rs.getString("arrivetime"));
                ps4.setString(++index1,rs.getString("information"));
                ps4.setString(++index1,rs.getString("remark"));
                ps4.setString(++index1,group);
                ps4.setInt(++index1,rs.getInt("gjtask"));
                ps4.setString(++index1,rs.getString("idate"));
                ps4.setString(++index1,rs.getString("iopendatetype"));
                ps4.setString(++index1,rs.getString("iarrivedatetype"));
                ps4.setInt(++index1,rs.getInt("ishowtype"));
                ps4.setLong(++index1,vid);
                ps4.addBatch();
                if(org.apache.commons.lang.StringUtils.isNotBlank(group)&&!cellmap.containsKey(group)) {
                    cellmap.put(group, group);
                }
            }
            if(0 != incount) {
                ps4.executeBatch();
                ps5.executeBatch();
            }
            ps4.executeBatch();
            ps5.executeBatch();
            //主动触发保存instance
            updateGant(conn,idataDate,0L);
            //TODO 主动触发方式有待商榷，目前有太多的逻辑都在轮询线程中，无法确认影响有多大
            conn.commit();
            _log.info("======自动解析发布成功======");
        } catch (Exception e) {
            _log.error("topopublish() is error2",e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY, e);
        } finally {
            DBResource.closePreparedStatement(ps5, "topopublish", _log);
            DBResource.closePreparedStatement(ps4, "topopublish", _log);
            DBResource.closePreparedStatement(ps3, "topopublish", _log);
            DBResource.closePSRS(rs2, ps2, "topopublish", _log);
            DBResource.closePSRS(rs, ps, "topopublish", _log);
            DBResource.closePSConn(conn, ps1, "topopublish", _log);
        }
    }


    private void deleteUpDownInfo(String queryDate) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            String deleteUpSql = " delete from IEAI_ACT_TOPO_INS_UPDOWN where IACTMESSID in (SELECT DISTINCT AA.IID FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA \n" +
                    "                    WHERE A.SYSTEMNAME=AA.IPROJECTNAME AND A.TASKNAME=AA.IACTNAME AND A.LASTSYSNAME IS NOT NULL AND AA.IDATADATE= ?) and IUPDOWNTYPE = 0";
            String deleteDownSql = "\tdelete from IEAI_ACT_TOPO_INS_UPDOWN where IACTMESSID in ( SELECT DISTINCT AA.IID FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA \n" +
                    "                    WHERE A.LASTSYSNAME=AA.IPROJECTNAME AND A.LASTTASKNAME=AA.IACTNAME AND A.SYSTEMNAME IS NOT NULL AND AA.IDATADATE= ?) and IUPDOWNTYPE = 1";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(deleteUpSql);
            ps.setString(1,queryDate);
            ps.execute();

            ps = conn.prepareStatement(deleteDownSql);
            ps.setString(1,queryDate);
            ps.execute();

            conn.commit();
        }catch (Exception e)
        {
            _log.error(method + " : " + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE, e);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveUpInfo", _log);
        }
    }

    public void updateGant(Connection conn,String queryDate,Long flag){
        _log.info("queryDate: "+queryDate +" flag: "+flag);
        if(flag==0){
            //存IEAI_ACT_TOPO_INSTANCE表的IID（相当于查询存储过程的临时表IID）
            List idList=new ArrayList();
            //获取解析数据
            List<GantModel> list = GantManager.getInstance().getActRelationMessList(conn,queryDate,idList);
            //保存IEAI_ACT_TOPO_INSTANCE表、 IEAI_EXEACT_TOPO_INSTANCE表
            GantManager.getInstance().saveActTopoInstance(conn,list);
            //更新活动跨天规则
            GantManager.getInstance().updateCrossSkyRule(conn,queryDate,idList);
            //活动平均耗时
            GantManager.getInstance().updateAverageTimeConsuming(conn,queryDate,idList);
            GantManager.getInstance().updateAvgtime(conn,queryDate,idList);
            //删除关联信息
            GantManager.getInstance().delteACTTopoInsUpdown(conn,queryDate);
            //保存上关联信息
            GantManager.getInstance().saveUpInfo(conn,queryDate);
            //保存下关联信息
            GantManager.getInstance().saveDownInfo(conn,queryDate);
        }
        //更新上下关联状态
        GantManager.getInstance().updateUpAndDownInfo(conn,queryDate);
        //警报清除
        GantManager.getInstance().updateAlarmClear(conn,queryDate);
        _log.info("============================updateGant...end===============================");
    }

    public ImportModel getMonitorActModel(){
        ImportModel server = new ImportModel();
        server.setDelete("否");
        server.setGroup("默认组");
        server.setGjtask("");
        server.setTaskdes("");
        server.setLastsysname("");
        server.setLasttaskname("");
        server.setLasttaskdes("");
        server.setOpentime("");
        server.setArrivetime("");
        server.setInformation("");
        server.setRemark("");
        server.setOpendatetype("");
        server.setArrivedatetype("");
        server.setRelationFile("");
        server.setFileResourse("");
        return server;
    }

    public void delImportTopo(String prjName) throws RepositoryException {
        if(StringUtils.isBlank(prjName)) {
            _log.info("delImportTopo prjName is null"+prjName);
        }else {
            Connection conn = null;
            PreparedStatement ps = null;
            String sql = "delete from ieai_topoinfo_pre  where systemname = ('" + prjName + "')";
            try {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                ps = conn.prepareStatement(sql);
                ps.executeUpdate();
                conn.commit();
            } catch (Exception e) {
                _log.error("delImportTopo() is error2" + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY, e);
            } finally {
                DBResource.closePSConn(conn, ps, "delImportTopo", _log);
            }
        }
    }


    private void saveUpInfo(String queryDate,String prjName) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            String sql = "INSERT INTO IEAI_ACT_TOPO_INS_UPDOWN (IACTMESSID,IDATADATE,ISYSTEMNAME,IACTNAME,IACTDES,IUPDOWNTYPE) (" +
                    "SELECT DISTINCT AA.IID,AA.IDATADATE,A.LASTSYSNAME,A.LASTTASKNAME,A.LASTTASKDES,0 FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA " +
                    "WHERE A.SYSTEMNAME=AA.IPROJECTNAME AND A.TASKNAME=AA.IACTNAME AND A.LASTSYSNAME IS NOT NULL AND AA.IDATADATE= ? and A.systemname = ?)";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setString(1,queryDate);
            ps.setString(2,prjName);
            ps.execute();
            conn.commit();
        }catch (Exception e)
        {
            _log.error(method + " : " + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT, e);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveUpInfo", _log);
        }
    }

    private void saveDownInfo(String queryDate,String prjName) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            String sql = "INSERT INTO IEAI_ACT_TOPO_INS_UPDOWN (IACTMESSID,IDATADATE,ISYSTEMNAME,IACTNAME,IACTDES,IUPDOWNTYPE) (" +
                    "SELECT DISTINCT AA.IID,AA.IDATADATE,A.SYSTEMNAME,A.TASKNAME,A.TASKDES,1 FROM IEAI_TOPOINFO A,IEAI_ACT_TOPO_INSTANCE AA " +
                    "WHERE A.LASTSYSNAME=AA.IPROJECTNAME AND A.LASTTASKNAME=AA.IACTNAME AND A.SYSTEMNAME IS NOT NULL AND AA.IDATADATE= ? and A.systemname = ?)";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setString(1,queryDate);
            ps.setString(2,prjName);
            ps.execute();
            conn.commit();
        }catch (Exception e)
        {
            _log.error(method + " : " + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT, e);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveDownInfo", _log);
        }
    }


}

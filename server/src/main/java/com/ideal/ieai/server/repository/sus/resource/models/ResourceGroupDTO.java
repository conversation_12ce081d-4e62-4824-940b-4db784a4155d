package com.ideal.ieai.server.repository.sus.resource.models;

import java.util.ArrayList;
import java.util.List;
/**
 * <ul>
 * <li>Title: ResourceGroupDTO.java</li>
 * <li>Description: 资源组DTO</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2014-8-22
 */
public class ResourceGroupDTO
{
    private Long   id;
    private String name;
    private String description;

    private  List<ResourceGroupParameterDTO> param = new ArrayList<ResourceGroupParameterDTO>();
    public long getId ()
    {
        return id;
    }

    public void setId ( long id )
    {
        this.id = id;
    }

    public String getName ()
    {
        return name;
    }

    public void setName ( String name )
    {
        this.name = name;
    }

    public String getDescription ()
    {
        return description;
    }

    public void setDescription ( String description )
    {
        this.description = description;
    }

    public List<ResourceGroupParameterDTO> getParam ()
    {
        return param;
    }

    public void setParam ( List<ResourceGroupParameterDTO> param )
    {
        this.param = param;
    }
}

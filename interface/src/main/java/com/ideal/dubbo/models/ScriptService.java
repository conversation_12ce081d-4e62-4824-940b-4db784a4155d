package com.ideal.dubbo.models;

import com.ideal.ieai.commons.Constants;

import java.util.List;
import java.util.Map;

public class ScriptService implements java.io.Serializable
{

    private String  userId;
    private String  workItemId;
    private String  loginName;
    private long    iid;
    private long    timeout;
    private long timeTaskid;
    private String runServiceflowId;// 服务监控用，此服务为推荐服务，存储上一个执行的服务flowid
    private int    flowState;
    private int    idbState;
    private String versionFlag;
    private  String label;
    private List   scriptDirId;
    private String   funcTypeName;
    private String   groupUserIIDs;
    private String   loginNames;
    private int insRun;

    private String ipsId;
    private Boolean batchBoCe;

    private String redisKey;
    private Integer dbIndex;
    //超时是否自动终止，值为1时超时自动终止
    private Integer timeoutAutoKill;

    private Long timeOutSeconds;

    private Long [] allAgentIds;

    private String taskStartTime;

    private boolean changeExecFlag = false;

    public boolean isChangeExecFlag() {
        return changeExecFlag;
    }

    public void setChangeExecFlag(boolean changeExecFlag) {
        this.changeExecFlag = changeExecFlag;
    }

    public String getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(String taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    private boolean heightPermissionFlag;

    private boolean execType = false;//是否接口发起，默认fasle，只有从http接口发起时才是true

    private int weights;//权重

    private int runstate;

    public int getRunstate() {
        return runstate;
    }

    public void setRunstate(int runstate) {
        this.runstate = runstate;
    }

    //调用接口参数(压缩、加密)
    private String requestData;

    private String restApiStartTime;

    public String getRestApiStartTime() {
        return restApiStartTime;
    }

    public void setRestApiStartTime(String restApiStartTime) {
        this.restApiStartTime = restApiStartTime;
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public boolean isHeightPermissionFlag() {
        return heightPermissionFlag;
    }

    public void setHeightPermissionFlag(boolean heightPermissionFlag) {
        this.heightPermissionFlag = heightPermissionFlag;
    }

    public boolean isExecType() {
        return execType;
    }

    public void setExecType(boolean execType) {
        this.execType = execType;
    }

    public Long[] getAllAgentIds() {
        return allAgentIds;
    }

    public void setAllAgentIds(Long[] allAgentIds) {
        this.allAgentIds = allAgentIds;
    }

    public int getWeights() {
        return weights;
    }

    public void setWeights(int weights) {
        this.weights = weights;
    }

    private Long apiStartTime;

    public Long getApiStartTime() {
        return apiStartTime;
    }

    public void setApiStartTime(Long apiStartTime) {
        this.apiStartTime = apiStartTime;
    }

    public Long getTimeOutSeconds() {
        return timeOutSeconds;
    }

    public void setTimeOutSeconds(Long timeOutSeconds) {
        this.timeOutSeconds = timeOutSeconds;
    }

    public Integer getTimeoutAutoKill() {
        return timeoutAutoKill;
    }

    public void setTimeoutAutoKill(Integer timeoutAutoKill) {
        this.timeoutAutoKill = timeoutAutoKill;
    }

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public Integer getDbIndex() {
        return dbIndex;
    }

    public void setDbIndex(Integer dbIndex) {
        this.dbIndex = dbIndex;
    }

    public String getIpsId() {
        return ipsId;
    }

    public void setIpsId(String ipsId) {
        this.ipsId = ipsId;
    }

    public int getInsRun() {
        return insRun;
    }

    public void setInsRun(int insRun) {
        this.insRun = insRun;
    }

    public Boolean getBatchBoCe() {
        return batchBoCe;
    }

    public void setBatchBoCe(Boolean batchBoCe) {
        this.batchBoCe = batchBoCe;
    }

    public String getLoginNames() {
        return loginNames;
    }

    public void setLoginNames(String loginNames) {
        this.loginNames = loginNames;
    }

    public String getGroupUserIIDs() {
        return groupUserIIDs;
    }

    public void setGroupUserIIDs(String groupUserIIDs) {
        this.groupUserIIDs = groupUserIIDs;
    }

    public String getFuncTypeName() {
        return funcTypeName;
    }

    public void setFuncTypeName(String funcTypeName) {
        this.funcTypeName = funcTypeName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    private boolean selectUnboundScript;
    private  String groupName;
    public boolean isSelectUnboundScript() {
        return selectUnboundScript;
    }

    public void setSelectUnboundScript(boolean selectUnboundScript) {
        this.selectUnboundScript = selectUnboundScript;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List getScriptDirId() {
        return scriptDirId;
    }

    public void setScriptDirId(List scriptDirId) {
        this.scriptDirId = scriptDirId;
    }

    private String execFrom; //标记请求是否来自前台页面，还是来自于agent回调provider，驱动下一个批次  0 代表来自前台页面，null为来自agent回调provider


    public String getExecFrom() {
        return execFrom;
    }

    public void setExecFrom(String execFrom) {
        this.execFrom = execFrom;
    }

    public String getVersionFlag() {
        return versionFlag;
    }

    public void setVersionFlag(String versionFlag) {
        this.versionFlag = versionFlag;
    }

    public int getIdbState ()
    {
        return idbState;
    }

    public void setIdbState ( int idbState )
    {
        this.idbState = idbState;
    }

    public long getTimeTaskid ()
    {
        return timeTaskid;
    }

    public void setTimeTaskid ( long timeTaskid )
    {
        this.timeTaskid = timeTaskid;
    }

    public long getTimeout ()
    {
        return timeout;
    }

    public void setTimeout ( long timeout )
    {
        this.timeout = timeout;
    }

    private String  scriptuuid;
    private long    coatId;
    private String  serviceName;
    private String  sysName;
    private String  bussName;
    private String  bussTypeName;
    private long    bussId;
    private long    bussTypeId;
    private String  scriptType;
    private String  bfVersion;
    private String  pUser;
    private String  scriptName;
    private String  ScriptPara;
    private String  content;
    private String  platForm;
    private String  inputParamDesc;
    private String  funcDesc;
    private String  createUserName;
    //浙江商行使用的 创建人 createuser 字段 里面存的是用户登陆名，但该属性实际值为用户名全程，查询使用
    private String  createUserNameForCz;
    private String  killUserName;
    private String  createTime;
    private int     status;
    private int     isdelete;
    private int     state;
    private int     isFlow;
    private String  butterflyversion;      // 关机维护 单号
    private Boolean queryHistoryTask;      // 关机维护使用 是否查看历史任务
    private String  hostName;              // 关机维护使用 计算机名
    private String  shutdownState;                 // 关机维护使用 关机检查实例状态
    private String  startCheckState;            // 关机维护使用 开机检查实例状态
    private String  jsonData;                                                                                                                               // 批量数据时使用
    private int     serviceState;                                                                                       // 0：取消服务
    private String     auditSysAgentListJson;  //山东 小信封打回后重新发起 使用                                                                                     // 0：取消服务
                                                                                                              // 1：发布服务

    private String  ignore;                               // 忽略异常继续执行 0：不忽略 1：忽略 // 2：待发布服务
    private int     hasPage;                                                                                                                                         // 1不分页
    private int     start;
    private int     limit;
    private int     fromType;                                                                 // 浦发需求
                                                                                              // 白名单脚本
                                                                                              // 查询时使用此项
                                                                                              // 码值
                                                                                              // 99
    private String  backInfo;
    private String  onlyScript;
    private String  expResult;
    private String  errExpResult;
    private String  version;
    private String  planTime;
    private String  publishDesc;
    private String  execUser;                                                                                                                           // 脚本的执行用户
    private String  performUser;                                                                                             // 任务的执行用户
    private int     hasParams;                                                                                                                 // 是否有参数（没参数0，有参数1）
    private int     level;
    private int     isShare;
    private String  useTimes;                                                                        // 浦发需求
    private String  isFromCustom;                                                                                                      // 任务申请页面
    // 执行次数
    private String  winTimes;                      // 成功率                                                             // 浦发需求
                                                                                                     // 任务申请页面
    private String  sysId; //应急处置，       业务系统id                                                                              
    public String getSysId ()
    {
        return sysId;
    }

    public void setSysId ( String sysId )
    {
        this.sysId = sysId;
    }

    private String  planId;                                                                                    // 预案id
    private String  scenceId;                                                                       // 场景id
    private String  stepId;                                                                                   // 步骤id
    private String  planName;                                                                        // 预案名称
    private String  scenceName;                                                           // 场景名称
    private String  taskTimeForDispaly;               // 定时执行，执行时间
    private long    flowId;
    private Long    shareType;              // 我的脚本 共享类型
    private Long[]  chosedShareIds;       // 我的脚本共享 用户、用户组 列表数组
    private Long    isCustomTask;          // 共享时使用，是否常用任务
    private String resGroupFlag;//任务申请时选择的是否是资源组类型设备
    // 光大页面，服务类型
    private int     serviceType;
    // 光大页面，数据库类型
    private String  dbType;
    // 光大页面，发布人
    private String  ssuer;
    // 光大页面，发布状态
    private String  startType;
    // 是否订阅
    private long    isSub;
    // 是否支持自动订阅
    private long    isAutoSub;
    // 是否支持手动发起
    private long    manualStart;
    // 服务分析算法状态
    private Long    anaStatus;
    // 服务id号
    private String  serviceId;
    // 浦发 单号操作类型
    private String oddNumbersType;

    private int                iscfgValue;

    // 服务权限
    private String  serviceAuto;
    // 服务组UUID
    private String  serviceGuuid;
    // 最后修改人
    private String  updateUserName;

    private int checkBeforeExec;

    public String getScriptWorkDir() {
        return scriptWorkDir;
    }

    public void setScriptWorkDir(String scriptWorkDir) {
        this.scriptWorkDir = scriptWorkDir;
    }

    private String scriptWorkDir;

    public int getCheckBeforeExec() {
        return checkBeforeExec;
    }

    public void setCheckBeforeExec(int checkBeforeExec) {
        this.checkBeforeExec = checkBeforeExec;
    }

    /**
     * 我的订阅发起
     */
    private boolean mysubStart;
    /**
     * 检测脚本的开始时间的CRON表达式
     */
    private String  planTimeTestingByCron;
    /**
     * 检测脚本的开始时间的CRON表达式
     */
    private String  planTimeTestingByDate;
    /**
     * 检测脚本的开始时间的类型(日/小时/分钟)
     */
    private String  planTimeTestingByType;

    private String  execModel;

    private String  execRule;

    private String  execTime;

    private String  serviceGId;

    private String  outTableName;

    private String  analyzeFunFlag;

    private String  analyzeFunText;

    private String  switchFlag;

    private String  startUser;            // 发起人

    private String  ifuncdesc;

    private String  keywords;

    private String  icontent;
    
    private java.sql.Timestamp  sysdate;            // 发起人
    private long  threeBsTypeId;            //  邮储三级分类id
    private String   threeBsTypeName;            //  邮储三级分类
    private String userPermission;//用户权限控制

    public String getImportScriptsStatusSearch() {
        return importScriptsStatusSearch;
    }

    public void setImportScriptsStatusSearch(String importScriptsStatusSearch) {
        this.importScriptsStatusSearch = importScriptsStatusSearch;
    }

    private String importScriptsStatusSearch; // 中原 脚本导入状态查询，方便修改基本信息

    public String getUserPermission() {
        return userPermission;
    }

    public void setUserPermission(String userPermission) {
        this.userPermission = userPermission;
    }

    public String getThreeBsTypeName() {
        return threeBsTypeName;
    }

    public void setThreeBsTypeName(String threeBsTypeName) {
        this.threeBsTypeName = threeBsTypeName;
    }

    public long getThreeBsTypeId() {
        return threeBsTypeId;
    }

    public void setThreeBsTypeId(long threeBsTypeId) {
        this.threeBsTypeId = threeBsTypeId;
    }

    public java.sql.Timestamp getSysdate ()
    {
        return sysdate;
    }

    public void setSysdate ( java.sql.Timestamp sysdate )
    {
        this.sysdate = sysdate;
    }

    public String getIcontent ()
    {
        return icontent;
    }

    public void setIcontent ( String icontent )
    {
        this.icontent = icontent;
    }

    public String getKeywords ()
    {
        return keywords;
    }

    public void setKeywords ( String keywords )
    {
        this.keywords = keywords;
    }

    public String getIfuncdesc ()
    {
        return ifuncdesc;
    }

    public void setIfuncdesc ( String ifuncdesc )
    {
        this.ifuncdesc = ifuncdesc;
    }

    public String getStartUser ()
    {
        return startUser;
    }

    public void setStartUser ( String startUser )
    {
        this.startUser = startUser;
    }

    public String getButterflyversion ()
    {
        return butterflyversion;
    }

    public void setButterflyversion ( String butterflyversion )
    {
        this.butterflyversion = butterflyversion;
    }

    public Boolean getQueryHistoryTask ()
    {
        return queryHistoryTask;
    }

    public void setQueryHistoryTask ( Boolean queryHistoryTask )
    {
        this.queryHistoryTask = queryHistoryTask;
    }

    public String getHostName ()
    {
        return hostName;
    }

    public void setHostName ( String hostName )
    {
        this.hostName = hostName;
    }

    public String getSwitchFlag ()
    {
        return switchFlag;
    }

    public String getCreateUserNameForCz() {
        return createUserNameForCz;
    }

    public void setCreateUserNameForCz(String createUserNameForCz) {
        this.createUserNameForCz = createUserNameForCz;
    }

    public void setSwitchFlag (String switchFlag )
    {
        this.switchFlag = switchFlag;
    }

    public String getExecModel ()
    {
        return execModel;
    }

    public Long getShareType ()
    {
        return shareType;
    }

    public Long getIsCustomTask ()
    {
        return isCustomTask;
    }

    public void setIsCustomTask ( Long isCustomTask )
    {
        this.isCustomTask = isCustomTask;
    }

    public void setShareType ( Long shareType )
    {
        this.shareType = shareType;
    }

    public Long[] getChosedShareIds ()
    {
        return chosedShareIds;
    }

    public void setChosedShareIds ( Long[] chosedShareIds )
    {
        this.chosedShareIds = chosedShareIds;
    }

    public void setExecModel ( String execModel )
    {
        this.execModel = execModel;
    }

    public String getExecRule ()
    {
        return execRule;
    }

    public void setExecRule ( String execRule )
    {
        this.execRule = execRule;
    }

    public String getExecTime ()
    {
        return execTime;
    }

    public void setExecTime ( String execTime )
    {
        this.execTime = execTime;
    }

    public String getServiceGId ()
    {
        return serviceGId;
    }

    public void setServiceGId ( String serviceGId )
    {
        this.serviceGId = serviceGId;
    }

    public int getServiceType ()
    {
        return serviceType;
    }

    public void setServiceType ( int serviceType )
    {
        this.serviceType = serviceType;
    }

    public String getDbType ()
    {
        return dbType;
    }

    public void setDbType ( String dbType )
    {
        this.dbType = dbType;
    }

    public String getSsuer ()
    {
        return ssuer;
    }

    public void setSsuer ( String ssuer )
    {
        this.ssuer = ssuer;
    }

    public String getStartType ()
    {
        return startType;
    }

    public void setStartType ( String startType )
    {
        this.startType = startType;
    }

    // 表名称
    private String tableName;

    public long getFlowId ()
    {
        return flowId;
    }

    public void setFlowId ( long flowId )
    {
        this.flowId = flowId;
    }

    public boolean isMysubStart ()
    {
        return mysubStart;
    }

    public void setMysubStart ( boolean mysubStart )
    {
        this.mysubStart = mysubStart;
    }

    public String getPlanTimeTestingByCron ()
    {
        return planTimeTestingByCron;
    }

    public void setPlanTimeTestingByCron ( String planTimeTestingByCron )
    {
        this.planTimeTestingByCron = planTimeTestingByCron;
    }

    public String getPlanTimeTestingByDate ()
    {
        return planTimeTestingByDate;
    }

    public void setPlanTimeTestingByDate ( String planTimeTestingByDate )
    {
        this.planTimeTestingByDate = planTimeTestingByDate;
    }

    public String getPlanTimeTestingByType ()
    {
        return planTimeTestingByType;
    }

    public void setPlanTimeTestingByType ( String planTimeTestingByType )
    {
        this.planTimeTestingByType = planTimeTestingByType;
    }

    public String getTableName ()
    {
        return tableName;
    }

    public void setTableName ( String tableName )
    {
        this.tableName = tableName;
    }

    public String getUseTimes ()
    {
        return useTimes;
    }

    public void setUseTimes ( String useTimes )
    {
        this.useTimes = useTimes;
    }

    public String getWinTimes ()
    {
        return winTimes;
    }

    public void setWinTimes ( String winTimes )
    {
        this.winTimes = winTimes;
    }

    public int getIsShare ()
    {
        return isShare;
    }

    public void setIsShare ( int isShare )
    {
        this.isShare = isShare;
    }

    public int getLevel ()
    {
        return level;
    }

    public void setLevel ( int level )
    {
        this.level = level;
    }

    public int getHasParams ()
    {
        return hasParams;
    }

    public void setHasParams ( int hasParams )
    {
        this.hasParams = hasParams;
    }

    public int getIsVisable ()
    {
        return isVisable;
    }

    public void setIsVisable ( int isVisable )
    {
        this.isVisable = isVisable;
    }

    private int    isVisable;                           // 可见性（公有0，私有1）

    private String resourceGroup;
    private List<Long> resourceGroups;//资源组数组ID，任务申请选择的可能是多个，复核时用
    private List<String> resGroupNames;//资源组名称数组
    private String chosedIps;
    private String chosedComputerListIds;//设备ids
    private int    scriptLevel;
    private String scriptLevelDisplay;
    private String audiType;
    private String taskName;
    private String eachNum;
    private String libType;
    private String taskType;
    private int    fromTable;
    private int    isCollected;

    public String getChosedComputerListIds() {
        return chosedComputerListIds;
    }

    public void setChosedComputerListIds(String chosedComputerListIds) {
        this.chosedComputerListIds = chosedComputerListIds;
    }

    public List<String> getResGroupNames ()
    {
        return resGroupNames;
    }

    public void setResGroupNames ( List<String> resGroupNames )
    {
        this.resGroupNames = resGroupNames;
    }

    public List<Long> getResourceGroups ()
    {
        return resourceGroups;
    }

    public void setResourceGroups (List<Long> resourceGroups )
    {
        this.resourceGroups = resourceGroups;
    }

    public String getPerformUser ()
    {
        return performUser;
    }

    public void setPerformUser ( String performUser )
    {
        this.performUser = performUser;
    }

    private String                expectType;
    private List<ScriptParameter> scriptParameters;
    private int                   isEMscript;
    private String                iappSysIds;
    private int                   isForbidden;
    private String                scriptAndVersion;

    private int                   isTimetask;                                                                  // 是否定时任务
    private String                taskTime;                                                                                    // 执行时间
    private String                nextStartTime;                                       // 下次执行时间
    private List<String>          execStartData;
    private String                visiableUser;                                                      // 白名单脚本可见用户

    public String getScriptAndVersion ()
    {
        if (null != version)
            return scriptName + "_V" + version;
        else
            return scriptName;
    }

    public void setScriptAndVersion ( String scriptAndVersion )
    {
        this.scriptAndVersion = scriptAndVersion;
    }

    public String getNextStartTime ()
    {
        return nextStartTime;
    }

    public void setNextStartTime ( String nextStartTime )
    {
        this.nextStartTime = nextStartTime;
    }

    private Map<Long, Long> selIpDsMap;

    private int             flag = 0;
    private int             type = Constants.IEAI_SCRIPT_SERVICE;

    public int getFlag ()
    {
        return flag;
    }

    public void setFlag ( int flag )
    {
        this.flag = flag;
    }

    public int getType ()
    {
        return type;
    }

    public void setType ( int type )
    {
        this.type = type;
    }

    public String getTaskType ()
    {
        return taskType;
    }

    public void setTaskType ( String taskType )
    {
        this.taskType = taskType;
    }

    public long getCoatId ()
    {
        return coatId;
    }

    public void setCoatId ( long coatId )
    {
        this.coatId = coatId;
    }

    public int getState ()
    {
        return state;
    }

    public void setState ( int state )
    {
        this.state = state;
    }

    public String getWorkItemId ()
    {
        return workItemId;
    }

    public void setWorkItemId ( String workItemId )
    {
        this.workItemId = workItemId;
    }

    public String getLoginName ()
    {
        return loginName;
    }

    public void setLoginName ( String loginName )
    {
        this.loginName = loginName;
    }

    public String getAudiType ()
    {
        return audiType;
    }

    public void setAudiType ( String audiType )
    {
        this.audiType = audiType;
    }

    public String getExpResult ()
    {
        return expResult;
    }

    public void setExpResult ( String expResult )
    {
        this.expResult = expResult;
    }

    public String getErrExpResult ()
    {
        return errExpResult;
    }

    public void setErrExpResult ( String errExpResult )
    {
        this.errExpResult = errExpResult;
    }

    public String getUserId ()
    {
        return userId;
    }

    public void setUserId ( String userId )
    {
        this.userId = userId;
    }

    public int getStart ()
    {
        return start;
    }

    public void setStart ( int start )
    {
        this.start = start;
    }

    public int getLimit ()
    {
        return limit;
    }

    public void setLimit ( int limit )
    {
        this.limit = limit;
    }

    public int getServiceState ()
    {
        return serviceState;
    }

    public void setServiceState ( int serviceState )
    {
        this.serviceState = serviceState;
    }

    public String getJsonData ()
    {
        return jsonData;
    }

    public void setJsonData ( String jsonData )
    {
        this.jsonData = jsonData;
    }

    public long getIid ()
    {
        return iid;
    }

    public void setIid ( long iid )
    {
        this.iid = iid;
    }

    public String getServiceName ()
    {
        return serviceName;
    }

    public void setServiceName ( String serviceName )
    {
        this.serviceName = serviceName;
    }

    public String getSysName ()
    {
        return sysName;
    }

    public void setSysName ( String sysName )
    {
        this.sysName = sysName;
    }

    public String getBussName ()
    {
        return bussName;
    }

    public void setBussName ( String bussName )
    {
        this.bussName = bussName;
    }

    public String getScriptType ()
    {
        return scriptType;
    }

    public void setScriptType ( String scriptType )
    {
        this.scriptType = scriptType;
    }

    public String getBfVersion ()
    {
        return bfVersion;
    }

    public void setBfVersion ( String bfVersion )
    {
        this.bfVersion = bfVersion;
    }

    public String getpUser ()
    {
        return pUser;
    }

    public void setpUser ( String pUser )
    {
        this.pUser = pUser;
    }

    public String getScriptName ()
    {
        return scriptName;
    }

    public void setScriptName ( String scriptName )
    {
        this.scriptName = scriptName;
    }

    public String getContent ()
    {
        return content;
    }

    public void setContent ( String content )
    {
        this.content = content;
    }

    public String getCreateUserName ()
    {
        return createUserName;
    }

    public void setCreateUserName ( String createUserName )
    {
        this.createUserName = createUserName;
    }

    public String getCreateTime ()
    {
        return createTime;
    }

    public void setCreateTime ( String createTime )
    {
        this.createTime = createTime;
    }

    public int getStatus ()
    {
        return status;
    }

    public void setStatus ( int status )
    {
        this.status = status;
    }

    public String getScriptPara ()
    {
        return ScriptPara;
    }

    public void setScriptPara ( String scriptPara )
    {
        ScriptPara = scriptPara;
    }

    public long getBussId ()
    {
        return bussId;
    }

    public void setBussId ( long bussId )
    {
        this.bussId = bussId;
    }

    public long getBussTypeId ()
    {
        return bussTypeId;
    }

    public void setBussTypeId ( long bussTypeId )
    {
        this.bussTypeId = bussTypeId;
    }

    public int getHasPage ()
    {
        return hasPage;
    }

    public void setHasPage ( int hasPage )
    {
        this.hasPage = hasPage;
    }

    public int getIsFlow ()
    {
        return isFlow;
    }

    public void setIsFlow ( int isFlow )
    {
        this.isFlow = isFlow;
    }

    public String getPlatForm ()
    {
        return platForm;
    }

    public void setPlatForm ( String platForm )
    {
        this.platForm = platForm;
    }

    public String getInputParamDesc ()
    {
        return inputParamDesc;
    }

    public void setInputParamDesc ( String inputParamDesc )
    {
        this.inputParamDesc = inputParamDesc;
    }

    public String getFuncDesc ()
    {
        return funcDesc;
    }

    public void setFuncDesc ( String funcDesc )
    {
        this.funcDesc = funcDesc;
    }

    public int getFromType ()
    {
        return fromType;
    }

    public void setFromType ( int fromType )
    {
        this.fromType = fromType;
    }

    public String getBackInfo ()
    {
        return backInfo;
    }

    public void setBackInfo ( String backInfo )
    {
        this.backInfo = backInfo;
    }

    public String getOnlyScript ()
    {
        return onlyScript;
    }

    public void setOnlyScript ( String onlyScript )
    {
        this.onlyScript = onlyScript;
    }

    public String getVersion ()
    {
        return version;
    }

    public void setVersion ( String version )
    {
        this.version = version;
    }

    public String getPlanTime ()
    {
        return planTime;
    }

    public void setPlanTime ( String planTime )
    {
        this.planTime = planTime;
    }

    public String getPublishDesc ()
    {
        return publishDesc;
    }

    public void setPublishDesc ( String publishDesc )
    {
        this.publishDesc = publishDesc;
    }

    public int getScriptLevel ()
    {
        return scriptLevel;
    }

    public void setScriptLevel ( int scriptLevel )
    {
        this.scriptLevel = scriptLevel;
    }

    public String getScriptLevelDisplay ()
    {
        return scriptLevelDisplay;
    }

    public void setScriptLevelDisplay ( String scriptLevelDisplay )
    {
        this.scriptLevelDisplay = scriptLevelDisplay;
    }

    public String getExecUser ()
    {
        return execUser;
    }

    public void setExecUser ( String execUser )
    {
        this.execUser = execUser;
    }

    public String getResourceGroup ()
    {
        return resourceGroup;
    }

    public void setResourceGroup ( String resourceGroup )
    {
        this.resourceGroup = resourceGroup;
    }

    public String getChosedIps ()
    {
        return chosedIps;
    }

    public void setChosedIps ( String chosedIps )
    {
        this.chosedIps = chosedIps;
    }

    public String getTaskName ()
    {
        return taskName;
    }

    public void setTaskName ( String taskName )
    {
        this.taskName = taskName;
    }

    public String getEachNum ()
    {
        return eachNum;
    }

    public void setEachNum ( String eachNum )
    {
        this.eachNum = eachNum;
    }

    public String getLibType ()
    {
        return libType;
    }

    public void setLibType ( String libType )
    {
        this.libType = libType;
    }

    public int getFromTable ()
    {
        return fromTable;
    }

    public void setFromTable ( int fromTable )
    {
        this.fromTable = fromTable;
    }

    public int getIsCollected ()
    {
        return isCollected;
    }

    public void setIsCollected ( int isCollected )
    {
        this.isCollected = isCollected;
    }

    public int getIsTimetask ()
    {
        return isTimetask;
    }

    public void setIsTimetask ( int isTimetask )
    {
        this.isTimetask = isTimetask;
    }

    public String getTaskTime ()
    {
        return taskTime;
    }

    public void setTaskTime ( String taskTime )
    {
        this.taskTime = taskTime;
    }

    public Map<Long, Long> getSelIpDsMap ()
    {
        return selIpDsMap;
    }

    public void setSelIpDsMap ( Map<Long, Long> selIpDsMap )
    {
        this.selIpDsMap = selIpDsMap;
    }

    public String getKillUserName ()
    {
        return killUserName;
    }

    public void setKillUserName ( String killUserName )
    {
        this.killUserName = killUserName;
    }

    public List<String> getExecStartData ()
    {
        return execStartData;
    }

    public void setExecStartData ( List<String> execStartData )
    {
        this.execStartData = execStartData;
    }

    public String getBussTypeName ()
    {
        return bussTypeName;
    }

    public void setBussTypeName ( String bussTypeName )
    {
        this.bussTypeName = bussTypeName;
    }

    public List<ScriptParameter> getScriptParameters ()
    {
        return scriptParameters;
    }

    public void setScriptParameters ( List<ScriptParameter> scriptParameters )
    {
        this.scriptParameters = scriptParameters;
    }

    public String getExpectType ()
    {
        return expectType;
    }

    public void setExpectType ( String expectType )
    {
        this.expectType = expectType;
    }

    public String getPlanId ()
    {
        return planId;
    }

    public void setPlanId ( String planId )
    {
        this.planId = planId;
    }

    public String getScenceId ()
    {
        return scenceId;
    }

    public void setScenceId ( String scenceId )
    {
        this.scenceId = scenceId;
    }

    public String getPlanName ()
    {
        return planName;
    }

    public void setPlanName ( String planName )
    {
        this.planName = planName;
    }

    public String getScenceName ()
    {
        return scenceName;
    }

    public void setScenceName ( String scenceName )
    {
        this.scenceName = scenceName;
    }

    public String getStepId ()
    {
        return stepId;
    }

    public void setStepId ( String stepId )
    {
        this.stepId = stepId;
    }

    public int getIsEMscript ()
    {
        return isEMscript;
    }

    public void setIsEMscript ( int isEMscript )
    {
        this.isEMscript = isEMscript;
    }

    public String getIappSysIds ()
    {
        return iappSysIds;
    }

    public void setIappSysIds ( String iappSysIds )
    {
        this.iappSysIds = iappSysIds;
    }

    public String getVisiableUser ()
    {
        return visiableUser;
    }

    public void setVisiableUser ( String visiableUser )
    {
        this.visiableUser = visiableUser;
    }

    public String getTaskTimeForDispaly ()
    {
        return taskTimeForDispaly;
    }

    public void setTaskTimeForDispaly ( String taskTimeForDispaly )
    {
        this.taskTimeForDispaly = taskTimeForDispaly;
    }

    public String getScriptuuid ()
    {
        return scriptuuid;
    }

    public void setScriptuuid ( String scriptuuid )
    {
        this.scriptuuid = scriptuuid;
    }

    /**
     * 数据库类型  1.oracle 2.db2 3.mysql
     */
    private String idbtype;
    /**
     * 服务类型 1.应用 2.检测
     */
    private String iservicetype;
    /**
     * 操作  0 发起  1 已发起
     */
    private String Istarttype;
    /**
     * 脚本参数 -- iid
     */
    private int    argsIid;
    /**
     * 脚本参数 -- 脚本排序
     */
    private int    argsOrder;
    /**
     * 脚本参数 -- 脚本配型
     */
    private String argsType;
    /**
     * 脚本参数 -- 脚本默认值
     */
    private String argsDefaultValue;
    /**
     * 脚本参数 -- 脚本描述
     */
    private String argsDesc;

    /**
     * 服务器  -- 业务系统
     */
    private String serviceSystem;
    /**
     * 服务器  --  数据库类型
     */
    private String serviceDBType;
    /**
     * 服务器  --  服务器版本
     */
    private String serviceDBVersion;
    /**
     * 服务器  --  服务器ip
     */
    private String iip;
    private String agentIp;
    private String agentPort;
    private String ostype;

    /**
     * 服务器  是否需要发起审核 0 不需要  1需要
     */
    private String iisexam;

    private String sqlModel;
    private String insUserName;
    private String insName;

    public String getInsUserName ()
    {
        return insUserName;
    }

    public void setInsUserName ( String insUserName )
    {
        this.insUserName = insUserName;
    }

    public String getInsName ()
    {
        return insName;
    }

    public void setInsName ( String insName )
    {
        this.insName = insName;
    }

    public String getSqlModel ()
    {
        return sqlModel;
    }

    public void setSqlModel ( String sqlModel )
    {
        this.sqlModel = sqlModel;
    }

    public String getIdbtype ()
    {
        return idbtype;
    }

    public void setIdbtype ( String idbtype )
    {
        this.idbtype = idbtype;
    }

    public String getIservicetype ()
    {
        return iservicetype;
    }

    public void setIservicetype ( String iservicetype )
    {
        this.iservicetype = iservicetype;
    }

    public String getIstarttype ()
    {
        return Istarttype;
    }

    public void setIstarttype ( String istarttype )
    {
        Istarttype = istarttype;
    }

    public int getArgsIid ()
    {
        return argsIid;
    }

    public void setArgsIid ( int argsIid )
    {
        this.argsIid = argsIid;
    }

    public int getArgsOrder ()
    {
        return argsOrder;
    }

    public void setArgsOrder ( int argsOrder )
    {
        this.argsOrder = argsOrder;
    }

    public String getArgsType ()
    {
        return argsType;
    }

    public void setArgsType ( String argsType )
    {
        this.argsType = argsType;
    }

    public String getArgsDefaultValue ()
    {
        return argsDefaultValue;
    }

    public void setArgsDefaultValue ( String argsDefaultValue )
    {
        this.argsDefaultValue = argsDefaultValue;
    }

    public String getArgsDesc ()
    {
        return argsDesc;
    }

    public void setArgsDesc ( String argsDesc )
    {
        this.argsDesc = argsDesc;
    }

    public String getServiceSystem ()
    {
        return serviceSystem;
    }

    public void setServiceSystem ( String serviceSystem )
    {
        this.serviceSystem = serviceSystem;
    }

    public String getServiceDBType ()
    {
        return serviceDBType;
    }

    public void setServiceDBType ( String serviceDBType )
    {
        this.serviceDBType = serviceDBType;
    }

    public String getServiceDBVersion ()
    {
        return serviceDBVersion;
    }

    public void setServiceDBVersion ( String serviceDBVersion )
    {
        this.serviceDBVersion = serviceDBVersion;
    }

    public String getIip ()
    {
        return iip;
    }

    public void setIip ( String iip )
    {
        this.iip = iip;
    }

    public String getAgentIp ()
    {
        return agentIp;
    }

    public void setAgentIp ( String agentIp )
    {
        this.agentIp = agentIp;
    }

    public String getAgentPort ()
    {
        return agentPort;
    }

    public void setAgentPort ( String agentPort )
    {
        this.agentPort = agentPort;
    }

    public String getOstype ()
    {
        return ostype;
    }

    public void setOstype ( String ostype )
    {
        this.ostype = ostype;
    }

    public String getIisexam ()
    {
        return iisexam;
    }

    public void setIisexam ( String iisexam )
    {
        this.iisexam = iisexam;
    }

    public String toString ()
    {
        return "ScriptService [userId=" + userId + ", workItemId=" + workItemId + ", loginName=" + loginName + ", iid="
                + iid + ", coatId=" + coatId + ", serviceName=" + serviceName + ", sysName=" + sysName + ", bussName="
                + bussName + ", bussTypeName=" + bussTypeName + ", bussId=" + bussId + ", bussTypeId=" + bussTypeId
                + ", scriptType=" + scriptType + ", scriptName=" + scriptName + ", ScriptPara=" + ScriptPara
                + ", content=" + content + ", platForm=" + platForm + ", inputParamDesc=" + inputParamDesc
                + ", funcDesc=" + funcDesc + ", createUserName=" + createUserName + ", killUserName=" + killUserName
                + ", createTime=" + createTime + ", status=" + status + ", state=" + state + ", isFlow=" + isFlow
                + ", jsonData=" + jsonData + ", serviceState=" + serviceState + ", hasPage=" + hasPage + ", start="
                + start + ", limit=" + limit + ", fromType=" + fromType + ", backInfo=" + backInfo + ", onlyScript="
                + onlyScript + ", expResult=" + expResult + ", errExpResult=" + errExpResult + ", version=" + version
                + ", planTime=" + planTime + ", publishDesc=" + publishDesc + ", execUser=" + execUser
                + ", resourceGroup=" + resourceGroup + ", chosedIps=" + chosedIps + ", scriptLevel=" + scriptLevel
                + ", scriptLevelDisplay=" + scriptLevelDisplay + ", audiType=" + audiType + ", taskName=" + taskName
                + ", eachNum=" + eachNum + ", libType=" + libType + ", taskType=" + taskType + ", fromTable="
                + fromTable + ", isCollected=" + isCollected + ", expectType=" + expectType + ", scriptParameters="
                + scriptParameters + ", isTimetask=" + isTimetask + ", taskTime=" + taskTime + ", nextStartTime="
                + nextStartTime + ", execStartData=" + execStartData + ", serviceType=" + serviceType + ", dbType="
                + dbType + ", ssuer=" + ssuer + ", startType=" + startType + ", mysubStart=" + mysubStart
                + ", planTimeTestingByCron=" + planTimeTestingByCron + ", planTimeTestingByDate="
                + planTimeTestingByDate + ", planTimeTestingByType=" + planTimeTestingByType + ", idbtype=" + idbtype
                + ", iservicetype=" + iservicetype + ", Istarttype=" + Istarttype + ", argsIid=" + argsIid
                + ", argsOrder=" + argsOrder + ", argsType=" + argsType + ", argsDefaultValue=" + argsDefaultValue
                + ", argsDesc=" + argsDesc + ", serviceSystem=" + serviceSystem + ", serviceDBType=" + serviceDBType
                + ", serviceDBVersion=" + serviceDBVersion + ", iip=" + iip + ", agentIp=" + agentIp + ", agentPort="
                + agentPort + ", ostype=" + ostype + ", iisexam=" + iisexam + ", selIpDsMap=" + selIpDsMap + ", flag="
                + flag + ", type=" + type + ", analyzeFunText=" + analyzeFunText + ",isSub=" + isSub + ",isAutoSub="
                + isAutoSub + "]";
    }

    public String getIsFromCustom ()
    {
        return isFromCustom;
    }

    public void setIsFromCustom ( String isFromCustom )
    {
        this.isFromCustom = isFromCustom;
    }

    public String getOutTableName ()
    {
        return outTableName;
    }

    public void setOutTableName ( String outTableName )
    {
        this.outTableName = outTableName;
    }

    public String getAnalyzeFunFlag ()
    {
        return analyzeFunFlag;
    }

    public int getIsdelete ()
    {
        return isdelete;
    }

    public void setIsdelete ( int isdelete )
    {
        this.isdelete = isdelete;
    }

    public void setAnalyzeFunFlag ( String analyzeFunFlag )
    {
        this.analyzeFunFlag = analyzeFunFlag;
    }

    public String getAnalyzeFunText ()
    {
        return analyzeFunText;
    }

    public void setAnalyzeFunText ( String analyzeFunText )
    {
        this.analyzeFunText = analyzeFunText;
    }

    public int getIsForbidden ()
    {
        return isForbidden;
    }

    public void setIsForbidden ( int isForbidden )
    {
        this.isForbidden = isForbidden;
    }

    public long getIsSub ()
    {
        return isSub;
    }

    public void setIsSub ( long isSub )
    {
        this.isSub = isSub;
    }

    public long getManualStart ()
    {
        return manualStart;
    }

    public void setManualStart ( long manualStart )
    {
        this.manualStart = manualStart;
    }

    public long getIsAutoSub ()
    {
        return isAutoSub;
    }

    public void setIsAutoSub ( long isAutoSub )
    {
        this.isAutoSub = isAutoSub;
    }

    public String getIgnore ()
    {
        return ignore;
    }

    public void setIgnore ( String ignore )
    {
        this.ignore = ignore;
    }

    public Long getAnaStatus ()
    {
        return anaStatus;
    }

    public void setAnaStatus ( Long anaStatus )
    {
        this.anaStatus = anaStatus;
    }

    public String getShutdownState ()
    {
        return shutdownState;
    }

    public void setShutdownState ( String shutdownState )
    {
        this.shutdownState = shutdownState;
    }

    public String getStartCheckState ()
    {
        return startCheckState;
    }

    public void setStartCheckState ( String startCheckState )
    {
        this.startCheckState = startCheckState;
    }

    public String getServiceAuto ()
    {
        return serviceAuto;
    }

    public void setServiceAuto ( String serviceAuto )
    {
        this.serviceAuto = serviceAuto;
    }

    public String getServiceId ()
    {
        return serviceId;
    }

    public void setServiceId ( String serviceId )
    {
        this.serviceId = serviceId;
    }

    private String usenumText;      // 使用次数
    private String usenum = "-1";      // 使用次数条件
    private String succText;      // 成功
    private String succ   = "-1";      // 成功条件

    public String getUsenumText ()
    {
        return usenumText;
    }

    public void setUsenumText ( String usenumText )
    {
        this.usenumText = usenumText;
    }

    public String getUsenum ()
    {
        return usenum;
    }

    public void setUsenum ( String usenum )
    {
        this.usenum = usenum;
    }

    public String getSuccText ()
    {
        return succText;
    }

    public void setSuccText ( String succText )
    {
        this.succText = succText;
    }

    public String getSucc ()
    {
        return succ;
    }

    public void setSucc ( String succ )
    {
        this.succ = succ;
    }

    public String getServiceGuuid ()
    {
        return serviceGuuid;
    }

    public void setServiceGuuid ( String serviceGuuid )
    {
        this.serviceGuuid = serviceGuuid;
    }

    private String  currentUserId;

    private boolean isManage;

    public boolean isManage ()
    {
        return isManage;
    }

    public void setManage ( boolean isManage )
    {
        this.isManage = isManage;
    }

    public String getCurrentUserId ()
    {
        return currentUserId;
    }

    public void setCurrentUserId ( String currentUserId )
    {
        this.currentUserId = currentUserId;
    }

    public String getUpdateUserName ()
    {
        return updateUserName;
    }

    public void setUpdateUserName ( String updateUserName )
    {
        this.updateUserName = updateUserName;
    }

    public String getResGroupFlag ()
    {
        return resGroupFlag;
    }

    public void setResGroupFlag ( String resGroupFlag )
    {
        this.resGroupFlag = resGroupFlag;
    }

    public int getIscfgValue ()
    {
        return iscfgValue;
    }

    public void setIscfgValue ( int iscfgValue )
    {
        this.iscfgValue = iscfgValue;
    }

    public String getRunServiceflowId ()
    {
        return runServiceflowId;
    }

    public void setRunServiceflowId ( String runServiceflowId )
    {
        this.runServiceflowId = runServiceflowId;
    }

    public int getFlowState ()
    {
        return flowState;
    }

    public void setFlowState ( int flowState )
    {
        this.flowState = flowState;
    }

    public String getOddNumbersType ()
    {
        return oddNumbersType;
    }

    public void setOddNumbersType ( String oddNumbersType )
    {
        this.oddNumbersType = oddNumbersType;
    }

    public String getAuditSysAgentListJson() {
        return auditSysAgentListJson;
    }

    public void setAuditSysAgentListJson(String auditSysAgentListJson) {
        this.auditSysAgentListJson = auditSysAgentListJson;
    }
}

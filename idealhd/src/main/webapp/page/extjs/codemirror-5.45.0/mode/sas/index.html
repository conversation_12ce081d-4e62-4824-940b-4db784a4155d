<!doctype html>

<title>CodeMirror: SAS mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../xml/xml.js"></script>
<script src="sas.js"></script>
<style>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
  .cm-s-default .cm-trailing-space-a:before,
  .cm-s-default .cm-trailing-space-b:before {position: absolute; content: "\00B7"; color: #777;}
  .cm-s-default .cm-trailing-space-new-line:before {position: absolute; content: "\21B5"; color: #777;}
</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">SAS</a>
  </ul>
</div>

<article>
<h2>SAS mode</h2>
<form><textarea id="code" name="code">
libname foo "/tmp/foobar";
%let count=1;

/* Multi line
Comment
*/
data _null_;
    x=ranuni();
    * single comment;
    x2=x**2;
    sx=sqrt(x);
    if x=x2 then put "x must be 1";
    else do;
        put x=;
    end;
run;

/* embedded comment
* comment;
*/

proc glm data=sashelp.class;
    class sex;
    model weight = height sex;
run;

proc sql;
    select count(*)
    from sashelp.class;

    create table foo as
    select * from sashelp.class;

    select *
    from foo;
quit;
</textarea></form>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    mode: 'sas',
    lineNumbers: true
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-sas</code>.</p>

</article>

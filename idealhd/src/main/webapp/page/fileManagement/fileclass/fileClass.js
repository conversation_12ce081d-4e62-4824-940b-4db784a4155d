Ext.onReady(function() {
	
	var queryButton = Ext.create("Ext.Button", {
		text : '查询',
		cls : 'Common_Btn',
		handler : function() {
			queryBtnFun();
		}
	});
	
	var fileNameQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入分类名称--',
		width : 260,
		name : 'className',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					fileClassGrid1.ipage.moveFirst();
				}
			}
		}
	});
	
	var fileClassForm1 = Ext.create('Ext.form.Panel', {
		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
	  	border : false,
	  	bodyCls:'x-docked-noborder-top',
	  	baseCls:'customize_gray_back',
	  	dockedItems : [{
	  		xtype : 'toolbar',
			border : false,
			dock : 'top',
			baseCls:'customize_gray_back',
			items:[fileNameQuery,queryButton,"->",{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加',
				handler : addFileClass
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存',
				handler : saveFileClass
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '删除',
				handler : deleteFileClass
			}]
	  	}]
	});
	Ext.define('fileClassGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'string'
		},{
			name : 'iclassname',
			type : 'string'
		},{
			name : 'iclassdesc',
			type : 'string'
		},{
			name : 'icreatetime',
			type : 'string'
		},{
			name : 'iparentid',
			type : 'string'
		}]
	});

	var fileClassGrid1Store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'fileClassGrid1Model',
		proxy : {
			type : 'ajax',
			url : 'getFileClassList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var fileClassGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
		store:fileClassGrid1Store,
		padding : grid_space,
	    columnLines : true,
	    cls:'customize_panel_back',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		region : 'center',
		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
		columns : [ {
			xtype : 'rownumberer',
			width: 50,
			text : '序号'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iid',
			text : '主键'
		},{
			xtype : 'gridcolumn',
			dataIndex: 'iclassname',
			flex:1,
			text : '分类名称',
			editor : {}
		}, {
			xtype : 'gridcolumn',
			dataIndex: 'iclassdesc',
			width:300,
			text : '分类描述',
			editor : {}
		}, {
			xtype : 'gridcolumn',
			dataIndex: 'icreatetime',
			width:300,
			text : '创建时间'
		}, { 
			text: '操作',  
			xtype : 'actiontextcolumn',
			dataIndex: 'sysOperation',
			width:200,
			items : [{
				text : '配置分类',
				iconCls : 'script_set',					 
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.iid; 
		            var iclassname = grid.getStore().data.items[rowIndex].data.iclassname;
		            showFileTwoClass(iid,iclassname);
				}
			}]
        }]
	});
	
	var fileClassPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'fileClass_div',
		height : contentPanel.getHeight() - modelHeigth,
        width : contentPanel.getWidth(),
		layout : 'border',
		cls:'customize_panel_back',
		items : [ fileClassForm1, fileClassGrid1 ]
	});
	
	fileClassGrid1Store.on('beforeload', function (store, options) {
		var new_params = {  
				queryString:fileNameQuery.getValue().trim()
		};
		Ext.apply(fileClassGrid1Store.proxy.extraParams, new_params);
	});
	
	contentPanel.on('resize', function() {
		fileClassPanel1.setHeight(contentPanel.getHeight() - modelHeigth);
		fileClassPanel1.setWidth(contentPanel.getWidth());
	});
	
	function queryBtnFun(){
		fileClassGrid1.ipage.moveFirst();
	}
	
	function addFileClass(){
		var p = {
				iid : -1,
				iclassname : '',
				iclassdesc : '',
			    icreatetime : '',
			    iparentid : 0
			};
		fileClassGrid1Store.insert(0, p);
	}
	
	function saveFileClass(){
		var m = fileClassGrid1Store.getModifiedRecords();
		if (m.length < 1) {
			Ext.Msg.alert('提示','您没有进行任何修改，无需保存');
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			
			var iclassname = m[i].get ("iclassname").trim ();
			if ('' == iclassname)
			{
				Ext.Msg.alert ('提示', '分类名称不能为空！');
				return;
			}
			if (fucCheckLength (iclassname) > 100)
			{
				Ext.Msg.alert ('提示', '分类名称不能超过100字符！');
				return;
			}
			var iclassdesc = m[i].get ("iclassdesc").trim ();
			if (fucCheckLength (iclassdesc) > 255)
			{
				Ext.Msg.alert ('提示', '分类描述不能超过255字符！');
				return;
			}
			
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		Ext.Ajax.request({
			url : 'saveFileClassBean.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					fileClassGrid1Store.load();
				}
				Ext.Msg.alert('提示', message);
			},
			failure : function(result, request) {
				secureFilterRs(result, '保存失败！');
			}
		});
	}
	
	function deleteFileClass(){
		var data = fileClassGrid1.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
			return;
		} else {
			Ext.Msg.confirm("请确认","是否要删除该数据及相关联数据？",function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(data, function(record) {
						var iid = record.get('iid');
						if (iid != "" && null != iid) {
							ids.push(iid);
						}
					});
					if (ids.length <= 0) {
						return;
					}
					Ext.Ajax.request({
						url : 'deleteFileClassBean.do',
						params : {
							deleteIds : ids.join(',')
						},
						method : 'POST',
						success : function(response, opts) {
							var success = Ext.decode(response.responseText).success;
							if (success) {
								fileClassGrid1Store.reload();
							}
							Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
						},
						failure : function(result, request) {
							secureFilterRs(result,'操作失败！');
						}
					});
				}
			});
		}
	}
	
    function showFileTwoClass(iid,iclassname) {
    	if (iid == -1) {
    		Ext.Msg.alert('提示', '请先保存类别后再配置分类。');
    		return;
    	}
    	
    	var queryButton = Ext.create("Ext.Button", {
    		text : '查询',
    		cls : 'Common_Btn',
    		handler : function() {
    			queryBtnFun();
    		}
    	});
    	
    	var fileNameQuery = Ext.create('Ext.form.TextField', {
    		emptyText : '--请输入分类名称--',
    		width : 260,
    		name : 'className',
    		xtype : 'textfield',
    		listeners : {
    			specialkey : function(field, e) {
    				if (e.getKey() == Ext.EventObject.ENTER) {
    					fileClassTwoGrid.ipage.moveFirst();
    				}
    			}
    		}
    	});
    	
    	var fileClassTwoForm = Ext.create('Ext.form.Panel', {
    		region : 'north',
    	  	layout : 'anchor',
    	  	buttonAlign : 'center',
    	  	border : false,
    	  	dockedItems : [{
    	  		xtype : 'toolbar',
    			border : false,
    			dock : 'top',
    			items:[fileNameQuery,queryButton,"->",{
    				xtype : 'button',
    				cls : 'Common_Btn',
    				text : '增加',
    				handler : addFileClassTwo
    			},{
    				xtype : 'button',
    				cls : 'Common_Btn',
    				text : '保存',
    				handler : saveFileClassTwo
    			},{
    				xtype : 'button',
    				cls : 'Common_Btn',
    				text : '删除',
    				handler : deleteFileClassTwo
    			}]
    	  	}]
    	});
    	
    	Ext.define('fileClassTwoModel', {
    		extend : 'Ext.data.Model',
    		fields : [{
    			name : 'iid',
    			type : 'string'
    		},{
    			name : 'iclassname',
    			type : 'string'
    		},{
    			name : 'iclassdesc',
    			type : 'string'
    		},{
    			name : 'icreatetime',
    			type : 'string'
    		},{
    			name : 'iparentid',
    			type : 'string'
    		}]
    	});

    	var fileClassTwoStore = Ext.create('Ext.data.Store', {
    		autoLoad : true,
    		autoDestroy : true,
    		model : 'fileClassTwoModel',
    		proxy : {
    			type : 'ajax',
    			url : 'getFileClassTwoList.do?iparentid='+iid,
    			reader : {
    				type : 'json',
    				root : 'dataList',
    				totalProperty : 'total'
    			}
    		}
    	});
    	
    	var fileClassTwoGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    		store:fileClassTwoStore,
    		padding : grid_space,
    	    columnLines : true,
    	    cls:'customize_panel_back',
    	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    		region : 'center',
    		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
    		columns : [ {
    			xtype : 'rownumberer',
    			width: 50,
    			text : '序号'
    		}, {
    			xtype : 'gridcolumn',
    			hidden : true,
    			dataIndex : 'iid',
    			text : '主键'
    		},{
    			xtype : 'gridcolumn',
    			dataIndex: 'iclassname',
    			flex:1,
    			text : '分类名称',
    			editor : {}
    		}, {
    			xtype : 'gridcolumn',
    			dataIndex: 'iclassdesc',
    			width:250,
    			text : '分类描述',
    			editor : {}
    		}, {
    			xtype : 'gridcolumn',
    			dataIndex: 'icreatetime',
    			width:250,
    			text : '创建时间'
    		}]
    	});
    	
    	var classTwoPanel = Ext.create('Ext.panel.Panel', {
    		height : '100%',
    		width : '100%',
    		layout : 'border',
    		cls:'customize_panel_back',
    		items : [ fileClassTwoForm, fileClassTwoGrid ]
    	});
    	
    	fileClassTwoStore.on('beforeload', function (store, options) {
    		var new_params = {  
    				queryString:fileNameQuery.getValue().trim()
    		};
    		Ext.apply(fileClassTwoStore.proxy.extraParams, new_params);
    	});

    	var classTwoWin = Ext.create('Ext.window.Window', {
				title : iclassname+"二级分类",
				draggable: false,
				resizable : false,// 禁止缩放
				modal : true,
				closeAction : 'destroy',
				height : 600,
				width : 800,
				layout : 'fit',
				items : [ classTwoPanel ]
		}).show();
    	
    	function queryBtnFun(){
    		fileClassTwoGrid.ipage.moveFirst();
    	}
    	
    	function addFileClassTwo(){
    		var p = {
    				iid : -1,
    				iclassname : '',
    				iclassdesc : '',
    			    icreatetime : '',
    			    iparentid : iid
    			};
    		fileClassTwoStore.insert(0, p);
    	}
    	
    	function saveFileClassTwo(){
    		var m = fileClassTwoStore.getModifiedRecords();
    		if (m.length < 1) {
    			Ext.Msg.alert('提示','您没有进行任何修改，无需保存');
    			return;
    		}
    		var jsonData = "[";
    		for (var i = 0, len = m.length; i < len; i++) {
    			
    			var iclassname = m[i].get ("iclassname").trim ();
    			if ('' == iclassname)
    			{
    				Ext.Msg.alert ('提示', '分类名称不能为空！');
    				return;
    			}
    			if (fucCheckLength (iclassname) > 100)
    			{
    				Ext.Msg.alert ('提示', '分类名称不能超过100字符！');
    				return;
    			}
    			var iclassdesc = m[i].get ("iclassdesc").trim ();
    			if (fucCheckLength (iclassdesc) > 255)
    			{
    				Ext.Msg.alert ('提示', '分类描述不能超过255字符！');
    				return;
    			}
    			
    			var ss = Ext.JSON.encode(m[i].data);
    			if (i == 0)
    				jsonData = jsonData + ss;
    			else
    				jsonData = jsonData + "," + ss;
    		}
    		jsonData = jsonData + "]";
    		Ext.Ajax.request({
    			url : 'saveFileClassTwo.do',
    			method : 'POST',
    			params : {
    				jsonData : jsonData,
    				iparentid : iid
    			},
    			success : function(response, request) {
    				var success = Ext.decode(response.responseText).success;
    				var message = Ext.decode(response.responseText).message;
    				if (success) {
    					fileClassTwoStore.load();
    				}
    				Ext.Msg.alert('提示', message);
    			},
    			failure : function(result, request) {
    				secureFilterRs(result, '保存失败！');
    			}
    		});
    	}
    	
    	function deleteFileClassTwo(){
    		var data = fileClassTwoGrid.getView().getSelectionModel().getSelection();
    		if (data.length == 0) {
    			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
    			return;
    		} else {
    			Ext.Msg.confirm("请确认","是否要删除该数据及相关联数据？",function(button, text) {
    				if (button == "yes") {
    					var ids = [];
    					Ext.Array.each(data, function(record) {
    						var iid = record.get('iid');
    						if (iid != "" && null != iid) {
    							ids.push(iid);
    						}
    					});
    					if (ids.length <= 0) {
    						return;
    					}
    					Ext.Ajax.request({
    						url : 'deleteFileClassTwo.do',
    						params : {
    							deleteIds : ids.join(','),
    							iparentid : iid
    						},
    						method : 'POST',
    						success : function(response, opts) {
    							var success = Ext.decode(response.responseText).success;
    							if (success) {
    								fileClassTwoStore.reload();
    							}
    							Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
    						},
    						failure : function(result, request) {
    							secureFilterRs(result,'操作失败！');
    						}
    					});
    				}
    			});
    		}
    	}
    }
    
    String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};
});
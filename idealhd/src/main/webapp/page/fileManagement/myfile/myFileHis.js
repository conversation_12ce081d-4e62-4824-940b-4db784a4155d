Ext.onReady(function() {
	var uploadFileWin;
	Ext.define('fileClassModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'long'
		},{
			name : 'iclassname',
			type : 'string'
		}]
	});
	var oneClassStore = Ext.create('Ext.data.JsonStore', {
	    	autoLoad : true,
	    	autoDestroy : true,
	    	model:'fileClassModel',
	    	proxy: {
	    		type: 'ajax',
	    		url: 'getOneClass.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
	    	}
	  });
	var twoClassStore = Ext.create('Ext.data.JsonStore', {
	    	autoLoad : false,
	    	autoDestroy : true,
	    	model:'fileClassModel',
	    	proxy: {
	    		type: 'ajax',
	    		url: 'getTwoClass.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
	    	}
	});
	//一级分类
	var oneClass = Ext.create('Ext.form.field.ComboBox', {
		name : 'oneClassName',
		queryMode : 'local',
		displayField : 'iclassname',
		valueField : 'iid',
		editable : false,
		emptyText : "--请选择一级分类--",
		store : oneClassStore,
		width:180,
		listeners : {
			change : function() {
				twoClass.clearValue();
				twoClass.applyEmptyText();
				twoClass.getPicker().getSelectionModel().doMultiSelect([], false);
				twoClassStore.load({
					params : {
						oneClassId : this.value
					}
				});
			},
			specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myFileHis.ipage.moveFirst();
	                }
	         },
	         select : function(combo, records, options) {
	        	 myFileHis.ipage.moveFirst();
 			}
		}
	});
	//二级分类
	var twoClass = Ext.create('Ext.form.field.ComboBox', {
		name : 'twoClassName',
		queryMode : 'local',
		displayField : 'iclassname',
		valueField : 'iid',
		editable : false,
		emptyText : "--请选择二级分类--",
		store : twoClassStore,
		width:180,
		listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myFileHis.ipage.moveFirst();
	                }
	            },
	            select : function(combo, records, options) {
		        	 myFileHis.ipage.moveFirst();
	 			}
	     }
	});
	
	var fileNameQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入文档名称--',
		width : 180,
		name : 'fileName',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					myFileHis.ipage.moveFirst();
				}
			}
		}
	});
	
	var fileTypeQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入文档类型--',
		width : 150,
		name : 'fileType',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					myFileHis.ipage.moveFirst();
				}
			}
		}
	});
	
	var myFileHisForm = Ext.create('Ext.form.Panel', {
		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
	  	border : false,
	  	bodyCls:'x-docked-noborder-top',
	  	baseCls:'customize_gray_back',
	  	dockedItems : [{
	  		xtype : 'toolbar',
			border : false,
			dock : 'top',
			baseCls:'customize_gray_back',
			items:[oneClass,twoClass,fileNameQuery,fileTypeQuery,{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : queryBtnFun
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '清空',
				handler : cleanBtnFun
			},"->",{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '删除',
				handler : deleteFile
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '返回',
				handler : function() {
		        	contentPanel.getLoader().load({url: "initMyFilePage.do?temp="+new Date().getTime(),scripts: true});
		        }
			}]
	  	}]
	});
	Ext.define('myFileHisModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'string'
		},{
			name : 'ifilename',
			type : 'string'
		},{
			name : 'ifiletype',
			type : 'string'
		},{
			name : 'oneClassName',
			type : 'string'
		},{
			name : 'twoClassName',
			type : 'string'
		},{
			name : 'ifiledesc',
			type : 'string'
		},{
			name : 'ifileversion',
			type : 'string'
		},{
			name : 'iuploadtime',
			type : 'string'
		}]
	});

	var myFileHisStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'myFileHisModel',
		proxy : {
			type : 'ajax',
			url : 'getMyFileHisList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var myFileHis = Ext.create('Ext.ux.ideal.grid.Panel', {
		store:myFileHisStore,
		padding:panel_margin,
	    columnLines : true,
	    cls:'customize_panel_back',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		region : 'center',
		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
		columns : [ {
			xtype : 'rownumberer',
			width: 50,
			text : '序号'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iid',
			text : '主键'
		},{
			xtype : 'gridcolumn',
			dataIndex: 'ifilename',
			flex:1,
			text : '文档名称'
		}, {
			xtype : 'gridcolumn',
			dataIndex: 'ifiletype',
			width:80,
			text : '文档类型'
		},{
			xtype : 'gridcolumn',
			dataIndex: 'oneClassName',
			flex:1,
			text : '一级分类'
		}, {
			xtype : 'gridcolumn',
			dataIndex: 'twoClassName',
			flex:1,
			text : '二级分类'
		},{
			xtype : 'gridcolumn',
			dataIndex: 'ifiledesc',
			flex:1,
			text : '文档说明'
		},{
			xtype : 'gridcolumn',
			dataIndex: 'ifileversion',
			width:80,
			text : '文档版本',
			renderer:function(value,p,record){
				return value+".0";
			}
		},{
			xtype : 'gridcolumn',
			dataIndex: 'iuploadtime',
			flex:1,
			text : '创建时间'
		},{ 
			text: '操作',  
			dataIndex: 'sysOperation',
			width:200,
			renderer:function(value,p,record){
             	var iid =  record.get('iid');
             	var ifilename =  record.get('ifilename');
             	var ifiletype =  record.get('ifiletype');
            	var ifileversion = record.get('ifileversion');
            	return '<span class="switch_span"><a href="javascript:void(0)" onclick="showFile('+iid+',\''+ifiletype+'\',\''+ifilename+'\',\''+ifileversion+'\')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;预览</a></span>'
             		+'&nbsp;&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="downloadFile(' +iid+')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>&nbsp;下载</a></span>';
             }
        }]
	});
	
	var myFileHisPanel = Ext.create('Ext.panel.Panel', {
		renderTo : 'myFileHis_div',
		height : contentPanel.getHeight() - modelHeigth,
        width : contentPanel.getWidth(),
		layout : 'border',
		cls:'customize_panel_back',
		items : [ myFileHisForm, myFileHis ]
	});
	
	myFileHisStore.on('beforeload', function (store, options) {
		var new_params = {
				imyfileid:imyfileid,
				iclassidone:oneClass.getValue()==null?-1:oneClass.getValue(),
				iclassidtwo:twoClass.getValue()==null?-1:twoClass.getValue(),
				ifilename:fileNameQuery.getValue().trim(),
				ifiletype:fileTypeQuery.getValue()==null?"":fileTypeQuery.getValue().trim()
		};
		Ext.apply(myFileHisStore.proxy.extraParams, new_params);
	});
	
	contentPanel.on('resize', function() {
		myFileHisPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		myFileHisPanel.setWidth(contentPanel.getWidth());
	});
	
	function queryBtnFun(){
		myFileHis.ipage.moveFirst();
	}
	
	function cleanBtnFun(){
		myFileHisForm.getForm().reset();
	}
	
	function deleteFile(){
		var data = myFileHis.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
			return;
		} else {
			Ext.Msg.confirm("请确认","是否要删除该数据？",function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(data, function(record) {
						var iid = record.get('iid');
						if (iid != "" && null != iid) {
							ids.push(iid);
						}
					});
					if (ids.length <= 0) {
						return;
					}
					Ext.Ajax.request({
						url : 'deleteMyFileBeanHis.do',
						params : {
							deleteIds : ids.join(',')
						},
						method : 'POST',
						success : function(response, opts) {
							var success = Ext.decode(response.responseText).success;
							if (success) {
								myFileHisStore.reload();
							}
							Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
						},
						failure : function(result, request) {
							secureFilterRs(result,'操作失败！');
						}
					});
				}
			});
		}
	}
});

function downloadFile(iid){
	window.location.href = 'shareFileDownload.do?iid='+iid+'&isHistory=1';
}
function showFile(iid,ifiletype,ifilename,ifileversion){
	ifiletype = ifiletype.toLowerCase();
	if( ifiletype == 'docx' || ifiletype == 'xls' || ifiletype == 'xlsx' || ifiletype == 'pdf' || ifiletype == 'doc' || ifiletype == 'ppt'|| ifiletype == 'pptx'){  
		window.open('showFile.do?iid='+iid+'&ifilename='+ifilename+'&ifiletype='+ifiletype+'&version='+ifileversion+'&ishistory=1',"_blank");
	}else{
		Ext.Msg.alert('提示', '预览失败，文件格式不支持预览');
	}
}
Ext.onReady(function() {

	// 定义方案名称
	Ext.define('planModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		},{
			name : 'iplanname',
			type : 'string'
		},{
			name : 'iversiontext',
			type : 'string'
		},{
			name : 'iplanstate',
			type : 'string'
		},{
			name : 'iupdatetime',
			type : 'long'
		},{
			name : 'iupdateusername',
			type : 'string'
		},{
			name : 'state',
			type : 'long'
		}]
	});
	var inameQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入模板名称--',
		width : 260,
		name : 'selName',
		xtype : 'textfield',
//		value:iselname,
		listeners: {  
              specialkey: function(field,e){    
                  if (e.getKey()==Ext.EventObject.ENTER){    
                    store.load();
                  }  
              }  
          }
	});
	var queryButton = Ext.create("Ext.Button", {
		text : '查询',
		cls : 'Common_Btn',
		handler : function() {
			queryBtnFun();
		}
	});
	var resetButton = Ext.create("Ext.Button", {
		text : '重置',
		cls : 'Common_Btn',
		handler : function() {
			resetBtnFun();
		}
	});
//	var addButton = Ext.create("Ext.Button", {
//		text : '增加',
//		cls : 'Common_Btn',
//		handler : function() {
//			addCfg();
//		}
//	});
//	var saveButton = Ext.create("Ext.Button", {
//		text : '保存',
//		cls : 'Common_Btn',
//		handler : function() {
//			saveCfg();
//		}
//	});
//	var delButton = Ext.create("Ext.Button", {
//		text : '删除',
//		cls : 'Common_Btn',
//		handler : function() {
//			delCfgs();
//		}
//	});
	var reButton = Ext.create("Ext.Button", {
	text : '返回',
	cls : 'Common_Btn',
	handler : function() {
		  contentPanel.setTitle('应急预案');
		  contentPanel.getLoader().load({
			  url: 'initEmPlanView.do',
			  params: {
				  pid:iplantype
			  },
			  scripts: true
		  });
	}
});
	
	var form = Ext.create('Ext.form.FormPanel', {
		baseCls:'customize_gray_back',
		region: 'north',
		border : false,
		dockedItems : [ {
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
//			items : [ inameQuery,queryButton,resetButton,'->', addButton,saveButton,delButton]
			items:['->',reButton]
		} ]
	});
	var columns = [{
		text : '序号',
		width:40,
		xtype : 'rownumberer'
	},{
		text : 'id',
		dataIndex : 'iid',
		hidden : true
	},{
		text : '预案名称',
		dataIndex : 'iplanname',
		flex:1,
		hidden : false
	},{
		text : '版本号',
		dataIndex : 'iversiontext',
		flex:1,
		hidden : false
	},{
		text : '更新时间',
		flex:1,
		dataIndex : 'iupdatetime',
		hidden : false
	},{
		text : '更新人',
		flex:1,
		dataIndex : 'iupdateusername',
		hidden : false
	},{
		text : '状态',
		flex:1,
		dataIndex : 'iplanstate',
		hidden : false,
		renderer:function(value){
			if(value=='0'){
				return "草稿";
			}else if(value=='1'){
				return '审核中';
			}else if(value=='2'){
				return '驳回'
			}else if(value=='3'){
				return '已发布'
			}
		}
	},{
		xtype:'actiontextcolumn',
        text : '操作',
        width:500,
        items: [{
        	iconCls: 'monitor_skip',  // Use a URL in the icon config
            tooltip: '导出Word',
            text : '导出Word',
            // getClass: function(value,metadata,record){
 	    	//    return "x-hidden";
 	    	// },
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
				var iplanname = record.get('iplanname');
				var iversiontext = record.get('iversiontext');
				window.location.href = 'exportPlanVersionById.do?iid=' + iid + '&iplanname=' + iplanname+ '&iversiontext=' + iversiontext;
			}
        },{
        	iconCls: 'role_permission',  // Use a URL in the icon config
            tooltip: '驳回原因',
            text : '驳回原因',
            getClass: function(value,metadata,record){ 
            	var statestr = record.get("iplanstate");
            	if(statestr!='2'){
            		return "x-hidden";
            	}
  	    	},
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
            	Ext.Ajax.request({
		              url: 'getEmPlanBackInfo.do',
		              method: 'post',
		              params: {
		            	  iplanid:iid
		              },
		              success: function (response) {
		                  var result = Ext.decode(response.responseText);
		                  if(result.success){
		                	  Ext.Msg.alert('驳回原因', result.message);
		                  }else{
		                	  Ext.Msg.alert('提示', "未获取到驳回原因");
		                  }
		                  
		              },
		              failure: function () {
		                  Ext.Msg.alert('提示', '提交保存请求失败');
		              }
		          });
            	
            }
        },{
        	iconCls: 'role_permission',  // Use a URL in the icon config
            tooltip: '编辑',
            text : '编辑',
            getClass: function(value,metadata,record){ 
            	var statestr = record.get("iplanstate");
            	if(statestr!='0'&&statestr!='2'){
            		return "x-hidden";
            	}
  	    	},
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
				var iplanname = record.get('iplanname');
				var iupdatetime = record.get('iupdatetime');
				var iupdateuser = record.get('iupdateusername');
				if(iid&&iid>0){
					contentPanel.setTitle("预案编辑");
					contentPanel.getLoader().load({
						url : 'initEmPlanDetail.do',
						params : {
							iplanid:iid,
							iplanname:iplanname,
							viewflag:'2',
							reurlflag:'1',
							iplantype:iplantype,
							versionFlag:'true',
						},
						scripts : true
					});
				}else{
					Ext.Msg.alert("提示","初始化数据失败！！！");
					
				}
//				showPlanView(iid,iplanname);
            }
        },{

        	iconCls: 'role_permission',  // Use a URL in the icon config
            tooltip: '查看',
            text : '查看',
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
				var iplanname = record.get('iplanname');
				var iupdatetime = record.get('iupdatetime');
				var iupdateuser = record.get('iupdateusername');
				if(iid&&iid>0){
					contentPanel.setTitle("预案查看");
					contentPanel.getLoader().load({
						url : 'initEmPlanDetail.do',
						params : {
							iplanid:iid,
							iplanname:iplanname,
							viewflag:'1',
							reurlflag:'1',
							messageFlag:'true',
							iplantype:iplantype
						},
						scripts : true
					});
				}else{
					Ext.Msg.alert("提示","初始化数据失败！！！");
					
				}
//				showPlanView(iid,iplanname);
            }
        
        },{

        	iconCls: 'role_permission',  // Use a URL in the icon config
            tooltip: '删除',
            text : '删除',
            getClass: function(value,metadata,record){ 
            	var statestr = record.get("iplanstate");
            	if(statestr!='0'&&statestr!='2'){
            		return "x-hidden";
            	}
  	    	},
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
				if(iid&&iid>0){
					Ext.Ajax.request({
			              url: 'deleteEmPlanInfo.do',
			              method: 'post',
			              params: {
			            	  iplanid:iid
			              },
			              success: function (response) {
			                  var result = Ext.decode(response.responseText);
			                  if(result.success){
			                	  Ext.Msg.alert('提示', '删除成功');
			                	  store.reload();
			                  }else{
			                	  Ext.Msg.alert('提示', result.message);
			                  }
			                  
			              },
			              failure: function () {
			                  Ext.Msg.alert('提示', '提交保存请求失败');
			              }
			          });
				}
            }
        },{

        	iconCls: 'role_permission',  // Use a URL in the icon config
            tooltip: '复制',
            text : '复制',
            getClass: function(value,metadata,record){ 
            	var statestr = record.get("iplanstate");
            	var state = record.get("state");
//            	console.log(state);
//            	console.log(statestr!='3'||state>0);
            	if(statestr!='3'||state>0){
            		return "x-hidden";
            	}
            },
            handler: function(grid, rowIndex, colIndex) {
            	var record = grid.getStore().getAt(rowIndex);
            	var iid = record.get('iid');
            	var iplanname = record.get('iplanname');
            	contentPanel.setTitle("预案复制");
				contentPanel.getLoader().load({
					url : 'initEmPlanDetail.do',
					params : {
						iplanid:iid,
						iplantype:iplantype,
						iplanname:iplanname,
						versionFlag:'true',
						reurlflag:'1',
					},
					scripts : true
				});
//				var iplanname = record.get('iplanname');
//				var iupdatetime = record.get('iupdatetime');
//				var iupdateuser = record.get('iupdateusername');
//				if(iid&&iid>0){
//					contentPanel.setTitle("预案查看");
//					contentPanel.getLoader().load({
//						url : 'initEmPlanDetail.do',
//						params : {
//							iplanid:iid,
//							iplanname:iplanname,
//							viewflag:'1',
//							reurlflag:'1',
//							messageFlag:'true'
//						},
//						scripts : true
//					});
//				}else{
//					Ext.Msg.alert("提示","初始化数据失败！！！");
//					
//				}
////				showPlanView(iid,iplanname);
            }
        
        }]
	}];
	var store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'planModel',
		pageSize : 30,
		proxy : {
			type : 'ajax',
			url : 'getEmPlanVersionList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProterty : 'total'
			}
		}
	});
	store.on('beforeload', function(store, options) {
		var new_params = {
				iplanname : iplanname,
				iplantype:iplantype
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});
	var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		cls:'customize_panel_back',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    padding:grid_space,
		region : 'center',
		store : store,
		autoScroll : true,
		border : false,
		columnLines : true,
		selModel:Ext.create('Ext.selection.CheckboxModel'),
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		columns : columns,
		viewConfig:{  
            enableTextSelection:true  
        },
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })]
	});
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "plan_version_area",
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		border : false,
		layout : 'border',
		items : [form,grid]
	});

	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});
	function resetBtnFun(){
		inameQuery.setValue('');
		store.load();
	};
	function queryBtnFun() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		grid.ipage.moveFirst();
	};
//	function addCfg(){
//		var p = {
//				iid:-1,
//				iplanname:'',
//				iupdatetime:'',
//				iupdateuser:''
//		}
//		store.insert(0,p);
//	};
	
//	function saveCfg(){
//		var records = store.getModifiedRecords();
//		
//		if(records.length==0){
//			Ext.Msg.show({
//			     title:'提示',
//			     msg: '信息没有变更，请选择需要保存的记录',
//			     buttons: Ext.Msg.OK,
//			     icon: Ext.Msg.INFO 
//			});
//
//		}else{	
//			for(var i=0;i<records.length;i++){
////				var n=0;
//				var iplanname = records[i].get("imodelname").trim();
//				
//				if ( !checkIsNotEmpty(iplanname)) {
//					Ext.Msg.alert('提示', '模板名称不能为空！');
//	                return;
//	            }
//				
//				if(fucCheckLength(iplanname)>255){
//					Ext.Msg.alert('提示', '方案名称字符长度不能超过255！');
//	                return;
//				}
////				for ( var k = 0; k < store.getCount(); k++) {
////					var record = store.getAt(k);
////					var userName = record.data.iplanname.trim();
////					if (iplanname == userName) {
////						n = n + 1;
////					}
////				}
////				if(n>1){
////					Ext.MessageBox.alert("提示", "方案名称重复！");
////					return;
////				}
//			}
//			var lstAddRecord = new Array();
//			Ext.each(records, function(record) {
//				lstAddRecord.push(record.data); 
//			});
//			Ext.Ajax.request({
//			    url: '*********.do',
//				params : {
//					jsonData : Ext.encode(lstAddRecord)
//				},				
//			    success: function(response, opts) {
//			        var message = Ext.decode(response.responseText).message;
//			        Ext.Msg.alert('提示',message);
//			        store.reload();
//			    },
//			    failure: function(result, opts) {
//			    	secureFilterRs(result,"请求返回失败！");
//			    }
//			  });
//			
//		}
//	};
//	function delCfgs() {
//		if (Ext.isIE) {
//			CollectGarbage();
//		}
//		var seleCount = grid.getSelectionModel().getSelection();
//		if (seleCount.length == 0) {
//			Ext.MessageBox.alert("提示", "请选择要删除的数据");
//			return;
//		}
//
//		Ext.MessageBox.buttonText.yes = "确定";
//		Ext.MessageBox.buttonText.no = "取消";
//		Ext.Msg.confirm("确认删除", "确定删除选中的组记录", function(id) {
//			if (id == 'yes')
//				delCfg();
//		});
//	};

	function delCfg() {		
		var records = grid.getSelectionModel().getSelection();
		if (records.length == 0) {
			Ext.MessageBox.alert("提示", "请选择要删除的数据");
			return;
		}
		var jsonArray=[];
		Ext.each(records,function(item){
			jsonArray.push(item.data.iid);
		});
		Ext.Ajax.request( {
			url : 'deleteEmPlanModel.do',
			method : 'post',
			params : {
				deleteIds : jsonArray.join(',')
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					grid.ipage.moveFirst();
					Ext.Msg.alert('提示', message);
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,"请求返回失败！",request);
			}
		});
	};
	
});

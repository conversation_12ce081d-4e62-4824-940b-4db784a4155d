<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.common.utils.SessionData"%>

<html>
<head>
<script type="text/javascript">
var ieventNum = '<%=request.getAttribute("ieventNum")%>'; 
var ieventName = decodeURIComponent('<%=request.getAttribute("ieventName")%>'); 
var ieventType = '<%=request.getAttribute("ieventType")%>'; 
var ieventLevelName= decodeURIComponent('<%=request.getAttribute("ieventLevelName")%>'); 
var ieventTag= '<%=request.getAttribute("ieventTag")%>';
var istateName= '<%=request.getAttribute("istateName")%>'; 
var reportTime= '<%=request.getAttribute("reportTime")%>'; 
var updateTime= '<%=request.getAttribute("updateTime")%>'; 
var updateUserName= '<%=request.getAttribute("updateUserName")%>';
var ieventDesc= '<%=request.getAttribute("ieventDesc")%>'; 
var ieventid = '<%=request.getAttribute("iid")%>'; 
var currentUserId = <%=SessionData.getSessionData(request).getUserInnerCode()%>;

var handlemeasure = '<%=request.getParameter("handlemeasure")%>'; 
var planIds = '<%=request.getParameter("planIds")%>'; 
var iscenceIds = '<%=request.getParameter("iscenceIds")%>'; 
var state = '<%=request.getParameter("state")%>';
var workitemId = '<%=request.getAttribute("iworkitemid")%>';
</script>

<script type="text/javascript" src="<%=request.getContextPath()%>/page/neweswitch/emergencyeventmanagement/event/moreScene.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/neweswitch/emergencyeventmanagement/event/performEvent.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/neweswitch/emergencyeventmanagement/event/eventManage.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/neweswitch/emergencyplan/selectEmPlan.js"></script>
</head>
<body>
<div id="eventManage_div" style="width: 100%;height: 100%"></div>
</body>
</html>
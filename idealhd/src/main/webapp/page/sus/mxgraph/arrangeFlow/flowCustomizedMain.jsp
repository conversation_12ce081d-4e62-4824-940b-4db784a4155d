<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.controller.sus.onoffswitch.OnOffSwitchController"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.common.utils.SessionData"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
var activeTabNum= null==<%=request.getParameter("activeTabNum")%>?0:<%=request.getParameter("activeTabNum")%>;
var iid = (null==<%=request.getParameter("iid")%>)?0:<%=request.getParameter("iid")%>;
var instanceName='<%=request.getParameter("instanceName")==null?"":request.getParameter("instanceName")%>';
var showOnly=<%=request.getParameter("showOnly")%>;
var ibusnesSysIid=<%=request.getParameter("ibusnesSysIid")%>;
var issusflow=<%=request.getParameter("issusflow")%>;
var isbackinstance=<%=request.getParameter("isbackinstance")%>;
var iversiontype=<%=request.getParameter("iversiontype")%>;
var chgCchrReleaseId='<%=request.getParameter("chgCchrReleaseId")%>';
var iversiondes='<%=request.getParameter("iversiondes")%>';
var doubleCheckState=<%=request.getParameter("doubleCheckState")%>;
var evnIds='<%=request.getParameter("evnIds")%>';
var patchPath='<%=request.getParameter("patchPath")%>';
var iversiondes='<%=request.getParameter("iversiondes")%>';
var iworkItemid=<%=request.getParameter("iworkItemid")%>;
var ibackInfoString='<%=request.getParameter("ibackInfo")%>';
var iexecUserString='<%=request.getParameter("iexecUser")%>';
var doublecheckOnOffFlag_SUS=<%=OnOffSwitchController.getConfigSwitch("dbcheckFlag")%>;
var _idelayed_starttime='<%=request.getParameter("idelayed_starttime")%>';;
var isStartBtnEnabled=<%=request.getAttribute("isStartBtnEnabled")%>;
var istartUserFullName='<%=request.getParameter("istartUserFullName")%>';
var startUserLoginName = '<%=request.getParameter("startUserLoginName")%>';
if(null ==isStartBtnEnabled){
	isStartBtnEnabled=false;
}
//流程定制主页js变量的声明
var startInstid=iid;
var envStore=null;
var envGrid=null;
var ipStore=null;
var preparedViewStore=null;
var preparedStepStore=null;
var paraViewModel=null;
var startTimeSwitch=<%=ServerEnv.getInstance().getBooleanConfig(Environment.STARTTIME_SUS_SWITCH, false)%>;
var sendSusReviewSmsSwitch=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SEND_SUS_REVIEW_SMS_SWITCH, false)%>;
var susFlowcustomNoserverSwitch=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_FLOWCUSTOM_NOSERVER_SWITCH, false)%>;
var susReviewPasswordSwitch=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_REVIEW_PASSWORD_SWITCH, false)%>;
var userNameCurrent='<%=SessionData.getSessionData(request).getUserName() %>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/mxgraph/mxgraphTools.js"></script>

<script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/mxgraph/arrangeFlow/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
	.uploadify-button {
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px;
	-webkit-box-shadow:inset 0 0 0 #fff;  
	-moz-box-shadow:inset 0 0 0 #fff;  
	box-shadow:inset 0 0 0 #fff;   
	text-align:center;
	font-size:12px;
	height:34px;
	line-height:34px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:12px 0 0 5px;
}
</style>
</head>
<body>
<div id="flowCustomizedMainDiv" style="width: 100%;height: 100%"></div>
</body>
</html>
Ext.onReady(function () {
    destroyRubbish();
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    /***********************项目概况tab ***********************************************/
    var ifileName = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        readOnly: true,
        name: 'ifilename',
        value: ifilename,
        fieldLabel: '文件名称'
    });

    var proState = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proState',
        readOnly: true,
        value: ieai_group_env,
        fieldLabel: '环境'
    });

    var proIp = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proIp',
        value: iip,
        readOnly: true,
        fieldLabel: 'IP地址',

    });

    var proType = Ext.create('Ext.form.field.Text',  {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        editable: false,
        readOnly:true,
        name: 'proType',
        value: imodeltype,
        fieldLabel: '模块类型'
    });

    var departMent = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'departMent',
        value: ipath,
        readOnly: true,
        fieldLabel: '分发路径'
    });


    var ifileName1 = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        //readOnly: true,
        name: 'ifilename1',
        afterLabelTextTpl: required,
        value: ifilename,
        fieldLabel: '文件名称'
    });

    /** 环境model* */
    Ext.define('proStateModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'envname',
                    type: 'string'
                }
            ]
        });

    var proStateStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'proStateModel',
        proxy:
            {
                type: 'ajax',
               // url: 'queryEnvName.do?icicdsys_code=' + sysName,
                url: 'queryEnvBySysAndUser.do?sysName=' + sysName,
                reader:
                    {
                        type: 'json',
                        root: 'dataList'
                    }
            }
    });


    var proState1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        store: proStateStore,
        afterLabelTextTpl: required,
        margin: '0 0 25 0',
        name: 'proState1',
        displayField: "envname",
        valueField: "envname",
       // multiSelect: true,//多选
        value: ieai_group_env,
        editable: false,
        fieldLabel: '环境',
        listeners: {
            select: function (combo, records) {
                proType1.setValue(null);
                proTypeStore.load();

            }
        }
    });

    var proIp1 = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proIp1',
        id:'ipConfig_id',
        value: iip,
        fieldLabel: 'IP地址',
        afterLabelTextTpl: required,
        listeners: {
            render: function (p) {
                p.getEl().on('click', function (p) {
                    //处理点击事件代码
                    //重新
                    let envName =proState1.getValue();
                    let modelType= proType1.getValue(); //模块类型
                    if(sysName && envName ){
                        var ips = proIp1.getValue();
                        var ids = ipIdTextField.getValue();
                        var from ="feishengchanfuzhi";
                        proIpwindow(ips,ids,0,from,sysName,envName, modelType);
                    }else{
                        Ext.Msg.alert('提示', "请确认是否选择了环境!");
                    }

                });
            },
        }
    });

    //用来ip的选择以及回显
    var ipIdTextField = Ext.create('Ext.form.field.Text', {
        width: '33%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        afterLabelTextTpl: required,
        name: 'proIp',
        hidden:true,
        id:'ipIdTextField_id',
        value: '',
        readOnly: true
    });

    /** 模块类型model* */
    Ext.define('proTypeModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                },
                {
                    name: 'idepName',
                    type: 'string'
                }
            ]
        });

    var proTypeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'proTypeModel',
        proxy:
            {
                type: 'ajax',
                url: 'getModelType.do?systemcode=' + sysName,
               // url: 'queryIpBindListPageNew.do',
                reader:
                    {
                        type: 'json',
                        root: 'dataList'
                    }
            }
    });

    // proTypeStore.on('beforeload', function (store, options) {
    //     //proState.getValue();
    //     var new_params =
    //     {
    //         syscode: sysName,
    //         envName:proState1.getValue()
    //     };
    //
    //     Ext.apply(proTypeStore.proxy.extraParams, new_params);
    // });


    var proType1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proType1',
        displayField: "idepName",
        valueField: "iid",
        afterLabelTextTpl: required,
        value: imodeltype,
        //multiSelect: true,//多选
        store: proTypeStore,
        fieldLabel: '模块类型',
        listeners: {
            select: function (combo, records) {
                //清空ip的值
                proIp1.setValue('');

            }
        }
    });

    var departMent1 = Ext.create('Ext.form.field.Text', {
        width: '90%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'departMent1',
        afterLabelTextTpl: required,
        value: ipath,
        fieldLabel: '分发路径'
    });


    var leftComboBox = Ext.create('Ext.form.field.ComboBox', {
        name: 'stcTypeId',
        store: Ext.create('Ext.data.Store', {
            fields: ['name', 'value'],
            data: [{
                name: "跳过",
                value: "1"
            }, {
                name: "覆盖",
                value: "2"
            }]
        }),
        fieldLabel: '相同配置',
        labelWidth: 85,
        labelAlign: 'right',
        afterLabelTextTpl: required,
        width: '90%',
        displayField: 'name',
        valueField: 'value',
        value: '2',
        listConfig: {
            maxHeight: 200
        },
        editable: true
    });


    var projectForm = Ext.create('Ext.panel.Panel', {
        title: '源配置信息',
        name: 'projectForm',
        region: 'west',
        layout: 'anchor',
        buttonAlign: 'center',
        width: '50%',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [ifileName]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proState]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proType]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proIp]
        },{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [departMent]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: ['<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardFun(' +
            + iid + ')">点击查看源文件详情 </a>']
        }
        ]
    });

    var projectForm1 = Ext.create('Ext.panel.Panel', {
        title: '新配置信息',
        name: 'projectForm1',
        region: 'east',
        layout: 'anchor',
        buttonAlign: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        width: '50%',
        bodyBorder: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [ifileName1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proState1]
        },{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proType1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proIp1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [departMent1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [leftComboBox]
        }]
    });

    var proTab = Ext.create('Ext.form.FormPanel', {
        title: '',
        layout: 'border',
        border: false,
        buttonAlign: 'center',
        items: [projectForm, projectForm1],
        buttons: [{
            text: '开始复制',
            handler: function () {
                copyThis();
            }
        },{
            text: '返回',
            handler: function () {
                closeThis();
            }
        }]
    });

    function closeThis() {
        tcCopyWindows.close();
        contentPanel.getLoader().load(
            {
                url: 'accessCI_CONFIGFILE.do',
                params:
                    {sysName: sysName},
                scripts: true
            });
    }

    function copyThis() {
        var ifilename = ifileName1.getValue();//文件
        var ieai_group_env = proState1.getValue();//环境
        var iip = proIp1.getValue();//IP地址
        var imodeltype = proType1.getRawValue();//目标模块类型
        if (imodeltype == '' || imodeltype == null) {
            Ext.Msg.alert('提示', "请选择模块类型...");
            return;
        }
        var array = imodeltype.split(',');
        var store = proType1.getStore();
        var imodeltypeIdsArray = [];
        for (var i = 0; i < array.length; i++) {
            var element = array[i].trim();
            var record = store.findRecord(proType1.displayField, element);
            var value = record.get(proType1.valueField);
            imodeltypeIdsArray.push(value);
        }
        var imodeltypeIds = imodeltypeIdsArray.join(",");

        var ipath = departMent1.getValue();//分发路径
        var itype = leftComboBox.getValue();//选择类型



        if (ifilename == '' || ifilename == null) {
            Ext.Msg.alert('提示', "请输入文件名");
            return;
        }
        if (ieai_group_env == '' || ieai_group_env == null) {
            Ext.Msg.alert('提示', "请选择环境...");
            return;
        }


        if (iip == '' || iip == null) {
            Ext.Msg.alert('提示', "请选择IP地址...");
            return;
        }

        if (ipath == '' || ipath == null) {
            Ext.Msg.alert('提示', "请输入分发路径...");
            return;
        }

        if (itype == '' || itype == null) {
            Ext.Msg.alert('提示', "请选择相同配置类型...");
            return;
        }

        var reg = /\s/;
        if (reg.test(ipath)) {
            Ext.Msg.alert('提示', "请输入正确的分发路径...");
            return;
        }

        Ext.Msg.confirm("提示", "请确认是否复制当前数据?", function (button, text) {
            if (button == "yes") {
                Ext.Ajax.request({
                    url: 'copyCI_CONFIGFILE.do',
                    method: 'POST',
                    params: {
                        iid: iid,
                        ifilename:ifilename,
                        ieai_group_env:ieai_group_env,
                        iip:iip,
                        imodeltype:imodeltype,
                        ipath:ipath,
                        itype:itype,
                        isysname:sysName,
                        imodeltypeId:imodeltypeIds,
                        agentids:ipIdTextField.getValue()
                    },
                    success: function (response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                            Ext.Msg.alert('提示', message);
                            tcCopyWindows.close();
                            CI_CONFIGFILEStore.reload();
                            // 清理翻页缓存中的数据，避免在已经删除了记录后，还存在缓存数据，导致其他记录无法进行后续操作。
                            CI_CONFIGFILEGrid.getSelectionModel().deselectAll();
                        } else {
                            Ext.Msg.alert('提示', message);
                            tcCopyWindows.close();
                            CI_CONFIGFILEStore.reload();
                            // 清理翻页缓存中的数据，避免在已经删除了记录后，还存在缓存数据，导致其他记录无法进行后续操作。
                            CI_CONFIGFILEGrid.getSelectionModel().deselectAll();
                        }
                    },
                    failure: function (result, request) {
                        secureFilterRs(result, '操作失败！');
                    }
                });

            } else {
                return;
            }
        });
    }


    /***********************其他 ***********************************************/
    var tabArray = new Array();
    tabArray.push(proTab);


    var mainTabs = Ext.widget('tabpanel', {
        cls: 'customize_panel_back',
        region: 'center',
        tabPosition: 'top',
        activeTab: 0,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        buttonAlign: 'center',
        items: tabArray
    });


    let renderTo =  "CI_CONFIG_COPY";
    if (isTabSwitch){
        renderTo += configcopynow;
    }
    var MainPanel = Ext.create('Ext.panel.Panel',
        {
            renderTo: renderTo,
            width: '100%',
            layout: 'border',
            cls: 'customize_panel_header_arrow',
            height: '100%',
            border: false,
            items: [mainTabs]
        });

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function (obj, options, eOpts) {
            Ext.destroy(MainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });

});

//详情
function forwardFun(iid){

    // contentPanel.setTitle ('非生产');
    // contentPanel.getHeader().show();//让contentPanel显示标题头

    detailWindows = Ext.create('Ext.window.Window', {
        title: '配置详情',
        modal: true,
        closeAction: 'destroy',
        closable:false, // 设置为false，禁用关闭按钮
        constrain: true,
        autoScroll: true,
        width: contentPanel.getWidth() * 0.7,
        height: contentPanel.getHeight() * 0.9,
        draggable: false,// 禁止拖动
        resizable: false,// 禁止缩放
        layout: 'fit',
        loader: {
            url: 'accessCI_CONFIGFILE1.do',
            params: {
                sysName:sysName,optType:4,iid:iid,iname:iname,ifrom:"copy"
            },
            autoLoad: true,
            scripts: true
        }
    });
    detailWindows.show();
    // tcCopyWindows.close();
    // contentPanel.getLoader ().load (
    //     {
    //         url : 'accessCI_CONFIGFILE1.do',
    //         params :
    //             {sysName:sysName,optType:3,iid:iid},
    //         scripts : true
    //     });
    // if (Ext.isIE)
    // {
    //     createConfiguration();
    // }
}
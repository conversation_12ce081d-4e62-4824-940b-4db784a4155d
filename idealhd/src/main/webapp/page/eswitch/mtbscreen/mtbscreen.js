var mtbtx1;
var mymtbChart1;
var mtbtx2;
var mymtbChart2;
var mtbtx3;
var finishsysid="-100";
var wholeiruninsid = "-100";
var isflag =0;
var outtmp = 1;
var loadFunction;
var count=0;
var ss=0;
var flag;
var connerarr = [];
$(function(){
	fillScreen();
	mtbtx1 = document.getElementById("chart1").getContext('2d');
	mymtbChart1 = new Chart(mtbtx1, {
		    type: 'doughnut',
		    data: {
				datasets: [{
					data: [1,1,1,1],
					backgroundColor:[
						'#32fa13', // 绿
						'#03e5ec', // 蓝
						'#dbdbdb', // 灰
						'#ffa837'  // 黄
					],
					borderWidth : 0
				}]
			},
			options: {
				cutoutPercentage :70,
				tooltips: {
					enabled:false
		        },
		        position : 'absolute'
			}
	});
	
	mtbtx2 = document.getElementById("chart2").getContext('2d');
	mymtbChart2 = new Chart(mtbtx2, {
		    type: 'doughnut',
		    data: {
				datasets: [{
					data: [1,1],
					backgroundColor:[
						'#32fa13', // 绿
						'#03e5ec' // 蓝
					],
					borderWidth : 0
				}]
			},
			options: {
				cutoutPercentage :70,
				tooltips: {
					enabled:false
		        },
		        position : 'absolute'
			}
	});
	
	mtbtx3 = echarts.init(document.getElementById('chart3'));
	mtbtx3.setOption({
		series : [ {
			type : 'pie',
			radius : [ '80%', '60%' ],
			center : [ '43%', '60%' ],
			color : [ '#32fa13', '#03e5ec', '#dbdbdb','#ffa837'],
			minAngle: 10,  // 设置每块扇形的最小占比
			hoverAnimation:false,
			itemStyle : {
                normal : {
                    label : {
                        show : true,
                        textStyle:{
                        	fontSize:18,
                        	fontWeight:800
                        }
                    },
                    labelLine : {
                        show : true
                    }
                }
            },
			labelLine : {
				normal : {
					length : 15,
					length2 : 110
				}
			},
			label : {
				normal : {
					formatter : function(param) {
						return param.value + '\n\n'+ param.name;
					},
					borderWidth : 22,
					borderRadius : 4,
					padding : [ 0, -65 ]
				}
			},
			data : [{value: 0, name: '已完成'},
                {value: 0, name: '运行中'},
                {value: 0, name: '未开始'},
                {value: 0, name: '超时'}]
		} ]
	});
	
	showDT();
	loadData();
	loadFunction = window.setInterval(function(){
		loadData();
	}, 1000); 
	
	window.onresize = function(){
		fillScreen();
	}
});

function loadData(){
	$.ajax({
		type:"post",
		url: path+"/getMtbInfoList.do",
		async:true,
		dataType:"json",
		success: function(jsonData){
			var systemMessage = '';
			
			var proportion_stepSum=0;
			var proportion_finishedstepSum=0;
			//系统总数
			var systemSum = 0;
			var finishedNum = 0;
			var runningNum = 0;
			var noBeginNum = 0;
			var faultedNum = 0; 
			
			//设备总数
//			var stepSum = 0;
//			var produceStepNum = 0;
//			var cityWideStepNum = 0;
//			var remoteStepNum = 0;
			//异地中心
			var remoteCenterName = '';
			var remoteCenterident = '';
			var remoteCenterSystems = [];
			var systemNumOne = 0;
			var finishedOne = 0;
			var runningOne = 0;
			var noBeginOne = 0;
			var faultedOne = 0;
			//生产中心
			var produceCenterName = '';
			var produceCenterident = '';
			var produceCenterSystems = [];
			var systemNumTwo = 0;
			var finishedTwo = 0;
			var runningTwo = 0;
			var noBeginTwo = 0;
			var faultedTwo = 0;
			//同城中心
			var cityWideCenterName = '';
			var cityWideCenterident = '';
			var cityWideCenterSystems = [];
			var systemNumThree = 0;
			var finishedThree = 0;
			var runningThree = 0;
			var noBeginThree = 0;
			var faultedThree = 0;
			var produceCenteComputerCount;
			var cityWideCenterComputerCount;
			var remoteCenterComputerCount;
			//正在运行的系统数
			var runningcount = 0;
			var produceCenterCSS = 1;
			var cityWideCenterCSS = 1;
			var remoteCenterCSS = 1;
			
			var hour2;
			var minute2;
			var second2;
			
			var trHTML="";
			var trdiv = "";
			var haos = "";
			if(jsonData){
				$(".success_bg").css("display", "none");
				var center = jsonData.center;
				if(center.length>0){
					for(var i=0;i<center.length;i++){
						if(i==0){
							produceCenterName = center[i].centername;
							produceCenterident = center[i].centerident;
							produceCenterSystems = center[i].systemCenter;
							produceCenteComputerCount = center[i].computerCount;
						}else if(i==1){
							cityWideCenterName = center[i].centername;
							cityWideCenterident = center[i].centerident;
							cityWideCenterSystems = center[i].systemCenter;
							cityWideCenterComputerCount = center[i].computerCount;
						}else if(i==2){
							remoteCenterName = center[i].centername;
							remoteCenterident = center[i].centerident;
							remoteCenterSystems = center[i].systemCenter;
							remoteCenterComputerCount = center[i].computerCount;
						}
					}
				}else{
					return;
				}
				var isfinish = true;
				var system = jsonData.system;
				if(system.length>0){
					for (var i=0;i<system.length;i++){
						var rs=system[i];
						var tabletr = '';
						var tableSysstate='';
						var sysstate=rs.sysstate;
						
						var hour1 = Math.floor(rs.thetime/1000/60/60);
						var minute1 = Math.floor(rs.thetime/1000/60%60);
						var second1 = Math.floor(rs.thetime/1000%60);
						
						//左侧切换耗时
						var syshourlong = hour1 + "时" + minute1 + "分" + second1 + "秒";
						if(hour1==0 && minute1==0){
							syshourlong = second1 + "秒";
						}else if(hour1==0 && minute1!=0){
							syshourlong = minute1 + "分" + second1 + "秒";
						}else{
							syshourlong = hour1 + "时" + minute1 + "分" + second1 + "秒";
						}
						if (sysstate=="运行中"){
							runningcount++;
							isfinish= false;
							wholeiruninsid = rs.iid;
							flag =0;
							tabletr='<tr class="list_blue" onclick=\'getInstanceData('+rs.iid+','+0+')\' id=\'instance_'+rs.iid+'\'>';
							tableSysstate='<span>运行中</span>';
							runningNum++;
							if(produceCenterSystems.length>0){
								for(var n=0;n<produceCenterSystems.length;n++){
									if(rs.isysname==produceCenterSystems[n].sysname){
										runningTwo++;
										systemNumTwo++;
									}
								}
							}
							
							if(cityWideCenterSystems.length>0){
								for(var n=0;n<cityWideCenterSystems.length;n++){
									if(rs.isysname==cityWideCenterSystems[n].sysname){
										runningThree++;
										systemNumThree++;
									}
								}
							}
							if(remoteCenterSystems.length>0){
								for(var n=0;n<remoteCenterSystems.length;n++){
									if(rs.isysname==remoteCenterSystems[n].sysname){
										runningOne++;
										systemNumOne++;
									}
								}
							}
							
							if(rs.iswitchto){
								if(produceCenterident+"->"+cityWideCenterident==rs.iswitchto){
									cityWideCenterCSS = 0;
								}
								if(cityWideCenterident+"->"+produceCenterident==rs.iswitchto){
									produceCenterCSS = 0;
								}
							}
						}
						
						if (sysstate=="已完成"){
							wholeiruninsid = rs.iid;
							finishsysid=rs.iid;
							flag=1;
							tabletr='<tr class="list_green" id=\'instance_'+rs.iid+'\'>';
							tableSysstate='<span>已完成</span>';
							finishedNum++;
							if(produceCenterSystems.length>0){
								for(var n=0;n<produceCenterSystems.length;n++){
									if(rs.isysname==produceCenterSystems[n].sysname){
										finishedTwo++;
										systemNumTwo++;
									}
								}
							}
							if(cityWideCenterSystems.length>0){
								for(var n=0;n<cityWideCenterSystems.length;n++){
									if(rs.isysname==cityWideCenterSystems[n].sysname){
										finishedThree++;
										systemNumThree++;
									}
								}
							}
							if(remoteCenterSystems.length>0){
								for(var n=0;n<remoteCenterSystems.length;n++){
									if(rs.isysname==remoteCenterSystems[n].sysname){
										finishedOne++;
										systemNumOne++;
									}
								}
							}
						}
						if (sysstate=="未开始"){
							isfinish= false;
							flag=0;
							tabletr='<tr class="list_gray"  id=\'instance_'+rs.iid+'\'>';
							tableSysstate='<span>未开始</span>';
							noBeginNum++;
							if(produceCenterSystems.length>0){
								for(var n=0;n<produceCenterSystems.length;n++){
									if(rs.isysname==produceCenterSystems[n].sysname){
										noBeginTwo++;
										systemNumTwo++;
									}
								}
							}
							if(cityWideCenterSystems.length>0){
								for(var n=0;n<cityWideCenterSystems.length;n++){
									if(rs.isysname==cityWideCenterSystems[n].sysname){
										noBeginThree++;
										systemNumThree++;
									}
								}
							}
							if(remoteCenterSystems.length>0){
								for(var n=0;n<remoteCenterSystems.length;n++){
									if(rs.isysname==remoteCenterSystems[n].sysname){
										noBeginOne++;
										systemNumOne++;
									}
								}
							}
							if(rs.iswitchto){
								if(produceCenterident+"->"+cityWideCenterident==rs.iswitchto){
									cityWideCenterCSS = 0;
									
								}
								if(cityWideCenterident+"->"+produceCenterident==rs.iswitchto){
									produceCenterCSS = 0;
								}
							}
						}
						if (sysstate=="异常"){
							runningcount++;
							wholeiruninsid = rs.iid;
							isfinish= false;
							flag=0;
							tabletr='<tr class="list_red"  id=\'instance_'+rs.iid+'\'>';
							tableSysstate='<span>超时</span>';
							faultedNum++;
							if(produceCenterSystems.length>0){
								for(var n=0;n<produceCenterSystems.length;n++){
									if(rs.isysname==produceCenterSystems[n].sysname){
										faultedTwo++;
										systemNumTwo++;
									}
								}
							}
							if(cityWideCenterSystems.length>0){
								for(var n=0;n<cityWideCenterSystems.length;n++){
									if(rs.isysname==cityWideCenterSystems[n].sysname){
										faultedThree++;
										systemNumThree++;
									}
								}
							}
							if(remoteCenterSystems.length>0){
								for(var n=0;n<remoteCenterSystems.length;n++){
									if(rs.isysname==remoteCenterSystems[n].sysname){
										faultedOne++;
										systemNumOne++;
									}
								}
							}
							if(rs.iswitchto){
								if(produceCenterident+"->"+cityWideCenterident==rs.iswitchto){
									cityWideCenterCSS = 0;
								}
								if(cityWideCenterident+"->"+produceCenterident==rs.iswitchto){
									produceCenterCSS = 0;
								}
							}
						}
						trHTML=trHTML+tabletr + 
						'<td><div class="sc_list_dir01">'+rs.isysname+'</div></td>'+
						'<td><div class="sc_list_dir02">'+rs.iswitchto+'</div></td>'+
						'<td><div class="sc_list_dir03">'+syshourlong+'</div></td>'+
						'<td><div class="sc_list_dir04">'+tableSysstate+'</div></td></tr>';
						//计算百分比
						proportion_stepSum=parseInt(proportion_stepSum)+parseInt(rs.sysstepnum);
						proportion_finishedstepSum=parseInt(proportion_finishedstepSum)+parseInt(rs.sysfinishednum);
						systemSum++;
					}
				}else{
					return;
				}
				loadTime(system);
				
				$("#systemTable").html(trHTML);
				
				if(isfinish && outtmp == 1){
					wholeiruninsid =system[system.length-1].iid;
					systemMessage = "系统名称:"+system[system.length-1].isysname+"&nbsp&nbsp切换方向:"+system[system.length-1].iswitchto+"&nbsp&nbsp耗时:"+syshourlong;
					isflag=1;
					/*if(loadFunction){
						window.clearInterval(loadFunction);
					}*/
					$(".success_bg").css("display", "block");
				}

				if(count==0){
					ss=0;
				}
				if(count%5==0 && count!=0){
					if(ss==runningcount-1 || ss>=runningcount || runningcount==1){
						ss=0;
					}else{
						ss++;
					}
				}
				wholeiruninsid = system[ss].iid;
				isflag = 0;
				$("#systemTable tr").eq(ss).addClass("highlight");
				$("#systemTable tr").eq(ss).click(function(){
					getInstanceData(wholeiruninsid,isflag);
				});
				systemMessage = "系统名称:"+system[ss].isysname+"&nbsp&nbsp切换方向:"+system[ss].iswitchto+"&nbsp&nbsp耗时:"+getTime(system[ss].thetime);
				$("#systemMessage").html(systemMessage);
				if(ss>4){
					if((ss-4)==0){
						toUp(32);
					}else{
						toUp((ss-4)*32);
					}
				}else{
					toUp(0);
				}
			
				count++;
				getInstanceData(wholeiruninsid,isflag);
				
				//系统总数
				$('#systemSum').html(systemSum);
				
				//中心
				$('#centerNameTwo').html(produceCenterName);
				$('#centerNameThree').html(cityWideCenterName);
				
				$('#centerNameTwo1').html(produceCenterName);
				$('#centerNameThree1').html(cityWideCenterName);
				
				$('#centerNameTwo2').html(produceCenterName);
				$('#centerNameThree2').html(cityWideCenterName);
				
				$('#systemNumTwo').html(systemNumTwo);
				$('#systemNumThree').html(systemNumThree);
				
				$('#finishedNum').html(finishedNum);
				$('#runningNum').html(runningNum);
				$('#noBeginNum').html(noBeginNum);
				$('#faultedNum').html(faultedNum);
				//设备总数
				$('#stepNumTwo').html(produceCenteComputerCount);
				$('#stepNumThree').html(cityWideCenterComputerCount);
				$('#produceStepNum').html(produceCenteComputerCount);
				$('#cityWideStepNum').html(cityWideCenterComputerCount);
				$('#stepSum').html(produceCenteComputerCount+cityWideCenterComputerCount+remoteCenterComputerCount);
				
				$('#finishedTwo').html(finishedTwo);
				$('#runningTwo').html(runningTwo);
				$('#noBeginTwo').html(noBeginTwo);
				$('#faultedTwo').html(faultedTwo);
				
				$('#finishedThree').html(finishedThree);
				$('#runningThree').html(runningThree);
				$('#noBeginThree').html(noBeginThree);
				$('#faultedThree').html(faultedThree);
				
				$('#finishedOne').html(finishedOne);
				$('#runningOne').html(runningOne);
				$('#noBeginOne').html(noBeginOne);
				$('#faultedOne').html(faultedOne);
				
				var scdh=Math.floor((proportion_finishedstepSum/proportion_stepSum)*383);
				$("#proportionCSS").animate({width:scdh+'px',top:0,left:0},{step : function(){
					var scdhString = this.style.width;
					var scdhInt = scdhString.replace('px','');
					$('#proportion').html((Math.floor(scdhInt*100/383))+"%");
				}});
				
				if(produceCenterCSS==0){
					$('#cityWideCenter').removeClass('shine_box');
					$('#mtbstreamer').removeClass('streamer_shadow');
					$('#mtbstreamer').removeClass('streamer_shadow1');
					$('#produceCenter').addClass('shine_box');
					$('#mtbstreamer').addClass('streamer_shadow1');
				}
				
				if(cityWideCenterCSS==0){
					$('#produceCenter').removeClass('shine_box');
					$('#mtbstreamer').removeClass('streamer_shadow1');
					$('#mtbstreamer').removeClass('streamer_shadow');
					$('#cityWideCenter').addClass('shine_box');
					$('#mtbstreamer').addClass('streamer_shadow');
				}
										
				mymtbChart1.data.datasets[0].data[0] = finishedNum;
				mymtbChart1.data.datasets[0].data[1] = runningNum;
				mymtbChart1.data.datasets[0].data[2] = noBeginNum;
				mymtbChart1.data.datasets[0].data[3] = faultedNum;
				mymtbChart1.update();
				
				mymtbChart2.data.datasets[0].data[0] = produceCenteComputerCount;
				mymtbChart2.data.datasets[0].data[1] = cityWideCenterComputerCount;
				mymtbChart2.data.datasets[0].data[2] = remoteCenterComputerCount;
				mymtbChart2.update();
				
			}
		}
	});
}

function getInstanceData(wholeiruninsid,isflag){
	//控制台输出
	$.ajax({
		type:"post",
		url: path+"/getSysShelloutput.do",
		data : {
			iruninsid: wholeiruninsid,
			iflag:isflag
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			if(jsonData){
				//文字统计信息加载
				var message = jsonData.message;
				if(message!=null&&message!=''){
					finishsysid = wholeiruninsid;
					$("#stepOutput").html(message);
					var textarea = document.getElementById("stepOutput");
					textarea.scrollTop = textarea.scrollHeight;
				}
			}
		}
	});

	$.ajax({
		type:"post",
		url: path+"/getSwitchMonitorConnerMtbScreen.do",
		data : {
			iruninsid: wholeiruninsid
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			if(jsonData){
				//并发步骤图加载
		    	var connerData = jsonData.connerStep;
		    	loadConnerData(connerData,wholeiruninsid);
			}
		}
	});
	//步骤状态
	$.ajax({
		type:"post",
		url: path+"/getSysProgressDataMtbScreen.do",
		data : {
			iruninsid:wholeiruninsid
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			if(jsonData){
				var finished = 0;
				var running = 0;
				var ready = 0;
				var failed = 0;
				for(var i=0;i<jsonData.length;i++){
					if(jsonData[i].name=='已完成'){
						finished = jsonData[i].data;
					}
					if(jsonData[i].name=='未开始'){
						ready =	jsonData[i].data;
					}
					if(jsonData[i].name=='运行中'){
						running = jsonData[i].data;
					}
					if(jsonData[i].name=='异常'){
						failed = jsonData[i].data;
					}
				}
				$('#finishStepNum').html(finished);
				$('#runStepNum').html(running);
				$('#noBeginStepNum').html(ready);
				$('#faultedStepNum').html(failed);
				$('#switchStepSum').html(finished+running+ready+failed);
				
				var option = mtbtx3.getOption();
				 option.series[0].data = [
				                          {value:finished, name:'已完成'},
				                          {value:running, name:'运行'},
				                          {value:ready, name:'未开始'},
				                          {value:failed, name:'超时'}
				                          ];
				 mtbtx3.setOption(option);   
			}
		}
	});
}

function loadConnerData(connerDataList,iruninsid){
	var tempiid = '-100';
	var connerDataHTML = '';
	var lastDiv = "";
	var lastrunningconner = "-100";
	var lastiruninsid = "-100";
	var position = 0;
	for(var i = 0;i<connerDataList.length;i++){
	  	var conner = connerDataList[i].conner;
	  	var state  = connerDataList[i].state;
		var connername = connerDataList[i].connername;
		 if(i==connerDataList.length-1){
			 lastDiv='style="margin:0"';
		 }
		if(state=="0"){//运行中
			connerDataHTML+= '<td valign="top" '+lastDiv+'>'+
							'<div class="step_part" '+lastDiv+'>'+
							'<div class="step_runing step_status">'+
							'<div class="step_img03 step_img_common">'+
							'<h1 style="height:100px;overflow:hidden">'+connername+'</h1>'+
							'</div></div></div></td>';
			lastrunningconner = connerDataList[i].conner;
			lastiruninsid = iruninsid;
			position = i+1;
		}else if(state=="1"){//未开始
			connerDataHTML+='<td valign="top">'+
        					'<div class="step_part" '+lastDiv+'>'+
        					'<div class="step_not_running step_status">'+
        					'<div class="step_img02 step_img_common">'+
        					'<h1 style="height:100px;overflow:hidden">'+connername+'</h1>'+
        					'</div></div></div></td>';
		}else if(state=="2"){//完成
			connerDataHTML+='<td valign="top">'+
            			'<div class="step_part" '+lastDiv+'>'+
            			'<div class="step_complete step_status">'+
            			'<div class="step_img step_img_common">'+
            			'<h1 style="height:100px;overflow:hidden">'+connername+'</h1>'+
            			'</div></div></div></td>';
			tempiid = connerDataList[i].conner; 
			
			if(i==connerDataList.length-1){
				lastrunningconner = connerDataList[i].conner;
				lastiruninsid = iruninsid;
				position = i+1;
			}
		}else{
			connerDataHTML+='<td valign="top">'+
							'<div class="step_part" '+lastDiv+'>'+
							'<div class="step_abnormal step_status">'+
							'<div class="step_img04 step_img_common">'+
							'<h1 style="height:100px;overflow:hidden">'+connername+'</h1>'+
							'</div></div></div></td>';
			
			lastrunningconner = connerDataList[i].conner;
			lastiruninsid = iruninsid;
			position = i+1;
		}
    }
	connerDataHTML = connerDataHTML+'<td valign="top"><div style="width:24px;"></div></td>';
	
	$("#connerGraph").html(connerDataHTML);
	if(position==1){
		outtmp=1;
		$('#step_run').animate({scrollLeft: 0}, 300);
	}
	if(position>1){
		var oldLeft = $("#step_run").scrollLeft();
		if((position-3)*176-oldLeft==176){
			toRight();
		}else{
			$('#step_run').animate({scrollLeft: (position-3)*176}, 300);
		}
	}
	/*if(position>5){
		var oldLeft = $("#step_run").scrollLeft();
		if(connerarr.indexOf(position)<0){
			if((position-5)*176-oldLeft==176){
				connerarr.push(position);
				if(connerDataList.length-position<3){
					toRight((connerDataList.length-position+1)*176);
				}else{
					toRight(528);
				}
				
			}else{
				if(position>6 && position%3==0 && position!=connerDataList.length){
					connerarr.push(position);
					$('#step_run').animate({scrollLeft: (position-5)*528}, 300);
				}
			}
		}
	}*/
	if('-100'==lastrunningconner){
		lastrunningconner = tempiid;
	}
	selectConner(lastrunningconner,iruninsid);
}

function selectConner(conner,iruninsid){
	var laststepid = "-100";
	var lastiflowid = "-100";
	$.ajax({
		type:"post",
		url: path+"/getSwitchStepMtbScreen.do",
		data : {
			iruninsid : iruninsid,
			connerid : conner
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			var tableHtml="";
			for(var i=0;i<jsonData.length;i++){
				var backValue = "";
            	if(jsonData[i].istate=='0'){
            		backValue = "运行中";
            	}else if(jsonData[i].istate=='1'){
            		backValue = "未开始";
            	}else if(jsonData[i].istate=='2'){
            		backValue = "已完成";
            	}else if(jsonData[i].istate=='3'){
            		backValue = "已跳过";
            	}
            	
				if(jsonData[i].istate=='0'){
					if(jsonData[i].iisfail<1){
						tableHtml += '<tr class="list_blue highlight">'+
                                	'<td><div class="sc_list_dir01">'+(jsonData[i].iactname==null?"":jsonData[i].iactname)+'</div></td>'+
                                	'<td class="sc_list_dir02">'+jsonData[i].iruntime+'</td>'+
                                	'<td class="sc_list_dir03"><span>'+backValue+'</span></td>'+'</tr>';
					}else{
						tableHtml += '<tr class="list_red">'+
						'<td><div class="sc_list_dir01">'+(jsonData[i].iactname==null?"":jsonData[i].iactname)+'</div></td>'+
						'<td class="sc_list_dir02">'+jsonData[i].iruntime+'</td>'+
						'<td class="sc_list_dir03"><span>超时</span></td>'+'</tr>';
					}
					laststepid = jsonData[i].iid;
					lastiflowid = jsonData[i].iflowid;
				}else if(jsonData[i].istate=='1'){
					tableHtml += '<tr class="list_gray">'+
						'<td><div class="sc_list_dir01">'+(jsonData[i].iactname==null?"":jsonData[i].iactname)+'</div></td>'+
						'<td class="sc_list_dir02">'+jsonData[i].iruntime+'</td>'+
						'<td class="sc_list_dir03"><span>'+backValue+'</span></td>'+'</tr>';
				}else if(jsonData[i].istate=='2'){
					tableHtml += '<tr class="list_green">'+
						'<td><div class="sc_list_dir01">'+(jsonData[i].iactname==null?"":jsonData[i].iactname)+'</div></td>'+
						'<td class="sc_list_dir02">'+jsonData[i].iruntime+'</td>'+
						'<td class="sc_list_dir03"><span>'+backValue+'</span></td>'+'</tr>';
				}else if(jsonData[i].istate=='3'){
					tableHtml += '<tr class="list_gray">'+
						'<td><div class="sc_list_dir01">'+(jsonData[i].iactname==null?"":jsonData[i].iactname)+'</div></td>'+
						'<td class="sc_list_dir02">'+jsonData[i].iruntime+'</td>'+
						'<td class="sc_list_dir03"><span>'+backValue+'</span></td>'+'</tr>';
				}
			}
			$("#stepData").html(tableHtml);
		}
	});
}

function loadTime(system){	
	var hourLongSum = 0;
	var nowtime = "";
	var minStartTime = '';
	var maxEndTime = '';
	
	if(system.length>0){
		for (var i=0;i<system.length;i++){
			var rs=system[i];
			nowtime = rs.nowtime;
			if(i==0){
				minStartTime = rs.starttime;
				maxEndTime = rs.endtime;
			}else{
				if((rs.starttime<minStartTime||minStartTime=='')&&rs.starttime!=''){
					minStartTime = rs.starttime;
				}
				if(rs.endtime==''){
					maxEndTime = '';
				}else if(rs.endtime>maxEndTime&&maxEndTime!=''){
					maxEndTime = rs.endtime;
				}
			}
		}
	} else{
		return;
	}	
	var startTimeWhole=0;
	var endTimeWhole=0;
	if(minStartTime==''){
		startTimeWhole = (getDate(nowtime)).getTime();
	}else{
		startTimeWhole = (getDate(minStartTime)).getTime();
	}

	if(maxEndTime==''){
		endTimeWhole = (getDate(nowtime)).getTime();
	}else{
		endTimeWhole = (getDate(maxEndTime)).getTime();
	}
	hourLongSum = endTimeWhole - startTimeWhole;
	var hour = Math.floor(hourLongSum/1000/60/60);
	var minute = Math.floor(hourLongSum/1000/60%60);
	var second = Math.floor(hourLongSum/1000%60);
	
	hour = hour + "";
	minute = minute + "";
	second = second + "";
	
	if(hour<10){
		hour = "0" + hour;
	}
	if(minute<10){
		minute = "0" + minute;
	}
	if(second<10){
		second = "0" + second;
	}
	if (hour.length>1){
		$('#hourOne').html(hour.substring(0,hour.length-1));
		$('#hourTwo').html(hour.substring(hour.length-1));
	} else {
		$('#hourTwo').html(hour);
	}
	if (minute.length>1){
		$('#minuteOne').html(minute.substring(0,minute.length-1));
		$('#minuteTwo').html(minute.substring(minute.length-1));
	} else {
		$('#minuteTwo').html(minute);
	}
	if (second.length>1){
		$('#secondsOne').html(second.substring(0,second.length-1));
		$('#secondsTwo').html(second.substring(second.length-1));
	} else {
		$('#secondsTwo').html(second);
	}
}

function getDate(dateString){
	dateString = dateString.replace(/-/g,"/");
	var date = new Date(dateString);
	return date;
}

function checkChange(obj){
	if(obj.checked){
		loadFunction = window.setInterval(function(){
			loadData();
		}, 1000); 
	}else{
		window.clearInterval(loadFunction);
	}
}

function closeWin(){
	outtmp = 0;
	$(".success_bg").css("display", "none");
}
function toLeft(){
	var oldLeft = $("#step_run").scrollLeft();
	$('#step_run').animate({scrollLeft: oldLeft-176}, 300);
}
function toRight(){
	var oldLeft = $("#step_run").scrollLeft();
	$('#step_run').animate({scrollLeft: oldLeft+176}, 300);
}
function toUp(ll){
    $("#systemTable").scrollTop(ll);
}

function getTime(htime){
	var hour1 = Math.floor(htime/1000/60/60);
	var minute1 = Math.floor(htime/1000/60%60);
	var second1 = Math.floor(htime/1000%60);
	
	//左侧切换耗时
	var tiem="";
	if(hour1==0 && minute1==0){
		tiem = second1 + "秒";
	}else if(hour1==0 && minute1!=0){
		tiem = minute1 + "分" + second1 + "秒";
	}else{
		tiem = hour1 + "时" + minute1 + "分" + second1 + "秒";
	}
	return tiem;
}

function isIE() { 
	if (!window.ActiveXObject || "ActiveXObject" in window){
		return true;
	}
	return false;
}

function fillScreen(){
	var w = document.documentElement.clientWidth;
	var h = document.documentElement.clientHeight;
	$('.zj_sc_bg').css({
		'transform': `scale(${w / 1920},${h / 1080})`,
		'transform-origin': 'left top'
	});
}

var days = new Array ("日", "一", "二", "三", "四", "五", "六");
function showDT ()
{
	var currentDT = new Date ();
	var y, m, date, day, hs, ms, ss, theDateStr;
	y = currentDT.getFullYear (); // 四位整数表示的年份
	m = currentDT.getMonth ()+1; // 月 返回值是0-11,需要加一个1 才能达到1-12月 
	date = currentDT.getDate (); // 日
	day = currentDT.getDay (); // 星期
	hs = currentDT.getHours (); // 时
	ms = currentDT.getMinutes (); // 分
	ss = currentDT.getSeconds (); // 秒
	//月份不足两位，用0占位
	if (m < 10)
	{
		m = '0' + m;
	}
	//日期不足两位，用0占位
	if (date < 10)
	{
		date = '0' + date;
	}
	//小时不足两位，用0占位
	if (hs < 10)
	{
		hs = '0' + hs;
	}
	//分钟不足两位，用0占位
	if (ms < 10)
	{
		ms = '0' + ms;
	}
	//秒数不足两位，用0占位
	if (ss < 10)
	{
		ss = '0' + ss;
	}
	
	
	theDateStr = y + "年" + m + "月" + date + "日" + " " + hs + " : " + ms + " : " + ss+" 星期"+ days[day];
	document.getElementById ("mtbclock").innerHTML = theDateStr;
	// setTimeout 在执行时,是在载入后延迟指定时间后,去执行一次表达式,仅执行一次
	window.setTimeout (showDT, 1000);
}
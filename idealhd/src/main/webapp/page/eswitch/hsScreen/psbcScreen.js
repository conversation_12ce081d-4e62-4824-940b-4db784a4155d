var sysMap={};
var stepList=[];
var taskPercentage;//任务进度条
var sysNameList=[];//系统名称+，+切换方向的集合
//var firstTime=0;//步骤集合中最早的运行时间
var lastTime=0;//步骤集合中最后完成时间

var selectId=0;
var sysNameLength=0;
var changeSec=0;
var curSite=0;
var lllTime=0;
var steplastTime=0;
var lastFlag=false;
var itime="";
$(function(){
	function loadData(){
		var allSys=0;//总系统数
        var switchSys=0;//总系统数
		var finishSys=0;//系统完成数
		var runningSys=0;//系统运行数量
		var waitSys=0;//系统未执行数量
		var errorSys=0;//系统异常数量
		var allStep=0;//步骤总数
		var finishStep=0;//步骤完成数
		var runningStep=0;//步骤运行数量
		var waitStep=0;//步骤未执行数量
		var errorStep=0;//步骤异常数量
		var endflag=true;
		var firstH=0;//总计时的每个数
		var secondH=0;
		var firstM=0;
		var secondM=0;
		var firstS=0;
		var secondS=0;
		$.ajax({
			type:"post",
			url: "getSystemAndStepList.do",
			async:true,
            data:{hsFlag:1},
			dataType:"json",
			success: function(jsonData){
				sysNameList=[];
				sysMap=jsonData.sys;
				stepList=jsonData.step;
//				console.log(sysMap);
//				console.log(stepList);
				var html="";
				var finHtml="";//完成
				var runHtml="";//运行
				var errHtml="";//异常
				var waitHtml="";//暂停
				var i=0;
				for(var sys in sysMap){
					allSys++;
					var istate=sysMap[sys].istate;
					var iid=sysMap[sys].iid;
					var iisfail=sysMap[sys].iisfail;
					var isysname=sysMap[sys].isysname;//系统名
					var iswitchto=sysMap[sys].iswitchto;//切换方向
					var html="";
					if(!isNaN(iid)){
                        switchSys++;
                    }
					sysNameList.push(isysname+","+iswitchto+","+iid+","+istate+","+iisfail);
					if(iisfail!=0&&istate==0){//异常
						errorSys++;
					}
					if(istate==0){//运行
						runningSys++;
					}else if(istate==1||istate==2){//2代表终止
						finishSys++;
					}else if(istate==4||istate==3){//3代表暂停
						waitSys++;
					}
//					if(iisfail!=0&&istate==0){
//						errorSys++;
//					}
					
//					if(selectId>0){
//						if(selectId==iid){
//							html=html+"<li class=\""+style(iisfail,istate)+" select_status \"  onclick=\"getStepList("+iid+")\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
//							getStepList(iid);
//						}else{
//							html=html+"<li class=\""+style(iisfail,istate)+"\"  onclick=\"getStepList("+iid+")\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
//						}
//					}else{
					if(selectId==0){
						if(curSite==i){
//							html=html+"<li class=\""+style(istate,iisfail)+" select_status \"  onclick=\"getStepList("+iid+")\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							if(getOSysState(istate,iisfail)=="异常"){
								errHtml=errHtml+"<li class=\""+style(istate,iisfail)+" \"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="已完成"){
								finHtml=finHtml+"<li class=\""+style(istate,iisfail)+"  \"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="运行中"){
								runHtml=runHtml+"<li class=\""+style(istate,iisfail)+" \"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="未执行"){
								waitHtml=waitHtml+"<li class=\""+style(istate,iisfail)+" \"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}
//							getStepList1(iid);
						}else {
//							html=html+"<li class=\""+style(istate,iisfail)+"\"  onclick=\"getStepList("+iid+")\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							if(getOSysState(istate,iisfail)=="异常"){
								errHtml=errHtml+"<li class=\""+style(istate,iisfail)+"\"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="已完成"){
								finHtml=finHtml+"<li class=\""+style(istate,iisfail)+"\"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="运行中"){
								runHtml=runHtml+"<li class=\""+style(istate,iisfail)+"\"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}else if(getOSysState(istate,iisfail)=="未执行"){
								waitHtml=waitHtml+"<li class=\""+style(istate,iisfail)+"\"  onclick=\"getStepList('"+iid+"')\"> <span></span> <span>"+isysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+iswitchto+"</span> </li>";
							}
						}
					}else{
						html="";
						finHtml="";//完成
						runHtml="";//运行
						errHtml="";//异常
						waitHtml="";//暂停
						for(var isys in sysMap){
							var state=sysMap[isys].istate;
							var id=sysMap[isys].iid;
							var isfail=sysMap[isys].iisfail;
							var sysname=sysMap[isys].isysname;//系统名
							var switchto=sysMap[isys].iswitchto;//切换方向
							if(getOSysState(state,isfail)=="异常"){
								if(selectId==id){
									errHtml=errHtml+"<li class=\""+style(state,isfail)+" select_status \"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
									getStepList1(id);
								}else{
									errHtml=errHtml+"<li class=\""+style(state,isfail)+"\"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
								}
							}else if(getOSysState(state,isfail)=="已完成"){
								if(selectId==id){
									finHtml=finHtml+"<li class=\""+style(state,isfail)+" select_status \"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
									getStepList1(id);
								}else{
									finHtml=finHtml+"<li class=\""+style(state,isfail)+"\"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
								}
							}else if(getOSysState(state,isfail)=="运行中"){
								if(selectId==id){
									runHtml=runHtml+"<li class=\""+style(state,isfail)+" select_status \"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
									getStepList1(id);
								}else{
									runHtml=runHtml+"<li class=\""+style(state,isfail)+"\"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
								}
							}else if(getOSysState(state,isfail)=="未执行"){
								if(selectId==id){
									waitHtml=waitHtml+"<li class=\""+style(state,isfail)+" select_status \"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
									getStepList1(id);
								}else{
									waitHtml=waitHtml+"<li class=\""+style(state,isfail)+"\"  onclick=\"getStepList('"+id+"')\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
								}
							}
							
//							if(selectId==id){
//								html=html+"<li class=\""+style(state,isfail)+" select_status \"  onclick=\"getStepList("+id+")\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
//								getStepList1(id);
//							}else{
//								html=html+"<li class=\""+style(state,isfail)+" \"  onclick=\"getStepList("+id+")\"> <span></span> <span>"+sysname+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+switchto+"</span> </li>";
//							}
						}
					}
//					}
					i++;
					html=html+finHtml+errHtml+runHtml;//sysnameAndiswitchto
				}
				sysNameLength=sysNameList.length;
				$("#sysnameAndiswitchto").html(html);
				if(selectId==0){
					if(changeSec>=5){
						curSite=curSite+1;
						if(curSite>=sysNameList.length){
							curSite=0;
						}
						changeSec=0;
					}
					
					var liarr=document.getElementById("sysnameAndiswitchto").getElementsByTagName("li");
                    if(liarr&&liarr.length>0){
                        if(curSite<liarr.length){
                            liarr[curSite].setAttribute("class",liarr[curSite].getAttribute("class")+" select_status");
                            if(curSite>3){
                                $("#sysnameAndiswitchto").scrollTop((curSite-3)*58);
                            }else{
                                $("#sysnameAndiswitchto").scrollTop(0);
                            }
                            liarr[curSite].click();
                        }else{
                            liarr[0].setAttribute("class",liarr[0].getAttribute("class")+" select_status");
                            $("#sysnameAndiswitchto").scrollTop(0);
                            liarr[0].click();
                        }
                    }
					selectId=0;
				}
				
				var firstTime=0;
				for(var i=0;i<stepList.length;i++){
					var istate=stepList[i].istate;
					var iisfail=stepList[i].iisfail;
					var istarttime=stepList[i].istarttime;
					var iendtime=stepList[i].iendtime;
					var nowtime=stepList[i].itime;//数据库当前时间
					itime=nowtime;
					if(firstTime==0&&istate!=1){
						var a= istarttime.replace(new RegExp("-","gm"),"/");
						firstTime= (new Date(a)).getTime();
					}else if(istate!=1){//没考虑已经运行然后中途暂停这个系统运行的情况
						var a = istarttime.replace(new RegExp("-","gm"),"/");
						var b=(new Date(a)).getTime();
						if(b<firstTime&& b!=0){
							firstTime=b;
						}
					}
					if(firstTime==0){//目前选中的所有系统都是未运行状态
//						firstTime=new Date().getTime();//就和最后的时间一样获取当前毫秒数，相减得0;
						var a=itime.replace(new RegExp("-","gm"),"/");
						firstTime=(new Date(a)).getTime();
					}
					if(iendtime=="1970-01-01 08:00:00"){//说明未执行或运行中
						var a =itime.replace(new RegExp("-","gm"),"/");
						lastTime=(new Date(a)).getTime();//获取当前毫秒数
						endflag=false;
					}else if(endflag==true){
						var endtime= iendtime.replace(new RegExp("-","gm"),"/");
						var last=(new Date(endtime)).getTime();
						if(last>lastTime){
							lastTime=last;
						}
					}
					allStep++;
					// if(istate==0&&iisfail>0){//不考虑状态是已完成或者跳过但是iisfail仍然显示是异常的 只考虑运行中的异常不算运行中算异常
					// 	errorStep++;//可能存在-1的值 不清楚-1代表什么 1，2，3，4一定代表异常 暂时不是0都是异常
					// }
					if(istate==0){
                        if(iisfail>0){
                            errorStep++;
                        }else{
                            runningStep++;
                        }
					}else if(istate==1){
						waitStep++;
					}else if(istate==2||istate==3){
						finishStep++;
					}
//					if((istate==0&&(iisfail==1||iisfail==2||iisfail==3||iisfail==4))){
//						errorStep++;
//					}
				}
				if(finishSys==switchSys){
					for(var i=0;i<stepList.length;i++){
						var e=stepList[i].iendtime;
						var a= e.replace(new RegExp("-","gm"),"/");
						var b=(new Date(a)).getTime();
						if(iendtime!="1970-01-01 08:00:00"&&lllTime==0){
							lllTime=b;
						}else if(lllTime<b){
							lllTime=b;
						}
					}
					lastTime=lllTime;
				}
				
//				console.log("firstTime",firstTime);
//				console.log("lastTime",lastTime);
				var v="";
				if(allSys==0){
					v=v+"<span id=\"progress_bar\" class=\"progress_bar\" style=\"width:"+0+"%"+"\"></span>";
				}else{
					v=v+"<span id=\"progress_bar\" class=\"progress_bar\" style=\"width:"+(parseInt(finishStep/allStep*100))+"%"+"\"></span>";
				}
				$("#progress_bar").html(v);
//				if(finishSys!=allSys){
                totalTime(firstTime,lastTime);
//				}else if(finishSys==allSys){
//					if(lastFlag){
////						lastTime=new Date().getTime();//获取当前毫秒数
//						
//						lllTime=lastTime;
//						lastFlag=false;
//					}
//					totalTime(firstTime,lllTime);
//				}



				$("#allSys").html(switchSys);
                $("#allSys2").html(allSys);
				$("#finishSys").html(finishSys);
				$("#runningSys").html(runningSys);
				$("#waitSys").html(waitSys);
				$("#errorSys").html(errorSys);

				$("#allStep").html(allStep);
				$("#finishStep").html(finishStep);
				$("#runningStep").html(runningStep);
				$("#waitStep").html(waitStep);
                $("#errorStep").html(errorStep);
//				if(errorSys>0){
////					var a="<p class=\"sp_icon05 sp_iconcommon\" id=\"runSysStyle\"></p>";
//					$("#runSysStyle").attr("class","sp_icon05 sp_iconcommon");
//				}
//				if(errorStep>0){
////					var a="<p class=\"sp_icon05 sp_iconcommon\" id=\"runStpeStyle\"></p>";
//					$("#runStpeStyle").attr("class","sp_icon05 sp_iconcommon");
//					$("#runSysStyle").attr("class","sp_icon05 sp_iconcommon");
//				}

//				var html="";
//				for(var j=0;j<sysNameList.length;j++){
//					console.log("系统和切换方向："+sysNameList[j]);
//					var arr=sysNameList[j].split(",");
//					html=html+"<li class=\""+style()+"\"  onclick=\"getStepList("+arr[2]+")\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
//				}
//				$("#sysnameAndiswitchto").html(html);
				
				
//				var liarr=document.getElementById("sysnameAndiswitchto").getElementsByTagName("li");
//				for (var l = 0; l < liarr.length; l++) {
//					var sd = liarr[l].getAttribute("class");
//					if(sd.indexOf("select_status")>-1){//&&l>3
//						if(l>3){
//							$("#sysnameAndiswitchto").scrollTop((l-3)*58);//58
//						}else{
//							$("#sysnameAndiswitchto").scrollTop(0);
//						}
//					}
//				}
				
				
				//计算进度条的百分比
				taskPercentage=(isNaN(parseInt((finishStep/allStep)*100))?"0":parseInt((finishStep/allStep)*100))+"%";
				
				
				if(allSys==0){
					$("#taskPercentage").html(0+"%");
				}else{
					$("#taskPercentage").html(taskPercentage);
				}
				getSysInfo(sysMap,stepList);
				//设置展示步骤信息
//				var zz ="";
//				if(selectId!=0){
//					var ss ="";
//					for (var i=0;i<stepList.length;i++) {
//						var iruninsid=stepList[i].iruninsid;
//						var istarttime=stepList[i].istarttime;
//						var iendtime=stepList[i].iendtime;
//						if(istarttime=="1970-01-01 08:00:00"){
//							istarttime="";
//						}
//						if(iendtime=="1970-01-01 08:00:00"){
//							iendtime="";
//						}
//						if(selectId==iruninsid){
//							ss =ss+
//							"<tr>"+
//							  "<td>"+
//								"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
//							  "</td>"+
//							  "<td>"+
//								"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
//							  "</td>"+
//							  "<td>"+
//								"<div class=\"ot_column03\">"+istarttime+"</div>"+
//							  "</td>"+
//							  "<td>"+
//								"<div class=\"ot_column04\">"+iendtime+"</div>"+
//							  "</td>"+
//							  "<td>"+
//								"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
//							  "</td>"+
//							  "<td>"+
//								"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
//							  "</td>"+
//						   "</tr>";
//						}
//						 
//						
//						
//					}
//					$("#steplist").html(ss);
//				}else{
//					for(var z=0;z<sysNameList.length;z++){
//						 if(z==0){
//							var zArr=sysNameList[z].split(",");
//							
//							for (var i=0;i<stepList.length;i++) {
//								var iruninsid=stepList[i].iruninsid;
//								var istarttime=stepList[i].istarttime;
//								var iendtime=stepList[i].iendtime;
//								if(istarttime=="1970-01-01 08:00:00"){
//									istarttime="";
//								}
//								if(iendtime=="1970-01-01 08:00:00"){
//									iendtime="";
//								}
//								if(zArr[2]==iruninsid){
//									zz =zz+
//									"<tr>"+
//									  "<td>"+
//										"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
//									  "</td>"+
//									  "<td>"+
//										"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
//									  "</td>"+
//									  "<td>"+
//										"<div class=\"ot_column03\">"+istarttime+"</div>"+
//									  "</td>"+
//									  "<td>"+
//										"<div class=\"ot_column04\">"+iendtime+"</div>"+
//									  "</td>"+
//									  "<td>"+
//										"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
//									  "</td>"+
//									  "<td>"+
//										"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
//									  "</td>"+
//								   "</tr>";
//								}
//							}
//							
//						}
//					}
//					$("#steplist").html(zz);
//				}
			}
		});
		var mydate=itime.replace(new RegExp("-","gm"),"/");
		var mydatelong = (new Date(mydate)).getTime();
		if(!isNaN(mydatelong)){
			showDT(mydatelong);
		}
	}
	loadData();
	window.setInterval(function(){
		loadData();
//		console.log(sysNameList.length);
//		if(selectId!=0){
//			changeSec=changeSec;
//		}else{
			changeSec=changeSec+1;
//		}
	}, 1000);
//	loadData();
//	function loopScroll(){
//		if(selectId==0){
//			for(var i=0;i<sysNameList.length;i++){
//				var arr=sysNameList[i];
//				getStepList(arr[2]);
//				setTimeout(5000);
//			}
//		}
//	}
//	loopScroll();
//	window.setInterval(function(){
//		loopScroll();
//	}, 5000*sysNameLength); 
	
	
	function style(istate,iisfail){
//		console.log(istate+","+iisfail);
		if(iisfail!=0&&istate==0){
			return "s_erred_status status_public_style";//s_failed_status status_public_style
		}else if(istate==1||istate==2){
			return "s_completed_status status_public_style";//s_completed_status status_public_style
		}else if(istate==4||istate==3){
			return "s_failed_status status_public_style";//s_failed_status status_public_style 
		}else if(istate==0){
			return "s_execute_status status_public_style";//s_execute_status status_public_style
		}
	}
	
	
	function totalTime(firstTime,lastTime){
//		console.log(firstTime);
//		console.log(lastTime);
//		console.log((lastTime-firstTime)/1000);
		var s=parseInt((lastTime-firstTime)/1000);
		var h=0;
		var m=0;
		if(s>60){
			m=parseInt(s/60);
			s=s%60;
		} 
		if(m>60){
			h=parseInt(m/60);
			m=m%60;
		}
		if(s<10){
			firstS=0;
			secondS=s;
		}else{
//			s=s+"";
//			firstS=s.substring(0,s.length-1);
//			secondS=s.substring(s.length-1);
			secondS=s%10;
			firstS=parseInt(s/10);
		}
		if(m<10){
			firstM=0;
			secondM=m;
		}else{
			secondM=m%10;
			firstM=parseInt(m/10);
		}
		if(h<10){
			firstH=0;
			secondH=h;
		}else{
			secondH=h%10;
			firstH=parseInt(h/10);
		}
//		console.log(firstH+secondH+firstM+secondM+firstS+secondS);
//		console.log(secondH);
//		console.log(firstM);
//		console.log(secondM);
//		console.log(firstS);
//		console.log(secondS);
		$("#firstH").html(firstH);
		$("#secondH").html(secondH);
		$("#firstM").html(firstM);
		$("#secondM").html(secondM);
		$("#firstS").html(firstS);
		$("#secondS").html(secondS);
	}
	function getSysInfo(sysMap,stepList) {
        var ohtml = "";
        var errHtml = "";
        var runHtml = "";
        var waitHtml = "";
        var finHtml = "";
        for (var sys in sysMap) {
            var finishflag = true;
            var allNum = 0;
            var finishNum = 0;
            var initTime = 0;
            var finishTime = 0;
            var timeDifference = 0;
            var istate = sysMap[sys].istate;
            var iid = sysMap[sys].iid;
            var iisfail = sysMap[sys].iisfail;
            var isysname = sysMap[sys].isysname;//系统名
            var istarttime = sysMap[sys].istarttime;
            var iendtime = sysMap[sys].iendtime;
            var itime = sysMap[sys].itime;
            var aa = itime.replace(new RegExp("-", "gm"), "/");
            if(istarttime == "1970-01-01 08:00:00"){
                initTime = (new Date(aa)).getTime();
            }else{
                var start = istarttime.replace(new RegExp("-", "gm"), "/");
                initTime = (new Date(start)).getTime();

            }
            if (iendtime == "1970-01-01 08:00:00") {//说明未执行或运行中//终止状态下，会持续走这里
                finishTime = (new Date(aa)).getTime();//获取当前毫秒数
            }else{
                var end = iendtime.replace(new RegExp("-", "gm"), "/");
                finishTime=(new Date(end)).getTime();//获取当前毫秒数
            }
            for (var i = 0; i < stepList.length; i++) {
                var istarttime = stepList[i].istarttime;
                var iendtime = stepList[i].iendtime;
                var itime = stepList[i].itime;
//				if(iid==7835){
//					console.log("istarttime:"+istarttime);
//					console.log("iendtime:"+iendtime);
//				}
                if (stepList[i].iruninsid == iid) {
                    allNum++;
                    if (stepList[i].istate == 2 || stepList[i].istate == 3) {
                        finishNum++;
                    }
                    // if (istarttime != "1970-01-01 08:00:00" && initTime == 0) {
                    //     var a = istarttime.replace(new RegExp("-", "gm"), "/");
                    //     initTime = (new Date(a)).getTime();
                    // } else if (istarttime != "1970-01-01 08:00:00") {
                    //     var a = istarttime.replace(new RegExp("-", "gm"), "/");
                    //     var b = (new Date(a)).getTime();
                    //     if (b < initTime) {
                    //         initTime = b;
                    //     }
                    // }
                    // if (iendtime == "1970-01-01 08:00:00") {//说明未执行或运行中//终止状态下，会持续走这里
                    //     var a = itime.replace(new RegExp("-", "gm"), "/");
                    //     finishTime = (new Date(a)).getTime();//获取当前毫秒数
                    //     finishflag = false;
                    // } else if (finishflag == true) {
                    //     var endtime = iendtime.replace(new RegExp("-", "gm"), "/");
                    //     var last = (new Date(endtime)).getTime();
                    //     if (last > finishTime) {
                    //         finishTime = last;
                    //     }
                    // }

                }
            }
            // steplastTime = 0;
            // if (allNum == finishNum) {
            //     for (var i = 0; i < stepList.length; i++) {
            //         if (stepList[i].iruninsid == iid) {
            //             var e = stepList[i].iendtime;
            //             var a = e.replace(new RegExp("-", "gm"), "/");
            //             var b = (new Date(a)).getTime();
            //             if (e != "1970-01-01 08:00:00" && steplastTime == 0) {
            //                 steplastTime = b;
            //             } else if (steplastTime < b) {
            //                 steplastTime = b;
            //             }
            //         }
            //     }
            //     finishTime = steplastTime;
            // }
//			console.log(initTime);
            if (initTime == 0) {
                timeDifference = 0;
            } else {
                timeDifference = parseInt((finishTime - initTime) / 1000);//单个目前系统耗时（秒）
            }
            var m = 0;
            var h = 0;
            if (timeDifference > 60) {
                m = parseInt(timeDifference / 60);
                timeDifference = timeDifference % 60;
                if (m >= 60) {
                    h = parseInt(m / 60);
                    m = m % 60;
                }
            }
            if (timeDifference < 10) {
                timeDifference = "0" + timeDifference;
            }
            if (m < 10) {
                m = "0" + m;
            }
            if (h < 10) {
                h = "0" + h;
            }
            var x = h + ":" + m + ":" + timeDifference;
            if (getOSysState(istate, iisfail) == '异常') {
// 				errHtml=errHtml+"<div class=\""+getOstate(istate,iisfail)+"\" onclick=\"getStepList("+iid+")\">"+
// 				  "<div class=\""+getDivStyel(istate,iisfail)+"\">"+
// 					"<div class=\"circle_cn\">"+
// //					  <!--放进度-->
// 					  "<span>"+finishNum+"/"+allNum+"</span>"+
// 					  "<p>己完成/总任务</p>"+
// 					"</div>"+
// 				  "</div>"+
// 				  "<h1 style=\"height: 45px;overflow: hidden;\">"+isysname+"</h1>"+
// 				  "<div class=\"sw_circle_text\">"+
// 					"<p><span>计时</span> </p>"+
// 					"<p><span>"+x+"</span> </p>"+
// 				  "</div>"+
// 				"</div>"
                errHtml = errHtml  + "<div class=\"switchSystem systemOrange\" onclick=\"getStepList('" + iid + "')\">"+
                        "<h1>"+isysname+"</h1>"+
                        "<span></span>"+
                        "<div class=\"systemHour\">"+x+"</div>"+
                        "<div class=\"systemStep\">"+
                        "<div class=\"stepPart partOrange\">"+
                        "<h1>"+allNum+"</h1>"+
                        "<h2>总步数</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partOrange\">"+
                        "<h1 class=\"title\">"+finishNum+"</h1>"+
                        "<h2>已完成</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partOrange\">"+
                        "<h1>"+calculatePercentage(finishNum,allNum)+"%</h1>"+
                        "<h2>完成率</h2>"+
                        "</div>"+
                        "</div>"+
                        "</div class=\"end\">";
            }
            if (getOSysState(istate, iisfail) == '运行中') {
// 				runHtml=runHtml+"<div class=\""+getOstate(istate,iisfail)+"\" onclick=\"getStepList("+iid+")\">"+
// 				  "<div class=\""+getDivStyel(istate,iisfail)+"\">"+
// 					"<div class=\"circle_cn\">"+
// //					  <!--放进度-->
// 					  "<span>"+finishNum+"/"+allNum+"</span>"+
// 					  "<p>己完成/总任务</p>"+
// 					"</div>"+
// 				  "</div>"+
// 				  "<h1 style=\"height: 45px;overflow: hidden;\">"+isysname+"</h1>"+
// 				  "<div class=\"sw_circle_text\">"+
// 					"<p><span>计时</span> </p>"+
// 					"<p><span>"+x+"</span> </p>"+
// 				  "</div>"+
// 				"</div>";

                runHtml = runHtml + "<div class=\"switchSystem systemBlue\" onclick=\"getStepList('" + iid + "')\">"+
                        "<h1>"+isysname+"</h1>"+
                        "<span></span>"+
                        "<div class=\"systemHour\">"+x+"</div>"+
                        "<div class=\"systemStep\">"+
                        "<div class=\"stepPart partBlue\">"+
                        "<h1>"+allNum+"</h1>"+
                        "<h2>总步数</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partBlue\">"+
                        "<h1 class=\"title\">"+finishNum+"</h1>"+
                        "<h2>已完成</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partBlue\">"+
                        "<h1>"+calculatePercentage(finishNum,allNum)+"%</h1>"+
                        "<h2>完成率</h2>"+
                        "</div>"+
                        "</div>"+
                        "</div class=\"end\">";
            }
            if (getOSysState(istate, iisfail) == '未执行') {
// 				waitHtml=waitHtml+"<div class=\""+getOstate(istate,iisfail)+"\" onclick=\"getStepList("+iid+")\">"+
// 				  "<div class=\""+getDivStyel(istate,iisfail)+"\">"+
// 					"<div class=\"circle_cn\">"+
// //					  <!--放进度-->
// 					  "<span>"+finishNum+"/"+allNum+"</span>"+
// 					  "<p>己完成/总任务</p>"+
// 					"</div>"+
// 				  "</div>"+
// 				  "<h1 style=\"height: 45px;overflow: hidden;\">"+isysname+"</h1>"+
// 				  "<div class=\"sw_circle_text\">"+
// 					"<p><span>计时</span> </p>"+
// 					"<p><span>"+x+"</span> </p>"+
// 				  "</div>"+
// 				"</div>";
//                 waitHtml = waitHtml + "<div class=\"swiper-slide\" style=\"cursor:pointer;\" onclick=\"getStepList(" + iid + ")\">" +
//                     "<div class=\"overview_common gray_state\">" +
//                     "<h1>" + isysname + "</h1>" +
//                     "<h2>" + x + "</h2>" +
//                     "<div class=\"step_all\">" +
//                     "<div class=\"step_num\">" +
//                     "<span>" + allNum + "</span>" +
//                     "<span>总步数</span>" +
//                     "</div>" +
//                     "<div class=\"step_num num_color\">" +
//                     "<span>" + finishNum + "</span>" +
//                     "<span>已完成</span>" +
//                     "</div>" +
//                     "</div>" +
//                     "</div>" +
//                     "</div>";

                waitHtml = waitHtml + "<div class=\"switchSystem systemGray\" onclick=\"getStepList('" + iid + "')\">"+
                        "<h1>"+isysname+"</h1>"+
                        "<span></span>"+
                        "<div class=\"systemHour\">--:--:--</div>"+
                        "<div class=\"systemStep\">"+
                        "<div class=\"stepPart partGray\">"+
                        "<h1>-</h1>"+
                        "<h2>总步数</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partGray\">"+
                        "<h1 class=\"title\">-</h1>"+
                        "<h2>已完成</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partGray\">"+
                        "<h1>-</h1>"+
                        "<h2>完成率</h2>"+
                        "</div>"+
                        "</div>"+
                        "</div class=\"end\">";
            }
            if (getOSysState(istate, iisfail) == '已完成') {
// 				finHtml=finHtml+"<div class=\""+getOstate(istate,iisfail)+"\" onclick=\"getStepList("+iid+")\">"+
// 				  "<div class=\""+getDivStyel(istate,iisfail)+"\">"+
// 					"<div class=\"circle_cn\">"+
// //					  <!--放进度-->
// 					  "<span>"+finishNum+"/"+allNum+"</span>"+
// 					  "<p>己完成/总任务</p>"+
// 					"</div>"+
// 				  "</div>"+
// 				  "<h1 style=\"height: 45px;overflow: hidden;\">"+isysname+"</h1>"+
// 				  "<div class=\"sw_circle_text\">"+
// 					"<p><span>计时</span> </p>"+
// 					"<p><span>"+x+"</span> </p>"+
// 				  "</div>"+
// 				"</div>";
                finHtml = finHtml + "<div class=\"switchSystem systemGreen\" onclick=\"getStepList('" + iid + "')\">"+
                        "<h1>"+isysname+"</h1>"+
                        "<span></span>"+
                        "<div class=\"systemHour\">"+x+"</div>"+
                        "<div class=\"systemStep\">"+
                        "<div class=\"stepPart partGreen\">"+
                        "<h1>"+allNum+"</h1>"+
                        "<h2>总步数</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partGreen\">"+
                        "<h1 class=\"title\">"+finishNum+"</h1>"+
                        "<h2>已完成</h2>"+
                        "</div>"+
                        "<div class=\"stepPart partGreen\">"+
                        "<h1>"+calculatePercentage(finishNum,allNum)+"%</h1>"+
                        "<h2>完成率</h2>"+
                        "</div>"+
                        "</div>"+
                        "</div class=\"end\">";
            }

        }
        ohtml = finHtml+errHtml + runHtml + waitHtml;
        ohtml=insertLiBetweenDivByFor(ohtml);
        $("#sysInfo").html(ohtml);

        jQuery(".slideGroup .slideBox").slide({
            mainCell: "ul",
            vis: 7,
            prevCell: ".sPrev",
            nextCell: ".sNext",
            effect: "leftLoop"
        });
        // if (swiper) {
        //     // swiper.destroy(true, true);
        //     swiper.update(false);
        //     if(isMouseOn==0){
        //         swiper.slideTo(0);
        //     }
        // }
        // swiper = new Swiper('.swiper-container', {
        //     pagination: '.swiper-pagination',
        //     slidesPerColumnFill : 'row',
        //     slidesPerView : 10,
        //     slidesPerColumn: 2,
        //     centeredSlides: false,
        //     paginationClickable: true,
        //     spaceBetween: 5
        // });

    }

    function calculatePercentage(part, total) {
        if (total === 0) {
            return 0; // 防止除以零的错误
        }
        return Math.ceil((part / total) * 100);
    }

    function insertLiBetweenDivs(htmlString) {
        // 使用正则表达式匹配每两个 div 块
        // const regex = /(<div[^>]*class\s*=\s*["'][^"']*switchSystem[^"']*["'][^>]*>.*?<\/div>)\s*(<div[^>]*class\s*=\s*["'][^"']*switchSystem[^"']*["'][^>]*>.*?<\/div>)/gs;
        const regex = /(<div[^>]*class\s*=\s*["'][^"']*switchSystem[^"']*["'][^>]*>.*?<\/div>)\s*(<div[^>]*class\s*=\s*["'][^"']*switchSystem[^"']*["'][^>]*>.*?<\/div>)/gs;
         // 替换每两个 div 块之间的内容
        // return htmlString.replace(regex, '$1</li><li>$2');
        return htmlString.replace(regex, (match, p1, p2) => `${p1}${p2}</li><li>`);
    }

    function insertLiBetweenDivByFor(htmlString){
        // let divPattern = /(<div class="switchSystem[^"]*">.*?<\/div class="end">)/gs;
        let divPattern = /<div class="switchSystem[^"]*".*?<\/div class="end">/gs;
        let divBlocks = htmlString.match(divPattern);

    // 重新组合 HTML 字符串
        let result = '';
        for (let i = 0; i < divBlocks.length; i += 2) {
            result += '<li>';
            result += divBlocks[i];
            if (i + 1 < divBlocks.length) {
                result += divBlocks[i + 1];
            }
            result += '</li>';
        }
        return result;
    }
    function getOSysState(istate, iisfail) {//系统的参数
        if (iisfail != 0 && istate == 0) {
            return "异常";
        } else if (istate == 1 || istate == 2) {
            return "已完成";//
        } else if (istate == 4 || istate == 3) {
            return "未执行";
        } else if (istate == 0) {
            return "运行中";//
        }
    }

    function getOstate(istate, iisfail) {//系统的参数
        if (iisfail != 0 && istate == 0) {//只能是运行中并且iisfail!=0归类异常不是运行中
            return "circle_orange circle_green_common";
        } else if (istate == 1 || istate == 2) {
            return "circle_green circle_green_common";//
        } else if (istate == 4 || istate == 3) {
            return "circle_gray circle_green_common";
        } else if (istate == 0) {
            return "circle_blue circle_green_common";
        }
    }

    function getDivStyel(istate, iisfail) {//系统的参数
        if (iisfail != 0 && istate == 0) {
            return "circle_orange_bg";
        } else if (istate == 1 || istate == 2) {//
            return "circle_green_bg";
        } else if (istate == 4 || istate == 3) {
            return "circle_gray_bg";
        } else if (istate == 0) {
            return "circle_blue_bg";
        }
    }

    fillScreen();
});
window.onresize = function () {
    fillScreen();
}

function fillScreen() {
    var w = document.documentElement.clientWidth;
    var h = document.documentElement.clientHeight;
	$('body').css({
		'transform': `scale(${w / 1920},${h / 1080})`,
		'transform-origin': 'left top'
	});
}
function getStepList(iid){//iid是系统运行id
	if(selectId!=0&&selectId==iid){
		selectId=0;
	}else{
		selectId=iid;
	}
	if(selectId!=0){
		var html="";
		var errHtml="";
		var finHtml="";
		var runHtml="";
		var waitHtml="";
		for(var j=0;j<sysNameList.length;j++){
			var arr=sysNameList[j].split(",");
			if(selectId==arr[2]){
				if(getOSysState(arr[3],arr[4])=="异常"){
					errHtml=errHtml+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="已完成"){
					finHtml=finHtml+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="运行中"){
					runHtml=runHtml+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="未执行"){
					waitHtml=waitHtml+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}
//				html=html+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList("+arr[2]+")\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
			}else{
				if(getOSysState(arr[3],arr[4])=="异常"){
					errHtml=errHtml+"<li class=\""+style(arr[3],arr[4])+"  \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="已完成"){
					finHtml=finHtml+"<li class=\""+style(arr[3],arr[4])+"  \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="运行中"){
					runHtml=runHtml+"<li class=\""+style(arr[3],arr[4])+" \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}else if(getOSysState(arr[3],arr[4])=="未执行"){
					waitHtml=waitHtml+"<li class=\""+style(arr[3],arr[4])+"  \"  onclick=\"getStepList('"+arr[2]+"')\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
				}
//				html=html+"<li class=\""+style(arr[3],arr[4])+"\"  onclick=\"getStepList("+arr[2]+")\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
			}
		}
		html=finHtml+html+errHtml+runHtml;
		$("#sysnameAndiswitchto").html(html);
	}
	getStepList1(selectId);
//		var ss ="";
////		$("#steplist").html(ss);
//		for (var i=0;i<stepList.length;i++) {
//			var iruninsid=stepList[i].iruninsid;
//			var istarttime=stepList[i].istarttime;
//			var iendtime=stepList[i].iendtime;
//			if(istarttime=="1970-01-01 08:00:00"){
//				istarttime="";
//			}
//			if(iendtime=="1970-01-01 08:00:00"){
//				iendtime="";
//			}
//			if(selectId==iruninsid){
//				ss =ss+
//				"<tr>"+
//				  "<td>"+
//					"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
//				  "</td>"+
//				  "<td>"+
//					"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
//				  "</td>"+
//				  "<td>"+
//					"<div class=\"ot_column03\">"+istarttime+"</div>"+
//				  "</td>"+
//				  "<td>"+
//					"<div class=\"ot_column04\">"+iendtime+"</div>"+
//				  "</td>"+
//				  "<td>"+
//					"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
//				  "</td>"+
//				  "<td>"+
//					"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
//				  "</td>"+
//			   "</tr>";
//			}
//		}
//		$("#steplist").html(ss);
	}

function getStepList1(iid){//iid是系统运行id
	console.log(iid);
	if(iid==0){
		return;
	}
//	if(selectId!=0&&selectId==iid){
//		selectId=0;
//	}else{
//		selectId=iid;
//	}
//	var html="";
//	for(var j=0;j<sysNameList.length;j++){
//		var arr=sysNameList[j].split(",");
//		if(selectId==arr[2]){
//			html=html+"<li class=\""+style(arr[3],arr[4])+" select_status \"  onclick=\"getStepList("+arr[2]+")\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
//		}else{
//			html=html+"<li class=\""+style(arr[3],arr[4])+"\"  onclick=\"getStepList("+arr[2]+")\"> <span></span> <span>"+arr[0]+"</span> <span style=\"height: 45px;width: 310px;overflow: hidden;\">"+arr[1]+"</span> </li>";
//		}
//	}
//	$("#sysnameAndiswitchto").html(html);
		var ss ="";
		var finss="";
		var runss="";
		var waitss="";
		var errss="";
//		$("#steplist").html(ss);
		for (var i=0;i<stepList.length;i++) {
			var iruninsid=stepList[i].iruninsid;
			var istarttime=stepList[i].istarttime;
			var iendtime=stepList[i].iendtime;
			var istate=stepList[i].istate;
			var iisfail=stepList[i].iisfail;
//			console.log(istarttime+","+iendtime);
			if(istarttime=="1970-01-01 08:00:00"){
				istarttime="";
			}
			if(iendtime=="1970-01-01 08:00:00"){
				iendtime="";
			}
//			console.log("kssj",stepList[i].istarttime);
//			console.log("jssj",stepList[i].iendtime);
			if(iid==iruninsid){
				if(stepIstate(istate,iisfail)=="异常"){
					errss=errss+
					"<tr>"+
					  "<td>"+
						"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column03\">"+istarttime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column04\">"+iendtime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
					  "</td>"+
				   "</tr>";
				}else if(stepIstate(istate,iisfail)=="运行中"){
					runss=runss+
					"<tr>"+
					  "<td>"+
						"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column03\">"+istarttime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column04\">"+iendtime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
					  "</td>"+
				   "</tr>";
				}else if(stepIstate(istate,iisfail)=="等待"){
					waitss=waitss+
					"<tr>"+
					  "<td>"+
						"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column03\">"+istarttime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column04\">"+iendtime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
					  "</td>"+
				   "</tr>";
				}else if(stepIstate(istate,iisfail)=="完成"){
					finss=finss+
					"<tr>"+
					  "<td>"+
						"<div class=\"ot_column01\">"+stepList[i].isysname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column02\">"+stepList[i].iactname+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column03\">"+istarttime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column04\">"+iendtime+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column05\">"+timeDifference(stepList[i].istarttime,stepList[i].iendtime)+"</div>"+
					  "</td>"+
					  "<td>"+
						"<div class=\"ot_column06\"><span class="+setStyle(stepList[i].istate,stepList[i].iisfail)+">"+stepIstate(stepList[i].istate,stepList[i].iisfail)+"</span></div>"+
					  "</td>"+
				   "</tr>";
				}
			}
		}
		ss=runss+errss+waitss+finss;
		$("#steplist").html(ss);
	}
//时间差
function timeDifference(istarttime,iendtime){
//	console.log(iendtime);
//	var date = new Date(time);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
//    var Y = date.getFullYear() + '-';
//    var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
//    var D = date.getDate() + ' ';
//    var h = date.getHours() + ':';
//    var m = date.getMinutes() + ':';
//    var s = date.getSeconds();
//    return Y+M+D+h+m+s;
//	istarttimg="2021-11-11 12:11:12";
//	iendtime="2021-11-11 13:12:12";
	if(istarttime=="1970-01-01 08:00:00"){
		return 0+"秒";
	}
	var a= istarttime.replace(new RegExp("-","gm"),"/");
//	console.log(a);
	var b= (new Date(a)).getTime();
	if(iendtime=="1970-01-01 08:00:00"){
//		alert(1);
		var q=itime.replace(new RegExp("-","gm"),"/");
		var d=(new Date(q)).getTime();
//		console.log(new Date());
//		console.log(d);
	}else{
		var c= iendtime.replace(new RegExp("-","gm"),"/");
		var d= (new Date(c)).getTime();
	}
//	console.log((d-b)/1000);
	var e=parseInt((d-b)/1000);//秒
//	console.log(e);
	var m=0;
	var h=0;
	if(e>60){
	    m=parseInt(e/60);
	    e=e%60;
	    if(m>=60){
	    	h=parseInt(m/60);
	    	m=m%60;
	    }
	}
	if(e<10){
		e="0"+e;
	}
	if(m<10){
		m="0"+m;
	}
	if(h<10){
		h="0"+h;
	}
	var time=h+"时"+m+"分"+e+"秒"
	return time;
}
function setStyle(istate,iisfail){//步骤的
	if(istate==0&&iisfail!=0&&iisfail!=-1){//((istate==2||istate==3||istate==0)&&(iisfail==1||iisfail==2||iisfail==3||iisfail==4))
		return "complete_orange";
	}else if(istate==0){
		return "complete_blue";
	}else if(istate==1){
		return "complete_gray";
	}else if(istate==2||istate==3){
		return "complete_green";
	}
}

function stepIstate(istate,iisfail){//步骤
	if(istate==0&&iisfail!=0&&iisfail!=-1){
		return '异常';
	}else if(istate==0){
		return '运行中';
	}else if(istate==1){
		return '等待';
	}else if(istate==2||istate==3){
		return '完成';
	}else{
		return '';
	}
}
function style(istate,iisfail){
	if(iisfail!=0&&istate==0){
		return "s_erred_status status_public_style";//s_failed_status status_public_style
	}else if(istate==1||istate==2){
		return "s_completed_status status_public_style";//s_completed_status status_public_style
	}else if(istate==4||istate==3){
		return "s_failed_status status_public_style";//s_failed_status status_public_style 
	}else if(istate==0){
		return "s_execute_status status_public_style";//s_execute_status status_public_style
	}
}
function getOSysState(istate,iisfail){//系统的参数
	if(iisfail!=0&&istate==0){
		return "异常";
	}else if(istate==1||istate==2){
		return "已完成";//
	}else if(istate==4||istate==3){
		return "未执行";
	}else if(istate==0){
		return "运行中";//
	}
}
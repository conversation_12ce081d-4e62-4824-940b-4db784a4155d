var grid_panel
var refreshObj;
var businessCenterStore;
var uid;
Ext.onReady(function() {
	var computerLimit = 30;

	Ext.define('Ext.chart.theme.CustomBlue', {
		extend : 'Ext.chart.theme.Base',
		constructor : function(config) {
			var titleLabel = {
				font : '12px 微软雅黑',
				fill : '#898989'
			}, axisLabel = {
				fill : '#898989',
				font : '12px 微软雅黑',
				spacing : 2,
				padding : 5
			};
			this.callParent([ Ext.apply({
				axis : {
					stroke : '#a6a6a6',
					'stroke-width' : 1
				},
				axisLabelLeft : axisLabel,
				axisLabelBottom : axisLabel,
				axisTitleLeft : titleLabel,
				axisTitleBottom : titleLabel
			}, config) ]);
		}
	});

//---------------------------业务系统---------------------------
	Ext.define ('businessSystemModel',
		{
			extend : 'Ext.data.Model',
			remoteSort : true,
			fields : [
				{
					name : 'centerid',
					type : 'string'
				},
				{
					name : 'centername',
					type : 'string'
				},
				{
					name : 'equipmentnum',
					type : 'long'
				},
				{
					name : 'normalnum',
					type : 'long'
				},
				{
					name : 'abnormalnum',
					type : 'long'
				},
				{
					name : 'noresultnum',
					type : 'long'
				},
				{
					name : 'readynum',
					type : 'long'
				},
				{
					name : 'checkitemnum',
					type : 'long'
				},
				{
					name : 'checkpointnum',
					type : 'long'
				}
			]
		});

	/** 业务系统列表数据源* */
	businessCenterStore = Ext.create ('Ext.data.Store',
		{
			autoLoad : true,
			remoteSort : false,
			model : 'businessSystemModel',
			pageSize : 30,
			proxy :
				{
					type : 'ajax',
					timeout: 600000,
					url : 'getKanbanCenterList.do',
					reader :
						{
							type : 'json',
							root : 'dataList',
							totalProperty : 'total'
						}
				},
		});

	var hcstatusStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "2",
			"name" : "全部"
		},{
			"id" : "1",
			"name" : "巡检异常"
		},{
			"id" : "0",
			"name" : "巡检正常"
		},{
			"id" : "3",
			"name" : "未巡检"
		}]
	});

	var hcstatusComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'cpstatus',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : ' 巡检状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '全部',
		store : hcstatusStore,
		width : '25%',
		labelAlign : 'right'
	});

	var timeText = Ext.create('Ext.form.TextField', {
		fieldLabel: '时间(秒)',
		labelAlign : 'right',
		labelWidth : 70,
		minValue :10,
		name: 'refreshTime',
		value: '60',
		width : '20%',
		xtype: 'textfield'
	});

	/** 业务系统列表Columns* */
	var gridColumns = [
		{
			text : '序号',
			width : 40,
			xtype : 'rownumberer'
		},
		{
			text : '物理中心ID',
			hidden : true,
			width : 200,
			dataIndex : 'centerid'
		},
		{
			text : '物理中心',
			renderer : turnDetailOne,
			sortable : true,
			minWidth : 180,
			dataIndex : 'centername',
			flex : 1
		},
		{
			text : '设备总数',
			width : 120,
			dataIndex : 'equipmentnum'
		},
		{
			text : '巡检正常',
			width : 120,
			dataIndex : 'normalnum'
		},
		{
			text : '巡检异常',
			width : 120,
			dataIndex : 'abnormalnum'
		},
		{
			text : '未巡检',
			width : 120,
			dataIndex : 'noresultnum'
		},
		{
			text : '巡检中',
			width : 120,
			dataIndex : 'readynum'
		},
		{
			text : '巡检项总数',
			width : 120,
			dataIndex : 'checkitemnum'
		},
		{
			text : '巡检点总数',
			width : 120,
			dataIndex : 'checkpointnum'
		}
	];

	function turnDetailOne(value, metaData, record) {
		return "<a style='cursor: pointer;'>" +value + "</a>";
	}

	grid_panel = Ext.create ('Ext.ux.ideal.grid.Panel',{
		height : '100%',
		region : 'center',
		store : businessCenterStore,
		border : false,
		padding : grid_space,
		cls:'customize_panel_back',
		columnLines : true,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		columns : gridColumns,
		collapsible : false,
		renderTo : 'strategykanbancenter',
		listeners :
			{
				itemclick : function (view, record, item, index, e, eOpts)
				{
					searchId = record.get("centerid");
					searchType = 'center'
					equipmentInfoStore.load ();
				}
			},
		dockedItems : [
			{
				xtype : 'toolbar',
				items : ['->'
					,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function(){
							businessCenterStore.load();
						}
					},timeText,{
						xtype : 'button',
						baseCls : 'Common_Btn',
						text : '刷新',
						width: 58,
						handler : function() {
							if(refreshObj){
								clearInterval(refreshObj);
							}
							var refreshTime = timeText.getValue();
							refreshObj = setInterval(refreshSwitchMonitor, parseInt(refreshTime)*1000);

							businessCenterStore.load({callback:mainLoadAfter});
						}
					}
				]
			}
		]
	});

	function refreshSwitchMonitor(){
		businessCenterStore.load({callback:mainLoadAfter});
	}

	refreshObj = setInterval(refreshSwitchMonitor, 60*1000);
	function mainLoadAfter(records, operation, success){
		for(var i=0;i<records.length;i++) {
			var record=records[i];
			if(record.get('centerid')==uid) {
				grid_panel.getSelectionModel().select(record,true);
			}
		}
	}
});

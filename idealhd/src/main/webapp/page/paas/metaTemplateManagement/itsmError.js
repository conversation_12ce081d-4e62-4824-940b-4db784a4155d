var gridPanel = null;
Ext.onReady(function() {
	
	Ext.create('Ext.window.MessageBox');
	Ext.tip.QuickTipManager.init();

	var failCount = Ext.create('Ext.form.DisplayField', {
		margin : '5',
		id : 'failCount',
		fieldLabel : '异常详情',
		width : 180,
		labelWidth : 120,
		editable:false,
		readOnly:true,
		xtype : 'displayfield',
		fieldStyle:'color:white;font-size:16px!important'
	});
	
	Ext.getCmp('failCount').setValue("用户不存在，请检查用户！");
	
//	gridPanel = Ext.create('Ext.grid.Panel',{
//		region : 'center',
////		id : 'cmpGridId',
//		border : false,
//		//columnLines : true,
////		viewConfig:{
////		enableTextSelection:true
////		},
//		dockedItems : [
//			{
//				xtype : 'toolbar',
//				dock : 'top',
//				items : [failCount]
//			} ],
//			autoScroll : true,
//			plugins : [ Ext.create(
//					'Ext.grid.plugin.CellEditing', {
//						clicksToEdit : 2
//					}) ]
//	});
	
	var mainPanel = Ext.create("Ext.panel.Panel", {
		layout : 'border',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		border : false,
		items : [ failCount ],
		renderTo : "itsmError_div"
	});

	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});
	contentPanel.getLoader().on("beforeload",
			function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
});

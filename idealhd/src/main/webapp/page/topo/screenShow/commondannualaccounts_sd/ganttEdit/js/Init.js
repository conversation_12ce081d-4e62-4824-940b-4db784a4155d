	// Specifies connection mode for touch devices (at least one should be true)
	var tapAndHoldStartsConnection = true;
	var showConnectorImg = true;
	var urlParams = (function(url) {
		var result = new Object();
		var idx = url.lastIndexOf('?');

		if (idx > 0) {
			var params = url.substring(idx + 1).split('&');

			for (var i = 0; i < params.length; i++) {
				idx = params[i].indexOf('=');

				if (idx > 0) {
					result[params[i].substring(0, idx)] = params[i]
							.substring(idx + 1);
				}
			}
		}

		return result;
	})(window.location.href);

	// Sets the base path, the UI language via URL param and configures the
	// supported languages to avoid 404s. The loading of all core language
	// resources is disabled as all required resources are in grapheditor.
	// properties. Note that in this example the loading of two resource
	// files (the special bundle and the default bundle) is disabled to
	// save a GET request. This requires that all resources be present in
	// each properties file since only one file is loaded.
	mxLoadResources = false;
	mxLanguage = urlParams['lang'];
	mxLanguages = [ 'de' ];

window.urlParams = urlParams || {};

// Public global variables
window.MAX_REQUEST_SIZE = window.MAX_REQUEST_SIZE  || 10485760;
window.MAX_AREA = window.MAX_AREA || 15000 * 15000;

// URLs for save and export
window.EXPORT_URL = window.EXPORT_URL || '/export';
window.SAVE_URL = window.SAVE_URL || '/save';
window.OPEN_URL = window.OPEN_URL || '/open';
window.RESOURCES_PATH = RESOURCES_PATH || 'resources';
window.RESOURCE_BASE = RESOURCE_BASE || window.RESOURCES_PATH + '/grapheditor';
window.STENCIL_PATH = STENCIL_PATH || 'stencils';
window.IMAGE_PATH = IMAGE_PATH || 'images';
window.STYLE_PATH = STYLE_PATH || 'styles';
window.CSS_PATH = CSS_PATH || 'styles';
window.OPEN_FORM = window.OPEN_FORM || BasePath+'/page/topo/topology/topoedit/open.html';

// Sets the base path, the UI language via URL param and configures the
// supported languages to avoid 404s. The loading of all core language
// resources is disabled as all required resources are in grapheditor.
// properties. Note that in this example the loading of two resource
// files (the special bundle and the default bundle) is disabled to
// save a GET request. This requires that all resources be present in
// each properties file since only one file is loaded.
window.mxBasePath = mxBasePath || '../../src';
window.mxLanguage = mxLanguage || urlParams['lang'];
window.mxLanguages = mxLanguages || ['de'];

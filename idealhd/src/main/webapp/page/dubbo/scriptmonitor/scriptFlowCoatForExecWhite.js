var scriptmonitor_store2ForExecForWhite;
var search_form2ForExecForWhite;
Ext
		.onReady(function() {
			destroyRubbish();
			var flag = 1; // 生产
			/*
			 * Ext.define('startUserModel', { extend : 'Ext.data.Model', fields : [{
			 * name : 'iid', type : 'string' }, { name : 'iusername', type :
			 * 'string' }]
			 * 
			 * });
			 */

			var stateStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
		        {
		            "id": "102",
		            "name": "运行"
		        },
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "终止"
		        } ]
			});

			search_form2ForExecForWhite = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				region : 'north',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					dock : 'top',
					items : [ {
						fieldLabel : '命令说明',
						labelWidth : 70,
						name : 'scriptName',
						labelAlign : 'right',
						width : '22%',
						xtype : 'textfield',
						value: filter_scriptNameForExecForWhite
					}, {
						fieldLabel : '执行状态',
						labelAlign : 'right',
						width : '16%',
						labelWidth : 70,
						name : 'state',
						displayField : 'name',
						valueField : 'id',
						store : stateStore,
						queryMode : 'local',
						listeners : {
							afterRender : function(combo) {
					               if(filter_stateForExecForWhite=='-1') {
										combo.setValue(stateStore.getAt(0).data.id);
									} else if(filter_stateForExecForWhite=='101'){
										combo.setValue(stateStore.getAt(1).data.id);
									} else if(filter_stateForExecForWhite=='102'){
										combo.setValue(stateStore.getAt(2).data.id);
									}  else if(filter_stateForExecForWhite=='30'){
										combo.setValue(stateStore.getAt(3).data.id);
									} else if(filter_stateForExecForWhite=='60'){
										combo.setValue(stateStore.getAt(4).data.id);
									}
					            }
						},
						// editable:false,
						xtype : 'combobox'
					}, {
						fieldLabel : '开始时间',
						xtype : 'datefield',
						labelAlign : 'right',
						width : '22%',
						labelWidth : 70,
						name : 'startTime',
						format : 'Y-m-d',
						value: filter_startTimeForExecForWhite
					}, {
						fieldLabel : '结束时间',
						xtype : 'datefield',
						labelAlign : 'right',
						width : '22%',
						labelWidth : 70,
						name : 'endTime',
						format : 'Y-m-d',
						value: filter_endTimeForExecForWhite
					}, {
						xtype : 'button',
						// columnWidth:.07,
						text : '查询',
						cls : 'Common_Btn',
						handler : function() {
							pageBar.moveFirst();
						}
					}, {
						xtype : 'button',
						// columnWidth:.07,
						text : '清空',
						cls : 'Common_Btn',
						handler : function() {
							clearQueryWhere();
						}
					},
		            {
		                xtype: 'button',
		                text: '返回',
		                cls: 'Common_Btn',
		                handler: function() {
		                	forwardtestmainExec2ForExecForWhite();
		                }
		            } 
					]
				} ]
			});

			Ext.define('scriptmonitorData', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'taskName',
					type : 'string'
				}, {
					name : 'state',
					type : 'int'
				}, {
					name : 'cata',
					type : 'int'
				}, {
		        	name: 'flowId',
		        	type: 'int'
		        },
		        {
		        	name: 'actNo',
		        	type: 'int'
		        }, {
					name : 'startUser',
					type : 'string'
				}, {
					name : 'startTime',
					type : 'string'
				}, {
					name : 'endTime',
					type : 'string'
				}, {
					name : 'actType',
					type : 'string'
				},
				{
		        	name: 'actName',
		        	type: 'string'
		        },
		        {
		        	name: 'childFlowId',
		        	type: 'int'
		        },
		        {
					name : 'serverNum',
					type : 'int'
				},{name: 'scriptContent',     type: 'string'} ]
			});

			scriptmonitor_store2ForExecForWhite = Ext.create('Ext.data.Store', {
				autoLoad : true,
				pageSize : 50,
				model : 'scriptmonitorData',
				proxy : {
					type : 'ajax',
					url : 'getScriptCoatList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			var scriptmonitor_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '执行状态',
						dataIndex : 'state',
						width : 100,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == -1) {
				                backValue = '<span class="Not_running State_Color">未运行</span>';
				            } else if (value == 10 || value == 11) {
								backValue = '<span class="Run_Green State_Color">运行</span>';
							} else if (value == 20 || value == 5) {
								backValue = "<span class='Complete_Green State_Color'>完成</span>"
							} else if (value == 30) {
								backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
							} else if (value == 40) {
								backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
							} else if (value == 50) {
								backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
							} else if (value == 60) {
								backValue = '<span class="Kill_red State_Color">已终止</span>';
							}
							return backValue;
						}
					},
					{
						text : '实例主键',
						dataIndex : 'iid',
						hidden : true
					},
					{
						text : 'actType',
						dataIndex : 'actType',
						hidden : true
					},
//					{
//				    	text: '活动名称',
//				    	dataIndex: 'actName',
//				    	width : 100,
//				    },
					{
						text : '命令说明',
						dataIndex : 'scriptName',
						width : 100,
					},{
						text : '命令内容',
						dataIndex : 'scriptContent',
						flex : 1
					},
					{
						text : '任务名称',
						dataIndex : 'taskName',
						width : 150
					},
					{
						text : '启动人',
						dataIndex : 'startUser',
						width : 150
					},
					{
						text : '开始时间',
						dataIndex : 'startTime',
						width : 150
					},
					{
						text : '结束时间',
						dataIndex : 'endTime',
						width : 150
					},
					{
						text : '操作',
						dataIndex : 'sysOperation',
						width : 160,
						renderer : function(value, p, record) {
							var coatid = record.get('iid');
							var cata = 1;// record.get('cata');
							var actType = record.get('actType');
							var actNo = record.get('actNo');
				            var flowId = record.get('flowId');
				            var state = record.get('state');
				            var childFlowId = record.get('childFlowId');
							
								return '<span class="switch_span">'
										+ '<a href="javascript:void(0)" onclick="forwardruninfo2ForExecForWhite('
										+ coatid
										+ ', '
										+ cata
										+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'
										+
										'<a href="javascript:void(0)" onclick="resultExport2ForExecForWhite('
										+ coatid
										+ ', '
										+ cata
										+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>'
										+ '</span>';

						}
					} ];

			scriptmonitor_store2ForExecForWhite.on('beforeload',
					function(store, options) {
						var new_params = {
							 flowId:flowId2ForExecForWhite,
							 scriptName:search_form2ForExecForWhite.getForm().findField("scriptName").getValue(),
							// startUser:search_form2ForExecForWhite.getForm().findField("startUser").getValue(),
							state : search_form2ForExecForWhite.getForm().findField("state").getValue(),
							cata : 1,// search_form2ForExecForWhite.getForm().findField("cata").getValue(),
							startTime :search_form2ForExecForWhite.getForm().findField("startTime").getValue(),
							endTime : search_form2ForExecForWhite.getForm().findField("endTime").getValue()/*,
							forScriptFlow: 1*/
						};

						Ext.apply(scriptmonitor_store2ForExecForWhite.proxy.extraParams,
								new_params);
					});

			var pageBar = Ext.create('Ext.PagingToolbar', {
				store : scriptmonitor_store2ForExecForWhite,
				dock : 'bottom',
				displayInfo : true
			});

			var scriptmonitor_grid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : scriptmonitor_store2ForExecForWhite,
				border : false,
				columnLines : true,
				columns : scriptmonitor_columns,
				cls:'sc_tab_height',
				bbar : pageBar
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptflowcoatmonitorForExecWhite_area",
				border : true,
				layout : 'border',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight()-modelHeigth,
				items : [ search_form2ForExecForWhite, scriptmonitor_grid ]
			});

			function clearQueryWhere() {
				search_form2ForExecForWhite.getForm().findField("scriptName").setValue(''),
				// search_form2ForExecForWhite.getForm().findField("startUser").setValue(''),
				search_form2ForExecForWhite.getForm().findField("state").setValue("-1"),
				// search_form2ForExecForWhite.getForm().findField("cata").setValue("-1"),
				search_form2ForExecForWhite.getForm().findField("startTime").setValue(''),
				search_form2ForExecForWhite.getForm().findField("endTime").setValue('');
			}

			contentPanel.on('resize', function() {
				mainPanel.setWidth(contentPanel.getWidth());
				mainPanel.setHeight(contentPanel.setHeight()-modelHeigth);
			});
		});


function forwardtestmainExec2ForExecForWhite() {
	   contentPanel.getLoader().load({
	        url: "forwardwhitescriptcoat.do",
	        scripts: true,
	         params: {
    			filter_serviceName:filter_serviceNameForExecForWhite,
			    filter_serviceState:filter_serviceStateForExecForWhtie,
				filter_serviceStartTime:filter_serviceStartTimeForExecForWhite,
				filter_serviceEndTime:filter_serviceEndTimeForExecForWhite
            }
	    });
}
function forwardruninfo2ForExecForWhite(coatid, flag) {
	var scriptName=search_form2ForExecForWhite.getForm().findField("scriptName").getValue();
	var state =search_form2ForExecForWhite.getForm().findField("state").getValue();
	var startTime = search_form2ForExecForWhite.getForm().findField("startTime").getRawValue();
	var endTime =search_form2ForExecForWhite.getForm().findField("endTime").getRawValue();
	contentPanel.getLoader().load({
		url : "forwardscriptserverForFlowForWhite.do",
		scripts : true,
		params : {
			flowId:flowId2ForExecForWhite, 
			forScriptFlow: 1,
			coatid : coatid,
			flag : flag,
			filter_scriptName:scriptName,
			filter_state:state,
			filter_startTime:startTime,
			filter_endTime:endTime,
			filter_serviceName:filter_serviceNameForExecForWhite,
			filter_serviceState:filter_serviceStateForExecForWhtie,
			filter_serviceStartTime:filter_serviceStartTimeForExecForWhite,
			filter_serviceEndTime:filter_serviceEndTimeForExecForWhite
		}
	});
}
function resultExport2ForExecForWhite(coatid, flag) {
	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag='
			+ flag;
}

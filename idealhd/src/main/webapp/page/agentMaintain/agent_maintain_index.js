var agentId;
var execActWin;
var installMsg_window;
var  install_window;
var startstop_window;
var sysWin="";
var iid_du=0;
var agentStore;
var selModel;
var refreshObj;
var resultStore;
var  agentStartStopResultWindow;
var cord=[];
var netScanMsg_window;
var  netScan_window;
Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();

	var agentSelected=new Ext.util.MixedCollection();
	var win;
	var ftpbindwin;
	var countdown = 18;
    Ext.apply(Ext.form.VTypes, {
        'numbers' : function (_v) {
            if (/^\d+$/.test(_v)) {// 判断必须是数字
                return true;
            }
            return false;
        },
        numbersText : '请输入数字', // 出错信息后的默认提示信息
    });

    Ext.apply(Ext.form.VTypes, {
        'numbersForKb' : function (_v,filed) {
            if (/^\d+$/.test(_v)) {// 判断必须是数字
                return true;
            }
            return false;
        },
        numbersForKbText : '请输入正整数', // 出错信息后的默认提示信息
    });

    // 锦州代码迁移 新增简单查看页面
    // 所属系统下拉框数据源Model
    Ext.define('systemModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'i_system_id',
            type : 'string'
        } ]
    });
    // 所属系统下拉框数据源
    var cstore_isystem = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        model : 'systemModel',
        proxy : {
            type : 'ajax',
            timeout : 300000,
            url : 'queryHomeInfo.do?action=jzQueryHomeInfo',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
    // 所属系统下拉框
    var sysoper_combobox_isystem = Ext.create('Ext.form.field.ComboBox', {
        width : '25%',
        labelWidth : 79,
        padding : '0 10 0 0',
//		fieldLabel : '所属系统',
        displayField : 'i_system_id',
        valueField : 'i_system_id',
        editable : true,
        typeAhead : true,
        store : cstore_isystem,
        queryMode : 'local',
        labelAlign : "right",
        listeners : {
            select : function() {
                sysoper_combobox_datadate.setValue('');
                var fjnxSysName = this.value;
                cstore_datadate.load({
                    params : {
                        fjnxSysName : fjnxSysName
                    }
                });
            },
            beforequery : function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store
                        .filterBy(function(record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(
                                value.toLowerCase()) != -1);
                        });
                    combo.expand();
                    return false;
                }
            }
        }
    });

	Ext.define('fjnxSystemModel', {
		extend : 'Ext.data.Model',
		fields : [
			{name : 'systemid', type : 'long'},
			{name : 'systemname', type : 'string'}
		]
	});
	/**福建农信业务系统下拉数据源**/
	var fjnxSystemStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'fjnxSystemModel',
		proxy: {
			type: 'ajax',
			url: 'queryCiTaskBussName.do',
			timeout:1800000,
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	Ext.define('fjnxOsModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'agentOsName',
			type : 'string'
		} ]
	});

	/**福建农信操作系统下拉数据源**/
	var fjnxOsNameStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'fjnxOsModel',
		proxy: {
			type: 'ajax',
			url: 'getOsNameFromAgent.do',
			timeout:1800000,
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	// fjnxOsNameStore.on('beforeload', function(store, options) {
	// 	var new_params = {
	// 	};
	// 	Ext.apply(fjnxOsNameStore.proxy.extraParams, new_params);
	// });

	fjnxSystemStore.on('beforeload', function(store, options) {
		var new_params = {
		};
		Ext.apply(fjnxSystemStore.proxy.extraParams, new_params);
	});

	Ext.define('fjnxEnvNameModel', {
		extend : 'Ext.data.Model',
		fields : [
			{name : 'iid', type : 'string'},
			{name : 'name', type : 'string'}
		]
	});
	/**福建农信环境下拉数据源**/
	var fjnxEnvNameStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'fjnxEnvNameModel',
		proxy: {
			type: 'ajax',
			url: 'getEnvByProjectId.do',
			timeout:1800000,
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	fjnxEnvNameStore.on('beforeload', function(store, options) {
		var new_params = {
			projectId:0
		};
		Ext.apply(fjnxEnvNameStore.proxy.extraParams, new_params);
	});


	//Device or virtual machine  设备或者虚拟机 筛选条件 数据源 jiaMing 2023-5-17
	Ext.define('DeviViMachModel', {
		extend: 'Ext.data.Model',
		fields: [
			{name: 'name', type: 'string'},
			{name: 'id',  type: 'string'}
		]
	});
	var deviViMachStore = Ext.create('Ext.data.Store', {
		model: 'DeviViMachModel',
		data : [
			//{name: '全部',   id: '-1'},
			{name: '物理机', id: '1'},
			{name: '虚拟机', id: '2'},
			{name: '容器',   id: '3'}
		]
	});

	Ext.define('AgentStateModel', {
		extend: 'Ext.data.Model',
		fields: [
		         {name: 'name', type: 'string'},
		         {name: 'id',  type: 'string'}
		         ]
	});

	var stateStore = Ext.create('Ext.data.Store', {
		model: 'AgentStateModel',
		data : [
		        {name: '新建',    id: '-1'},
		        {name: '正常',    id: '0'},
		        {name: '异常', id: '1'},
		        {name: '升级中', id: '2'}/*,
			    {name: '禁用', id: '7'}*/
		        // {name: '未知', id: '996'}
		        ]
	});

	var cidStore = Ext.create('Ext.data.Store', {
		fields : [ 'id','name' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCidCenterList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	if(agentPauseRecoverSwitch){
		stateStore.add({ name:'暂停',  id:'4' });
	}
	if(isFJNX){
		stateStore.add({ name:'下线',  id:'9' });
	}

	Ext.define('sysNameModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'proId',
			type : 'long'
		}, {
			name : 'fjnxSysName',
			type : 'string'
		} ]

	});

	Ext.define('proTypeModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'proId',
			type : 'long'
		}, {
			name : 'proName',
			type : 'string'
		} ]

	});

	var sysNameStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		model : 'sysNameModel',
		proxy : {
			type : 'ajax',
			url : 'getSysName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var query_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		border : false,
		baseCls:'customize_gray_back',
		dockedItems:[{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [{
				fieldLabel: '名称',
				labelWidth: 90,
				width : '18%',
				name: 'iagentName',
				xtype: 'textfield',
				hidden: isFJNX,
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},
				{ fieldLabel: isFJNX ? 'IP地址':'地址',
					labelWidth: 90,
					width : '18%',
					name: 'iagentIp',
					xtype: 'textfield',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},
                // {
				// 	fieldLabel: '操作系统',
				// 	labelWidth: 90,
				// 	width : '18%',
				// 	name: 'iagentosname',
				// 	xtype: 'textfield',
				// 	disabled: true ,
				// 	hidden: true ,
				// 	listeners: {
				// 		specialkey: function(field,e){
				// 			if (e.getKey()==Ext.EventObject.ENTER){
				// 				grid_panel.ipage.moveFirst();
				// 			}
				// 		}
				// 	}
				// },
				{
					labelWidth : 90,
					width: '18%',
					xtype :isFJNX ? 'combo':'textfield',
					fieldLabel : '操作系统',
					queryMode: 'local',
					name : 'iagentosname',
					anyMatch : true,
					triggerAction : "all",
					store: fjnxOsNameStore,
					displayField: 'agentOsName',
					valueField: 'agentOsName',
					listConfig: {minHeight: 26} ,
                    listeners: {
                        specialkey: function(field,e){
                            if (e.getKey()==Ext.EventObject.ENTER){
                                grid_panel.ipage.moveFirst();
                            }
                        }
                    }
				},{
					fieldLabel: isFJNX ? '主机名':'计算机名',
					labelWidth: 90,
					width : '18%',
					name: 'iagentcomputername',
					hidden:lnzgyhfhSwitch,
					xtype: 'textfield',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},{
					fieldLabel: 'proxy名称',
					labelWidth: 90,
					width : '18%',
					name: 'proxyName',
					hidden :!isHSBank,//徽商将proxy名称调整到计算机名后面
					xtype: 'textfield',
				},{
					fieldLabel: '自定义详情',
					labelWidth: 90,
					width : '18%',
					name: 'icustommess',
					hidden : jobSchedulingDbbackSwitch||lnzgyhfhSwitch||isFJNX||isHSBank,
					xtype: 'textfield',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				}]
		},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			hidden:isignornewagentinfo,
			dock : 'top',
			items :[{
				fieldLabel: '信息系统名称',
				labelWidth: 90,
				width : '18%',
				name: 'systeminfo',
				hidden:isignornewagentinfo||lnzgyhfhSwitch||isFJNX,
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '所属区域',
				labelWidth: 90,
				width : '18%',
				name: 'centername',
				hidden:isignornewagentinfo||lnzgyhfhSwitch||isFJNX,
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '系统管理员A',
				labelWidth: 90,
				width : '18%',
				name: 'sysadmin_a',
				hidden:isignornewagentinfo||lnzgyhfhSwitch||isFJNX,
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '应用管理员A',
				labelWidth: 90,
				width : '18%',
				name: 'appadmin_a',
				hidden:isignornewagentinfo||lnzgyhfhSwitch||isFJNX,
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			}]
		},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items :[{
				fieldLabel: '状态',
				labelWidth: 90,
				width : '18%',
				name: 'iagentState',
				displayField: 'name',
				valueField: 'id',
				store: stateStore,
				queryMode: 'local',
				editable:false,
				xtype: 'combobox',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '版本',
				labelWidth: 90,
				width : '18%',
				name: 'iagentversion',
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '所属团队',
				labelWidth: 90,
				width : '18%',
				name: 'iteam',
				xtype: 'textfield',
				hidden : !lnzgyhfhSwitch||isFJNX,
			},{
				fieldLabel: '纳管用户',
				labelWidth: 90,
				width : '18%',
				hidden : jobSchedulingDbbackSwitch||lnzgyhfhSwitch||isFJNX,
				name: 'icreateuser',
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '开始时间',
				xtype: 'datefield',
				labelWidth: 90,
				width : '18%',
				name: 'istarttime',
				hidden : jobSchedulingDbbackSwitch||lnzgyhfhSwitch||isFJNX,
				format: 'Y-m-d H:i:s',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: '结束时间',
				xtype: 'datefield',
				labelWidth: 90,
				hidden : jobSchedulingDbbackSwitch||lnzgyhfhSwitch||isFJNX,
				width : '18%',
				name: 'iendtime',
				format: 'Y-m-d H:i:s',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			}]
		},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items :[{
				fieldLabel: 'os_smalltype',
				labelWidth: 90,
				width : '18%',
				hidden :!isght||lnzgyhfhSwitch||isFJNX,
				name: 'osSmalltype',
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			},{
				fieldLabel: isFJNX ? '用途':'Agent描述',
				labelWidth: 90,
				width : '18%',
				hidden :isignoreagentdesc||lnzgyhfhSwitch||isHSBank,
				name: 'iagentDesc',
				xtype: 'textfield'
				//	hidden: true
			},{
				labelWidth : 90,
				width: '18%',
				xtype : 'combo',
				fieldLabel : '业务系统',
				queryMode: 'local',
				name : 'fjnxSysName',
				anyMatch : true,
				triggerAction : "all",
				store: fjnxSystemStore,
				displayField: 'systemname',
				valueField: 'systemname',
				hidden:!isFJNX || isHSBank,
				listConfig: {minHeight: 26}
			},{
				labelWidth : 90,
				width: '18%',
				xtype : 'combo',
				fieldLabel : '环境',
				queryMode: 'local',
				name : 'fjnxEnvName',
				anyMatch : true,
				triggerAction : "all",
				store: fjnxEnvNameStore,
				displayField: 'name',
				valueField: 'name',
				hidden:!isFJNX,
				listConfig: {minHeight: 26}
			},{
				fieldLabel: 'proxy名称',
				labelWidth: 90,
				width : '18%',
				name: 'proxyName',
				hidden :lnzgyhfhSwitch||isFJNX||isHSBank,
				xtype: 'textfield',
				//	hidden: true
			},{
				fieldLabel: '服务名称',
				labelWidth: 60,
				width : '20%',
				name: 'serverName',
				xtype: 'textfield',
				hidden :lnzgyhfhSwitch||isFJNX||isHSBank,
				//	hidden: true
			},{
				fieldLabel: '设备或者虚拟机',
				labelWidth: 110,
				width : '18%',
				name: 'iequipmentOrVmId',
				displayField: 'name',
				valueField: 'id',
				store: deviViMachStore,
				queryMode: 'local',
				hidden :lnzgyhfhSwitch||isFJNX||isHSBank,
				editable:false,
				xtype: 'combobox',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			}]},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			hidden:!ismustdcid||isFJNX,
			dock : 'top',
			items :[{
				fieldLabel: '数据中心',
				labelWidth: 90,
				width : '18%',
				name: 'idcid',
				displayField: 'name',
				valueField: 'id',
				store: cidStore,
				queryMode: 'local',
				editable:true,
				xtype: 'combobox',
				emptyText : "请选择数据中心",
				triggerAction : "all",
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							grid_panel.ipage.moveFirst();
						}
					}
				}
			}]
		}
			,{
				baseCls:'customize_gray_back',
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [{
					xtype: 'button',
					cls:'Common_Btn',
					id:'tongbu',
					text: '同步',
					handler: syncAgent
				}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: '编辑Agent',
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: '增加',
								handler: function() {
									if(isFJNX){
										agentStore.insert(0, new AgentModel({iagentport:'15000',isSSL:'1',iagentstate:'-1'}));
									}else if(isHSBank){
										agentStore.insert(0, new AgentModel({iagentport:'18000',iagentstate:'-1'}));
									}else{
										agentStore.insert(0, new AgentModel({iagentport:'15000',iagentstate:'-1'}));
									}
									cellEditing.startEditByPosition({
										row: 0,
										column: 0
									});
								}
							},{
								text: '保存',
								handler: saveAgent
							},{
								text: '暂停',
								hidden : !agentPauseRecoverSwitch,
//						id : 'agentPauseButton',
								handler : pauseAgent
							},{
								text: '恢复',
								hidden : !agentPauseRecoverSwitch,
//						id : 'agentRecoverButton',
								handler : recoverAgent
							},{
								text: '删除',
								handler: deleteAgent
							},{
								text: '自定义命令',
								handler: icustomCMD
							}]
						})
					}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: 'Excel',
						hidden : jobSchedulingDbbackSwitch?true:false,
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: '下载导入模板',
								handler: function() {
									if(lnzgyhfhSwitch){
										window.location.href = 'excelTemplateDownload.do?fileName=AGETN_INFO_TEMPLATE.xls';
									} else{
										if(isHSBank){
											showExportTypeWindow('downloadAgentInfoTemplate.do');
										}else{
											window.location.href = 'downloadAgentInfoTemplate.do';
										}

									}
								}
							},{
								text: '导入',
								handler: uploadExcel
							},{
								text: '导出',
								handler : function() {


									var record = grid_panel.getSelectionModel().getSelection();
									var ids=[];
									if(record.length!=0){
										Ext.each(record,function(item){
											if(item.data.iid != 0){
												ids.push(item.data.iid);
												ids.join(',')
											}
										});
									}
									var iagentName= query_form.getForm().findField("iagentName").getValue();
									var iagentIp= query_form.getForm().findField("iagentIp").getValue();
									var iagentosname=query_form.getForm().findField("iagentosname").getValue();
									var iagentcomputername=query_form.getForm().findField("iagentcomputername").getValue();
									var icustommess=query_form.getForm().findField("icustommess").getValue();
									var	iagentDesc= query_form.getForm().findField("iagentDesc").getValue();
									var	iagentState= query_form.getForm().findField("iagentState").getValue();
									var	iagentversion=query_form.getForm().findField("iagentversion").getValue();
									var	icreateuser= query_form.getForm().findField("icreateuser").getValue();
									var	istarttime= query_form.getForm().findField("istarttime").getValue();
									var	iendtime= query_form.getForm().findField("iendtime").getValue();
									var	os_smalltype= query_form.getForm().findField("osSmalltype").getValue();
									var	proxyName= query_form.getForm().findField("proxyName").getValue();
									var	iequipmentOrVmId= query_form.getForm().findField("iequipmentOrVmId").getValue();
									var	serverName= query_form.getForm().findField("serverName").getValue();
									var date = new Date(istarttime);
									var timeStar = date.getTime();
									var datea = new Date(iendtime);
									var timeEnd = datea.getTime();
									if(ids.length>0){
										if(isHSBank){
											showExportTypeWindow('exportIPAll.do?id='+ids);
										}else{
											window.location.href = 'exportIPAll.do?id='+ids;
										}
										return;
									}
									var url = 'exportIPAll.do?iagentName='+iagentName+'&iagentIp='+iagentIp+'&iagentosname='+iagentosname
										+'&iagentcomputername='+iagentcomputername+'&icustommess='+icustommess
										+'&iagentState='+iagentState+""
										+'&iagentDesc='+iagentDesc
										+'&iagentversion='+iagentversion
										+'&istarttime='+timeStar+'&iendtime='+timeEnd
										+'&os_smalltype='+os_smalltype
										+'&proxyName='+proxyName
										+'&createuser='+icreateuser
										+'&iequipmentOrVmId='+iequipmentOrVmId
										+'&serverName='+serverName;
									if(isHSBank){
										showExportTypeWindow(url);
									}else{
										window.location.href = url;
									}
								}
							},{
								text: '导入应用标识',
								handler: uploadExcel2
							},{
								text: '导出应用标识',
								handler : function() {
									var record = grid_panel.getSelectionModel().getSelection();
									var ids=[];
									if(record.length!=0){
										Ext.each(record,function(item){
											if(item.data.iid != 0){
												ids.push(item.data.iid);
												ids.join(',')
											}
										});
									}
									if(isHSBank){
										showExportTypeWindow('exportIsystemTypeExcel.do?ids='+ids);
									}else{
										window.location.href = 'exportIsystemTypeExcel.do?ids='+ids;
									}
								}
							},{
								text: '导入环境类型',
								handler: uploadExcelEnv
							},{
								text: '导出环境类型',
								handler : function() {
									var record = grid_panel.getSelectionModel().getSelection();
									var ids=[];
									if(record.length!=0){
										Ext.each(record,function(item){
											if(item.data.iid != 0){
												ids.push(item.data.iid);
												ids.join(',')
											}
										});
									}
									window.location.href = 'exportIEnvTypeExcel.do?ids='+ids;
								}
							},{
								text: '导入描述',
								hidden: isFJNX,
								handler: uploadExcel3
							}
							,{
								text: '导出描述模板',
								hidden: isFJNX,
								handler : function() {
									var record = grid_panel.getSelectionModel().getSelection();
									var ids=[];
									if(record.length!=0){
										Ext.each(record,function(item){
											if(item.data.iid != 0){
												ids.push(item.data.iid);
												ids.join(',')
											}
										});
									}
									if(isHSBank){
										showExportTypeWindow('exportIsystemTypeExceldes.do?ids='+ids);
									}else{
										window.location.href = 'exportIsystemTypeExcel.do?ids='+ids;
									}
								}
							}]
						})
					}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: '安装升级',
						hidden : jobSchedulingDbbackSwitch?true:jobSchedulingQuerySystemSwitch?true:false||isFJNX,
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: '配置',
								handler: bindFTP
							},{
								text: '安装',
								//text: isMinShengBank? '远程安装':'安装',
								//hidden: true,
								handler: installAgent
							},/*{
								text: '本地安装',
								hidden: !isMinShengBank,
								handler: localinstall
							},*/{
								text: '批量启停',
								handler: batchStartStopAgent
							},{
								text: 'Excel批量启停',
								handler: batchStartStopAgentExcel
							}
								,{
									text: '批量启停(守护进程)',
									handler: batchStartStopAgentDaemons
								},{
									text: '模板下载(批量安装或批量卸载)',
									handler: function() {
										if(isHSBank){
											var exportTypeWindow = new Ext.window.Window({
												title: '选择导出类型',
												modal: true,
												layout: 'fit',
												width: 350,
												height: 250,
												items: [{
													xtype: 'form',
													bodyPadding: 10,
													items: [{
														xtype: 'radiofield',
														boxLabel: 'Excel格式',
														name: 'exportType',
														inputValue: 'xls',
														checked: true // 默认选中
													}, {
														xtype: 'radiofield',
														boxLabel: 'WPS格式',
														name: 'exportType',
														inputValue: 'et'
													}]
												}],
												buttons: [{
													text: '确定',
													handler: function () {
														var form = this.up('window').down('form').getForm();
														if (form.isValid()) {
															var exportType = form.findField('exportType').getGroupValue();
															// 调用导出函数
															if (exportType === 'xls') {
																window.location.href = 'excelTemplateDownload.do?fileName=BATCH_INSTALL_AND_UNINSTALL.xlsx';
															}else{
																window.location.href = 'excelTemplateDownload.do?fileName=BATCH_INSTALL_AND_UNINSTALL.et';
															}
															exportTypeWindow.close(); // 关闭弹窗
														}
													}
												}, {
													text: '取消',
													handler: function () {
														exportTypeWindow.close(); // 关闭弹窗
													}
												}]
											});
											exportTypeWindow.show(); // 关闭弹窗
										}else{
											window.location.href = 'excelTemplateDownload.do?fileName=BATCH_INSTALL_AND_UNINSTALL.xlsx';
										}
									}
								}
								,{
									text: isMinShengBank? '导入安装':'批量安装',
									handler: batchInstallAgent
								},{
									text: '批量卸载',
									handler: batchUnloadAgent
								},{
									text: '升级',
									handler: upgradeAgent
								},{
									text: '批量升级配置',
									handler: batchUpgradeAgentConfigure
								},{
									text: '批量升级',
									handler: batchUpgradeAgent
								},{
									text: '升级监控',
									handler: upgradeAgentMonitor
								},{
									text: '清除升级标记',
									handler: clearUpgradeFlag
								},{
									text: '安装结果',
									handler: installResult
								},{
									text: '卸载结果',
									handler: unloadResult
								},{
									text: '一键下线',
									hidden:!oneClickOffline,
									handler: OneClickOffline
								}]
						})
					}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: '获取信息',
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: '获取VCS信息',
								hidden: true,
								handler: oneKey
							},{
								text: '获取全部VCS信息',
								hidden: true,
								handler: allKey
							},{
								text: '获取Agent信息',
								handler: fetchAgentInfoQuery
							},{
								text: '获取全部Agent信息',
								handler: fetchAgentInfo
							}]
						})
					}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: '停止agent',
						hidden: true,
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: 'agent停止',
								handler: stopAgentInfoQuery
							},{
								text: '停止全部Agent',
								handler: stopAgentInfo
							}]
						})
					}
					,{
						xtype: 'splitbutton',
						cls : 'Common_Btn2',
						textAlign:'center',
						text: '报文配置',
						hidden : true,

						//hidden : jobSchedulingDbbackSwitch?true:jobSchedulingQuerySystemSwitch?true:false,
						handler : function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain : true,
							items: [{
								text: '发送报文',
								handler: function() {
									sendMsgCfg(0);
								}
							},{
								text: '取消发送',
								handler: function() {
									sendMsgCfg(1);
								}
							}]
						})
					}
					,{
						xtype: 'button',
						cls : 'Common_Btn',
						text: '获取主机列表',
						hidden:!isGetHostList||isFJNX,
						handler: newFunction
					},
					{
						text: '刷新',
						hidden : true,
						icon: '',
						cls: 'Common_Btn',
						handler: function() {
							agentStore.reload();
						}
					},
					{
						text: '批量启停监控',
						xtype: 'button',
						cls : 'Common_Btn',
						hidden:lnzgyhfhSwitch||isFJNX,
						handler: function() {
							showStartStopResult();
						}
					}
					,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : 'cmdb信息同步',
						hidden:isignornewagentinfo||lnzgyhfhSwitch||isFJNX,
						handler : cmdbsync
					}
					,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '网段扫描',
						hidden:!isNetScanSwitch||isFJNX,
						handler : netscan
					}
					,{
						text: '清空选择',
						xtype: 'button',
						cls : 'Common_Btn',
						handler: function() {
							agentSelected.clear();
							grid_panel.getSelectionModel().deselectAll();
						}
					}
					,{
						text: '卸载',
						xtype: 'button',
						hidden: !isMinShengBank||isFJNX,
						cls : 'Common_Btn',
						handler: function() {
							var data=[]
							agentSelected.each(function(record,ind,len){
								data.push(record);
							});
							if (data.length == 0) {
								Ext.Msg.alert('提示', '请先选择您要卸载的记录!');
								return;
							} else {
								var msgContent = "";
								msgContent += "<ol>";
								msgContent += "<li>";
								msgContent += "即将执行卸载任务";
								msgContent += "</li>";
								msgContent += "<li>";
								msgContent += "请再次确认选中的Agent是否正确！";
								msgContent += "</li>";
								msgContent += "<ol>";
								Ext.Msg.show({
									title : '请认真阅读以下提示',
									msg : msgContent,//Ext.util.Format.htmlDecode(action.result.MESSAGE),
									buttons : Ext.Msg.OKCANCEL,
									icon : Ext.MessageBox.INFO,
									width : 500,
									fn: function(btn) {
										if (btn == "ok") {
											var jsonData = "[";
											for (var i = 0, len = data.length; i < len; i++) {
												var ss = Ext.JSON.encode(data[i].data);
												if (i == 0) jsonData = jsonData + ss;
												else jsonData = jsonData + "," + ss;
											}
											jsonData = jsonData + "]";

											console.log(jsonData);
											var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"卸载中..."});
											checkMask.show();
											Ext.Ajax.request({
												url: 'uninstallAgent.do',
												method: 'POST',
												params: {
													jsonData: jsonData
												},
												success: function (response, request) {
													checkMask.hide();
													var success = Ext.decode(response.responseText).success;
													var message = Ext.decode(response.responseText).message;
													Ext.Msg.alert('提示', message);
													agentStore.reload();
												}
											})
										}
									}
								});
							}
						}
					}
					,{
						text: 'CMDB同步',
						xtype: 'splitbutton',
						cls:'Common_Btn2',
						textAlign: 'center',
						hidden:!czFlag||isFJNX,
						handler: function() {
							this.showMenu();
						},
						menu: new Ext.menu.Menu({
							plain: true,
							items: [{
								text: '手动同步',
								id: 'cztongbu',
								handler: function() {
									syncCmdb();
								}
							},{
								text : '定时同步',
								handler: function() {
									var oneKeySwitchWindow1;
									var oneKeySwitchForm1 = Ext.create('Ext.form.Panel',{
										border: false,
										buttonAlign: 'center',
										dockedItems: [{
											xtype: 'toolbar',
											dock: 'top',
											items: [{
												xtype: 'textfield',
												width: '80%',
												fieldLabel: '执行周期',
												emptyText: '--请选择执行周期--',
												labelWidth: 71,
												id: 'cycleExecCronText',
												name: 'cycleExecCronText',
												labelAlign: 'right'
											},{
												text: '选择',
												xtype: 'button',
												baseCls:'Common_Btn',
												handler: selectExecCron
											}]
										}],
										buttons: [{
											cls:'Common_Btn',
											text: '保存',
											handler: function() {
												var cron = oneKeySwitchForm1.getForm.findField('cycleExecCronText').getValue();
												Ext.Ajax.request({
													url : 'saveCmdbTimeTask.do',
													method : 'POST',
													params : {
														cron : cron
													},
													success : function(response, request) {
														var success = Ext.decode(response.responseText).success;
														var message = Ext.decode(response.responseText).message;
														if (true == success) {
															Ext.Msg.alert("提示", message);
															closeOneKeySwitchWindow1
														} else {
															Ext.Msg.alert("提示", message);
														}
													},
													failure : function(result, request) {
														secureFilterRs(result, "操作失败！");
													}
												});
											}
										},{
											cls: 'Common_Btn',
											text: '取消',
											handler: closeOneKeySwitchWindow1
										}]
									});
									oneKeySwitchWindow1 = Ext.create('Ext.window.Window', {
										height : 611,
										modal : true,
										resizable : true,
										closeAction: 'destroy',
										layout : 'fit',
										title : '选择执行周期',
										items : [ oneKeySwitchForm1 ]
									}).show();

									function closeOneKeySwitchWindow1(){
										oneKeySwitchWindow1.close();
									}
								}
							},{
								text: '关闭同步',
								handler: deleteCmdbTimeTask
							}]
						})
					},'->'
					,{
						xtype: 'button',
						cls:'Common_Btn',
						text: '查询',
						handler: function() {
							cord=[];
							grid_panel.ipage.moveFirst();
						}
					},
					{
						xtype: 'button',
						cls:'Common_Btn',
						text: '清空',
						handler: function() {
							clearQueryWhere();
						}
					}
					,{
						xtype: 'button',
						cls:'Common_Btn',
						text : '返回',
						hidden : !requestFromC3Char,
						handler: function(){
							popNewTab('脚本看板', 'pandect1.do', {},10, true);
						}
					}]

			}]
	});



	//执行周期
	function selectExecCron()
	{
	var	creatCronWin = Ext.create('Ext.window.Window', {
			title : '定时任务参数设置',
			modal : true,
			id : 'creatCronWin',
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			upperWin : creatCronWin,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight() - 30,
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			loader : {
				url : 'cronMainForSpdb.do',
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		});
		creatCronWin.show();
	}
	if(isOpen){//开关
		Ext.getCmp('tongbu').show();

	}else{
		Ext.getCmp('tongbu').hide();
	}

//	if(agentPauseRecoverSwitch){
//		Ext.getCmp('agentPauseButton').show();
//		Ext.getCmp('agentRecoverButton').show();
//	}

	/** *********************Model********************* */
	/** Agent 数据Model* */
	Ext.define('AgentModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'string'
		},
		{
			name: 'iequipmentorvm',
			type: 'string'
		},
        {
            name: 'serverName',//agent表增加新的字段：服务名称 jiaMing 2022-5-18
			type: 'string'
		},
		{
			name: 'iagentname',
			type: 'string'
		},
		{
			name: 'iagentdesc',
			type: 'string'
		},
		{
			name: 'iagentip',
			type: 'string'
		},
		{
			name: 'iagentport',
			type: 'string'
		},
		{
			name: 'iname',//系统名称
			type: 'string'
		},
		{
			name: 'iagentclusterid',
			type: 'string'
		},
		{
			name: 'iagentclustername',
			type: 'string'
		},
		{name: 'iagentnetid',     type: 'string'},
		{name: 'ibusinessip',     type: 'string'},
		{name: 'ibusinesssys',     type: 'string'},
		{name: 'systeminfo',     type: 'string'},
		{name: 'centername',     type: 'string'},
		{name: 'sysadmin_a',     type: 'string'},
		{name: 'sysadmin_b',     type: 'string'},
		{name: 'appadmin_a',     type: 'string'},
		{name: 'appadmin_b',     type: 'string'},
		{name: 'ICMDB_UPDATE_LASTTIME',     type: 'string'},
		{name: 'ICMDB_UPDATE_DESC',     type: 'string'},


        {name: 'uuid',     type: 'string'},
        {name: 'sn',     type: 'string'},
        {name: 'os_type',     type: 'string'},
        {name: 'localip',     type: 'string'},
        {name: 'os_smalltype',     type: 'string'},
        {name: 'isoncloud',     type: 'string'},
		{
			name: 'iagentos',
			type: 'string'
		},
		{
			name: 'iagentcomputername',
			type: 'string'
		},
			{
			name: 'proxyIp',
			type: 'string'
		},{
				name: 'proxyName',
				type: 'string'
			},
		{
			name: 'iagentstartuser',
			type: 'string'
		},
		{
			name: 'iagentstate',
			type: 'string'
		},
		{
			name: 'iagentversion',
			type: 'string'
		},
		{
			name: 'iagentactivitynum',
			type: 'int'
		},{
			name : 'isource_path',
			type : 'string'
		},{
			name : 'itarget_path',
			type : 'string'
		}, {
			name : 'ibackup_path',
			type : 'string'
		}, {
			name : 'ioperation_user',
			type : 'string'
		}, {
			name : 'ioperation_password',
			type : 'string'
		}, {
			name : 'ios_name',
			type : 'string'
		}, {
			name : 'udpateosname',
			type : 'string'
		}, {
			name : 'iconnect_type',
			type : 'string'
		}, {
			name : 'iconnect_port',
			type : 'string'
		}, {
			name : 'itransmission_type',
			type : 'string'
		}, {
			name : 'itransmission_ip',
			type : 'string'
		}, {
			name : 'itransmission_prot',
			type : 'string'
		}, {
			name : 'itransmission_user',
			type : 'string'
		}, {
			name : 'itransmission_password',
			type : 'string'
		}, {
			name : 'ienvtype',
			type : 'string'
		},{
			name : 'iagentCchange',
			type : 'string'
		},{
			name : 'ifsendMsg',
			type : 'int'
		},{
			name : 'iagentUpId',
			type : 'int'
		},{
			name : 'icustom_cmd',
			type : 'string'
		},{
			name : 'icustom_mess',
			type : 'string'
		},{
			name : 'icreateuser',
			type : 'string'
		},{
			name : 'icreatetime',
			type : 'string'
		}, {
			name : 'issued',
			type : 'string'
		},{
			name : 'idcid',
			type : 'string'
		},
		{
			name: 'iagentwebport',
			type: 'string'
		},{
			name : 'isusp',
			type : 'string'
		},{
			name: 'iagentguardport',
			type: 'string'
		},{
			name: 'iagentazname',
			type: 'string'
		},{
			name: 'iagentnetid',
			type: 'string'
		},{
			name: 'iosbasevalue',
			type: 'string'
		},{
			name: 'systemStatus',
			type: 'string'
		},{
			name: 'isSSL',
			type: 'int'
		},{
				name: 'iteam',
				type: 'string'
			},{
                name: 'systemid',
                type: 'string'
            }]
	});

	Ext.define('AgentActModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iflowid',
			type: 'string'
		},
		{
			name: 'iprojectname',
			type: 'string'
		},
		{
			name: 'iflowname',
			type: 'string'
		},
		{
			name: 'iactname',
			type: 'string'
		}, {
			name: 'proType',
			type: 'string'
		}]
	});

	Ext.define('UpgradeInfoModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iagentup_id',
			type : 'long'
		},{
			name : 'isource_path',
			type : 'string'
		},{
			name : 'itarget_path',
			type : 'string'
		}, {
			name : 'ibackup_path',
			type : 'string'
		}, {
			name : 'ioperation_user',
			type : 'string'
		}, {
			name : 'ios_name',
			type : 'string'
		}, {
			name : 'iconnect_type',
			type : 'string'
		}, {
			name : 'iconnect_port',
			type : 'string'
		},{
			name : 'itransmission_type',
			type : 'string'
		}, {
			name : 'itransmission_ip',
			type : 'string'
		}, {
			name : 'itransmission_prot',
			type : 'string'
		}, {
			name : 'itransmission_user',
			type : 'string'
		}, {
			name : 'itransmission_password',
			type : 'string'
		},{
			name : 'isusp',
			type : 'string'
		}]
	});
	Ext.define('dataCenterModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'iname',
			type : 'string'
		}]
	});

	Ext.define('ActModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'agentId',
			type: 'long'
		},
			{
				name: 'agentIp',
				type: 'string'
			},
			{
				name: 'agentGroupName',
				type: 'string'
			},
			{
				name: 'proType',
				type: 'string'
			}, {
				name: 'prjName',
				type: 'string'
			}, {
				name: 'flowName',
				type: 'string'
			}, {
				name: 'actName',
				type: 'string'
			}]
	});


	/** *********************Store********************* */

	var dataCenterStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		model : 'dataCenterModel',
		proxy : {
			type : 'ajax',
			url : 'getDataCenterList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	agentStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		remoteSort: true,
		model: 'AgentModel',
		pageSize: 100,
		proxy: {
			type: 'ajax',
			url: 'getAgentMaintainList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	agentStore.on('beforeload', function(store, options) {
		var new_params = {
				iagentName: query_form.getForm().findField("iagentName").getValue(),
				iagentIp: query_form.getForm().findField("iagentIp").getValue(),
				iteam: query_form.getForm().findField("iteam").getValue(),
			    iagentosname:query_form.getForm().findField("iagentosname").getValue(),
				iagentcomputername:query_form.getForm().findField("iagentcomputername").getValue(),
				icustommess:query_form.getForm().findField("icustommess").getValue(),
				iagentDesc: query_form.getForm().findField("iagentDesc").getValue(),
				iagentState: query_form.getForm().findField("iagentState").getValue(),
				iagentversion:query_form.getForm().findField("iagentversion").getValue(),
				icreateuser: query_form.getForm().findField("icreateuser").getValue(),
				istarttime: query_form.getForm().findField("istarttime").getValue(),
				iendtime: query_form.getForm().findField("iendtime").getValue(),
				idcid: query_form.getForm().findField("idcid").getValue(),
				os_smalltype: query_form.getForm().findField("osSmalltype").getValue(),
				proxyName: query_form.getForm().findField("proxyName").getValue(),
				systeminfo: query_form.getForm().findField("systeminfo").getValue(),
				centername: query_form.getForm().findField("centername").getValue(),
				sysadmin_a: query_form.getForm().findField("sysadmin_a").getValue(),
				appadmin_a: query_form.getForm().findField("appadmin_a").getValue(),
			    serverName:query_form.getForm().findField("serverName").getValue(),
			    iequipmentOrVmId:query_form.getForm().findField("iequipmentOrVmId").getValue(),
			    fjnxSysName:query_form.getForm().findField("fjnxSysName").getValue(),
			    envName:query_form.getForm().findField("fjnxEnvName").getValue()
				//proId:query_form.getForm().findField("sysName_combo").getValue()
		};
		Ext.apply(agentStore.proxy.extraParams, new_params);
		if(isFJNX){
			agentSelected.clear();
			grid_panel.getSelectionModel().deselectAll();
		}
	});

	var clusterStore = Ext.create('Ext.data.JsonStore', {
		fields: ['iclusterid', 'iclustername'],
		autoDestroy: true,
		autoLoad: false,
		proxy: {
			type: 'ajax',
			url: 'getAgentMaintainClusterList.do',
			reader: {
				type: 'json',
				root: 'centerlist'
			}
		}
	});

	/** *********************Store********************* */
	var upgradeInfoStore = Ext.create ('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		model : 'UpgradeInfoModel',
		pageSize : 50,
		proxy : {
			type : 'ajax',
			url : 'getAgentMaintainUpgradeInfo.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	/** 列表分页工具栏* */

	var bsUpgradeInfoPageBar = Ext.create('Ext.PagingToolbar', {
		store: upgradeInfoStore,
		dock: 'bottom',
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo: true,
		border:false,
		displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
		emptyMsg: "没有记录"
	});

	var envStore = Ext.create('Ext.data.Store', {
		fields: ['name'],
		data : [
		        {"name":"测试"},
		        {"name":"生产"}
		        ]
	});


	var dataCenterCombo = Ext.create('Ext.form.field.ComboBox', {
		store : dataCenterStore,
		name : "dataCenter",
		emptyText : '---请选择---',
		valueField : 'iid',
		displayField : 'iname',
		queryMode : 'local',
		forceSelection : true,
		triggerAction : 'all',
		width:200,
		editable : false,
		typeAhead : false// typeAhead 和 editable 同时设置成false下拉框才不能输入
		// ，否则下拉框可以随意输入其他值
	});
	var envCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : envStore,
		queryMode : 'local',
		width : 600,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'name',
		valueField : 'name',
		triggerAction : "all"
	});

	/** 列表Columns* */
	var agentColumns =null;
	var ip ;
	if(agentDnsSwitchValue){
		ip = {
				text: '地址',
				dataIndex: 'iagentip',
				width: 130,
				editor: {
					xtype: 'textfield',
					maxLength: 40
				},
			renderer: function(value,metaData,record){
				return '<a href="javascript:void(0);" onclick="showActInfo('+ record.get('iid') +');">'+value+'</a>';
			}
		};
	}else{
		ip = {
				text: '地址',
				dataIndex: 'iagentip',
				width: 130,
				editor: {
					xtype: 'textfield',
					maxLength: 255
				},
			renderer: function(value,metaData,record){
				return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
			}
		};

	}
	if(jobSchedulingDbbackSwitch){


		agentColumns = [{
			text: '序号',
			width: 65,
			xtype: 'rownumberer'
		},
		{
			text: 'IID',
			dataIndex: 'iid',
			width: 45,
			hidden: true
		},
		{
			text: '升级信息ID',
			dataIndex: 'iagentUpId',
			width: 45,
			hidden: true
		},
		{
			text: '名称',
			dataIndex: 'iagentname',
			width: 150,
			editor: {
				xtype: 'textfield',
				maxLength: 255
			}
		},
		{
			text: '描述',
			dataIndex: 'iagentdesc',
			hidden: !ismustdcid,
			width: 100,
			editor: {
				xtype: 'textfield',
				maxLength: 255
			}
		},
		ip,
		{
			text: '端口号',
			dataIndex: 'iagentport',
			width: 100,
			editor: {
				xtype: 'numberfield',
				maxValue: 65535,
				minValue: 0
			}
		},
		{
			text: '采集系统名称',
			dataIndex: 'iname',
			width: 100,
			hidden:!displaySystemName
		},
		{
			text: 'WEB端口号',
			dataIndex: 'iagentwebport',
			width: 100,
			hidden:!suppercheck||isHSBank,
			editor: {
				xtype: 'numberfield',
				maxValue: 65535,
				minValue: 0
			}
		},{
			text: '守护端口号',
			dataIndex: 'iagentguardport',
			width: 100,
			hidden:true,
			editor: {
				xtype: 'numberfield',
				maxValue: 65535,
				minValue: 0
			}
		},{
			text: '数据中心',
			dataIndex: 'idcid',
			width: 80,
			hidden:!ismustdcid,
			editor: dataCenterCombo,
			renderer : function (value, p, record, rowIndex) {
				var backValue = "";
				if (value != '') {
					var index = dataCenterStore.findExact("iid", value);
					if (index != -1) {
						backValue = scriptStore.getAt(index).data.iname;
					}
				}
				return backValue;
			}
		},
		{
			text: '所属集群',
			dataIndex: 'iagentclustername',
			hidden: true,
			width: 100
		}, {
			text: '设备或者虚拟机',
			dataIndex: 'iequipmentorvm',
			hidden: true,
			width: 100
		}, {
			text: '操作系统',
			dataIndex: 'iagentos',
			width: 100
		}, {
			text: '计算机名',
			dataIndex: 'iagentcomputername',
			width: 100
		},{
				text: 'proxy名称',
				dataIndex: 'proxyName',
				width: 100
			}, {
			text: '启动用户',
			dataIndex: 'iagentstartuser',
			width: 100
		},
			{
				text: '所属团队',
				dataIndex: 'iteam',
				width: 150,
				hidden: !lnzgyhfhSwitch,
				editor: {
					xtype: 'textfield',
					maxLength: 100
				}
			},{
				text : '开启SSL',
				dataIndex: 'isSSL',
				width: 90,
				renderer: function(value,metaData,record){
					if(value==0){
						return '否';
					}else if(value==1){
						return '是';
					}else{
						return '';
					}
				},
				editor : new Ext.form.ComboBox({
					store :  new Ext.data.SimpleStore({
						fields : ['id', 'isSSL_flag'],
						data : [[1, '是'], [0, '否']]
					}),
					displayField : 'isSSL_flag',
					valueField : 'id',
					typeAhead : true,
					triggerAction : 'all',
					allowBlank : false,
					forceSelection : true,
					mode : 'local'
				})
			}, {
			text: '版本',
			dataIndex: 'iagentversion',
			width: 70
		}, {
			text: '自定义详情',
			dataIndex: 'icustom_mess',
			hidden: true,
			width: 120
		},{
			text: '活动数量',
			dataIndex: 'iagentactivitynum',
			hidden:true,
			width: 100/**
		        renderer: function(value,metaData,record){
		            return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
		        }**/
		}, {
			text: '状态',
			dataIndex: 'iagentstate',
			width: 80,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if(value=='0') {
					cls = "<span class='Green_color State_Color'>正常</span>";
				} else if(value=='1') {
					cls = "<span class='Red_color State_Color'>异常</span>";
				} else if(value=='2') {
					return '升级中';
				}else if(value=='8'){
					return '卸载中';
				} else if(value=='3'){
					return '异常';
				} else if(value == '4') {
					if(agentPauseRecoverSwitch){
						return "<span class='Red_color State_Color'>暂停</span>";
					}else{
						return "<span class='Red_color State_Color'>异常</span>";
					}
				}/*else if(value=='7'){
					return '禁用';
				}*/else {
					return '新建';
				}
				return cls;
			}
		},{
			text : '环境',
			dataIndex : 'ienvtype',
			flex : 1,
			hidden : true,
			editor : envCombo
		},{
			text : '是否通用',
			dataIndex : 'isusp',
			flex : 1,
			hidden : true
		}]

	}
	else if(isFJNX){

				agentColumns = [{
					text: '序号',
					width: 65,
					xtype: 'rownumberer'
				},
					{
						text: 'IID',
						dataIndex: 'iid',
						width: 45,
						hidden: true
					},
					{
						text: '升级信息ID',
						dataIndex: 'iagentUpId',
						width: 45,
						hidden: true
					},
					{
						text: '名称',
						dataIndex: 'iagentname',
						width: 150,
						hidden : true,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
					},
					{
						text: 'IP地址',
						dataIndex: 'iagentip',
						width: 130,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
					},
					{
						text: '端口号',
						dataIndex: 'iagentport',
						width: 80,
						editor: {
							xtype: 'numberfield',
							maxValue: 65535,
							minValue: 0
						}
					},{
						text: '采集系统名称',
						dataIndex: 'iname',
						width: 100,
						hidden: true
					},
					{
						text: 'WEB端口号',
						dataIndex: 'iagentwebport',
						hidden: true,
						width: 100,
						editor: {
							xtype: 'numberfield',
							maxValue: 65535,
							minValue: 0
						}
					},{
						text: '守护端口号',
						dataIndex: 'iagentguardport',
						width: 100,
						hidden:true,
						editor: {
							xtype: 'numberfield',
							maxValue: 65535,
							minValue: 0
						}
					},{
						text: 'iagentnetid',
						dataIndex: 'iagentnetid',
						width: 100,
						hidden: true,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
					},{
						text: '数据中心',
						dataIndex: 'idcid',
						width: 80,
						hidden: true,
						editor: dataCenterCombo,
						renderer : function (value, p, record, rowIndex) {
							var backValue = "";
							if (value != '') {
								var index = dataCenterStore.findExact("iid", value);
								if (index != -1) {
									backValue = dataCenterStore.getAt(index).data.iname;
								}
							}
							return backValue;
						}
					},
					{
						text: '所属集群',
						dataIndex: 'iagentclustername',
						hidden: true,
						width: 100
					}, {
						text: '设备或者虚拟机',
						dataIndex: 'iequipmentorvm',
						hidden: true,
						width: 100,
						//Agent字段维护：设备或者虚拟机字段类型为long,且后台并没有查询此字段。现定义此字段含义：1-物理机，2-虚拟机，3-容器 jiaMing 2022-5-18
						renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
							//return "e5";
							return iequipmentorvmFn(value);
						}
					}, {
						text: '服务名称',
						dataIndex: 'serverName',
						hidden: true,
						width: 100

					}, {
						text: 'uuid',
						dataIndex: 'uuid',
						hidden: true,
						width: 100
					},{
						text: lnzgyhfhSwitch? '生产地址':'业务地址',
						dataIndex: 'ibusinessip',
						hidden: true,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						},
						width: 100
					},
					{
						text : '业务系统',
						dataIndex : 'ibusinesssys',
						minWidth : 145,
						editor: new Ext.form.field.ComboBox({
							//id : 'ibusinesssys',
							editable : !isFJNX,
							anyMatch: true,
							valueField : 'systemname',
							displayField : 'systemname',
							queryMode : 'local' ,
							store: fjnxSystemStore,
							lazyRender:true
						})
					},{
						text: 'sn',
						dataIndex: 'sn',
						hidden: true,
						width: 100
					},{
						text: 'os_type',
						dataIndex: 'os_type',
						hidden: true,
						width: 100
					},{
						text: 'localip',
						dataIndex: 'localip',
						hidden: true,
						width: 100
					},{
						text: 'os_smalltype',
						dataIndex: 'os_smalltype',
						hidden: true,
						width: 100
					},{
						text : '是否云上设备',
						dataIndex: 'isoncloud',
						hidden: true,
						width: 90,
						renderer: function(value,metaData,record){
							if(value=='no'){
								return '否';
							}else if(value=='yes'){
								return '是';
							}else{
								return '';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'isoncloud_flag'],
								data : [['yes', '是'], ['no', '否']]
							}),
							displayField : 'isoncloud_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					},
					{
						text: '操作系统',
						dataIndex: 'iagentos',
						width: 100
					}, {
						text: '主机名',
						dataIndex: 'iagentcomputername',
						width: 100
					},{
						xtype : 'gridcolumn',
						dataIndex : 'systeminfo',
						hidden: true,
						text : '信息系统名称'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'centername',
						hidden: true,
						text : '所属区域'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'sysadmin_a',
						hidden: true,
						text : '系统管理员A角'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'sysadmin_b',
						hidden:true,
						text : '系统管理员B角'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'appadmin_a',
						hidden: true,
						text : '应用管理员A角'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'appadmin_b',
						hidden:true,
						text : '应用管理员B角'
					}, {
						xtype : 'gridcolumn',
						dataIndex : 'ICMDB_UPDATE_LASTTIME',
						text : 'CMDB上次同时时间',
						hidden: true,
						width : 150
					},  {xtype : 'gridcolumn',
						dataIndex : 'ICMDB_UPDATE_DESC',
						text : 'CMDB同步状态',
						hidden: true,
						width : 150
					},
					{
						text: 'proxy名称',
						dataIndex: 'proxyName',
						width: 100
					},{
						text: 'proxyIp',
						dataIndex: 'proxyIp',
						hidden:true,
						width: 100
					}, {
						text: 'AZ名称',
						dataIndex: 'iagentazname',
						hidden:true,
						width: 100
					}, {
						text: '启动用户',
						dataIndex: 'iagentstartuser',
						width: 100
					},
					{
						text: '所属团队',
						dataIndex: 'iteam',
						hidden: true,
						width: 150,
						editor: {
							xtype: 'textfield',
							maxLength: 100
						}
					},{
						text : '开启SSL',
						dataIndex: 'isSSL',
						width: 90,
						renderer: function(value,metaData,record){
							if(value==0){
								return '否';
							}else if(value==1){
								return '是';
							}else{
								return '';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'isSSL_flag'],
								data : [[1, '是'], [0, '否']]
							}),
							displayField : 'isSSL_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					}, {
						text: '版本',
						dataIndex: 'iagentversion',
						width: 70
					}, {
						text: '自定义命令',
						dataIndex: 'icustom_cmd',
						hidden: true,
						width: 120
					}, {
						text: '自定义详情',
						dataIndex: 'icustom_mess',
						hidden: true,
						width: 120
					},{
						text: '活动数量',
						dataIndex: 'iagentactivitynum',
						width: 100,
						renderer: function(value,metaData,record){
							return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
						}
					},
					{
						text: '用途',
						dataIndex: 'iagentdesc',
						hidden: !isFJNX,
						width: 100,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
					}, {
						text: '状态',
						dataIndex: 'iagentstate',
						width: 80,
						renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
							var cls = "";
							if(value=='0') {
								cls = "<span class='Green_color State_Color'>正常</span>";
							} else if(value=='1') {
								cls = "<span class='Red_color State_Color'>异常</span>";
							} else if(value=='2') {
								return '升级中';
							}else if(value=='8'){
								return '卸载中';
							} else if(value=='3'){
								return '异常';
							} else if(value == '4') {
								if(agentPauseRecoverSwitch){
									return "<span class='Red_color State_Color'>暂停</span>";
								}else{
									return "<span class='Red_color State_Color'>异常</span>";
								}
							}/*else if(value=='7'){
							return '禁用';
						}*/
							else if(value == '9'){
								return '下线';
							}else {
								return '新建';
							}
							return cls;
						}
					},{
						text: '系统状态',
						dataIndex: 'systemStatus',
						width: 80,
						hidden: true
					},{
						text: '是否变化',
						dataIndex: 'iagentCchange',
						hidden: true,
						width: 80,
						renderer:function(value,p,record,rowIndex){
							var backMessage = "";
							if(value==1){
								backMessage = "<span style='background: #fe969e;color:#ffffff;padding: 2px;'>是</span>";
							}else{
								backMessage = "否";
							}
							return backMessage;
						},
						listeners:{
							click:function(a,b,c,d){
								var x=a.getStore().getAt(c).data.iagentCchange;
								var iid=a.getStore().getAt(c).data.iid;
								if (x==1){
									onclickBH(iid);
								}
							}
						}
					}, {
						xtype : 'actioncolumn',
						text : '应用标识配置',
						width : 100,
						sortable : false,
						menuDisabled : true,
						items : [ {
							iconCls:'role_permission',
							tooltip : '应用标识',
							handler : setPermission
						} ]
					},{
						iconCls:'role_permission',
						header : '数据库配置',
						align : 'left',
						hidden: true,
						xtype:'actioncolumn',
						width : 200,
						dataIndex : 'iid',
						items : [
							{
								tooltip: '数据库配置',
								text : '数据库配置',
								handler : function(grid, rowIndex) {
									var iid = grid.getStore().getAt(rowIndex).get('iid');
									showagentMTWin(iid);
								}


							}]
					},{
						xtype : 'actioncolumn',
						text : '详情',
						width : 100,
						sortable : false,
						menuDisabled : true,
						items : [{	iconCls:'monitor_curve',
							tooltip : '并发度实时监控',
							handler : function (a,b,c,d,e,record) {
								var ip = record.data.iagentip;
								var port = record.data.iagentport;
//							console.log('agnet ====' , ip + ':' + port);
								showAgentMonitor(ip,port);
							}
						}
							// 20210610 sxh 浙商漏洞 屏蔽下载agent日志功能
							,
							{	iconCls:'monitor_download',
								tooltip : '下载远程日志',
								handler : function (a,b,c,d,e,record) {
									var state = record.data.iagentstate;
									var ip = record.data.iagentip;
									var port = record.data.iagentport;
									if(state!=0)
									{
										Ext.Msg.alert('警告', '不能下载');
									}else{
										showAgentLogList(ip,port);
									}
								}
							}
							,{
								//icon : 'images/permission.png',
								iconCls:'monitor_search',
								tooltip : '升级配置详情',
								handler : function (grid, rowIndex) {
									var agentInfo = grid.getStore().getAt(rowIndex);
									if (agentInfo.get('iid')) {
										var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
											'<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
											'<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p>'+
											'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
											'<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
											'<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
											'<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p>'+
											'<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
											'<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
											'<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
											'<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';

										Ext.Msg.show({
											title : '升级配置详情信息',
											msg : msgContent,
											buttons : Ext.Msg.OK,
											icon : Ext.MessageBox.INFO,
											width : 500
										});
									} else {
										Ext.Msg.alert('详细信息', '该记录没有详细信息!');
									}
								}
							} ]
					}, {
						dataIndex : 'ifsendMsg',
						width : 90,
						hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
						text : '是否发送报文',
						renderer: function(value,metaData,record){
							if(value==0){
								value = "发送";
							}else{
								value = "不发送";
							}
							return value;
						}
					},{
						xtype : 'actioncolumn',
						dataIndex : 'ienvtype',
						text : '环境类型',
						width : 100,
						sortable : false,
						menuDisabled : true,
						items : [ {
							iconCls:'role_permission',
							tooltip : '环境配置',
							handler : setEnv
						}]
					},{
						text : '查看用户',
						hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
						dataIndex: 'iid',
						width: 70,
						renderer: function(value,metaData,record){
							var agentId = record.get('iid');
							var agentIp = record.get('iagentip');
							return "<a href='#' script='javascript:void(0)' onclick=\"showUser('"+ agentId+"','"+agentIp+"'"+")\">用户</a>";
						}
					},{
						text : '纳管用户',
						hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
						dataIndex: 'icreateuser',
						width: 120
					},{
						text : '纳管时间',
						hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
						dataIndex: 'icreatetime',
						width: 150
					},{
						text : '是否下发',
						dataIndex: 'issued',
						hidden: true,
						width: 90,
						renderer: function(value,metaData,record){
							if(value==0){
								return '否';
							}else{
								return '是';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'issued_flag'],
								data : [[0, '否'], [1, '是']]
							}),
							displayField : 'issued_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					},
					{
						text: '资源预值',
						dataIndex: 'iosbasevalue',
						width: 100,
						hidden : true,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
					},{
						text : '操作',
						dataIndex : 'iagentstate',
						width : 90,
						renderer : function (value, meta, record) {
							cord.push(record)
							meta.style = 'white-space:normal;word-break:break-all;';
							var url ="";
							var agentId = record.get('iid');
							var agentIp = record.get('iagentip');
							var agentPort = record.get('iagentport');
							/*var url1 ="";
                            if('7' ==value){
                                url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('use','"
                                    + agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启用</a>";
                            }else{
                                url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('ban','"
                                    + agentId +"','"+agentIp+"','"+agentPort+"'" +")\">禁用</a>";
                            }*/
							if('0' ==value){
								url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
									+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
							}else if('1' == value ){
								url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
									+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
							}else if('2' == value || '3' == value){
								//将待处理按钮 全部变成停止按钮 2022.9.2 修改
								url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
									+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
							}else{
								url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
									+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
							}
							// return url+"&nbsp "+url1;
							return url;
						}
					},{
						text : '是否通用',
						dataIndex : 'isusp',
						flex : 1,
						hidden : true
					}];

	}
	else{

		if(jobSchedulingQuerySystemSwitch)
		{
			if(dowonLoadLongRangeLog){
				agentColumns = [{
					text: '序号',
					width: 65,
					xtype: 'rownumberer'
				},
				{
					text: 'IID',
					dataIndex: 'iid',
					width: 45,
					hidden: true
				},
				{
					text: '升级信息ID',
					dataIndex: 'iagentUpId',
					width: 45,
					hidden: true
				},
				{
					text: '名称',
					dataIndex: 'iagentname',
					width: 150,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				{
					text: '描述',
					dataIndex: 'iagentdesc',
					hidden: !ismustdcid,
					width: 100,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				ip,
				{
					text: '端口号',
					dataIndex: 'iagentport',
					width: 80,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
						text: '采集系统名称',
						dataIndex: 'iname',
						width: 100,
						hidden:!displaySystemName
					},
				{
					text: 'WEB端口号',
					dataIndex: 'iagentwebport',
					width: 100,
					hidden:!suppercheck||isHSBank,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '守护端口号',
					dataIndex: 'iagentguardport',
					width: 100,
					hidden:true,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '数据中心',
					dataIndex: 'idcid',
					width: 80,
					hidden:!ismustdcid,
					editor: dataCenterCombo,
					renderer : function (value, p, record, rowIndex) {
						var backValue = "";
						if (value != '') {
							var index = dataCenterStore.findExact("iid", value);
							if (index != -1) {
								backValue = scriptStore.getAt(index).data.iname;
							}
						}
						return backValue;
					}
				},
				{
					text: '所属集群',
					dataIndex: 'iagentclustername',
					hidden: true,
					width: 100/*,
			        editor: {
			            xtype: 'combobox',
			            margin: '5',
			            store: clusterStore,
			            editable: false,
			            queryMode: 'local',
			            forceSelection: true,
			            // 要求输入值必须在列表中存在
			            typeAhead: true,
			            // 允许自动选择
			            displayField: 'iclustername',
			            valueField: 'iclustername',
			            triggerAction: "all"
			        }*/
					/*,
					renderer : function(value, metadata, record, rowIndex, colIndex, store, view) {
						var index = clusterStore.find('iclusterid', value);
						if (index != -1) {
							return clusterStore.getAt(index).data.iclustername;
						}
						return value;
					}*/
				}, {
					text: '设备或者虚拟机',
					dataIndex: 'iequipmentorvm',
					hidden: true,
					width: 100
				}, {
					text: '操作系统',
					dataIndex: 'iagentos',
					width: 100
				}, {
					text: '计算机名',
					dataIndex: 'iagentcomputername',
					width: 100
				},{
						text: 'proxy名称',
						dataIndex: 'proxyName',
						width: 100
					}, {
					text: '启动用户',
					dataIndex: 'iagentstartuser',
					width: 100
				},{
						text : '开启SSL',
						dataIndex: 'isSSL',
						width: 90,
						renderer: function(value,metaData,record){
							if(value==0){
								return '否';
							}else if(value==1){
								return '是';
							}else{
								return '';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'isSSL_flag'],
								data : [[1, '是'], [0, '否']]
							}),
							displayField : 'isSSL_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					}, {
					text: '版本',
					dataIndex: 'iagentversion',
					width: 70
				}, {
					text: '自定义命令',
					dataIndex: 'icustom_cmd',
					hidden: isHSBank,
					width: 120
				}, {
					text: '自定义详情',
					dataIndex: 'icustom_mess',
					hidden: isHSBank,
					width: 120
				},{
					text: '活动数量',
					dataIndex: 'iagentactivitynum',
					align : 'center',
					hidden:true,
					width: 100/**
			        renderer: function(value,metaData,record){
			            return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
			        }**/
				}, {
					text: '状态',
					dataIndex: 'iagentstate',
					width: 80,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						var cls = "";
						if(value=='0') {
							cls = "<span class='Green_color State_Color'>正常</span>";
						} else if(value=='1') {
							cls = "<span class='Red_color State_Color'>异常</span>";
						} else if(value=='2') {
							return '升级中';
						}else if(value=='8'){
							return '卸载中';
						} else if(value=='3'){
							return '异常';
						} else if(value == '4') {
							if(agentPauseRecoverSwitch){
								return "<span class='Red_color State_Color'>暂停</span>";
							}else{
								return "<span class='Red_color State_Color'>异常</span>";
							}
						}/*else if(value=='7'){
							return '禁用';
						}*/else {
							return '新建';
						}
						return cls;
					}
				},{
					text: '是否变化',
					dataIndex: 'iagentCchange',
					width: 80,
					renderer:function(value,p,record,rowIndex){
						var backMessage = "";
						if(value==1){
							backMessage = "<span style='background: #fe969e;color:#ffffff;padding: 2px;'>是</span>";
						}else{
							backMessage = "否";
						}
						return backMessage;
					},
					listeners:{
						click:function(a,b,c,d){
							var x=a.getStore().getAt(c).data.iagentCchange;
							var iid=a.getStore().getAt(c).data.iid;
							if (x==1){
								onclickBH(iid);
							}
						}
					}
				}, {
					xtype : 'actioncolumn',
					text : '应用标识配置',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [ {
						iconCls:'role_permission',
						tooltip : '应用标识',
						handler : setPermission
					} ]
				},
					{
						iconCls:'role_permission',
						header : '数据库配置',
						align : 'left',
						hidden: !agentmaintenance,
						xtype:'actioncolumn',
						width : 200,
						dataIndex : 'iid',
						items : [
							{
								tooltip: '数据库配置',
								iconCls:'role_permission',
								text : '数据库配置',
								handler : function(grid, rowIndex) {
									var iid = grid.getStore().getAt(rowIndex).get('iid');
									showagentMTWin(iid);
								}


							}]

					},
					{
					xtype : 'actioncolumn',
					text : '详情',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [{	iconCls:'monitor_curve',
						tooltip : '并发度实时监控',
						handler : function (a,b,c,d,e,record) {
							var ip = record.data.iagentip;
							var port = record.data.iagentport;
//							console.log('agnet ====' , ip + ':' + port);
							showAgentMonitor(ip,port);
						}
					}
					 //20210610 sxh 浙商漏洞 屏蔽下载agent日志功能
					 , {	iconCls:'monitor_download',
					 	tooltip : '下载远程日志',
					 	hidden:dowonLoadLongRangeLog,
					 	handler : function (a,b,c,d,e,record) {
					 		var state = record.data.iagentstate;
					 		var ip = record.data.iagentip;
					 		var port = record.data.iagentport;
					 		if(state!=0)
					 		{
					 			Ext.Msg.alert('警告', '不能下载');
					 		}else{
					 			showAgentLogList(ip,port);
					 		}
					 	}
					 }
					,{
						//icon : 'images/permission.png',
						iconCls:'monitor_search',
						tooltip : '升级配置详情',
						handler : function (grid, rowIndex) {
							var agentInfo = grid.getStore().getAt(rowIndex);
							if (agentInfo.get('iid')) {
								var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
								'<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
								'<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p><br>'+
								'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
								'<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
								'<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
								'<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p><br>'+
								'<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
								'<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
								'<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
								'<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';
								Ext.Msg.show({
									title : '升级配置详情信息',
									msg : msgContent,
									buttons : Ext.Msg.OK,
									icon : Ext.MessageBox.INFO,
									width : 500
								});
							} else {
								Ext.Msg.alert('详细信息', '该记录没有详细信息!');
							}
						}
					} ]
				}, {
					dataIndex : 'ifsendMsg',
					width : 90,
					text : '是否发送报文',
					renderer: function(value,metaData,record){
						if(value==0){
							value = "发送";
						}else{
							value = "不发送";
						}
						return value;
					}
				},{
					text : '环境',
					dataIndex : 'ienvtype',
					width : 90,
					hidden : true,
					editor : envCombo/*,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						if(value==0) {
							return '测试';
						} else if(value==1) {
							return '生产';
						}
					}*/
				},{
					text : '查看用户',
					dataIndex: 'iid',
					width: 70,
					renderer: function(value,metaData,record){
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						return "<a href='#' script='javascript:void(0)' onclick=\"showUser('"+ agentId+"','"+agentIp+"'"+")\">用户</a>";
					}
				},{
					text : '纳管用户',
					dataIndex: 'icreateuser',
					width: 120
				},{
					text : '纳管时间',
					dataIndex: 'icreatetime',
					width: 150
				},{
					text : '是否下发',
					dataIndex: 'issued',
					width: 90,
					renderer: function(value,metaData,record){
						if(value==0){
							return '否';
						}else{
							return '是';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'issued_flag'],
							data : [[0, '否'], [1, '是']]
						}),
						displayField : 'issued_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				},{
					text : '操作',
					dataIndex : 'iagentstate',
					width : 90,
					renderer : function (value, meta, record) {
						cord.push(record)
						meta.style = 'white-space:normal;word-break:break-all;';
						var url ="";
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						var agentPort = record.get('iagentport');
						/*var url1 ="";
						if('7' ==value){
							url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('use','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启用</a>";
						}else{
							url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('ban','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">禁用</a>";
						}*/
						if('0' ==value){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else if('1' == value ){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}else if('2' == value || '3' == value){
							//将待处理按钮 全部变成停止按钮 2022.9.2 修改
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
									+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else{
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}
						// return url+"&nbsp "+url1;
						return url;
					}
				},{
					text : '是否通用',
					dataIndex : 'isusp',
					flex : 1,
					hidden : true
				}];
			}else{
				agentColumns = [{
					text: '序号',
					width: 65,
					xtype: 'rownumberer'
				},
				{
					text: 'IID',
					dataIndex: 'iid',
					width: 45,
					hidden: true
				},
				{
					text: '升级信息ID',
					dataIndex: 'iagentUpId',
					width: 45,
					hidden: true
				},
				{
					text: '名称',
					dataIndex: 'iagentname',
					width: 150,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				{
					text: '描述',
					dataIndex: 'iagentdesc',
					hidden: !ismustdcid,
					width: 100,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				ip,
				{
					text: '端口号',
					dataIndex: 'iagentport',
					width: 80,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
						text: '采集系统名称',
						dataIndex: 'iname',
						width: 100,
						hidden:!displaySystemName
					},
				{
					text: 'WEB端口号',
					dataIndex: 'iagentwebport',
					width: 100,
					hidden:!suppercheck || isHSBank,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '守护端口号',
					dataIndex: 'iagentguardport',
					width: 100,
					hidden:true,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '数据中心',
					dataIndex: 'idcid',
					width: 80,
					hidden:!ismustdcid,
					editor: dataCenterCombo,
					renderer : function (value, p, record, rowIndex) {
						var backValue = "";
						if (value != '') {
							var index = dataCenterStore.findExact("iid", value);
							if (index != -1) {
								backValue = scriptStore.getAt(index).data.iname;
							}
						}
						return backValue;
					}
				},
				{
					text: '所属集群',
					dataIndex: 'iagentclustername',
					hidden: true,
					width: 100/*,
			        editor: {
			            xtype: 'combobox',
			            margin: '5',
			            store: clusterStore,
			            editable: false,
			            queryMode: 'local',
			            forceSelection: true,
			            // 要求输入值必须在列表中存在
			            typeAhead: true,
			            // 允许自动选择
			            displayField: 'iclustername',
			            valueField: 'iclustername',
			            triggerAction: "all"
			        }*/
					/*,
					renderer : function(value, metadata, record, rowIndex, colIndex, store, view) {
						var index = clusterStore.find('iclusterid', value);
						if (index != -1) {
							return clusterStore.getAt(index).data.iclustername;
						}
						return value;
					}*/
				}, {
					text: '设备或者虚拟机',
					dataIndex: 'iequipmentorvm',
					hidden: true,
					width: 100
				}, {
					text: '操作系统',
					dataIndex: 'iagentos',
					width: 100
				}, {
					text: '计算机名',
					dataIndex: 'iagentcomputername',
					width: 100
				},{
						text: 'proxy名称',
						dataIndex: 'proxyName',
						width: 100
					}, {
					text: '启动用户',
					dataIndex: 'iagentstartuser',
					width: 100
				},
					{
						text: '所属团队',
						dataIndex: 'iteam',
						width: 150,
						hidden: !lnzgyhfhSwitch,
						editor: {
							xtype: 'textfield',
							maxLength: 100
						}
					},{
						text : '开启SSL',
						dataIndex: 'isSSL',
						width: 90,
						renderer: function(value,metaData,record){
							if(value==0){
								return '否';
							}else if(value==1){
								return '是';
							}else{
								return '';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'isSSL_flag'],
								data : [[1, '是'], [0, '否']]
							}),
							displayField : 'isSSL_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					}, {
					text: '版本',
					dataIndex: 'iagentversion',
					width: 70
				}, {
					text: '自定义命令',
					dataIndex: 'icustom_cmd',
					hidden: isHSBank,
					width: 120
				}, {
					text: '自定义详情',
					dataIndex: 'icustom_mess',
					hidden: isHSBank,
					width: 120
				},{
					text: '活动数量',
					dataIndex: 'iagentactivitynum',
					align : 'center',
					hidden:true,
					width: 100/**
			        renderer: function(value,metaData,record){
			            return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
			        }**/
				}, {
					text: '状态',
					dataIndex: 'iagentstate',
					width: 80,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						var cls = "";
						if(value=='0') {
							cls = "<span class='Green_color State_Color'>正常</span>";
						} else if(value=='1') {
							cls = "<span class='Red_color State_Color'>异常</span>";
						} else if(value=='2') {
							return '升级中';
						}else if(value=='8'){
							return '卸载中';
						} else if(value=='3'){
							return '异常';
						} else if(value == '4') {
							if(agentPauseRecoverSwitch){
								return "<span class='Red_color State_Color'>暂停</span>";
							}else{
								return "<span class='Red_color State_Color'>异常</span>";
							}
						}/*else if(value=='7'){
							return '禁用';
						}*/else {
							return '新建';
						}
						return cls;
					}
				},{
					text: '是否变化',
					dataIndex: 'iagentCchange',
					width: 80,
					renderer:function(value,p,record,rowIndex){
						var backMessage = "";
						if(value==1){
							backMessage = "<span style='background: #fe969e;color:#ffffff;padding: 2px;'>是</span>";
						}else{
							backMessage = "否";
						}
						return backMessage;
					},
					listeners:{
						click:function(a,b,c,d){
							var x=a.getStore().getAt(c).data.iagentCchange;
							var iid=a.getStore().getAt(c).data.iid;
							if (x==1){
								onclickBH(iid);
							}
						}
					}
				}, {
					xtype : 'actioncolumn',
					text : '应用标识配置',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [ {
						iconCls:'role_permission',
						tooltip : '应用标识',
						handler : setPermission
					} ]
				},
					{
						iconCls:'role_permission',
						header : '数据库配置',
						align : 'left',
						hidden: !agentmaintenance,
						xtype:'actioncolumn',
						width : 200,
						dataIndex : 'iid',
						items : [
							{
								tooltip: '数据库配置',
								text : '数据库配置',
								handler : function(grid, rowIndex) {
									var iid = grid.getStore().getAt(rowIndex).get('iid');
									showagentMTWin(iid);
								}


							}]
				},{
					xtype : 'actioncolumn',
					text : '详情',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [{	iconCls:'monitor_curve',
						tooltip : '并发度实时监控',
						handler : function (a,b,c,d,e,record) {
							var ip = record.data.iagentip;
							var port = record.data.iagentport;
//							console.log('agnet ====' , ip + ':' + port);
							showAgentMonitor(ip,port);
						}
					}
					,{
						//icon : 'images/permission.png',
						iconCls:'monitor_search',
						tooltip : '升级配置详情',
						handler : function (grid, rowIndex) {
							var agentInfo = grid.getStore().getAt(rowIndex);
							if (agentInfo.get('iid')) {
								var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
								'<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
								'<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p><br>'+
								'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
								'<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
								'<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
								'<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p><br>'+
								'<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
								'<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
								'<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
								'<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';
								Ext.Msg.show({
									title : '升级配置详情信息',
									msg : msgContent,
									buttons : Ext.Msg.OK,
									icon : Ext.MessageBox.INFO,
									width : 500
								});
							} else {
								Ext.Msg.alert('详细信息', '该记录没有详细信息!');
							}
						}
					} ]
				}, {
					dataIndex : 'ifsendMsg',
					width : 90,
					text : '是否发送报文',
					renderer: function(value,metaData,record){
						if(value==0){
							value = "发送";
						}else{
							value = "不发送";
						}
						return value;
					}
				},{
					text : '环境',
					dataIndex : 'ienvtype',
					width : 90,
					hidden : true,
					editor : envCombo/*,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						if(value==0) {
							return '测试';
						} else if(value==1) {
							return '生产';
						}
					}*/
				},{
					text : '查看用户',
					dataIndex: 'iid',
					width: 70,
					renderer: function(value,metaData,record){
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						return "<a href='#' script='javascript:void(0)' onclick=\"showUser('"+ agentId+"','"+agentIp+"'"+")\">用户</a>";
					}
				},{
					text : '纳管用户',
					dataIndex: 'icreateuser',
					width: 120
				},{
					text : '纳管时间',
					dataIndex: 'icreatetime',
					width: 150
				},{
					text : '是否下发',
					dataIndex: 'issued',
					width: 90,
					renderer: function(value,metaData,record){
						if(value==0){
							return '否';
						}else{
							return '是';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'issued_flag'],
							data : [[0, '否'], [1, '是']]
						}),
						displayField : 'issued_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				},{
					text : '操作',
					dataIndex : 'iagentstate',
					width : 90,
					renderer : function (value, meta, record) {
						cord.push(record)
						meta.style = 'white-space:normal;word-break:break-all;';
						var url ="";
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						var agentPort = record.get('iagentport');
						if('0' ==value){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else if('1' == value ){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}else if('2' == value || '3' == value){
							//将待处理按钮 全部变成停止按钮 2022.9.2 修改
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else{
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}
						return url;
					}
				},{
					text : '是否通用',
					dataIndex : 'isusp',
					flex : 1,
					hidden : true
				}];
			}

		}else
		{
			var iagentname;
			if(iscib){
				iagentname = {
						text: '名称',
						dataIndex: 'iagentname',
						width: 150
				};
			}else{
				iagentname = {
						text: '名称',
						dataIndex: 'iagentname',
						width: 150,
					    hidden : isFJNX,
						editor: {
							xtype: 'textfield',
							maxLength: 255
						}
				};
			}

			if(dowonLoadLongRangeLog){
				agentColumns = [{
					text: '序号',
					width: 65,
					xtype: 'rownumberer'
				},
				{
					text: 'IID',
					dataIndex: 'iid',
					width: 45,
					hidden: true
				},
				{
					text: '升级信息ID',
					dataIndex: 'iagentUpId',
					width: 45,
					hidden: true
				},
				iagentname,
				{
					text: '描述',
					dataIndex: 'iagentdesc',
					hidden: !ismustdcid,
					width: 100,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				ip,
				{
					text: '端口号',
					dataIndex: 'iagentport',
					width: 80,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
						text: '采集系统名称',
						dataIndex: 'iname',
						width: 100,
						hidden:!displaySystemName||isFJNX
					},
				{
					text: 'WEB端口号',
					dataIndex: 'iagentwebport',
					hidden:!suppercheck||isFJNX||isHSBank,
					width: 100,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '守护端口号',
					dataIndex: 'iagentguardport',
					width: 100,
					hidden:true,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '数据中心',
					dataIndex: 'idcid',
					width: 80,
					hidden:!ismustdcid||isFJNX,
					editor: dataCenterCombo,
					renderer : function (value, p, record, rowIndex) {
						var backValue = "";
						if (value != '') {
							var index = dataCenterStore.findExact("iid", value);
							if (index != -1) {
								backValue = dataCenterStore.getAt(index).data.iname;
							}
						}
						return backValue;
					}
				},
				{
					text: '所属集群',
					dataIndex: 'iagentclustername',
					hidden: true,
					width: 100/*,
			        editor: {
			            xtype: 'combobox',
			            margin: '5',
			            store: clusterStore,
			            editable: false,
			            queryMode: 'local',
			            forceSelection: true,
			            // 要求输入值必须在列表中存在
			            typeAhead: true,
			            // 允许自动选择
			            displayField: 'iclustername',
			            valueField: 'iclustername',
			            triggerAction: "all"
			        }*/
					/*,
					renderer : function(value, metadata, record, rowIndex, colIndex, store, view) {
						var index = clusterStore.find('iclusterid', value);
						if (index != -1) {
							return clusterStore.getAt(index).data.iclustername;
						}
						return value;
					}*/
				}
				, {
					text: '设备或者虚拟机',
					dataIndex: 'iequipmentorvm',
					hidden: true,
					width: 100
//				},        {
//					text: 'uuid',
//					dataIndex: 'uuid',
//					hidden: !isght,
//					width: 100
//				},{
//					text: 'sn',
//					dataIndex: 'sn',
//					hidden: !isght,
//					width: 100
//				},{
//					text: 'os_type',
//					dataIndex: 'os_type',
//					hidden: !isght,
//					width: 100
//				},{
//					text: 'localip',
//					dataIndex: 'localip',
//					hidden: !isght,
//					width: 100
//				},{
//					text: 'os_smalltype',
//					dataIndex: 'os_smalltype',
//					hidden: !isght,
//					width: 100
				},
				 {
					text: '操作系统',
					dataIndex: 'iagentos',
					width: 100
				}
				, {
					text: '计算机名',
					dataIndex: 'iagentcomputername',
					width: 100
				}
				,{
					xtype : 'gridcolumn',
					dataIndex : 'systeminfo',
					hidden:isignornewagentinfo||isFJNX,
					text : '信息系统名称'
				}
				, {
					xtype : 'gridcolumn',
					dataIndex : 'sysadmin_a',
					hidden:isignornewagentinfo||isFJNX,
					text : '系统管理员A角'
				}
				, {
					xtype : 'gridcolumn',
					dataIndex : 'sysadmin_b',
					hidden:true,
					text : '系统管理员B角'
				}
				, {
					xtype : 'gridcolumn',
					dataIndex : 'appadmin_a',
					hidden:isignornewagentinfo||isFJNX,
					text : '应用管理员A角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'appadmin_b',
					hidden:true,
					text : '应用管理员B角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'ICMDB_UPDATE_LASTTIME',
					hidden:isignornewagentinfo || isHSBank,
					text : 'CMDB上次同时时间',
					width : 150
				},  {xtype : 'gridcolumn',
					dataIndex : 'ICMDB_UPDATE_DESC',
					text : 'CMDB同步状态',
					hidden:isignornewagentinfo || isHSBank,
					width : 150
				}, {
					    text: 'proxy名称',
						dataIndex: 'proxyName',
						width: 100
					}, {
					text: 'proxyIp',
//					dataIndex: 'iagentnetid',
					dataIndex: 'proxyIp',
					hidden:true,
					width: 100
				}, {
					text: 'AZ名称',
					dataIndex: 'iagentazname',
					hidden:true,
					width: 100
				}, {
					text: '启动用户',
					dataIndex: 'iagentstartuser',
					width: 100
				},
					{
						text: '所属团队',
						dataIndex: 'iteam',
						width: 150,
						hidden: !lnzgyhfhSwitch,
						editor: {
							xtype: 'textfield',
							maxLength: 100
						}
					},{
						text : '开启SSL',
						dataIndex: 'isSSL',
						width: 90,
						renderer: function(value,metaData,record){
							if(value==0){
								return '否';
							}else if(value==1){
								return '是';
							}else{
								return '';
							}
						},
						editor : new Ext.form.ComboBox({
							store :  new Ext.data.SimpleStore({
								fields : ['id', 'isSSL_flag'],
								data : [[1, '是'], [0, '否']]
							}),
							displayField : 'isSSL_flag',
							valueField : 'id',
							typeAhead : true,
							triggerAction : 'all',
							allowBlank : false,
							forceSelection : true,
							mode : 'local'
						})
					}, {
					text: '版本',
					dataIndex: 'iagentversion',
					width: 70
				}, {
					text: '自定义命令',
					dataIndex: 'icustom_cmd',
					hidden: isHSBank,
					width: 120
				}, {
					text: '自定义详情',
					dataIndex: 'icustom_mess',
					hidden: isHSBank,
					width: 120
				},{
					text: '活动数量',
					dataIndex: 'iagentactivitynum',
					align : 'center',
					hidden:true,
					width: 100,
					renderer: function(value,metaData,record){
						return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
					}
				}, {
					text: '状态',
					dataIndex: 'iagentstate',
					width: 80,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						var cls = "";
						if(value=='0') {
							cls = "<span class='Green_color State_Color'>正常</span>";
						} else if(value=='1') {
							cls = "<span class='Red_color State_Color'>异常</span>";
						} else if(value=='2') {
							return '升级中';
						}else if(value=='8'){
							return '卸载中';
						} else if(value=='3'){
							return '异常';
						} else if(value == '4') {
							if(agentPauseRecoverSwitch){
								return "<span class='Red_color State_Color'>暂停</span>";
							}else{
								return "<span class='Red_color State_Color'>异常</span>";
							}
						}/*else if(value=='7'){
							return '禁用';
						}*/else {
							return '新建';
						}
						return cls;
					}
				},{
					text: '系统状态',
					dataIndex: 'systemStatus',
					width: 80,
					hidden: !czFlag
				},{
					text: '是否变化',
					dataIndex: 'iagentCchange',
					width: 80,
					renderer:function(value,p,record,rowIndex){
						var backMessage = "";
						if(value==1){
							backMessage = "<span style='background: #fe969e;color:#ffffff;padding: 2px;'>是</span>";
						}else{
							backMessage = "否";
						}
						return backMessage;
					},
					listeners:{
						click:function(a,b,c,d){
							var x=a.getStore().getAt(c).data.iagentCchange;
							var iid=a.getStore().getAt(c).data.iid;
							if (x==1){
								onclickBH(iid);
							}
						}
					}
				}, {
					xtype : 'actioncolumn',
					text : '应用标识配置',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [ {
						iconCls:'role_permission',
						tooltip : '应用标识',
						handler : setPermission
					} ]
				},
					{
						iconCls:'role_permission',
						header : '数据库配置',
						align : 'left',
						hidden: !agentmaintenance,
						xtype:'actioncolumn',
						width : 200,
						dataIndex : 'iid',
						items : [
							{
								tooltip: '数据库配置',
								text : '数据库配置',
								handler : function(grid, rowIndex) {
									var iid = grid.getStore().getAt(rowIndex).get('iid');
									showagentMTWin(iid);
								}


							}]
				},{
					xtype : 'actioncolumn',
					text : '详情',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [{	iconCls:'monitor_curve',
						tooltip : '并发度实时监控',
						handler : function (a,b,c,d,e,record) {
							var ip = record.data.iagentip;
							var port = record.data.iagentport;
//							console.log('agnet ====' , ip + ':' + port);
							showAgentMonitor(ip,port);
						}
					}
					,{
						//icon : 'images/permission.png',
						iconCls:'monitor_search',
						tooltip : '升级配置详情',
						handler : function (grid, rowIndex) {
							var agentInfo = grid.getStore().getAt(rowIndex);
							if (agentInfo.get('iid')) {
								var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
								'<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
								'<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p>'+
								'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
								'<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
								'<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
								'<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p>'+
								'<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
								'<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
								'<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
								'<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';

								Ext.Msg.show({
									title : '升级配置详情信息',
									msg : msgContent,
									buttons : Ext.Msg.OK,
									icon : Ext.MessageBox.INFO,
									width : 500
								});
							} else {
								Ext.Msg.alert('详细信息', '该记录没有详细信息!');
							}
						}
					} ]
				}, {
					dataIndex : 'ifsendMsg',
					width : 90,
					text : '是否发送报文',
					renderer: function(value,metaData,record){
						if(value==0){
							value = "发送";
						}else{
							value = "不发送";
						}
						return value;
					}
				},{
					text : '环境',
					dataIndex : 'ienvtype',
					width : 90,
					hidden : true,
					editor : envCombo/*,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						if(value==0) {
							return '测试';
						} else if(value==1) {
							return '生产';
						}
					}*/
				},{
					text : '查看用户',
					dataIndex: 'iid',
					width: 70,
					renderer: function(value,metaData,record){
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						return "<a href='#' script='javascript:void(0)' onclick=\"showUser('"+ agentId+"','"+agentIp+"'"+")\">用户</a>";
					}
				},{
					text : '纳管用户',
					dataIndex: 'icreateuser',
					width: 120
				},{
					text : '纳管时间',
					dataIndex: 'icreatetime',
					width: 150
				},{
					text : '是否下发',
					dataIndex: 'issued',
					width: 90,
					renderer: function(value,metaData,record){
						if(value==0){
							return '否';
						}else{
							return '是';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'issued_flag'],
							data : [[0, '否'], [1, '是']]
						}),
						displayField : 'issued_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				},
				{
					text: '资源预值',
					dataIndex: 'iosbasevalue',
					width: 100,
					hidden : true,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},{
					text : '操作',
					dataIndex : 'iagentstate',
					width : 90,
					renderer : function (value, meta, record) {
						cord.push(record)
						meta.style = 'white-space:normal;word-break:break-all;';
						var url ="";
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						var agentPort = record.get('iagentport');
						if('0' ==value){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else if('1' == value ){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}else if('2' == value || '3' == value){
							//将待处理按钮 全部变成停止按钮 2022.9.2 修改
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else{
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}
						return url;
					}
				},{
					text : '是否通用',
					dataIndex : 'isusp',
					flex : 1,
					hidden : true
				}];
			}
			else{
				agentColumns = [{
					text: '序号',
					width: 65,
					xtype: 'rownumberer'
				},
				{
					text: 'IID',
					dataIndex: 'iid',
					width: 45,
					hidden: true
				},
				{
					text: '升级信息ID',
					dataIndex: 'iagentUpId',
					width: 45,
					hidden: true
				},
				iagentname,
				{
					text: '描述',
					dataIndex: 'iagentdesc',
					hidden: !ismustdcid,
					width: 100,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},
				ip,
				{
					text: '端口号',
					dataIndex: 'iagentport',
					width: 80,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
						text: '采集系统名称',
						dataIndex: 'iname',
						width: 100,
						hidden:!displaySystemName
					},
				{
					text: 'WEB端口号',
					dataIndex: 'iagentwebport',
					hidden:!suppercheck||isHSBank,
					width: 100,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: '守护端口号',
					dataIndex: 'iagentguardport',
					width: 100,
					hidden:true,
					editor: {
						xtype: 'numberfield',
						maxValue: 65535,
						minValue: 0
					}
				},{
					text: 'iagentnetid',
					dataIndex: 'iagentnetid',
					width: 100,
					hidden: true,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},{
					text: '数据中心',
					dataIndex: 'idcid',
					width: 80,
					hidden:!ismustdcid,
					editor: dataCenterCombo,
					renderer : function (value, p, record, rowIndex) {
						var backValue = "";
						if (value != '') {
							var index = dataCenterStore.findExact("iid", value);
							if (index != -1) {
								backValue = dataCenterStore.getAt(index).data.iname;
							}
						}
						return backValue;
					}
				},
				{
					text: '所属集群',
					dataIndex: 'iagentclustername',
					hidden: true,
					width: 100/*,
			        editor: {
			            xtype: 'combobox',
			            margin: '5',
			            store: clusterStore,
			            editable: false,
			            queryMode: 'local',
			            forceSelection: true,
			            // 要求输入值必须在列表中存在
			            typeAhead: true,
			            // 允许自动选择
			            displayField: 'iclustername',
			            valueField: 'iclustername',
			            triggerAction: "all"
			        }*/
					/*,
					renderer : function(value, metadata, record, rowIndex, colIndex, store, view) {
						var index = clusterStore.find('iclusterid', value);
						if (index != -1) {
							return clusterStore.getAt(index).data.iclustername;
						}
						return value;
					}*/
				}, {
					text: '设备或者虚拟机',
					dataIndex: 'iequipmentorvm',
					hidden: !isght,//默认展示 jiaMing 2022-5-18
					width: 100,
					//Agent字段维护：设备或者虚拟机字段类型为long,且后台并没有查询此字段。现定义此字段含义：1-物理机，2-虚拟机，3-容器 jiaMing 2022-5-18
                    renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
                            //return "e5";
                            return iequipmentorvmFn(value);
					}
				}, {
                    text: '服务名称',
                    dataIndex: 'serverName',
                    hidden: !isght,
                    width: 100

                },        {
					text: 'uuid',
					dataIndex: 'uuid',
					hidden: !isght,
					width: 100
				},{
					text: lnzgyhfhSwitch? '生产地址':'业务地址',
					dataIndex: 'ibusinessip',
					hidden:isignoreibusiness || isHSBank,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					},
					width: 100
				},{
					text: lnzgyhfhSwitch? '负责人':'业务系统',
					dataIndex: 'ibusinesssys',
					hidden:isignoreibusiness || isHSBank,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					},
					width: 100
				},{
					text: 'sn',
					dataIndex: 'sn',
					hidden: !isght,
					width: 100
				},{
					text: 'os_type',
					dataIndex: 'os_type',
					hidden: !isght,
					width: 100
				},{
					text: 'localip',
					dataIndex: 'localip',
					hidden: !isght,
					width: 100
				},{
					text: 'os_smalltype',
					dataIndex: 'os_smalltype',
					hidden: !isght,
					width: 100
//				},{
//					text: '是否云上设备',
//					dataIndex: 'isoncloud',
//					//hidden: true,
//					width: 100,
//			        editor: {
//			            xtype: 'combobox',
//			            margin: '5',
//			            store: isOnCloudStore,
//			           // editable: true,
//			            queryMode: 'local',
//			            forceSelection: true,
//			            // 要求输入值必须在列表中存在
//			            typeAhead: true,
//			            // 允许自动选择
//			            displayField: 'name',
//			            valueField: 'id',
//			            triggerAction: "all"
//			        },
//					renderer : function(value, metadata, record, rowIndex, colIndex, store, view) {
//
//						if (value == 'yes') {
//							return "是";
//						}else if(value=='no'){
//							return "否";
//						}
//						return "";
//					}
				},{
					text : '是否云上设备',
					dataIndex: 'isoncloud',
					hidden: !isghtorispf,
					width: 90,
					renderer: function(value,metaData,record){
						if(value=='no'){
							return '否';
						}else if(value=='yes'){
							return '是';
						}else{
							return '';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'isoncloud_flag'],
							data : [['yes', '是'], ['no', '否']]
						}),
						displayField : 'isoncloud_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				},
				 {
					text: '操作系统',
					dataIndex: 'iagentos',
					width: 100
				}, {
					text: '计算机名',
					dataIndex: 'iagentcomputername',
					width: 100
				},{
					xtype : 'gridcolumn',
					dataIndex : 'systeminfo',
					hidden:isignornewagentinfo,
					text : '信息系统名称'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'centername',
					hidden:isignornewagentinfo,
					text : '所属区域'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'sysadmin_a',
					hidden:isignornewagentinfo,
					text : '系统管理员A角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'sysadmin_b',
					hidden:true,
					text : '系统管理员B角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'appadmin_a',
					hidden:isignornewagentinfo,
					text : '应用管理员A角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'appadmin_b',
					hidden:true,
					text : '应用管理员B角'
				}, {
					xtype : 'gridcolumn',
					dataIndex : 'ICMDB_UPDATE_LASTTIME',
					text : 'CMDB上次同时时间',
					hidden:isignornewagentinfo || isHSBank,
					width : 150
				},  {xtype : 'gridcolumn',
					dataIndex : 'ICMDB_UPDATE_DESC',
					text : 'CMDB同步状态',
					hidden:isignornewagentinfo || isHSBank,
					width : 150
				},
					{
						text: 'proxy名称',
						dataIndex: 'proxyName',
						width: 100
					},{
					text: 'proxyIp',
//					dataIndex: 'iagentnetid',
					dataIndex: 'proxyIp',
					hidden:true,
					width: 100
				}, {
					text: 'AZ名称',
					dataIndex: 'iagentazname',
					hidden:true,
					width: 100
				}, {
					text: '启动用户',
					dataIndex: 'iagentstartuser',
					width: 100
				},
					{
						text: '所属团队',
						dataIndex: 'iteam',
						hidden: !lnzgyhfhSwitch,
						width: 150,
						editor: {
							xtype: 'textfield',
							maxLength: 100
						}
					},{
					text : '开启SSL',
					dataIndex: 'isSSL',
					width: 90,
					renderer: function(value,metaData,record){
						if(value==0){
							return '否';
						}else if(value==1){
							return '是';
						}else{
							return '';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'isSSL_flag'],
							data : [[1, '是'], [0, '否']]
						}),
						displayField : 'isSSL_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				}, {
					text: '版本',
					dataIndex: 'iagentversion',
					width: 70
				}, {
					text: '自定义命令',
					dataIndex: 'icustom_cmd',
					hidden: isHSBank,
					width: 120
				}, {
					text: '自定义详情',
					dataIndex: 'icustom_mess',
					hidden: isHSBank,
					width: 120
				},{
					text: '活动数量',
					dataIndex: 'iagentactivitynum',
					hidden:true,
					width: 100,
					renderer: function(value,metaData,record){
						return '<a href="javascript:void(0);" onclick="showActivityNum('+ record.get('iid') +');">'+value+'</a>';
					}
				}, {
					text: '状态',
					dataIndex: 'iagentstate',
					width: 80,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						var cls = "";
						if(value=='0') {
							cls = "<span class='Green_color State_Color'>正常</span>";
						} else if(value=='1') {
							cls = "<span class='Red_color State_Color'>异常</span>";
						} else if(value=='2') {
							return '升级中';
						}else if(value=='8'){
							return '卸载中';
						} else if(value=='3'){
							return '异常';
						} else if(value == '4') {
							if(agentPauseRecoverSwitch){
								return "<span class='Red_color State_Color'>暂停</span>";
							}else{
								return "<span class='Red_color State_Color'>异常</span>";
							}
						}/*else if(value=='7'){
							return '禁用';
						}*/else {
							return '新建';
						}
						return cls;
					}
				},{
					text: '系统状态',
					dataIndex: 'systemStatus',
					width: 80,
					hidden: !czFlag
				},{
					text: '是否变化',
					dataIndex: 'iagentCchange',
					width: 80,
					renderer:function(value,p,record,rowIndex){
						var backMessage = "";
						if(value==1){
							backMessage = "<span style='background: #fe969e;color:#ffffff;padding: 2px;'>是</span>";
						}else{
							backMessage = "否";
						}
						return backMessage;
					},
					listeners:{
						click:function(a,b,c,d){
							var x=a.getStore().getAt(c).data.iagentCchange;
							var iid=a.getStore().getAt(c).data.iid;
							if (x==1){
								onclickBH(iid);
							}
						}
					}
				}, {
					xtype : 'actioncolumn',
					text : '应用标识配置',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [ {
						iconCls:'role_permission',
						tooltip : '应用标识',
						handler : setPermission
					} ]
				},{
						iconCls:'role_permission',
						header : '数据库配置',
						align : 'left',
						hidden: !agentmaintenance,
						xtype:'actioncolumn',
						width : 200,
						dataIndex : 'iid',
						items : [
							{
								tooltip: '数据库配置',
								text : '数据库配置',
								handler : function(grid, rowIndex) {
									var iid = grid.getStore().getAt(rowIndex).get('iid');
									showagentMTWin(iid);
								}


							}]
					},{
					xtype : 'actioncolumn',
					text : '详情',
					width : 100,
					sortable : false,
					menuDisabled : true,
					items : [{	iconCls:'monitor_curve',
						tooltip : '并发度实时监控',
						handler : function (a,b,c,d,e,record) {
							var ip = record.data.iagentip;
							var port = record.data.iagentport;
//							console.log('agnet ====' , ip + ':' + port);
							showAgentMonitor(ip,port);
						}
					}
					// 20210610 sxh 浙商漏洞 屏蔽下载agent日志功能
					 ,
					 {	iconCls:'monitor_download',
					 	tooltip : '下载远程日志',
					 	handler : function (a,b,c,d,e,record) {
					 		var state = record.data.iagentstate;
					 		var ip = record.data.iagentip;
					 		var port = record.data.iagentport;
					 		if(state!=0)
					 		{
					 			Ext.Msg.alert('警告', '不能下载');
					 		}else{
					 			showAgentLogList(ip,port);
					 		}
					 	}
					 }
					,{
						//icon : 'images/permission.png',
						iconCls:'monitor_search',
						tooltip : '升级配置详情',
						handler : function (grid, rowIndex) {
							var agentInfo = grid.getStore().getAt(rowIndex);
							if (agentInfo.get('iid')) {
								var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
								'<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
								'<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p>'+
								'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
								'<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
								'<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
								'<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p>'+
								'<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
								'<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
								'<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
								'<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';

								Ext.Msg.show({
									title : '升级配置详情信息',
									msg : msgContent,
									buttons : Ext.Msg.OK,
									icon : Ext.MessageBox.INFO,
									width : 500
								});
							} else {
								Ext.Msg.alert('详细信息', '该记录没有详细信息!');
							}
						}
					} ]
				}, {
					dataIndex : 'ifsendMsg',
					width : 90,
					hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
					text : '是否发送报文',
					renderer: function(value,metaData,record){
						if(value==0){
							value = "发送";
						}else{
							value = "不发送";
						}
						return value;
					}
				},{
					text : '环境',
					dataIndex : 'ienvtype',
					width : 90,
					hidden : true,
					editor : envCombo/*,
					renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
						if(value==0) {
							return '测试';
						} else if(value==1) {
							return '生产';
						}
					}*/
				},{
					text : '查看用户',
					hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
					dataIndex: 'iid',
					width: 70,
					renderer: function(value,metaData,record){
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						return "<a href='#' script='javascript:void(0)' onclick=\"showUser('"+ agentId+"','"+agentIp+"'"+")\">用户</a>";
					}
				},{
					text : '纳管用户',
                    hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
					dataIndex: 'icreateuser',
					width: 120
				},{
					text : '纳管时间',
                    hidden:true,//默认不展示是否发送报文，查看用户，纳管用户，纳管时间字段 jiaMing 2022-5-18 start
					dataIndex: 'icreatetime',
					width: 150
				},{
					text : '是否下发',
					dataIndex: 'issued',
					width: 90,
					renderer: function(value,metaData,record){
						if(value==0){
							return '否';
						}else{
							return '是';
						}
					},
					editor : new Ext.form.ComboBox({
						store :  new Ext.data.SimpleStore({
							fields : ['id', 'issued_flag'],
							data : [[0, '否'], [1, '是']]
						}),
						displayField : 'issued_flag',
						valueField : 'id',
						typeAhead : true,
						triggerAction : 'all',
						allowBlank : false,
						forceSelection : true,
						mode : 'local'
					})
				},
				{
					text: '资源预值',
					dataIndex: 'iosbasevalue',
					width: 100,
					hidden : true,
					editor: {
						xtype: 'textfield',
						maxLength: 255
					}
				},{
					text : '操作',
					dataIndex : 'iagentstate',
					width : 90,
					renderer : function (value, meta, record) {
						cord.push(record)
						meta.style = 'white-space:normal;word-break:break-all;';
						var url ="";
						var agentId = record.get('iid');
						var agentIp = record.get('iagentip');
						var agentPort = record.get('iagentport');
						/*var url1 ="";
						if('7' ==value){
							url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('use','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启用</a>";
						}else{
							url1="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"banAgent('ban','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">禁用</a>";
						}*/
						if('0' ==value){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else if('1' == value ){
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}else if('2' == value || '3' == value){
							//将待处理按钮 全部变成停止按钮 2022.9.2 修改
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('stop','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'"+")\">停止</a>";
						}else{
							url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"onAgentOpt('start','"
								+ agentId +"','"+agentIp+"','"+agentPort+"'" +")\">启动</a>";
						}
						// return url+"&nbsp "+url1;
						return url;
					}
				},{
					text : '是否通用',
					dataIndex : 'isusp',
					flex : 1,
					hidden : true
				}];
			}

		}
	}

	var upgradeInfoColumns = [ {
		header: '序号', xtype: 'rownumberer',width:40},{
			text : 'ID',
			dataIndex : 'iagentup_id',
			flex : 1,
			hidden : true
		},{
			text : '升级源文件路径',
			flex : 1,
			dataIndex : 'isource_path'
		},{
			text : '升级目标文件路径',
			flex : 1,
			dataIndex : 'itarget_path'
		},{
			text : '备份路径',
			flex : 1,
			dataIndex : 'ibackup_path'
		},{
			text : '操作用户名',
			width : 100,
			dataIndex : 'ioperation_user'
		},{
			text : '操作系统',
			width : 80,
			dataIndex : 'ios_name'
		},{
			text : '连接方式',
			width : 70,
			dataIndex : 'iconnect_type'
		},{
			text : '连接端口',
			width : 70,
			dataIndex : 'iconnect_port'
		},{
			text : '是否通用',
			width : 70,
			dataIndex : 'isusp',
			hidden:!iscib && !czFlag,
			renderer : function(value, p, record, rowIndex) {
				var backValue = "否";
				if(iscib || czFlag){
					backValue = '是';
				}
				if(value=='1'){
					backValue = '是';
				}else{
					backValue = '否';
				}
				return backValue;
			}
		},{
			text : '文件传输类型',
			width : 90,
			dataIndex : 'itransmission_type'
		},{
			text : '文件服务器地址',
			dataIndex : 'itransmission_ip',
			width : 100
		},{
			text : '文件服务器端口',
			dataIndex : 'itransmission_prot',
			width : 100
		}, {
			text : '传输用户名',
			dataIndex : 'itransmission_user',
			width : 100
		}];

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});

	/** *********************Panel********************* */
	/** 业务系统类型列表panel* */

	var grid_panel = Ext.create('Ext.ux.ideal.grid.Panel', {
		cls:'customize_panel_back',
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
		border:true,
		region : 'center',
		store: agentStore,
		selModel: Ext.create('Ext.selection.CheckboxModel', {
			checkOnly: true,
			listeners:{
				select:function(rowModel, record, index, eOpts ){
					agentSelected.add(record.get("iid"),record);
				},
				deselect:function(rowModel, record, index, eOpts ){
					agentSelected.remove(record);
				}
			}
		}),
		plugins: [cellEditing/*, {
            ptype: 'rowexpander',
            expandOnDblClick: false,
            rowBodyTpl : new Ext.XTemplate(
                '<p><b>升级源文件路径:</b> {isource_path}</p>',
                '<p><b>升级目标文件路径:</b> {itarget_path}</p>',
                '<p><b>备份路径:</b> {ibackup_path}</p><br>',
                '<p><b>操作用户名:</b> {ioperation_user}</p>',
                '<p><b>操作系统:</b> {ios_name}</p>',
                '<p><b>连接方式:</b> {iconnect_type}</p>',
                '<p><b>连接端口:</b> {iconnect_port}</p><br>',
                '<p><b>文件传输类型:</b> {itransmission_type}</p>',
                '<p><b>文件服务器地址:</b> {itransmission_ip}</p>',
                '<p><b>文件服务器端口:</b> {itransmission_prot}</p>',
                '<p><b>传输用户名:</b> {itransmission_user}</p>')
        }*/],
        columnLines: true,
        columns: agentColumns
	});

	agentStore.on("load",function(store,records,successful,eOpts){
		if(!isFJNX){
			var selectRecords=[];
			Ext.Array.each(records,function(record){
				if(agentSelected.indexOfKey(record.get("iid"))>-1){
					selectRecords.push(record);
				}
			});
			grid_panel.getSelectionModel().select(selectRecords);
		}
	});

	grid_panel.on("cellclick",function(obj, td, cellIndex, record, tr, rowIndex, e, eOpts){
		var columnName = grid_panel.columnManager.getHeaderAtIndex( cellIndex ).dataIndex;
		if(columnName=="icustom_mess"){
			var icustom_messForm1 = Ext.create('Ext.form.Panel', {
				layout : 'fit',
				border : false,
				bodyPadding : 10,
				items : [ {
					xtype : 'textareafield',
					name : 'icustom_mess',
					value : record.get("icustom_mess")
				} ]
			});

			var icustom_messWindow = Ext.create('Ext.window.Window', {
				title:"自定义详情",
				modal : true,
				border : false,
				layout:'fit',
				closeAction: 'destroy',
				constrain: true,
				width : 800,
				height : 600,
				items :[icustom_messForm1]
			}).show();
		}else if(iscib && columnName=='iagentname'){
			var  sysWindow;
			Ext.define('sysModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				},{
					name : 'text',
					type : 'string'
				}]
			});

			var sysStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'sysModel',
				proxy : {
					type : 'ajax',
					url : 'getNewOrgManagementList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			var sysForm1 = Ext.create('Ext.form.Panel', {
				region : 'north',
				border : false,
				margins:grid_margin,
				bodyCls:'x-docked-noborder-top',
				dockedItems : [ {
					xtype : 'toolbar',
					dock : 'top',
					border : false,
					items : [{
						xtype : 'textfield',
						name : "queryString",
						emptyText : '--请输入系统名称--',
						listeners : {
							specialkey : function (field, e) {
								if (e.getKey() == Ext.EventObject.ENTER) {
									sysGrid1.ipage.moveFirst();
								}
							}
						}
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function () {
							sysGrid1.ipage.moveFirst();
						}
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '清空',
						handler : function () {
							sysForm1.getForm().findField("queryString").setValue('')
						}
					},"->",{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '确定',
						handler : function(){
							var m = sysGrid1.getView().getSelectionModel().getSelection();
							if(m.length == 0){
								Ext.Msg.alert('提示', '请选择一条记录!');
								return;
							}
							if (m.length > 1) {
								Ext.Msg.alert('提示', '请选择一条记录!');
								return;
							}
							Ext.Array.each(m, function(record) {
								var isysname = record.get('text');
								agentStore.getAt(rowIndex).set("iagentname",isysname);
								grid_panel.getView().refresh();
								sysWindow.close();
							});
						}
					}]
				} ]
			});

			var sysGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
				padding : panel_margin,
				cls:'customize_panel_back',
				ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				selModel : Ext.create('Ext.selection.CheckboxModel', {}),
				columns : [ {
					xtype : 'rownumberer',
					width : 60,
					text : '序号'
				}, {
					xtype : 'gridcolumn',
					width : 250,
					dataIndex : 'iid',
					text : '主键',
					hidden:true
				},{
					xtype : 'gridcolumn',
					flex : 1,
					dataIndex : 'text',
					text : '系统名称'
				}],
				store : sysStore
			});
			sysStore.on('beforeload', function (store, options) {
				var new_params = {
						queryString	: sysForm1.getForm().findField("queryString").getValue()
				};
				Ext.apply(sysStore.proxy.extraParams, new_params);
			});
			var sysPanel = Ext.create('Ext.panel.Panel', {
				region : 'center',
				layout : 'border',
				width : '100%',
				height : '100%',
				items : [ sysForm1, sysGrid1 ]
			});

			sysWindow = Ext.create('Ext.window.Window', {
				draggable: false,
				resizable : false,// 禁止缩放
				modal : true,
				closeAction : 'destroy',
				height : 600,
				width : 800,
				layout : 'fit',
				items : [ sysPanel ],
			}).show();
		}
	});


	upgradeInfoStore.addListener('load', function() {
		var records = [];// 存放选中记录
		var iAgentupId;
		var needSelect = true;
		//绑定不同的升级配置，页面不显示选定的配置
		var grid_data = grid_panel.getView().getSelectionModel().getSelection();
		Ext.Array.each(grid_data, function(record) {

			if(iAgentupId && iAgentupId!=record.get('iagentUpId')){
				needSelect = false;
			}
			console.log(1);
			console.log(needSelect);
			console.log(record.get('iagentUpId'));

			iAgentupId = record.get('iagentUpId');
		});
		if(needSelect){
			for (var i = 0; i < upgradeInfoStore.getCount(); i++) {
				var record = upgradeInfoStore.getAt(i);
				if (record.data.iagentup_id == iAgentupId) {
					records.push(record);
				}
			}

			selModel.select(records);// 选中记录
		}
	});
	selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: false,
		mode : "SINGLE"
	});
	var upgradeinfo_panel = Ext.create('Ext.grid.Panel', {
		cls:'customize_panel_back',
		store: upgradeInfoStore,
		selModel: selModel,
		margins : grid_margin,
		border:true,
		columnLines: true,
		columns: upgradeInfoColumns,
		dockedItems: [{
			xtype: 'toolbar',
			items: [{
				text: '绑定',
				icon: '',
				cls: 'Common_Btn',
				handler: function () {
					var data = upgradeinfo_panel.getView().getSelectionModel().getSelection();
					if (data.length != 1) {
						Ext.Msg.alert('提示', '请选择且只能选择一条记录!');
						return;
					}
					var grid_data = grid_panel.getView().getSelectionModel().getSelection();
					if (grid_data.length == 0) {
						Ext.Msg.alert('提示', '没有选择Agent记录!');
						return;
					}

					var agent_ids = [];
					var agent_upgrade_id;
					Ext.Array.each(grid_data, function(record) {
						var agentId = record.get('iid');
						if (agentId) {
							agent_ids.push(agentId);
						}
					});
					Ext.Array.each(data, function(record) {
						agent_upgrade_id = record.get('iagentup_id');
					});
					Ext.Ajax.request({
						url: 'agentBindUpgradeInfo.do',
						params: {
							agentIds: agent_ids.join(','),
							agentUpId: agent_upgrade_id
						},
						method: 'POST',
						success: function(response, opts) {
							var success = Ext.decode(response.responseText).success;
							var message = Ext.decode(response.responseText).message;
							// 当后台数据同步成功时
							Ext.Msg.alert('提示', message);
							ftpbindwin.close();
							grid_panel.ipage.moveFirst();
						},
						failure: function(result, request) {
							secureFilterRs(result, "操作失败！");
						}
					});
				}
			}]
		}]
	});

	if(isTabSwitch){
		/** 主Panel* */
		var fip_mainPanel = Ext.create('Ext.panel.Panel', {
			layout : 'border',
			renderTo: "agent_maintain_area"+agent_maintain_area_now,
			width: contentPanel.getWidth(),
			height: contentPanel.getHeight()-modelHeigth,
//		height: contentPanel.getHeight()-modelHeigth,
			bodyPadding : grid_margin,
			border : true,
			bodyCls:'service_platform_bodybg customize_stbtn',
			items: [query_form, grid_panel]
		});
	}else{
		/** 主Panel* */
		var fip_mainPanel = Ext.create('Ext.panel.Panel', {
			layout : 'border',
			renderTo: "agent_maintain_area",
			width: contentPanel.getWidth(),
			height: contentPanel.getHeight()-modelHeigth,
//		height: contentPanel.getHeight()-modelHeigth,
			bodyPadding : grid_margin,
			border : true,
			bodyCls:'service_platform_bodybg customize_stbtn',
			items: [query_form, grid_panel]
		});
	}

	/** 窗口尺寸调节* */
	contentPanel.on('resize',function() {
		fip_mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
//		fip_mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
		fip_mainPanel.setWidth(contentPanel.getWidth());
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
			function(obj, options, eOpts) {
		Ext.destroy(grid_panel);
		Ext.destroy(fip_mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
	/** *********************方法********************* */
	function clearQueryWhere() {
		query_form.getForm().findField("iteam").setValue('');
		query_form.getForm().findField("iagentName").setValue('');
		query_form.getForm().findField("osSmalltype").setValue('');
		query_form.getForm().findField("iagentIp").setValue('');
		query_form.getForm().findField("iagentDesc").setValue('');
		query_form.getForm().findField("iagentState").setValue('');
		query_form.getForm().findField("idcid").setValue('');
		query_form.getForm().findField("iagentosname").setValue('');
		query_form.getForm().findField("icreateuser").setValue('');
		query_form.getForm().findField("istarttime").setValue('');
		query_form.getForm().findField("iendtime").setValue('');
		query_form.getForm().findField("iagentcomputername").setValue('');
		query_form.getForm().findField("icustommess").setValue('');
		query_form.getForm().findField("iagentversion").setValue('');
		query_form.getForm().findField("proxyName").setValue('');
		query_form.getForm().findField("systeminfo").setValue('');
		query_form.getForm().findField("centername").setValue('');
		query_form.getForm().findField("sysadmin_a").setValue('');
		query_form.getForm().findField("appadmin_a").setValue('');
		query_form.getForm().findField("serverName").setValue('');
		query_form.getForm().findField("iequipmentOrVmId").setValue('');
		query_form.getForm().findField("fjnxSysName").setValue('');
		query_form.getForm().findField("fjnxEnvName").setValue('');

		//query_form.getForm().findField("sysName_combo").setValue('');
	}

	function setMessage(msg) {
		Ext.Msg.alert('提示', msg);
	}

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	/**删除CMDB定时任务*/
	function deleteCmdbTimeTask(){
		Ext.Ajax.request({
			url: 'deleteCmdbTimeTask.do',
			method: 'POST',
			success: function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				Ext.Msg.alert('提示', message);
			},
			failure: function(result, request) {
				secureFilterRs(result, "操作失败！",request);
			}
		});
	}

	function syncAgent(){

		var data = grid_panel.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要同步的记录!');
			return;
		} else {
			Ext.Msg.confirm("请确认", "是否真的要同步Agent信息？",
					function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(data, function(record) {
						var agentId = record.get('iid');
						// 如果同步的是幻影数据，什么多不做
						if (agentId) {
							ids.push(agentId);
						}
					});

					if(ids.length>0){
						Ext.Ajax.request({
							url: 'syncAgentMaintainInfos.do',
							params: {
								deleteIds: ids.join(',')
							},
							method: 'POST',
							success: function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								// 当后台数据同步成功时
								if (success) {
									agentStore.reload();
								}
								Ext.Msg.alert('提示', message);
							},
							failure: function(result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
					}
				}
			});
		}



	}

	function showagentMTWin(id) {
		var agentMTWin = Ext.create('Ext.window.Window', {
			draggable : true,// 禁止拖动
			resizable : false,// 禁止缩放
			modal : true,
			title : '数据库配置',
			closable : true,
			closeAction : 'destroy',
			id:'agentMTWin',
			width : 580,
			height : 360,
			layout : 'border',
			loader : {
				url : 'agentmaintenanceIndex.do',
				params : {
					iid : id
				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		}).show();
	}

	function saveAgent() {
		var m = agentStore.getModifiedRecords();
		if (m.length < 1) {
			setMessage('您没有进行任何修改，无需保存');
			return;
		}
		var jsonData = "[";
		var repetIps=new Array();
		for (var i = 0,len = m.length; i < len; i++) {
			var n = 0;
			var iteam = m[i].get("iteam").trim();
			var iosbasevalue = m[i].get("iosbasevalue").trim();
			var agentName = m[i].get("iagentname").trim();
			var agentDesc = m[i].get("iagentdesc").trim();
			var agentIp = m[i].get("iagentip").trim();
			var ibusinessip = m[i].get("ibusinessip").trim();
			var agentPort = m[i].get("iagentport").trim();
			var agentClusterName = m[i].get("iagentclustername").trim();
			//福建农信业务系统
			var ibusinesssys = m[i].get("ibusinesssys").trim();
			var ienvtype = m[i].get("ienvtype").trim();

			var issued = m[i].get("issued").trim();
			if(ibusinessip){
				if (!checkIP(ibusinessip)) {
					var msg = '业务地址:'+ibusinessip+'格式不正确! ipv4地址:xxx.xxx.xxx.xxx，ipv6需要首尾[]';
					if(lnzgyhfhSwitch){
						msg = '生产地址:'+ibusinessip+'格式不正确! ipv4地址:xxx.xxx.xxx.xxx，ipv6需要首尾[]';
					}
					setMessage(msg);
					return;
				}
			}

			if(issued==null||issued==""){
				m[i].set("issued",0);
			}
			if ("" == agentIp || null == agentIp) {
				setMessage('地址不能为空！');
				return;
			}
			//福建农信不用验证agent名称需要验证业务系统
			if(isFJNX){
				if ("" == ibusinesssys || null == ibusinesssys) {
					setMessage('业务系统不能为空！');
					return;
				}
			}else{
				if ("" == agentName || null == agentName) {
					setMessage('Agent名称不能为空！');
					return;
				}
			}
			if (fucCheckLength(agentName) > 255) {
				setMessage('Agent名称不能超过255字符！');
				return;
			}
			if(fucCheckLength(iteam) > 100){
				setMessage('团队名称不能超过100字符！');
				return;
			}


			if (fucCheckLength(agentDesc) > 255) {
				setMessage('Agent描述不能超过255字符！');
				return;
			}
			if(agentDnsSwitchValue){
				if (!checkIP(agentIp)) {
					setMessage('Agent IP:'+agentIp+'格式不正确! ipv4地址:xxx.xxx.xxx.xxx，ipv6需要首尾[]');
					return;
				}
			}else{
				if (checkDNS(agentIp)) {
					setMessage('地址:'+agentIp+'长度不能超过255字符!');
					return;
				}
			}
			if(ismustdcid){
				var agentDcId = m[i].get("idcid").trim();
				if ("" == agentDcId || null == agentDcId) {
					setMessage('请选择数据中心！');
					return;
				}
			}
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0) jsonData = jsonData + ss;
			else jsonData = jsonData + "," + ss;
		}

		for (var j = 0; j < agentStore.getCount(); j++) {
			var agentRecord = agentStore.getAt(j);
			var agentIp = agentRecord.data.iagentip.trim();
			var agentPort = agentRecord.data.iagentport.trim();
			for (var k = 0; k < agentStore.getCount(); k++) {
				if(j!=k){
					var record = agentStore.getAt(k);
					var cip = record.data.iagentip.trim();
					var cport = record.data.iagentport.trim();
					if (cip == agentIp && cport == agentPort) {
						n = n + 1;
						if(Ext.Array.indexOf(repetIps,agentIp+":"+agentPort)==-1){
							repetIps=Ext.Array.push(repetIps,agentIp+":"+agentPort);
						}
					}
				}
			}

		}


		if (n > 1) {
//			setMessage('Agent IP '+repetIps.join(',')+'不能重复！');
//			return;
		}
		jsonData = jsonData + "]";

		Ext.Ajax.request({
			url: 'checkBindingById.do',
			method: 'POST',
			params: {
				jsonData: jsonData
			},
			success: function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (!success) {
					Ext.Msg.alert('提示', message);
					return;
				}else{
					Ext.Ajax.request({
						url: 'saveAgentMaitainInfos.do',
						method: 'POST',
						params: {
							jsonData: jsonData
						},
						success: function(response, request) {
							var success = Ext.decode(response.responseText).success;
							var message = Ext.decode(response.responseText).message;
							if (success) {
								agentStore.modified = [];
								agentStore.reload();
								agentSelected.clear();
							}
							Ext.Msg.alert('提示', message);
						},
						failure: function(result, request) {
							secureFilterRs(result, "保存失败！");
						}
					});
				}

			},
		});
	}
	function syncCmdb(){
		settime(Ext.getCmp('cztongbu'));
		Ext.MessageBox.wait("CMDB同步中...","提示");
		Ext.Ajax.request({
			url: 'syncCmdbAndSave.do',
			method: 'POST',
			success: function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				agentStore.modified = [];
				agentStore.reload();
				Ext.Msg.alert('提示', message);
				Ext.MessageBox.hide();
			},
			failure: function(result, request) {
				Ext.MessageBox.hide();
				secureFilterRs(result, "同步失败！");
			}
		});
	}
	function settime(btn){
		if(countdown == 0){
			btn.setDisabled(false);
			countdown = 18;
		}else{
			btn.setDisabled(true);
			countdown--;
			console.log();
			timeOutTime = setTimeout(function(){
				settime(btn)
			},1000);
		}
	}

	function checkIP(ip) {
		var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
		var reg6 = /^\[(([0-9a-fA-F]{1,4}:){7,7}([0-9a-fA-F]{1,4}|:)|([0-9a-fA-F]{1,4}:){1,6}(:[0-9a-fA-F]{1,4}|:)|([0-9a-fA-F]{1,4}:){1,5}((:[0-9a-fA-F]{1,4}){1,2}|:)|([0-9a-fA-F]{1,4}:){1,4}((:[0-9a-fA-F]{1,4}){1,3}|:)|([0-9a-fA-F]{1,4}:){1,3}((:[0-9a-fA-F]{1,4}){1,4}|:)|([0-9a-fA-F]{1,4}:){1,2}((:[0-9a-fA-F]{1,4}){1,5}|:)|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6}|:)|:((:[0-9a-fA-F]{1,4}){1,7}|:))\]$/;
		if (reg.test(ip)) {
			return true;
		} else if(reg6.test(ip)) {	//ipv6格式校验
			return true;
		} else {
			return false;
		}
	}
	function checkIPV4(ip) {
		var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
		if (reg.test(ip)) {
			return true;
		} else {
			return false;
		}
	}
	function checkIPV6(ip) {
		var reg6 = /^\[(([0-9a-fA-F]{1,4}:){7,7}([0-9a-fA-F]{1,4}|:)|([0-9a-fA-F]{1,4}:){1,6}(:[0-9a-fA-F]{1,4}|:)|([0-9a-fA-F]{1,4}:){1,5}((:[0-9a-fA-F]{1,4}){1,2}|:)|([0-9a-fA-F]{1,4}:){1,4}((:[0-9a-fA-F]{1,4}){1,3}|:)|([0-9a-fA-F]{1,4}:){1,3}((:[0-9a-fA-F]{1,4}){1,4}|:)|([0-9a-fA-F]{1,4}:){1,2}((:[0-9a-fA-F]{1,4}){1,5}|:)|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6}|:)|:((:[0-9a-fA-F]{1,4}){1,7}|:))\]$/;
		if(reg6.test(ip)) {	//ipv6格式校验
			return true;
		} else {
			return false;
		}
	}
	function checkDNS(ip) {
		var flag = false;
		if (parseInt(ip.length) > 250)
		{
			flag = true;
		}
		return flag;
	}

	function deleteAgent() {
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data=[]
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});

		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要删除的记录!');
			return;
		} else {
			Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？",
					function(button, text) {
				if (button == "yes") {
					var jsonData="[";
					for (var i = 0,len = data.length; i < len; i++) {
						var ss = Ext.JSON.encode(data[i].data);
						if (i == 0) jsonData = jsonData + ss;
						else jsonData = jsonData + "," + ss;
					}
					jsonData = jsonData + "]";
					Ext.Ajax.request({
						url: 'isAgentBinding.do',
						method: 'POST',
						params: {
							jsonData: jsonData
						},
						success: function(response, request) {
							var success = Ext.decode(response.responseText).success;
							var message = Ext.decode(response.responseText).message;
							if (success) {
								Ext.Msg.alert('提示', message);
								return;
							}else{
								var ids = [];
								Ext.Array.each(data, function(record) {
									var agentId = record.get('iid');
									// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
									if (agentId) {
										ids.push(agentId);
									}else{
										agentStore.remove(record);
									}
								});


								if(ids.length>0){
									Ext.Ajax.request({
										url : 'resourceServer/agentIsBindGroup.do',
										params : {
											agentid : ids.join(',')
										},
										method : 'POST',
										success : function(response, opts) {
											var count = Ext.decode(response.responseText).count;
											var agentCount = Ext.decode(response.responseText).agentCount;
											var message = Ext.decode(response.responseText).message;
											var state = Ext.decode(response.responseText).state;

											if (count>0 || agentCount>0) {
												Ext.Msg.confirm('提示',message,function(btn){
													if(btn=='yes'){
														var timetask = checkValidTTStart(ids);
														var oldHc = checkValidHcStart(ids);
														if(!oldHc[0]){
															Ext.Msg.alert('提示', oldHc[2]);
															return;
														}else{
															//fjnx开关开启时场景
															if(isFJNX && oldHc[0] && oldHc[1]){
																Ext.Msg.alert('提示', oldHc[2]);
																return;
															}

															if((oldHc[0] && oldHc[1])&& timetask[0]){
																Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+oldHc[2]+"<br>"+timetask[1], function(btnn, text) {
																	if(btnn=='yes'){
																		//可以删除
																		toDeleteAgent(ids,state);
																	}else{
																		return;
																	}
																});
															}else if((oldHc[0] && oldHc[1])||timetask[0]){
																if(oldHc[0] && oldHc[1]){
																	Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+oldHc[2], function(btnn, text) {
																		if(btnn=='yes'){
																			//可以删除
																			toDeleteAgent(ids,state);
																		}else{
																			return;
																		}
																	});
																}
																if(timetask[0]){
																	Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+timetask[1], function(btnn, text) {
																		if(btnn=='yes'){
																			//可以删除
																			toDeleteAgent(ids,state);
																		}else{
																			return;
																		}

																	});
																}
															}else if(!oldHc[0] && !oldHc[1]){
																Ext.Msg.alert('提示', oldHc[2]);
																return ;
															}else{
																//无流程巡检正在执行
																//无定时任务正在执行
																toDeleteAgent(ids,state);
															}
														}
													}
												},this);
											} else {
												var timetask = checkValidTTStart(ids);
												var hc = checkValidHcStart(ids);
												if(!hc[0]){
													Ext.Msg.alert('提示', hc[2]);
												}else{
													//fjnx开关开启时场景
													if(isFJNX && hc[0] && hc[1]){
														Ext.Msg.alert('提示', hc[2]);
														return;
													}

													if((hc[0] && hc[1])&& timetask[0]){
														Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+hc[2]+"<br>"+timetask[1], function(btnn, text) {
															if(btnn=='yes'){
																//可以删除
																toDeleteAgent(ids,state);
															}else{
																return;
															}
														});
													}else if((hc[0] && hc[1])||timetask[0]){
														if(hc[0] && hc[1]){
															Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+hc[2], function(btnn, text) {
																if(btnn=='yes'){
																	//可以删除
																	toDeleteAgent(ids,state);
																}else{
																	return;
																}
															});
														}
														if(timetask[0]){
															Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+timetask[1], function(btnn, text) {
																if(btnn=='yes'){
																	//可以删除
																	toDeleteAgent(ids,state);
																}else{
																	return;
																}

															});
														}
													}else if(!hc[0] && !hc[1]){
														Ext.Msg.alert('提示', hc[2]);
														return ;
													}else{
														//无流程巡检正在执行
														//无定时任务正在执行
														toDeleteAgent(ids,state);
													}
												}
											}
										}
									});
								}
							}
						},
						failure: function(result, request) {
							secureFilterRs(result, "操作失败！");
						}
					});
				}
			});
		}
	}
//	Ext.Msg.confirm("请确认", "是否真的要删除Agent信息？",
//	function(button, text) {
//	if (button == "yes") {

//	var ids = [];
//	Ext.Array.each(data, function(record) {
//	var agentId = record.get('iid');
//	// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
//	if (agentId) {
//	ids.push(agentId);
//	}else{
//	agentStore.remove(record);
//	}
//	});

//	if(ids.length>0){
//	Ext.Ajax.request({
//	url : 'resourceServer/agentIsBindGroup.do',
//	params : {
//	agentid : ids.join(',')
//	},
//	method : 'POST',
//	success : function(response, opts) {
//	var count = Ext.decode(response.responseText).count;
//	var agentCount = Ext.decode(response.responseText).agentCount;
//	var message = Ext.decode(response.responseText).message;
//	var state = Ext.decode(response.responseText).state;

//	if (count>0 || agentCount>0) {
//	Ext.Msg.confirm('提示',message,function(btn){
//	if(btn=='yes'){
//	Ext.Ajax.request({
//	url: 'deleteAgentMaintainInfos.do?state='+state,
//	params: {
//	deleteIds: ids.join(',')
//	},
//	method: 'POST',
//	success: function(response, opts) {
//	var success = Ext.decode(response.responseText).success;
//	var message = Ext.decode(response.responseText).message;
//	// 当后台数据同步成功时
//	if (success) {
//	agentStore.reload();
//	}
//	Ext.Msg.alert('提示', message);
//	},
//	failure: function(result, request) {
//	secureFilterRs(result, "操作失败！");
//	}
//	});
//	}


//	},this);
//	} else {
//	Ext.Ajax.request({
//	url: 'deleteAgentMaintainInfos.do?state='+state,
//	params: {
//	deleteIds: ids.join(',')
//	},
//	method: 'POST',
//	success: function(response, opts) {
//	var success = Ext.decode(response.responseText).success;
//	var message = Ext.decode(response.responseText).message;
//	// 当后台数据同步成功时
//	if (success) {
//	agentStore.reload();
//	}
//	Ext.Msg.alert('提示', message);
//	},
//	failure: function(result, request) {
//	secureFilterRs(result, "操作失败！");
//	}
//	});
//	}
//	}
//	});
//	}
//	}
//	});
//	}}
	//Agent删除巡检状态校验
	function checkValidHcStart(ids){
		var res=[];
		Ext.Ajax.request({
			url : 'deleteAgentValidForHc.do',
			params : {
				deleteIds : ids.join(',')
			},
			async: false,
			method : 'POST',
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				var ishave = Ext.decode(response.responseText).ishave;
				var ips = Ext.decode(response.responseText).ips;
				if (success) {
					//存在启动巡检的
					if(ishave){
						res.push(true);
						res.push(true);
						let  notic = isFJNX?"存在以下设备["+ips+"]，存在关联巡检指标绑定关系":"存在以下设备["+ips+"]巡检状态为启动状态";
						res.push(notic);
					}else{
						res.push(true);
						res.push(false);
					}

				} else {
					if ("没有操作权限"=== message){
						res.push(false);
						res.push(false);
						res.push(message)
					}else {
						res.push(false);
						res.push(false);
						res.push("进行agent关联流程巡检设备的巡检状态验证失败");
					}
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,'操作失败！');
				res.push(false);
				res.push(false);
				res.push("进行agent关联流程巡检设备的巡检状态验证失败");
			}
		});

		return res;
	}

	//Agent删除定时任务状态校验 deleteAgentValidForTT.do
	function checkValidTTStart(ids){
		var res=[];
		Ext.Ajax.request({
			url : 'deleteAgentValidForTT.do',
			params : {
				deleteIds : ids.join(',')
			},
			async: false,
			method : 'POST',
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success){
					res.push(success);
					res.push(message);
				}else {
					if ("没有操作权限"===message){
						res.push(success);
						res.push(message);
					}else {
						res.push(success);
						res.push(message);
					}
				}

			},
			failure : function(result, request) {
				secureFilterRs(result,'请求返回失败！');
				res.push(false);
				res.push("进行agent关联定时任务设备的任务状态验证失败");
			}
		});

		return res;

	}


	function toDeleteAgent(ids,state){
		Ext.Ajax.request({
			url: 'deleteAgentMaintainInfos.do?state='+state,
			params: {
				deleteIds: ids.join(',')
			},
			method: 'POST',
			success: function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				// 当后台数据同步成功时
				if (success) {
					agentStore.reload();
					agentSelected.clear();
				}
				//Ext.Msg.alert('提示', message);
				if(!success && message.indexOf("绑定模块") != -1){
					Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+message, function(btn, text) {
						if(btn=='yes'){
							// return ;
							Ext.Ajax.request({
								url: 'deleteTimeTaskAgentMaintainInfos.do?state='+state,
								params: {
									deleteIds: ids.join(',')
								},
								method: 'POST',
								success: function(response, opts) {
									var success = Ext.decode(response.responseText).success;
									var message = Ext.decode(response.responseText).message;
									// 当后台数据同步成功时
									if (success) {
										agentStore.reload();
										agentSelected.clear();
									}
									Ext.Msg.alert('提示', message);
								},
								failure: function(result, request) {
									secureFilterRs(result, "操作失败！");
								}
							})
						}
					});
				}else{
					Ext.Msg.alert('提示', message);
				}

			},
			failure: function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}

	function clearUpgradeFlag() {
		var data = grid_panel.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择记录!');
			return;
		} else {
			Ext.Msg.confirm("请确认", "是否真的要清除选中Agent的升级标记？",
					function(button, text) {
				if (button == "yes") {
					Ext.Msg.confirm("请确认", "请再次确认 -- 是否真的要清除选中Agent的升级标记？", function(button, text) {
						if (button == "yes") {
							var ids = [];
							Ext.Array.each(data, function(record) {
								var agentId = record.get('iid');
								if (agentId) {
									ids.push(agentId);
								}
							});

							Ext.Ajax.request({
								url: 'deleteUpgradeAgentFlag.do',
								params: {
									ids: ids.join(',')
								},
								method: 'POST',
								success: function(response, opts) {
									var success = Ext.decode(response.responseText).success;
									var message = Ext.decode(response.responseText).message;
									Ext.Msg.alert('提示', message);
									if(success) {
										grid_panel.ipage.moveFirst();
									}
								},
								failure: function(result, request) {
									secureFilterRs(result, "操作失败！");
								}
							});
						}
					});
				}
			});
		}
	}

	function upgradeAgent() {
//		var data = grid_panel.getView().getSelectionModel().getSelection();
//		var data=[];
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要升级的Agent!');
			return;
		} else {
			var msgContent = "";
			msgContent += "<ol>";
			msgContent += "<li>";
			msgContent += "升级Agent存在一定风险，请确认您已经了解所有存在的风险并且知道如何处理错误";
			msgContent += "</li>";
			msgContent += "<li>";
			msgContent += "Agent升级过程中，不接受Server发来的任何执行活动请求";
			msgContent += "</li>";
			if(!isMinShengBank){
				msgContent += "<li>";
				msgContent += "Agent的升级日志存在于Agent安装目录下的log/agentUpgrade.log中，如果遇到升级出现错误，可以查看该日志";
				msgContent += "</li>";
				msgContent += "<li>";
				msgContent += "Agent备份路径必须存在且为文件夹。";
				msgContent += "</li>";
				msgContent += "<li>";
				msgContent += "Agent升级目标路径必须存在且为文件夹。";
				msgContent += "</li>";
				msgContent += "<li>";
				msgContent += "Agent升级补丁必须为zip文件。";
				msgContent += "</li>";
			}
			msgContent += "<ol>";

			Ext.Msg.show({
				title : '请认真阅读以下提示',
				msg : msgContent,//Ext.util.Format.htmlDecode(action.result.MESSAGE),
				buttons : Ext.Msg.OKCANCEL,
				icon : Ext.MessageBox.INFO,
				width : 500,
				fn: function(btn) {
					if (btn == "ok") {
						var ids = [];
						Ext.Array.each(data,
								function(record) {
							var agentId = record.get('iid');
							if (agentId) {
								ids.push(agentId);
							}
						});
						var url="upgradeAgent.do";
						if(upgradePostalSwitch){
							//走民生银行守护进程升级逻辑
							url="upgradeAgentMSBank.do";
						}
						// 原有请求路径  'upgradeAgent.do'
						Ext.Ajax.request({
							url: url,
							params: {
								ids: ids.join(',')
							},
							method: 'POST',
							success: function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								if(success==true){
									upgradeAgentMonitor();
								}
								Ext.Msg.alert('提示', message);
							},
							failure: function(result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
					}
				}
			});
			/*Ext.Msg.confirm("请确认", "是否真的要升级选中的Agent吗？",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var agentId = record.get('iid');
                        if (agentId) {
                            ids.push(agentId);
                        }
                    });

                    Ext.Ajax.request({
                        url: 'upgradeAgent.do',
                        params: {
                            ids: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            });*/
		}
	}
	function installAgent() {
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择Agent记录');
			return;
		} else {
			//禁用按钮
			var ids = [];
			Ext.Array.each(data, function(record) {
				var agentId = record.get('iid');
				var osName = record.get('ios_name');
				var agentIP = record.get('iagentip');
				if (agentId) {
					if(osName=='' || osName==undefined || osName==null || osName.length==0){
						Ext.Msg.alert('提示','请配置设备'+agentIP+'的操作系统类型!');
						return;
					}
					ids.push(agentId);
				}else{
					Ext.Msg.alert('提示','请先将记录保存后再推送安装!');
					return;
				}
			});
			if(ids.length>0 && data.length==ids.length){
				//确认操作用户和密码，此处可修改
				showInstallWindow(data);
			}
		}
	}

	function batchStartStopAgent(){

//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择Agent记录');
			return;
		} else {
			//禁用按钮
			var ids = [];
			Ext.Array.each(data, function(record) {
				var agentId = record.get('iid');
				var osName = record.get('ios_name');
				var agentIP = record.get('iagentip');
				if (agentId) {
					if(osName=='' || osName==undefined || osName=='没有设置' || osName.length==0){
						Ext.Msg.alert('提示','请配置设备'+agentIP+'的操作系统类型!');
						return;
					}
					ids.push(agentId);
				}else{
					Ext.Msg.alert('提示','请先将记录保存后再推送安装!');
					return;
				}
			});
			if(ids.length>0 && data.length==ids.length){
				//确认操作用户和密码，此处可修改
				showAgentWindow1(data);
			}
		}

	}
	//cmdb信息同步
	function cmdbsync(){
		var m = grid_panel.getSelectionModel().getSelection();
		var jsonData = "[";
		if(m.length==0){
			if(!cmdbAgentMess){
				Ext.MessageBox.alert("提示", "请选择数据！");
				return;
			}

		}else{

			for (var i = 0; i < m.length; i++) {
				var date = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData + date;
				else
					jsonData = jsonData + "," + date;
			}
			}
		jsonData = jsonData + "]";
		//alert(jsonData);
		commonMask.show();
		Ext.Ajax.request({
			url : 'job/syncCmdbNew.do',
			timeout:60000,
			params : {
				jsonData : jsonData
			},
			method : 'POST',
			success : function(response) {
				commonMask.hide();
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if(success||message==''||message==null){
					if(cmdbAgentMess){
						Ext.Msg.alert('提示', message);
					}else{
						Ext.Msg.alert('提示', '更新成功');
					}

				}else{
					message='同步失败ip为:'+message;
					Ext.Msg.alert('提示', message);
				}
			}

	  })
	}
	

	function netscan(){	 
		var batchUpgradeForm = Ext.create('Ext.form.Panel', {
			region : 'north',
			bodyCls:'x-docked-noborder-top',
			baseCls:'customize_gray_back',
			dockedItems : [ {
				xtype : 'toolbar',
				baseCls:'customize_gray_back',
				dock : 'top',
				items : [{
					xtype : 'textfield',
					fieldLabel: '例子',
//					name : '例子',
					labelWidth: 35,
					readOnly:true,
					value:'***********-255',
					//emptyText : '--请输入IP--',
//					listeners : {
//						specialkey : function (field, e) {
//							if (e.getKey() == Ext.EventObject.ENTER) {
//								resultGrid1.ipage.moveFirst();
//							}
//						}
//					}
				},'->', {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '增加',
					handler :addUpgradeInfo
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '删除',
					handler :deleteNetScan
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '扫描',
					handler :startScan
				},
				
				
				
				{
					xtype : 'button',
					textAlign : 'center',
					cls : 'Common_Btn',
					text : '关闭',
					handler : function(){
						this.up('window').close();
					}
				}]
			} ]
		});

		var retStore = Ext.create('Ext.data.Store', {
			autoLoad:false,
			fields:['iid','iostype', 'isrcpath', 'idestpath','ibakpath','iupdateversion','iactfinishmaxwait','iwritebuffersize'],
			proxy: {
				type : 'ajax',
				url : '',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});

		var cellEditing_install = Ext.create('Ext.grid.plugin.CellEditing',{
			clicksToEdit : 2
		});
		var upgradeConfiguregrid =  Ext.create('Ext.grid.Panel',{
			cls:'customize_panel_back',
			ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			selModel : Ext.create('Ext.selection.CheckboxModel', {}),
			region : 'center',
			columns: [
			          { text: '序号',       width:40,xtype : 'rownumberer'},
			          { text: 'iid',       dataIndex: 'iid' ,width:40,hidden:true},			         
			          { text: '网段',   dataIndex: 'isrcpath' ,flex:1,editor:{xtype : 'textfield',allowBlank: false}},
			          {	text: '端口',
				  		dataIndex: 'iagentport',
				  		//width: 120,
				  		flex:1,
				  		editor: {
					  		xtype: 'numberfield',
					  		height: 32,
					  		maxValue: 65535,
					  		minValue: 0
					  		}
				  		},
			        	  ],
			        	  store:retStore,
			        	  plugins : [ cellEditing_install ] 
		});
		function deleteNetScan(){
			var selDatas =upgradeConfiguregrid.getSelectionModel().getSelection();
			Ext.Array.each(selDatas, function(record) {
				retStore.remove(record);
            });
		}
		function addUpgradeInfo(){
			var p = {
					iid : '0',
					isrcpath : '',
					//iagentport:0
			};
			retStore.insert(0, p);
		}
		function startScan() {
			var reg1 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(\-\d{1,2}|\-1\d\d|\-2[0-4]\d|\-25[0-5]){0,1}$/;

			var m = retStore.getModifiedRecords();
			if (m.length < 1) {
				setMessage('请输入网段！');
				return;
			}
			Ext.Msg.alert('提示', '扫描中！');
			var jsonData = "[";
			for (var i = 0, len = m.length; i < len; i++) {
				var netisrcpath = m[i].get("isrcpath").trim();
				if ("" == netisrcpath || null == netisrcpath) {
					setMessage('网段不能为空！');
					return;
				}
				if(!reg1.test(netisrcpath)){
					Ext.Msg.alert('提示','网段不对，请输入如下格式的IP地址或者IP地址段:</br>&nbsp;&nbsp;&nbsp;&nbsp;1、***********</br>&nbsp;&nbsp;&nbsp;&nbsp;2、***********-254 (扫描**********-*************)</br>IP地址最大值为255');
					return;
				}
				var netiagentport = m[i].get("iagentport");
				if ("" == netiagentport || null == netiagentport) {
					setMessage('端口不能为空！');
					return;
				}
				var ss = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"扫描中..."});
			checkMask.show();			
			Ext.Ajax.request({
				url : 'startNetScan.do',
				//url : 'startNetScanTest.do',
				timeout : 300000,
				method : 'POST',
				params : {
					jsonData : jsonData
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					//var message = Ext.decode(response.responseText).message;
					var retList = Ext.decode(response.responseText).dataList;
										
					checkMask.hide();
					
					if (success) {
						retStore.load();
						showNetscanMsg(retList);
					}else{
						checkMask.hide();
						secureFilterRs('提示', '扫描失败！');
					}
					//Ext.Msg.alert('提示', message);
				},
				failure : function(result, request) {
					checkMask.hide();
					secureFilterRs('提示', '扫描失败！');
				}
			});
		}


		var netScanWin = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"网段扫描",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			height : '50%',
			width : '50%',
			items :[batchUpgradeForm,upgradeConfiguregrid]
		}).show();
	}	
	
function showStartStopResult(){
	if(agentStartStopResultWindow){
		agentStartStopResultWindow.close();
	}

	Ext.define('resultModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		},{
			name : 'iagentip',
			type : 'string'
		},{
			name : 'iagentport',
			type : 'string'
		},{
			name : 'iresult',
			type : 'string'
		},{
			name : 'iresultmsg',
			type : 'string'
		},{
			name : 'itime',
			type : 'string'
		},{
			name : 'iosname',
			type : 'string'
		},{
			name : 'iusername',
			type : 'string'
		}]
	});

	   resultStore = Ext.create('Ext.data.Store', {
		   autoLoad : true,
		   autoDestroy : true,
		   model : 'resultModel',
		   proxy : {
			   type : 'ajax',
			   url : 'getStartStopResult.do',
			   reader : {
				   type : 'json',
				   root : 'dataList',
				   totalProperty : 'total'
			   }
		   }
	   });

	   resultStore.on('beforeload', function (store, options) {
			var new_params = {
					queryString	: resultForm1.getForm().findField("queryString").getValue(),
					itime : resultForm1.getForm().findField("itime").getValue()
			};
			Ext.apply(resultStore.proxy.extraParams, new_params);
		});


		var itimestore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			fields : [ 'itime' ],
			proxy : {
				type : 'ajax',
				url : 'getStartStopTime.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});

		var resultForm1 = Ext.create('Ext.form.Panel', {
			region : 'north',
			border : false,
			margins:grid_margin,
			bodyCls:'x-docked-noborder-top',
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				border : false,
				items : [{
					xtype : 'textfield',
					name : "queryString",
					emptyText : '--请输入IP--',
					listeners : {
						specialkey : function (field, e) {
							if (e.getKey() == Ext.EventObject.ENTER) {
								resultGrid1.ipage.moveFirst();
							}
						}
					}
				},{
					labelWidth: 37,
					width : '25%',
					name: 'itime',
					displayField: 'itime',
					valueField: 'itime',
					store: itimestore,
					queryMode: 'local',
					emptyText : '--选择启停时间--',
					editable:false,
					xtype: 'combobox',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								resultGrid1.ipage.moveFirst();
							}
						}
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function () {
						resultGrid1.ipage.moveFirst();
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function () {
						resultForm1.getForm().findField("queryString").setValue('')
						resultForm1.getForm().findField("itime").setValue('')
					}
				},'->',{
		            fieldLabel: '刷新时间(秒)',
		            labelAlign : 'right',
		            labelWidth : 89,
		            minValue :10,
		            name: 'refreshTime',
		            value: '60',
		            width : 140,
		            xtype: 'textfield'
		        },{
					xtype : 'button',
					baseCls : 'Common_Btn',
					text : '刷新',
					width: 58,
					handler : function() {
						if(refreshObj){
							clearInterval(refreshObj);
						}
						var refreshTime = resultForm1.getForm().findField("refreshTime").getValue();

						refreshObj = setInterval(refreshStartStopResult, parseInt(refreshTime)*1000);

						resultStore.load();
						resultStore.on('beforeload', function (store, options) {
							var new_params = {
									queryString	: resultForm1.getForm().findField("queryString").getValue(),
									itime : resultForm1.getForm().findField("itime").getValue()
							};
							Ext.apply(resultStore.proxy.extraParams, new_params);
						});
					}
				}]
			} ]
		});

		var resultGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
			padding : panel_margin,
			cls:'customize_panel_back',
			ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			selModel : Ext.create('Ext.selection.CheckboxModel', {}),
			columns : [ {
				xtype : 'rownumberer',
				width : 60,
				text : '序号'
			}, {
				xtype : 'gridcolumn',
				width : 250,
				dataIndex : 'iid',
				text : '主键',
				hidden:true
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iagentip',
				text : 'AgentIP'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iagentport',
				text : '端口'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iosname',
				text : '操作系统'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iresult',
				text : '启停状态'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iresultmsg',
				text : '备注'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'itime',
				text : '启停时间'
			},{
				xtype : 'gridcolumn',
				flex : 1,
				dataIndex : 'iusername',
				text : '操作用户'
			}],
			store : resultStore
		});

		var resultPanel = Ext.create('Ext.panel.Panel', {
			region : 'center',
			layout : 'border',
			width : '100%',
			height : '100%',
			items : [ resultForm1, resultGrid1 ]
		});

		agentStartStopResultWindow = Ext.create('Ext.window.Window', {
			draggable: false,
			resizable : false,// 禁止缩放
			modal : true,
			closeAction : 'destroy',
			height : 600,
			width : 1000,
			layout : 'fit',
			items : [ resultPanel ],
			listeners :
		    {
		        'close' : function ()
		        {
		        	clearInterval(refreshObj);
		        }
		    }
		}).show();

		function refreshStartStopResult(){
			resultStore.load();
			resultStore.on('beforeload', function (store, options) {
				var new_params = {
						queryString	: resultForm1.getForm().findField("queryString").getValue(),
						itime : resultForm1.getForm().findField("itime").getValue()
				};
				Ext.apply(resultStore.proxy.extraParams, new_params);
			});
		}

		refreshObj = setInterval(refreshStartStopResult, 60*1000);
	}

	function batchStartStopAgentDaemons(){
		var start = Ext.create('Ext.form.field.Radio', {
			width: 80,
			name:'radio',
			labelAlign : 'left',
			fieldLabel: '',
			boxLabel: '启动',
			inputValue : 0
		});
		var stop = Ext.create('Ext.form.field.Radio', {
			width: 100,
			name:'radio',
			labelAlign : 'left',
			fieldLabel: '',
			boxLabel: '停止',
			inputValue : 1
		});

		var opt =  Ext.create('Ext.form.RadioGroup', {
			name:'opt',
			fieldLabel: '操作',
			labelAlign : 'left',
			layout: 'column',
			width : '160',
			labelWidth: 60,
			labelStyle:'margin-top:12px;',
			margin:10,
			items:[start,stop],
			listeners:{
				afterrender:function () {
					opt.items.get(0).setValue(true);
				}
			}
		});



		var importForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items: [{
				xtype: 'filefield',
				fieldLabel: '选择文件',
				name: 'importFile',
				msgTarget: 'side',
				labelWidth: 60,
				labelAlign:'right',
				anchor: '100%',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;',
				buttonMargin : 5,
				margin:10
			},opt,{
				labelWidth: 60,
				labelAlign:'right',
				name: 'type',
				xtype: 'textfield',
				fieldLabel:"",
				anchor : '100%',
				margin:10,
				msgTarget: 'side',
				labelStyle:'margin-top:12px;',
				hidden:true
			}],
			buttonAlign: 'center',
			buttons : [
				{
					text : '导入',
					handler : function() {
						var form = importForm.getForm();
						var hdupfile=form.findField("importFile").getValue();
						var type = opt.getChecked()[0].inputValue;
						form.findField("type").setValue(type);
						var hdtmpFilNam=hdupfile;
						if(hdupfile==''){
							Ext.Msg.alert('提示',"请选择Excel文件...！");
							return ;
						}
						if (form.isValid()) {
							var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
							checkMask.show();
							form.submit({
								url: 'daemonsStartStopAgent.do',
								success: function(form, action) {
									if(action.result.success){
										checkMask.hide();
										form.owner.up('window').close();
										Ext.Msg.alert('提示', action.result.message);
									}else{
										checkMask.hide();
										Ext.Msg.alert('提示', action.result.message);
									}
								},
								failure: function(form, action) {
									checkMask.hide();
									secureFilterRs(form,action.result.message);
								}
							});
						}
					}
				}, {
					text : '取消',
					handler : function() {
						this.up('window').close();
					}
				}
			]
		});

		var win = new Ext.Window({
			title : '批量启停',
			autoScroll : false,
			autoDestroy : true,
			closeable : true,
			modal : true,
			width : 600,
			height: 300,
			resizable : false,
			plain : true,
			layout : 'fit',
			draggable:true,
			items : [importForm]
		});
		win.show();
	}
    function batchStartStopAgentExcel(){
    	var start = Ext.create('Ext.form.field.Radio', {
    		width: 80,
    		name:'radio',
    		labelAlign : 'left',
    		fieldLabel: '',
    		boxLabel: '启动',
    		inputValue : 0
    	});
    	var stop = Ext.create('Ext.form.field.Radio', {
    		width: 100,
    		name:'radio',
    		labelAlign : 'left',
    		fieldLabel: '',
    		boxLabel: '停止',
    		inputValue : 1
    	});

    	var opt =  Ext.create('Ext.form.RadioGroup', {
    		name:'opt',
    		fieldLabel: '操作',
    		labelAlign : 'left',
    		layout: 'column',
    		width : '160',
    		labelWidth: 60,
    		labelStyle:'margin-top:12px;',
    		margin:10,
    		items:[start,stop],
    		listeners:{
    			afterrender:function () {
    				opt.items.get(0).setValue(true);
    			}
    		}
    	});



		var importForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items: [{
				labelWidth: 60,
				labelAlign:'right',
				name: 'istartstoppath',
				xtype: 'textfield',
				fieldLabel:"脚本路径",
				anchor : '100%',
				margin:10,
				msgTarget: 'side',
				labelStyle:'margin-top:12px;',
			 },{
				xtype: 'filefield',
				fieldLabel: '选择文件',
				name: 'importFile',
				msgTarget: 'side',
				labelWidth: 60,
				labelAlign:'right',
				anchor: '100%',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;',
				buttonMargin : 5,
				margin:10
			},opt,{
				labelWidth: 60,
				labelAlign:'right',
				name: 'type',
				xtype: 'textfield',
				fieldLabel:"",
				anchor : '100%',
				margin:10,
				msgTarget: 'side',
				labelStyle:'margin-top:12px;',
				hidden:true
			}],
			buttonAlign: 'center',
			buttons : [
			           {
			        	   text : '导入',
			        	   handler : function() {
			        		   var form = importForm.getForm();
			        		   var istartstoppath = form.findField("istartstoppath").getValue();
			        		   var hdupfile=form.findField("importFile").getValue();
			        		   var type = opt.getChecked()[0].inputValue;
			        		   form.findField("type").setValue(type);
			        		   var hdtmpFilNam=hdupfile;
			        		   if(hdupfile==''){
			        			   Ext.Msg.alert('提示',"请选择Excel文件...！");
			        			   return ;
			        		   }
			        		   if(istartstoppath==''){
			        			   Ext.Msg.alert('提示',"请配置脚本路径...！");
			        			   return ;
			        		   }

			        		   if(istartstoppath.indexOf('start') == -1 && type == 0){
			        			   Ext.Msg.alert('提示','脚本名称未含有start标志!');
			        			   return;
			        		   }

			        		   if(istartstoppath.indexOf('stop') == -1 && type == 1){
			        			   Ext.Msg.alert('提示','脚本名称未含有stop标志!');
			        			   return;
			        		   }

			        			if (form.isValid()) {
			        				var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
			        				checkMask.show();
			        				form.submit({
			        					url: 'importBatchStartStopAgent.do',
			        					success: function(form, action) {
			        						if(action.result.success){
			        							checkMask.hide();
			        							form.owner.up('window').close();
			        							Ext.Msg.alert('提示', action.result.message);
			        						}else{
			        							checkMask.hide();
			        							Ext.Msg.alert('提示', action.result.message);
			        						}
			        					},
			        					failure: function(form, action) {
			        						checkMask.hide();
			        						secureFilterRs(form,action.result.message);
			        					}
			        				});
			        			}
			        	   }
			           }, {
			        	   text : '取消',
			        	   handler : function() {
			        		   this.up('window').close();
			        	   }
			           }
			           ]
		});

		var win = new Ext.Window({
			title : '批量启停',
			autoScroll : false,
			autoDestroy : true,
			closeable : true,
			modal : true,
			width : 600,
			height: 300,
			resizable : false,
			plain : true,
			layout : 'fit',
			draggable:true,
			items : [importForm]
		});
		win.show();
    }

	function batchUpgradeAgent(){

		var importForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items: [{
				xtype: 'filefield',
				fieldLabel: '选择文件',
				name: 'importFile',
				msgTarget: 'side',
				labelWidth: 60,
				labelAlign:'right',
				anchor: '100%',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;',
				buttonMargin : 5,
				margin:10
			}],
			buttonAlign: 'center',
			buttons : [
			           {
			        	   text : '导入',
			        	   handler : function() {
			        		   var form = importForm.getForm();
			        		   var hdupfile=form.findField("importFile").getValue();
			        		   var hdtmpFilNam=hdupfile;
			        		   if(hdupfile==''){
			        			   Ext.Msg.alert('提示',"请选择Excel文件...！");
			        			   return ;
			        		   }
			        		   importBatchUpgradeFile(form);

			        	   }
			           }, {
			        	   text : '取消',
			        	   handler : function() {
			        		   var form = this.up('form').getForm();
			        		   this.up('window').close();
			        	   }
			           }
			           ]
		});

		var win = new Ext.Window({
			title : '批量升级',
			autoScroll : false,
			autoDestroy : true,
			closeable : true,
			modal : true,
			width : 600,
			height: 200,
			resizable : false,
			plain : true,
			layout : 'fit',
			draggable:true,
			items : [importForm]
		});
		win.show();
	}
	function importBatchUpgradeFile(form){
		if (form.isValid()) {
			var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
			form.submit({
				url: 'batchUpgradeAgent.do',
				success: function(form, action) {
					if(action.result.success){
						form.owner.up('window').close();
						upgradeAgentMonitor();
					}
					Ext.Msg.alert('提示', action.result.message);
				},
				failure: function(form, action) {
					secureFilterRs(form,action.result.message);
				}
			});
		}
	}

	function batchInstallAgent(){
		var importForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items: [{
				xtype: 'filefield',
				fieldLabel: '选择文件',
				name: 'importFile',
				msgTarget: 'side',
				labelWidth: 60,
				labelAlign:'right',
				anchor: '100%',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;',
				buttonMargin : 5,
				margin:10
			}],
			buttonAlign: 'center',
			buttons : [
			           {
			        	   text : '导入',
			        	   handler : function() {
			        		   var form = importForm.getForm();
			        		   var hdupfile=form.findField("importFile").getValue();
			        		   var hdtmpFilNam=hdupfile;
			        		   if(hdupfile==''){
			        			   Ext.Msg.alert('提示',"请选择Excel文件...！");
			        			   return ;
			        		   }
			        		   importBatchFile(form);

			        	   }
			           }, {
			        	   text : '取消',
			        	   handler : function() {
			        		   var form = this.up('form').getForm();
			        		   this.up('window').close();
			        	   }
			           }
			           ]
		});

		var win = new Ext.Window({
			title : '批量安装',
			autoScroll : false,
			autoDestroy : true,
			closeable : true,
			modal : true,
			width : 600,
			height: 200,
			resizable : false,
			plain : true,
			layout : 'fit',
			draggable:true,
			items : [importForm]
		});
		win.show();
	}

	function batchUnloadAgent(){
		var importForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items: [{
				xtype: 'filefield',
				fieldLabel: '选择文件',
				name: 'importFile',
				msgTarget: 'side',
				labelWidth: 60,
				labelAlign:'right',
				anchor: '100%',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;',
				buttonMargin : 5,
				margin:10
			}],
			buttonAlign: 'center',
			buttons : [
				{
					text : '导入',
					handler : function() {
						var form = importForm.getForm();
						var hdupfile=form.findField("importFile").getValue();
						var hdtmpFilNam=hdupfile;
						if(hdupfile==''){
							Ext.Msg.alert('提示',"请选择Excel文件...！");
							return ;
						}
						importBatchUnloadFile(form);

					}
				}, {
					text : '取消',
					handler : function() {
						var form = this.up('form').getForm();
						this.up('window').close();
					}
				}
			]
		});

		var win = new Ext.Window({
			title : '批量卸载',
			autoScroll : false,
			autoDestroy : true,
			closeable : true,
			modal : true,
			width : 600,
			height: 200,
			resizable : false,
			plain : true,
			layout : 'fit',
			draggable:true,
			items : [importForm]
		});
		win.show();
	}

	function importBatchFile(form){
		if (form.isValid()) {
			var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
			form.submit({
				url: 'batchInstallAgent.do',
				success: function(form, action) {
					if(action.result.success){
						agentStore.modified = [];
						agentStore.reload();
						form.owner.up('window').close();
						var initEndTime = new Date();
						initEndTime.setDate(initEndTime.getDate());
						var min=initEndTime.getMinutes();
						initEndTime.setMinutes(min-2);
						initEndTime.setSeconds(0);
						installResult(initEndTime);
//						Ext.Msg.alert('提示', action.result.message);
					}else{
						Ext.Msg.alert('提示', action.result.message);
					}
				},
				failure: function(form, action) {
					secureFilterRs(form,action.result.message);
				}
			});
		}
	}

	function importBatchUnloadFile(form){
		if (form.isValid()) {
			var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
			form.submit({
				url: 'batchUnloadAgent.do',
				success: function(form, action) {
					if(action.result.success){
						agentStore.modified = [];
						agentStore.reload();
						form.owner.up('window').close();
						var initEndTime = new Date();
						initEndTime.setDate(initEndTime.getDate());
						var min=initEndTime.getMinutes();
						initEndTime.setMinutes(min-2);
						initEndTime.setSeconds(0);
						unloadResult(initEndTime);
//						Ext.Msg.alert('提示', action.result.message);
					}else{
						Ext.Msg.alert('提示', action.result.message);
					}
				},
				failure: function(form, action) {
					secureFilterRs(form,action.result.message);
				}
			});
		}
	}


	function showInstallWindow(data){
		if(install_window){
			install_window.close();
		}
		var uspStore = Ext.create('Ext.data.Store', {
			fields: ['iid', 'iname'],
			data : [
			        {"iid":"1", "iname":"是"},
			        {"iid":"2", "iname":"否"}
			        ]
		});
		var retStore;
		if(isMinShengBank){
			retStore = Ext.create('Ext.data.Store', {
				fields:['iid','iagentip', 'ioperation_user',"isource_path", 'ioperation_password','isusp','force'],
				data:data,
				proxy: {
					type: 'memory',
					reader: {
						type: 'json'
					}
				}
			});
		}else{
			retStore = Ext.create('Ext.data.Store', {
				fields:['iid','iagentip', 'ioperation_user', 'ioperation_password','isusp','force'],
				data:data,
				proxy: {
					type: 'memory',
					reader: {
						type: 'json'
					}
				}
			});
		}
		var cellEditing_install = Ext.create('Ext.grid.plugin.CellEditing',{
			clicksToEdit : 2
		});
		var installgrid =  Ext.create('Ext.grid.Panel',{
			cls:'customize_panel_back',
			region : 'center',
			columns: [
			          { text: '序号',    width:40,xtype : 'rownumberer'},
			          { text: 'iid',  dataIndex: 'iid' ,width:40,hidden:true},
			          { text: '设备IP',  dataIndex: 'iagentip' ,width:120},
			          { text: '操作用户',  dataIndex: 'ioperation_user' ,flex:1,editor:{xtype : 'textfield',allowBlank: false}},
			          { text: '操作密码',  dataIndex: 'ioperation_password' ,flex:1,editor : new Ext.form.TextField({
			        	  inputType:'password', //设置输入类型为password
			        	  allowBlank: false
			          }),
			          renderer:retNotView},
				      { text: '指令',  dataIndex: 'isource_path' ,hidden:!isMinShengBank, flex:1,editor:{xtype : 'textfield',allowBlank: false}},
			          {text: '是否通用',  dataIndex: 'isusp' ,width:70,hidden:!iscib && !czFlag,editor : {
			        	  xtype : 'combobox',
			        	  store : uspStore,
			        	  queryMode: 'local',
			        	  editable: false,
			        	  displayField: 'iname',
			        	  valueField: 'iid'
			          },
			          renderer : function(value, p, record, rowIndex) {
			        	  var backValue = "否";
			        	  if(iscib || czFlag){
			        		  backValue = '是';
			        	  }
			        	  if(value=='1'){
			        		  backValue = '是';
			        	  }else{
			        		  backValue = '否';
			        	  }
			        	  return backValue;
			          }},
			          { text: '强制安装',dataIndex:'force', width:120,xtype:'checkcolumn'}
			          ],
			          store:retStore,
			          plugins : [ cellEditing_install ]
		});

		install_window = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"待安装设备信息",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			width : 600,
			height : 400,
			items :[installgrid],
			buttonAlign:"center",
			buttons: [{
				text: '安装',
				handler: function() {
					//操作用户和密码不能为空
					var storedata = installgrid.getStore();
					var count = storedata.getCount();
					var installJson=[];
					for(var i=0;i<count;i++){
						var record = storedata.getAt(i);
						var chost = record.data.iagentip;
						var operuser = record.data.ioperation_user.trim();
						var operpasswd = record.data.ioperation_password.trim();
						var isusp = record.data.isusp;
						if(operuser=='' || operuser==undefined || operuser==null || operuser.length==0){
							Ext.Msg.alert('提示','请配置设备'+chost+'的操作用户!');
							return;
						}
						if(isusp!=1 && (operpasswd=='' || operpasswd==undefined || operpasswd==null || operpasswd.length==0)){
							Ext.Msg.alert('提示','请配置设备'+chost+'的操作密码!');
							return;
						}
						installJson.push(record.data);
					}

					Ext.MessageBox.show({
						msg: '正在安装...',
						progressText: 'install...',
						width:300,
						wait:true,
						waitConfig: {interval:200}
					});

					var installUrl = "installAgent.do";
					// if(isMinShengBank){
					// 	installUrl = "installAgentNew.do";
					// }

					Ext.Ajax.request({
						//						url: 'installAgent.do',
						url: installUrl,
						timeout:900000,//90秒
						params: {
							installJson: Ext.encode(installJson)
						},
						method: 'POST',
						success: function(response, opts) {
							var retList = Ext.decode(response.responseText);
							Ext.MessageBox.updateProgress(1, 'install completed ');
							Ext.defer(function () { Ext.MessageBox.close(); }, 300);
							showInstallMsg(retList);
							store.reload();
						},
						failure: function(result, request) {
							//Ext.MessageBox.updateProgress(1, 'install completed ');
							//Ext.defer(function () { Ext.MessageBox.close(); }, 300);
							store.reload();
							secureFilterRs(result, "操作失败！",request);
						}
					});
					install_window.close();
				}
			},{
				text: '关闭',
				handler: function() {
					install_window.close();
				}
			}]
		}).show();

	}

	function showAgentWindow1(data){
		if(startstop_window){
			startstop_window.close();
		}
		var uspStore = Ext.create('Ext.data.Store', {
			fields: ['iid', 'iname'],
			data : [
			        {"iid":"1", "iname":"是"},
			        {"iid":"2", "iname":"否"}
			        ]
		});
		var retStore = Ext.create('Ext.data.Store', {
			fields:['iid','iagentip','iagentport','isusp','iagentos'],
			data:data,
			proxy: {
				type: 'memory',
				reader: {
					type: 'json'
				}
			}
		});

		var cellEditing_install = Ext.create('Ext.grid.plugin.CellEditing',{
			clicksToEdit : 2
		});

		var startstopForm = Ext.create('Ext.form.Panel', {
			region : 'north',
			border : false,
			margins:grid_margin,
			cls:'customize_panel_back',
			bodyPadding : 20,
			autoScroll:true,
				items : [{
					labelWidth: 70,
					width : '95%',
					name: 'iagentstartstoppath',
					xtype: 'textfield',
					fieldLabel:"脚本路径<font color=red>*</font>",
					anchor : '90%',
					margin:'10'
				},{
					labelWidth: 70,
					width : '95%',
					name: 'ioperationuser',
					xtype: 'textfield',
					anchor : '90%',
					margin:'10',
					fieldLabel:"操作用户<font color=red>*</font>"
				},{
					fieldLabel: '操作密码',
					labelWidth: 70,
					width : '95%',
					name: 'ioperationpassword',
					xtype: 'textfield',
					anchor : '90%',
					margin:'10',
					inputType:'password'
				}]
		});

		var startstopGrid =  Ext.create('Ext.grid.Panel',{
			cls:'customize_panel_back',
			region : 'center',
			columns: [
			          { text: '序号',    width:40,xtype : 'rownumberer'},
			          { text: 'iid',  dataIndex: 'iid' ,width:40,hidden:true},
			          { text: '设备IP',  dataIndex: 'iagentip' ,flex:1},
			          { text: '设备端口',  dataIndex: 'iagentport' ,flex:1},
			          /*{text: '是否通用',  dataIndex: 'isusp' ,width:70,hidden:!iscib,editor : {
			        	  xtype : 'combobox',
			        	  store : uspStore,
			        	  queryMode: 'local',
			        	  editable: false,
			        	  displayField: 'iname',
			        	  valueField: 'iid'
			          },
			          renderer : function(value, p, record, rowIndex) {
			        	  var backValue = "否";
			        	  if(iscib){
			        		  backValue = '是';
			        	  }
			        	  if(value=='1'){
			        		  backValue = '是';
			        	  }else{
			        		  backValue = '否';
			        	  }
			        	  return backValue;
			          }}*/
			          ],
			          store:retStore,
			          plugins : [ cellEditing_install ]
		});

		var startstopPanel = Ext.create('Ext.panel.Panel', {
			region : 'center',
			layout : 'border',
			width : '100%',
			height : '100%',
			items : [ startstopForm, startstopGrid ]
		});

		startstop_window = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"待批量启停设备信息",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			width : 800,
			height : 600,
			items :[startstopPanel],
			buttonAlign:"center",
			buttons: [{
				text: '启动Agent',
				handler: function() {
					startAndStopAgent(0)
				}
			},{
				text: '停止Agent',
				handler: function() {
					startAndStopAgent(1)
				}
			},{
				text: '关闭',
				handler: function() {
					startstop_window.close();
				}
			}]
		}).show();
		function startAndStopAgent(type){
			var storedata = startstopGrid.getStore();
			var count = storedata.getCount();
			var installJson=[];

			var scriptPath = startstopForm.getForm().findField('iagentstartstoppath').getValue();
			if(scriptPath == null || scriptPath == ''){
				Ext.Msg.alert('提示','请配置脚本路径!');
				return;
			}

			if(scriptPath.indexOf('start') == -1 && type == 0){
				Ext.Msg.alert('提示','脚本名称未含有start标志!');
				return;
			}

			if(scriptPath.indexOf('stop') == -1 && type == 1){
				Ext.Msg.alert('提示','脚本名称未含有stop标志!');
				return;
			}

			var ioperationuser = startstopForm.getForm().findField('ioperationuser').getValue();
			if(ioperationuser == null || ioperationuser.trim() == ''){
				Ext.Msg.alert('提示','请配置操作用户!');
				return;
			}

			var ioperationpassword = startstopForm.getForm().findField('ioperationpassword').getValue()==null?"":startstopForm.getForm().findField('ioperationpassword').getValue()
			if((!iscib || !czFlag) && ioperationpassword.trim() == ""){
				Ext.Msg.alert('提示','请配置操作密码!');
				return;
			}

			for(var i=0;i<count;i++){
				var record = storedata.getAt(i);
				installJson.push(record.data);
			}

//			Ext.MessageBox.show({
//				msg: '正在批量操作...',
//				progressText: 'wait...',
//				width:300,
//				wait:true,
//				waitConfig: {interval:200}
//			});

			Ext.Ajax.request({
				url: 'batchStartStopAgent.do',
				timeout:900000,//90秒
				params: {
					installJson: Ext.encode(installJson),
					scriptPath : scriptPath,
					type : type,
					ioperationuser :ioperationuser,
					ioperationpassword :startstopForm.getForm().findField('ioperationpassword').getValue()==null?"":startstopForm.getForm().findField('ioperationpassword').getValue()
				},
				method: 'POST',
				success: function(response, opts) {
					//var retList = Ext.decode(response.responseText);
					//Ext.MessageBox.updateProgress(1, 'mission completed ');
					//Ext.defer(function () { Ext.MessageBox.close(); }, 300);
					//showStartStopMsg(retList);
					Ext.Msg.alert('提示', '启停任务发起成功！');
					//store.reload();
				},
				failure: function(result, request) {
					//Ext.MessageBox.updateProgress(1, 'install completed ');
					//Ext.defer(function () { Ext.MessageBox.close(); }, 300);
					//store.reload();
					secureFilterRs(result, "操作失败！",request);
				}
			});
			startstop_window.close();
		}
	}

	function showInstallMsg(retData){
		var retStore = Ext.create('Ext.data.Store', {
			fields:['ip', 'success', 'errMsg'],
			data:retData,
			proxy: {
				type: 'memory',
				reader: {
					type: 'json'
				}
			}
		});
		if(installMsg_window){
			installMsg_window.close();
		}
		installMsg_window = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"安装Agent结果",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			width : 500,
			height : '60%',
			items :[{
				xtype:'gridpanel',
				region : 'center',
				columns: [
				          { text: 'IP',  dataIndex: 'ip' ,width:120},
				          { text: '是否安装成功',
				        	  dataIndex: 'success',
				        	  renderer: function(value) {
				        		  return value=="true"?"<img src='images/normal.png'>":"<img src='images/red_light.png'>";
				        	  }
				          },
				          { text: '备注', dataIndex: 'errMsg', flex: 1 }
				          ],
				          store:retStore
			}],
			buttonAlign:"center",
			buttons: [{
				text: '关闭',
				handler: function() {
					installMsg_window.close();
				}
			}]
		}).show();
	}

	/*  function installAgent() {

    	var data = grid_panel.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择Agent记录');
            return;
        } else {
        	//禁用按钮
        	grid_panel.getDockedItems('toolbar[dock="top"]')[0].setDisabled(true);

        	var ids = [];
        	var flag = true;
            Ext.Array.each(data, function(record) {
                var agentId = record.get('iid');
                if (agentId) {
                	if(checkInstallMsg(record)){
                		ids.push(agentId);
                	}else{
                		flag=false;
                	}
                }
            });
            if(flag){
	        	 Ext.MessageBox.show({
	                 msg: '正在安装...',
	                 progressText: 'install...',
	                 width:300,
	                 wait:true,
	                 waitConfig: {interval:200}
	             });

            	Ext.Ajax.request({
                    url: 'installAgent.do',
                    timeout:900000,//90秒
                    params: {
                        ids: ids.join(',')
                    },
                    method: 'POST',
                    success: function(response, opts) {
                    	var retList = Ext.decode(response.responseText);
                    	Ext.MessageBox.updateProgress(1, 'install completed ');
                    	Ext.defer(function () { Ext.MessageBox.close(); }, 300);
                    	showInstallMsg(retList);
                    },
                    failure: function(result, request) {
                    	Ext.MessageBox.updateProgress(1, 'install completed ');
                    	Ext.defer(function () { Ext.MessageBox.close(); }, 300);
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }

            grid_panel.getDockedItems('toolbar[dock="top"]')[0].setDisabled(false);
        }
    }
    function checkInstallMsg(record){
    	iagentip=record.get('iagentip');
    	if(!record.get('isource_path') || record.get('isource_path')=='没有设置'){
    		Ext.Msg.alert('提示',iagentip+': 请关联Agent升级配置');
    		return false;
    	}
    	if(!record.get('itransmission_type') || record.get('itransmission_type')=='没有设置'){
    		if( !((record.get('iconnect_type')=='ssh' && ('linux'.indexOf(record.get('udpateosname').toLowerCase())>-1 || 'aix'.indexOf(record.get('udpateosname').toLowerCase())>-1))
        		|| ('windows'.indexOf(record.get('udpateosname').toLowerCase())>-1 &&  record.get('iconnect_type')=='net'))){
    			Ext.Msg.alert('提示',iagentip+': 请关联传输配置');
    			return false;
        	}
    	}

    	return true;
    }

    function showInstallMsg(retData){
    	var retStore = Ext.create('Ext.data.Store', {
    	    fields:['ip', 'success', 'errMsg'],
    	    data:retData,
    	    proxy: {
    	        type: 'memory',
    	        reader: {
    	            type: 'json'
    	        }
    	    }
    	});
    	if(installMsg_window){
    		installMsg_window.close();
    	}
    	installMsg_window = Ext.create('Ext.window.Window', {
    	    autoScroll : true,
    	    title:"安装Agent结果",
    	    modal : true,
    	    laout:'fit',
    	    resizable : true,
    	    closeAction: 'destroy',
    	    constrain: true,
    	    width : 500,
    	    height : 200,
    	    items :[{
    	    	xtype:'grid',
    	    	columns: [
    	    	          { text: 'IP',  dataIndex: 'ip' ,width:120},
    	    	          { text: '是否安装成功',
    	    	        	dataIndex: 'success',
    	    	        	renderer: function(value) {
    	    	                return value=="true"?"<img src='images/normal.png'>":"<img src='images/red_light.png'>"
    	    	            }
    	    	          },
    	    	          { text: '备注', dataIndex: 'errMsg', flex: 1 }
    	    	      ],
    	    	 store:retStore
    	    }],
    	    buttonAlign:"center",
    	    buttons: [{
                text: '关闭',
                handler: function() {
                	installMsg_window.close();
                }
            }]
    	}).show();
    }*/
	function showNetscanMsg(retData){
		var retStore = Ext.create('Ext.data.Store', {
			fields:['iip', 'iport','iosmatch', 'isexist'],
			data:retData,
			proxy: {
				type: 'memory',
				reader: {
					type: 'json'
				}
			}
		});
		if(netScanMsg_window){
			netScanMsg_window.close();
		}
		netScanMsg_window = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"扫描结果",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			width : 500,
			height : '60%',
			items :[{
				xtype:'gridpanel',
				region : 'center',
				columns: [
				          { text: 'IP',  dataIndex: 'iip' ,flex: 1},
				          //{ text: '端口',  dataIndex: 'iport' ,width:120},
				          { text: '操作系统类型',  dataIndex: 'iosmatch' ,hidden:true,width:120},
				          { text: '是否存在Agent',dataIndex: 'isexist',width:120,renderer: function(value) {
			        		  return value?"是":"否";
			        	  	}
				          },
				          //{ text: '备注', dataIndex: 'errMsg', flex: 1 }
				          ],
				          store:retStore
			}],
			buttonAlign:"center",
			buttons: [{
				text: '导出',
				handler: function() {
					console.log(retData);
					//netScanMsg_window.close();
					//window.location.href = 'exportIsystemTypeExceldesss.do?ids='+ids;
					//post('exportAllNetScan.do',{"username":p1,"userId":p2,"userCode":p3,"imgUrl":imgUrl});
					post('exportAllNetScan.do',{jsonData:Ext.encode(retData)});
					//post('exportIsystemTypeExceldes.do',retData);
				}
			},{
				text: '关闭',
				handler: function() {
					netScanMsg_window.close();
				}
			}]
		}).show();
	}
	function oneKey() {
		var record = grid_panel.getSelectionModel().getSelection();
		if(record.length==0){
			Ext.Msg.alert('提示', '请至少选择一条要获取的记录！');
			return;
		}else{
			var Ids=[];
			Ext.each(record,function(item){
				if(item.data.iid != 0){
					Ids.push(item.data.iid);
				}
			});
			Ext.Ajax.request({
				url: 'oneKeyGetAgentInfo.do',
				params:{agentIds: Ids.join(',')},
				method: 'POST',
				success:function(response){
					var text = Ext.JSON.decode(response.responseText);
					Ext.Msg.alert('提示', text.msg);

				},
				failure:function(form, action){
					switch (action.failureType) {
					case Ext.form.action.Action.CLIENT_INVALID:
						Ext.Msg.alert('提示', '连接异常！');
						break;
					case Ext.form.action.Action.SERVER_INVALID:
						Ext.Msg.alert('提示', action.result.msg);
					}
				}
			});
		}
	}
	function allKey(){

		Ext.Ajax.request({
			url: 'allKeyGetAgentInfo.do',
			params:{},
			method: 'POST',
			success:function(response){
				var text = Ext.JSON.decode(response.responseText);
				Ext.Msg.alert('提示', text.msg);

			},
			failure:function(form, action){
				switch (action.failureType) {
				case Ext.form.action.Action.CLIENT_INVALID:
					Ext.Msg.alert('提示', '连接异常！');
					break;
				case Ext.form.action.Action.SERVER_INVALID:
					Ext.Msg.alert('提示', action.result.msg);
				}
			}
		});

	}
	function stopAgentInfo() {
		Ext.Msg.confirm("停止全部Agent信息确认", "确定要停止全部Agent信息么？", function(id) {
			if (id == 'yes'){
				commonMask.show();
				Ext.Ajax.request({
					url: 'stopAgentInfo.do',
					params: {},
					timeout: 300000,
					method: 'POST',
					success: function(response, opts) {
						Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
						commonMask.hide();
					},
					failure: function(result, request) {
						secureFilterRs(result, "停止失败！");
						commonMask.hide();
					}
				});
			}
		});
	}
	function stopAgentInfoQuery(){
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});

		if (data.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条要停止的记录！');

			return;
		} else {
			var ids = [];
			var flag = true;
			Ext.Array.each(data, function(record) {

				var agentId = record.get('iid');
				if(agentId==""){
					Ext.Msg.alert('提示', '请先保存记录再停止获取Agent！');
					flag = false;
					return;
				}
				var agentState = record.get('iagentState');
				if (agentId) {
					ids.push(agentId);
				}
			});

			Ext.Msg.confirm("停止Agent信息确认", "确定要停止选中的Agent信息么？", function(id) {
				if (id == 'yes'){
					if(flag){
						commonMask.show();
						Ext.Ajax.request({
							url: 'stopAgentInfoQuery.do',
							params: {
								agentIds: ids.join(',')
							},
							timeout: 300000,
							method: 'POST',
							success: function(response, opts) {
								commonMask.hide();
								Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
							},
							failure: function(result, request) {
								commonMask.hide();
								secureFilterRs(result, "停止失败！");
							}
						});
					}
				}
			});
		}
	}
	function fetchAgentInfo() {
		Ext.Msg.confirm("获取全部Agent信息确认", "确定要获取全部Agent信息么？", function(id) {
			if (id == 'yes'){
				commonMask.show();
				Ext.Ajax.request({
					url: 'fetchAgentInfo.do',
					params: {},
					timeout: 300000,
					method: 'POST',
					success: function(response, opts) {
						Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
						commonMask.hide();
					},
					failure: function(result, request) {
						secureFilterRs(result, "获取失败！");
						commonMask.hide();
					}
				});
			}
		});
	}
	function fetchAgentInfoQuery(){
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条要获取的记录！');
			return;
		} else {
			var ids = [];
			var flag = true;
			Ext.Array.each(data, function(record) {
				var agentId = record.get('iid');
				if(agentId==""){
					Ext.Msg.alert('提示', '请先保存记录再获取Agent信息！');
					flag = false;
					return;
				}
				var agentState = record.get('iagentState');
				if (agentId) {
					ids.push(agentId);
				}
			});

			Ext.Msg.confirm("获取Agent信息确认", "确定要获取选中的Agent信息么？", function(id) {
				if (id == 'yes'){
					if(flag){
						commonMask.show();
						Ext.Ajax.request({
							url: 'fetchAgentInfoQuery.do',
							params: {
								agentIds: ids.join(',')
							},
							timeout: 300000,
							method: 'POST',
							success: function(response, opts) {
								commonMask.hide();
								Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
							},
							failure: function(result, request) {
								commonMask.hide();
								secureFilterRs(result, "获取失败！");
							}
						});
					}
				}
			});
		}
	}
	function batchUpgradeAgentConfigure(){
		var batchUpgradeForm = Ext.create('Ext.form.Panel', {
			region : 'north',
			bodyCls:'x-docked-noborder-top',
			baseCls:'customize_gray_back',
			dockedItems : [ {
				xtype : 'toolbar',
				baseCls:'customize_gray_back',
				dock : 'top',
				items : [ {
					xtype : 'textfield',
					fieldLabel: '传输地址',
					name : 'ftpid',
					hidden:true,
					readOnly:true
				},{
					xtype : 'textfield',
					fieldLabel: '传输地址',
					name : 'ftpip',
					readOnly:true
				},{
					xtype : 'textfield',
					fieldLabel: '传输方式',
					name : 'ftptype',
					readOnly:true
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '绑定',
					handler :bindFtpInfo
				},'->', {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '增加',
					handler :addUpgradeInfo
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '删除',
					handler:deleteUpgradeInfo
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '保存',
					handler :saveUpgradeInfo
				},{
					xtype : 'button',
					textAlign : 'center',
					cls : 'Common_Btn',
					text : '关闭',
					handler : function(){
						this.up('window').close();
					}
				}]
			} ]
		});
		Ext.Ajax.request({
			url: 'getBatchUpgradeFtpInfo.do',
			method: 'POST',
			success: function(response, opts) {
				var transmisionId = Ext.decode(response.responseText).ftpid;
				var transmissionIp = Ext.decode(response.responseText).ftpip;
				var transmissionType = Ext.decode(response.responseText).ftptype;
				if(transmisionId){
					batchUpgradeForm.getForm().findField('ftpid').setValue(transmisionId);
					batchUpgradeForm.getForm().findField('ftpip').setValue(transmissionIp);
					batchUpgradeForm.getForm().findField('ftptype').setValue(transmissionType=="0"?"ftp":"sftp");
				}
			},
			failure: function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});

		function bindFtpInfo(){
			var iftpinoid = batchUpgradeForm.getForm().findField('ftpid').getValue();
			var transkeyStore = Ext.create('Ext.data.Store', {
				fields : [ 'transmissionId', 'transmissionType', 'transmissionIp', 'transmissionUser', 'transmissionPwd','transmissionPort', 'ischeck'],
				remoteSort: true,
				pageSize : 30,
				proxy : {
					type : 'ajax',
					url : 'getTrsPrimaryKey.do',
					reader : {
						type : 'json',
						root : 'tranIdList'
					}
				}
			});
			var selModelp = Ext.create('Ext.selection.CheckboxModel', { checkOnly : false,mode:'SINGLE',allowDeselect:true});
			transkeyStore.addListener('load', function() {
				var records = [];// 存放选中记录
				for (var i = 0; i < transkeyStore.getCount(); i++) {
					var record = transkeyStore.getAt(i);
					if (record.data.transmissionId == iftpinoid) {
						records.push(record);
					}
				}
				selModelp.select(records);// 选中记录
			});
			var primaryKeyGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
				cls:'customize_panel_back',
				ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				border : true,
				store : transkeyStore,
				columns : [ {
					text : 'ischeck',
					dataIndex : 'ischeck',
					flex:1,
					hidden : true
				}, {
					text : '传输配置主键',
					dataIndex : 'transmissionId',
					flex:1,
					hidden : true
				}, {
					text : '文件传输类型',
					dataIndex : 'transmissionType',
					flex:1,
					renderer : function(value, p, record, rowIndex){
						var backValue = "";
						if(value == "0"){
							backValue = "ftp";
						}else if(value == "1"){
							backValue = "sftp";
						}
						return backValue;
					}
				}, {
					text : '文件服务器地址',
					dataIndex : 'transmissionIp',
					flex:1
				}, {
					text : '文件服务器端口',
					dataIndex : 'transmissionPort',
					flex:1
				}, {
					text : '传输用户名',
					dataIndex : 'transmissionUser',
					flex:1
				}, {
					text : '传输密码',
					dataIndex : 'transmissionPwd',
					renderer:retNotView,
					sortable: false,
					flex:1
				} ],
				forceFit : true,
				selModel : selModelp,
				viewConfig:{
					enableTextSelection:true
				},
				dockedItems : [ {
					xtype : 'toolbar',
					dock : 'top',
					items : [ '->',{
						xtype : 'button',
						textAlign : 'center',
						cls : 'Common_Btn',
						text : '保存',
						handler : saveRole
					},{
						xtype : 'button',
						textAlign : 'center',
						cls : 'Common_Btn',
						text : '关闭',
						handler : function(){
							this.up('window').close();
						}
					} ]
				} ]
			});
			transkeyStore.on('beforeload', function(store, options) {
				var new_params = {
						agentUpId : 0
				};
				Ext.apply(transkeyStore.proxy.extraParams, new_params);
			});
			transkeyStore.load();
			var ftpWindow = Ext.create('Ext.window.Window', {
				title : '传输配置信息',
				height : '60%',
				width : '60%',
				modal : true,
				layout : 'fit',
				items : [ primaryKeyGrid ],
				listeners : {
					close : function(g, opt) {
						primaryKeyGrid.destroy();
					}
				}
			}).show();

			function saveRole() {
				var checkedRecords = primaryKeyGrid.getSelectionModel().getSelection();
				if(checkedRecords.length>1){
					Ext.Msg.show({
						title : '提示',
						msg: '最多只能保存一条记录，请重新选择！',
						buttons: Ext.Msg.OK,
						icon: Ext.Msg.INFO

					})
					return;
				}
				var transmisionId = 0;
				var transmissionIp = '';
				var transmissionType = '';
				if(checkedRecords.length==1){
					transmisionId = checkedRecords[0].get('transmissionId');
					transmissionIp = checkedRecords[0].get('transmissionIp');
					transmissionType = checkedRecords[0].get('transmissionType');
				}
				Ext.Ajax.request({
					url : 'saveBatchUpgradeFtpInfo.do',
					params : {
						transmisionId : transmisionId
					},
					success : function(response) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							Ext.Msg.alert("提示", "保存成功！");
							batchUpgradeForm.getForm().findField('ftpid').setValue(transmisionId);
							batchUpgradeForm.getForm().findField('ftpip').setValue(transmissionIp);
							batchUpgradeForm.getForm().findField('ftptype').setValue(transmissionType=="0"?"ftp":"sftp");
						} else {
							Ext.Msg.alert("提示", "保存失败！");
						}
					},
					failure : function(result, request) {
						secureFilterRs(result,"操作失败！",request);

					}
				});
			}

		}


		var retStore = Ext.create('Ext.data.Store', {
			autoLoad:true,
			fields:['iid','iostype', 'isrcpath', 'idestpath','ibakpath','iupdateversion','iactfinishmaxwait','iwritebuffersize'],
			proxy: {
				type : 'ajax',
				url : 'getBatchUpgradeModelList.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});
		var iostypeStore = Ext.create('Ext.data.Store', {
			fields: ['iid', 'iname'],
			data : [
			        {"iid":0, "iname":"Windows"},
			        {"iid":1, "iname":"Linux"},
			        {"iid":2, "iname":"Aix"},
			        {"iid":3, "iname":"HPUX"},
			        {"iid":4, "iname":"非Windows"}
			        ]
		});
		var iostypeComboBox = Ext.create("Ext.form.ComboBox", {
			typeAhead: true,
			triggerAction: 'all',
			lazyRender: true,
			queryMode : 'local',
			editable: false,
			allowBlank : false,
			store:  iostypeStore,
			valueField: 'iid',
			displayField: 'iname'
		});
		var cellEditing_install = Ext.create('Ext.grid.plugin.CellEditing',{
			clicksToEdit : 2
		});
		var upgradeConfiguregrid =  Ext.create('Ext.ux.ideal.grid.Panel',{
			cls:'customize_panel_back',
			ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			selModel : Ext.create('Ext.selection.CheckboxModel', {}),
			region : 'center',
			columns: [
			          { text: '序号',       width:40,xtype : 'rownumberer'},
			          { text: 'iid',       dataIndex: 'iid' ,width:40,hidden:true},
			          { text: '操作系统',    dataIndex: 'iostype' ,width:120,editor : iostypeComboBox,
			        	  renderer:function(value){
			        		  var record = iostypeComboBox.findRecord(iostypeComboBox.valueField, value);
			        		  return record
			        		  ? record.get(iostypeComboBox.displayField)
			        				  : iostypeComboBox.valueNotFoundText;
			        	  }},
			        	  { text: '源文件路径',   dataIndex: 'isrcpath' ,flex:1,editor:{xtype : 'textfield',allowBlank: false}},
			        	  { text: '目标文件路径',  dataIndex: 'idestpath' ,flex:1,editor:{xtype : 'textfield',allowBlank: false} },
			        	  { text: '备份文件路径',  dataIndex:'ibakpath', flex:1,editor:{xtype : 'textfield',allowBlank: false}},
			        	  { text: '升级版本',  dataIndex: 'iupdateversion' ,flex:1,editor:{xtype : 'textfield',allowBlank: true}},
			        	  { text: '等待任务(分钟)',tooltip:'Agent等待正在执行的任务完成，然后升级的最大分钟数',  dataIndex: 'iactfinishmaxwait' ,flex:1,editor:{xtype : 'textfield',vtype:'numbers',allowBlank: true}},
			        	  { text: '写入缓冲(KB)',tooltip:'下载升级文件，单次写入的KB值',  dataIndex: 'iwritebuffersize' ,flex:1,editor:{xtype : 'textfield',vtype:'numbersForKb',allowBlank: true}},
			        	  ],
			        	  store:retStore,
			        	  plugins : [ cellEditing_install ]
		});
		function addUpgradeInfo(){
			var p = {
					iid : '0',
					iostype : '',
					isrcpath : '',
					idestpath : '',
					ibakpath : '',
					iupdateversion:'',
					iactfinishmaxwait:30,
					iwritebuffersize:16
			};
			retStore.insert(0, p);
		}
		function saveUpgradeInfo() {
			var m = retStore.getModifiedRecords();
			if (m.length < 1) {
				setMessage('您没有进行任何修改，无需保存');
				return;
			}
			var jsonData = "[";
			for (var i = 0, len = m.length; i < len; i++) {
				var ss = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request({
				url : 'saveBatchUpgradeModel.do',
				method : 'POST',
				params : {
					jsonData : jsonData
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						retStore.load();
					}
					Ext.Msg.alert('提示', message);
				},
				failure : function(result, request) {
					secureFilterRs(result, '保存失败！');
				}
			});
		}
		function deleteUpgradeInfo() {
			var data = upgradeConfiguregrid.getView().getSelectionModel().getSelection();
			if (data.length == 0) {
				Ext.Msg.alert('提示', '请先选择您要操作的数据!');
				return;
			} else {
				Ext.Msg.confirm("请确认","是否真的要删除该数据？",function(button, text) {
					if (button == "yes") {
						var ids = [];
						Ext.Array.each(data, function(record) {
							var iid = record.get('iid');
							if (iid != "" && null != iid) {
								ids.push(iid);
							}
						});
						if (ids.length <= 0) {
							return;
						}
						Ext.Ajax.request({
							url : 'deleteBatchUpgradeModel.do',
							params : {
								deleteIds : ids.join(',')
							},
							method : 'POST',
							success : function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									retStore.reload();
									Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
								} else {
									Ext.Msg.alert('提示','删除失败！');
								}
							},
							failure : function(result, request) {
								secureFilterRs(result,'操作失败！');
							}
						});
					}
				});
			}
		}

		var upgradeConfigureWin = Ext.create('Ext.window.Window', {
			autoScroll : true,
			title:"批量升级配置",
			modal : true,
			layout:'border',
			resizable : true,
			closeAction: 'destroy',
			constrain: true,
			height : '60%',
			width : '70%',
			items :[batchUpgradeForm,upgradeConfiguregrid]
		}).show();
	}
	function upgradeAgentMonitor() {
		var url ="AgentUpgradeMonitor.do";
		if(upgradePostalSwitch){
			url="AgentUpgradeYZMonitor.do"
		}

		win = Ext.create('Ext.window.Window', {
			title : 'Agent升级监控',
			autoScroll : true,
			modal : true,
			resizable : false,
			closeAction : 'destroy',
			width : contentPanel.getWidth()-50,
			height : contentPanel.getHeight()-20,
			loader : {
				url : url,
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		}).show();
		agentStore.reload();
		win.on('close',function(){
			agentStore.reload();
		});
	}

	function bindFTP() {
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data=[];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});

		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择记录!');
			return;
		}
		if(ftpbindwin) {
			ftpbindwin.show();
		} else {
			ftpbindwin = Ext.create('Ext.window.Window', {
				title : 'Agent准备信息',
				layout : 'fit',
				autoScroll : true,
				modal : true,
				resizable : false,
				closeAction : 'hide',
				bbar: bsUpgradeInfoPageBar,
				width : contentPanel.getWidth()-50,
				height : contentPanel.getHeight()-20,
				items: [upgradeinfo_panel]
			}).show();
		}
		bsUpgradeInfoPageBar.moveFirst();
	}
	function uploadExcel(){
		var uploadWindows;
		var uploadForm
		uploadForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				anchor: '90%',
//				labelAlign: 'right',
				margin: '10 10 0 40',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;'
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData
			},{
				text : '取消',
				handler : function(){
					uploadWindows.close();
				}
			}]
		});
		/**
		 * Excel导入Agent信息窗体
		 */
		uploadWindows = Ext.create('Ext.window.Window', {
			title : 'Excel导入',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
//			autoScroll : true,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			}
		});
		uploadWindows.show();
		function upExeclData(){
			var form = uploadForm.getForm();
			var hdupfile=form.findField("fileName").getValue();
//			var fileName=hdupfile;
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate(form);


		}
		var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
		function uploadTemplate(form) {
			checkMask.show();
			uploadWindows.hide();
			if (form.isValid()) {
				form.submit({
					url: isFJNX? 'uploadAgentCfgFJNX.do':'uploadAgentCfg.do',
					success: function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',sumsg);
						checkMask.hide();
						agentStore.reload();
						checkMask.hide();
						return;
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						var mess = Ext.create('Ext.window.MessageBox', {
							minHeight : 110,
							minWidth : 500,
							resizable : false
						});
						Ext.Msg.alert('提示',msg);
						checkMask.hide();
						return;
					}
				});
			}
		}
	}

    // 导出数据的函数
	function exportData(url,exportType) {
		if (exportType === 'xls') {
			window.location.href = url;
		} else {
			// WPS 导出逻辑
			if (url.indexOf('?') === -1) {
				// 如果没有问号，直接添加参数
				window.location.href = url + '?flag=' + exportType;
			} else {
				// 如果有问号，添加参数时使用 '&'
				window.location.href = url + '&flag=' + exportType;
			}
		}
	}

	/*function localinstall(){
		var aixexecDesc = Ext.create('Ext.form.field.TextArea', {

			columnWidth: 1,
			fieldLabel:'<img src="images/aix1.png" width="70" height="50" title="aix">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;aix',
			readOnly:true,
			value:'adasdasdqwasasddqawe asdasdasd asdasdasdasdasdasdasdaasddddddadsfdasfffffffffasdfasdfdddddddddddddddddddddddddddddddddddddddddddddddsd',
			labelWidth:70,
			margin: '10 10 5 10',
			height: 80,
			width: 200,
			autoScroll: true
		});
		var linuxexecDesc = Ext.create('Ext.form.field.TextArea', {

			displayField: 'funcdesc',
			columnWidth: 1,
			fieldLabel:'</br><img src="images/linux1.png" width="70" height="50" title="linux">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;linux',
			readOnly:true,
			value:'sadfa sdfsf sdfsdfs dfsdfss ssssssss sssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss',
			labelWidth:70,
			margin: '0 10 5 10',
			height: 80,
			width: 200,
			autoScroll: true
		});
		var windowsexecDesc = Ext.create('Ext.form.field.TextArea', {

			displayField: 'funcdesc',
			columnWidth: 1,
			fieldLabel:'<img src="images/windows1.png" width="70" height="50" title="windows">&nbsp;windows',
			readOnly:true,
			value:'adasdasd asdasd asdasd asdqwe asdasdaasdasdqweqwadasdsdas  asdasdasasda dasdasa asd asdsdasas dsdddddasda dasdd asd aadddddddddddddddddddddddddddddddddddddddddddddsd',
			labelWidth:70,
			margin: '0 10 5 10',
			height: 80,
			width: 200,
			autoScroll: true
		});

		var hpuxexecDesc = Ext.create('Ext.form.field.TextArea', {

			displayField: 'funcdesc',
			columnWidth: 1,
			fieldLabel:'</br><img src="images/hpux.png" width="70" height="50" title="HP-UX">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;HP-UX',
			readOnly:true,
			value:'sadfa sdfsf sdfsdfs dfsdfss ssssssss sssssssssssssdasd asdqwe asdasdaasdasdqweqsssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss',
			labelWidth:70,
			margin: '0 10 5 10',
			height: 80,
			width: 200,
			autoScroll: true
		});
		var upload1Windows;
		var upload1Form
		upload1Form = Ext.create('Ext.form.Panel',{
			region : 'north',
			layout : 'anchor',
			buttonAlign : 'center',
			border : false,
			bodyStyle: "padding:5 5 5 5",
			height: 240,
			width: 250,
			baseCls:'customize_gray_back',
			items : [{
				layout: 'column',
				border: false,
				items: [linuxexecDesc,windowsexecDesc,aixexecDesc,hpuxexecDesc]
			}]

		});
		/!**
		 * Excel导入Agent信息窗体
		 *!/
		upload1Windows = Ext.create('Ext.window.Window', {
			title : '本地安装',
			layout : 'fit',
			height : 500,
			width : 1000,
//			modal : true,
//			autoScroll : true,
			items : [ upload1Form ],
			listeners : {
				close : function(g, opt) {
					upload1Form.destroy();
				}
			}
		});
		upload1Windows.show();



	}
*/
	function uploadExcel2(){
		var uploadWindows2;
		var uploadForm2
		uploadForm2 = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				anchor: '90%',
//				labelAlign: 'right',
				margin: '10 10 0 40',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;'
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData2
			},{
				text : '取消',
				handler : function(){
					uploadWindows2.close();
				}
			}]
		});
		/**
		 * Excel导入Agent信息窗体
		 */
		uploadWindows2 = Ext.create('Ext.window.Window', {
			title : 'Excel导入应用标识',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
//			autoScroll : true,
			items : [ uploadForm2 ],
			listeners : {
				close : function(g, opt) {
					uploadForm2.destroy();
				}
			}
		});
		uploadWindows2.show();
		function upExeclData2(){
			var form = uploadForm2.getForm();
			var hdupfile=form.findField("fileName").getValue();
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate2(form);


		}
		var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
		function uploadTemplate2(form) {
			checkMask.show();
			uploadWindows2.hide();
			if (form.isValid()) {
				form.submit({
					url: 'importIsystemTypeExcel.do',
					success: function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',sumsg);
						checkMask.hide();
						return;
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						var mess = Ext.create('Ext.window.MessageBox', {
							minHeight : 110,
							minWidth : 500,
							resizable : false
						});
						Ext.Msg.alert('提示',msg);
						checkMask.hide();
						return;
					}
				});
			}
		}
	}

    // 创建导出agent模板选择的弹窗
	function showExportTypeWindow(url) {
		console.log(url)
		var exportTypeWindow = new Ext.window.Window({
			title: '选择导出类型',
			modal: true,
			layout: 'fit',
			width: 350,
			height: 250,
			items: [{
				xtype: 'form',
				bodyPadding: 10,
				items: [{
					xtype: 'radiofield',
					boxLabel: 'Excel格式',
					name: 'exportType',
					inputValue: 'xls',
					checked: true // 默认选中
				}, {
					xtype: 'radiofield',
					boxLabel: 'WPS格式',
					name: 'exportType',
					inputValue: 'et'
				}]
			}],
			buttons: [{
				text: '确定',
				handler: function () {
					var form = this.up('window').down('form').getForm();
					if (form.isValid()) {
						var exportType = form.findField('exportType').getGroupValue();
						console.log(exportType);
						// 调用导出函数
						exportData(url,exportType);
						exportTypeWindow.close(); // 关闭弹窗
					}
				}
			}, {
				text: '取消',
				handler: function () {
					exportTypeWindow.close(); // 关闭弹窗
				}
			}]
		});
		exportTypeWindow.show(); // 关闭弹窗
	}

	function uploadExcel3(){
		var uploadWindows2;
		var uploadForm2
		uploadForm2 = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				anchor: '90%',
//				labelAlign: 'right',
				margin: '10 10 0 40',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;'
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData2
			},{
				text : '取消',
				handler : function(){
					uploadWindows2.close();
				}
			}]
		});
		/**
		 * Excel导入Agent信息窗体
		 */
		uploadWindows2 = Ext.create('Ext.window.Window', {
			title : 'Excel导入描述',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
//			autoScroll : true,
			items : [ uploadForm2 ],
			listeners : {
				close : function(g, opt) {
					uploadForm2.destroy();
				}
			}
		});
		uploadWindows2.show();
		function upExeclData2(){
			var form = uploadForm2.getForm();
			var hdupfile=form.findField("fileName").getValue();
//			var fileName=hdupfile;
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate2(form);


		}
		var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
		function uploadTemplate2(form) {
			checkMask.show();
			uploadWindows2.hide();
			if (form.isValid()) {
				form.submit({
					url: 'importIsystemTypeExceldes.do',
					success: function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',sumsg);
						agentStore.reload();
						checkMask.hide();
						return;
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						var mess = Ext.create('Ext.window.MessageBox', {
							minHeight : 110,
							minWidth : 500,
							resizable : false
						});
						Ext.Msg.alert('提示',msg);
						checkMask.hide();
						return;
					}
				});
			}
		}
	}

	function uploadExcelEnv(){
		var uploadWindows2;
		var uploadForm2
		uploadForm2 = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				anchor: '90%',
				margin: '10 10 0 40',
				buttonText: '浏览',
				labelStyle:'margin-top:12px;'
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData2
			},{
				text : '取消',
				handler : function(){
					uploadWindows2.close();
				}
			}]
		});
		/**
		 * Excel导入Agent信息窗体
		 */
		uploadWindows2 = Ext.create('Ext.window.Window', {
			title : 'Excel导入环境类型',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
//			autoScroll : true,
			items : [ uploadForm2 ],
			listeners : {
				close : function(g, opt) {
					uploadForm2.destroy();
				}
			}
		});
		uploadWindows2.show();
		function upExeclData2(){
			var form = uploadForm2.getForm();
			var hdupfile=form.findField("fileName").getValue();
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate2(form);
		}
		var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
		function uploadTemplate2(form) {
			checkMask.show();
			uploadWindows2.hide();
			if (form.isValid()) {
				form.submit({
					url: 'importIEnvTypeExcel.do',
					success: function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',sumsg);
						checkMask.hide();
						return;
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						var mess = Ext.create('Ext.window.MessageBox', {
							minHeight : 110,
							minWidth : 500,
							resizable : false
						});
						Ext.Msg.alert('提示',msg);
						checkMask.hide();
						return;
					}
				});
			}
		}
	}

	function setPermission(grid, rowIndex) {
		if (grid.getStore().getAt(rowIndex).get('iid')) {
			win = Ext.create('widget.window', {
				title : '应用标识配置',
				closable : true,
				closeAction : 'destroy',
				width : contentPanel.getWidth()-500,
				minWidth : 50,
				height : contentPanel.getHeight(),
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				modal : true,
				loader : {}
			});

			win.getLoader().load(
					{
						url : 'appMark.do',
						params : {
							iid : grid.getStore().getAt(rowIndex)
							.get('iid'),
							iagentip : grid.getStore().getAt(rowIndex)
							.get('iagentip'),
							iagentport : grid.getStore().getAt(rowIndex)
							.get('iagentport'),
							iagentcomputername: grid.getStore().getAt(rowIndex)
							.get('iagentcomputername'),
                            systemid: grid.getStore().getAt(rowIndex)
                                .get('systemid'),

						},
						autoLoad : true,
						scripts : true
					});
			if(isFJNX){
				win.setTitle('应用标识配置 - '
					+ grid.getStore().getAt(rowIndex).get('ibusinesssys')
					+ ' - '
					+ grid.getStore().getAt(rowIndex).get('iagentip'));
			}else{
				win.setTitle('应用标识配置' + ' - 名称：'
					+ grid.getStore().getAt(rowIndex).get('iagentname'));
			}
			win.show(this, function() {
			});
		}

	}

	function setEnv(grid, rowIndex) {
		if (grid.getStore().getAt(rowIndex).get('iid')) {
			win = Ext.create('widget.window', {
				title : '应用标识配置',
				closable : true,
				closeAction : 'destroy',
				width : contentPanel.getWidth()-500,
				minWidth : 50,
				height : contentPanel.getHeight(),
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				modal : true,
				loader : {}
			});
			win.getLoader().load(
				{
					url : 'envTypeMain.do',
					params : {
						iid : grid.getStore().getAt(rowIndex).get('iid'),
						projectName : grid.getStore().getAt(rowIndex).get('ibusinesssys'),
					},
					autoLoad : true,
					scripts : true
				});
			win.setTitle('环境类型维护');
			win.show(this, function() {
			});
		}

	}

	function sendMsgCfg(operType){
//		var data = grid_panel.getSelectionModel().getSelection();
		var data = [];
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		var ids = [];
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条要获取的记录！');
			return;
		}
		Ext.Array.each(data, function(record) {
			var agentId = record.get('iid');
			if (agentId) {
				ids.push(agentId);
			}
		});
		Ext.Ajax.request({
			url : 'sendMsgCfg.do',
			method : 'post',
			params : {
				agentIds : ids,
				operType : operType
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					grid_panel.ipage.moveFirst();
					Ext.Msg.alert('提示', '保存配置信息成功!');
				} else {
					Ext.Msg.alert('提示', '保存配置信息失败!');
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,"操作失败！",request);
			}
		});
	}

	function icustomCMD(){
		Ext.MessageBox.prompt("提示", "请输入自定义命令",function (id, msg){
			if('ok' != id){
				return;
			}else{
				var data = grid_panel.getSelectionModel().getSelection();
				var ids = [];
				if (data.length == 0) {
					Ext.Msg.alert('提示', '请至少选择一条要获取的记录！');
					return;
				}
				Ext.Array.each(data, function(record) {
					var agentId = record.get('iid');
					if (agentId) {
						ids.push(agentId);
					}
				});
				Ext.Ajax.request({
					url : 'updateIcustomCmd.do',
					method : 'POST',
					async : false,
					params : {
						iids:ids,
						icustomcmd : msg
					},
					success : function(response, opts) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							// grid_panel.ipage.moveFirst();
							agentStore.reload();
							Ext.Msg.alert('提示', '操作成功！');
						}else{
							Ext.Msg.alert('提示', '操作失败！');
						}
					},
					failure : function(result, request) {
						secureFilterRs(result,"操作失败！");
					}
				});
			}
		}, this, true, "");
	}


	function OneClickOfflineAgent() {
//		var data = grid_panel.getView().getSelectionModel().getSelection();
		var data=[]
		agentSelected.each(function(record,ind,len){
			data.push(record);
		});
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要删除的记录!');
			return;
		} else {
			var jsonData="[";
			for (var i = 0,len = data.length; i < len; i++) {
				var ss = Ext.JSON.encode(data[i].data);
				if (i == 0) jsonData = jsonData + ss;
				else jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request({
				url: 'isAgentBinding.do',
				method: 'POST',
				params: {
					jsonData: jsonData
				},
				success: function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
						return;
					}else{
						var ids = [];
						Ext.Array.each(data, function(record) {
							var agentId = record.get('iid');
							// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
							if (agentId) {
								ids.push(agentId);
							}else{
								agentStore.remove(record);
							}
						});


						if(ids.length>0){
							Ext.Ajax.request({
								url : 'resourceServer/agentIsBindGroup.do',
								params : {
									agentid : ids.join(',')
								},
								method : 'POST',
								success : function(response, opts) {
									var count = Ext.decode(response.responseText).count;
									var agentCount = Ext.decode(response.responseText).agentCount;
									var message = Ext.decode(response.responseText).message;
									var state = Ext.decode(response.responseText).state;

									if (count>0 || agentCount>0) {
										Ext.Msg.confirm('提示',message,function(btn){
											if(btn=='yes'){
												var timetask = checkValidTTStart(ids);
												var oldHc = checkValidHcStart(ids);
												if(!oldHc[0]){
													Ext.Msg.alert('提示', oldHc[2]);
													return;
												}else{
													if((oldHc[0] && oldHc[1])&& timetask[0]){
														Ext.Msg.confirm("请再一次确认", "是否一键下线<br>"+oldHc[2]+"<br>"+timetask[1], function(btnn, text) {
															if(btnn=='yes'){
																//可以删除
																OneClickOffDeleteAgent(ids,state);
															}else{
																return;
															}
														});
													}else if((oldHc[0] && oldHc[1])||timetask[0]){
														if(oldHc[0] && oldHc[1]){
															Ext.Msg.confirm("请再一次确认", "是否一键下线？<br>"+oldHc[2], function(btnn, text) {
																if(btnn=='yes'){
																	//可以删除
																	OneClickOffDeleteAgent(ids,state);
																}else{
																	return;
																}
															});
														}
														if(timetask[0]){
															Ext.Msg.confirm("请再一次确认", "是否一键下线？<br>"+timetask[1], function(btnn, text) {
																if(btnn=='yes'){
																	//可以删除
																	OneClickOffDeleteAgent(ids,state);
																}else{
																	return;
																}

															});
														}
													}else if(!oldHc[0] && !oldHc[1]){
														Ext.Msg.alert('提示', oldHc[2]);
														return ;
													}else{
														//无流程巡检正在执行
														//无定时任务正在执行
														OneClickOffDeleteAgent(ids,state);
													}
												}
											}
										},this);
									} else {
										var timetask = checkValidTTStart(ids);
										var hc = checkValidHcStart(ids);
										if(!hc[0]){
											Ext.Msg.alert('提示', hc[2]);
										}else{
											if((hc[0] && hc[1])&& timetask[0]){
												Ext.Msg.confirm("请再一次确认", "是否一键下线？<br>"+hc[2]+"<br>"+timetask[1], function(btnn, text) {
													if(btnn=='yes'){
														//可以删除
														OneClickOffDeleteAgent(ids,state);
													}else{
														return;
													}
												});
											}else if((hc[0] && hc[1])||timetask[0]){
												if(hc[0] && hc[1]){
													Ext.Msg.confirm("请再一次确认", "是否一键下线？<br>"+hc[2], function(btnn, text) {
														if(btnn=='yes'){
															//可以删除
															OneClickOffDeleteAgent(ids,state);
														}else{
															return;
														}
													});
												}
												if(timetask[0]){
													Ext.Msg.confirm("请再一次确认", "是否一键下线？<br>"+timetask[1], function(btnn, text) {
														if(btnn=='yes'){
															//可以删除
															OneClickOffDeleteAgent(ids,state);
														}else{
															return;
														}

													});
												}
											}else if(!hc[0] && !hc[1]){
												Ext.Msg.alert('提示', hc[2]);
												return ;
											}else{
												//无流程巡检正在执行
												//无定时任务正在执行
												OneClickOffDeleteAgent(ids,state);
											}
										}
									}
								}
							});
						}
					}
				},
				failure: function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		}
	}

	function OneClickOffDeleteAgent(ids,state){
		Ext.Ajax.request({
			url: 'deleteAgentMaintainInfos.do?state='+state,
			params: {
				deleteIds: ids.join(',')
			},
			method: 'POST',
			success: function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				// 当后台数据同步成功时
				if (success) {
					agentStore.reload();
					agentSelected.clear();
				}
				//Ext.Msg.alert('提示', message);
				if(!success && message.indexOf("绑定模块") != -1){
					Ext.Msg.confirm("请再一次确认", "是否真的要删除Agent信息？<br>"+message, function(btn, text) {
						if(btn=='yes'){
							// return ;
							Ext.Ajax.request({
								url: 'deleteTimeTaskAgentMaintainInfos.do?state='+state,
								params: {
									deleteIds: ids.join(',')
								},
								method: 'POST',
								success: function(response, opts) {
									var success = Ext.decode(response.responseText).success;
									var message = Ext.decode(response.responseText).message;
									// 当后台数据同步成功时
									if (success) {
										agentStore.reload();
									}
									Ext.Msg.alert('提示', "一键下线成功");
								},
								failure: function(result, request) {
									secureFilterRs(result, "操作失败！");
								}
							})
						}
					});
				}else{
					Ext.Msg.alert('提示', message);
				}

			},
			failure: function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}

	function OneClickOffline(){
		var jsonDate = [];
		var id=[];
		agentSelected.each(function(record,ind,len){
			var ip=record.data.iagentip+":"+record.data.iagentport;
			jsonDate.push(ip);
			id.push(record.data.iid);
		});
		if (jsonDate.length == 0) {
			Ext.Msg.alert('提示', '请先选择Agent记录');
			return;
		} else {
			Ext.Msg.confirm("请再一次确认", "是否真的要一键下线？",
				function(button, text) {
					if (button == "yes") {
						Ext.Ajax.request({
							url : 'oneClickOffline.do',
							method : 'post',
							params : {
								jsonData :jsonDate.join(","),
								agentID:  id.join(",")
							},
							success : function(response, request) {
								OneClickOfflineAgent();
							},
							failure : function(result, request) {
								myMask.hide();
								secureFilterRs(result, '操作失败！');
							}
						});
					}
				});
		}
	}


	function installResult(initEndTime){

		var installResultColumns  = [{
			text: '序号',
			width: 45,
			xtype: 'rownumberer'
		},
			{
				text: '安装结果iid',
				dataIndex: 'iid',
				hidden:true,
				width: 100
			},
			{
				text: '名称',
				dataIndex: 'iagent_name',
				width: 50,
				renderer :function(value, metaData, record){
					metaData.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},
			{
				text: '地址',
				dataIndex: 'iagent_ip',
				width: 120
			},
			{
				text: '端口号',
				dataIndex: 'iagent_port',
				width: 80
			},{
				text: '采集系统名称',
				dataIndex: 'iname',
				width: 100,
				hidden:!displaySystemName
			},
			{
				text: '安装结果',
				dataIndex: 'iinstall_state',
				width: 100,
				renderer :function(value, p, record){
					var ret="";
					if(record.get("ifinish")=="1"){
						if(value=='0'){
							ret='<span class="Green_color State_Color">成功</span>';
						}else if(value=='1'){
							ret='<span class="Red_color State_Color">失败</span>';
						}else if(value=='2'){
							ret='<span class="Blue_color State_Color">超时</span>';
						}
					}
					return ret;
				}
			},
			{
				text: '安装进度',
				dataIndex: 'ifinish',
				width: 100,
				renderer :function(value, p, record){
					var ret="";
					if(value=='0'){
						ret='<span class="Blue_color State_Color">安装中</span>';
					}else if(value=='1'){
						ret='<span class="Green_color State_Color">完成</span>';
					}else if(value=='2'){
						ret='<span>待安装</span>';
					}
					return ret;
				}},
			{
				text: '安装信息',
				dataIndex: 'iinstall_msg',
				flex: 1,
				renderer :function(value, metaData, record){
					var escape=1;
					return "<a href='#' script='javascript:void(0)' onclick=\"scheduledstart('"+ record.data.iid+"','"+escape+"'"+")\">"+value+"</a>";
				}
			},
			{
				text: '安装人',
				dataIndex: 'createName',
				width: 150
			},
			{
				text: '创建时间',
				dataIndex: 'icreatetime',
				width: 150
			}];
		Ext.define('installResultModel', {
			extend: 'Ext.data.Model',
			fields: [
				{name: 'iid',     type: 'string'},
				{name: 'iagent_name',     type: 'string'},
				{name: 'iagent_ip',     type: 'string'},
				{name: 'iagent_port',     type: 'string'},
				{name: 'ifinish',     type: 'string'},
				{name: 'iinstall_state',     type: 'string'},
				{name: 'iinstall_msg',     type: 'string'},
				{name: 'ioperation_user',     type: 'string'},
				{name: 'icreatetime',     type: 'string'},
				{name: 'createName',     type: 'string'}
			]
		});
		var installResultStore = Ext.create('Ext.data.Store', {
			autoLoad: true,
			autoDestroy: true,
			model: 'installResultModel',
			pageSize : 30,
			proxy: {
				type: 'ajax',
				url: 'getAgentInstallResult.do',
				reader: {
					type: 'json',
					root: 'dataList',
					totalProperty: 'total'
				}
			}
		});
		var isPageBar = Ext.create('Ext.PagingToolbar', {
			baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			store: installResultStore,
			dock: 'bottom',
			displayInfo: true,
			emptyMsg: "没有记录"
		});

		var installprogress = Ext.create('Ext.data.Store', {
			model: 'AgentStateModel',
			data : [
				{name: '全部',    id: ''},
				{name: '完成',      id: '1'},
				{name: '安装中',    id: '0'},
				{name: '待安装',    id: '2'}
			]
		});
		var installresult = Ext.create('Ext.data.Store', {
			model: 'AgentStateModel',
			data : [
				{name: '全部',    id: ''},
				{name: '失败',    id: '1'},
				{name: '成功',    id: '0'},
				{name: '超时',    id: '2'}
			]
		});

		var istarttime = Ext.create('Go.form.field.DateTime', {
			fieldLabel: '创建时间',
			name: 'istarttime',
			labelWidth: 65,
			width : '27%',
			xtype: 'field',
			format : 'Y-m-d H:i:s',
			value:initEndTime,
		});
		var iendtime = Ext.create('Go.form.field.DateTime', {
			name: 'iendtime',
			labelWidth: 65,
			width : '20%',
			xtype: 'field',
			format : 'Y-m-d H:i:s',
		});

		var installResultformPanel= Ext.create('Ext.form.Panel', {
			region : 'north',
			layout : 'anchor',
			buttonAlign : 'center',
			border : false,
			baseCls:'customize_gray_back',
			dockedItems:[{
				baseCls:'customize_gray_back',
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [{
					fieldLabel: '安装进度',
					labelWidth: 65,
					width : '25%',
					name: 'ifinish',
					displayField: 'name',
					valueField: 'id',
					store: installprogress,
					queryMode: 'local',
					editable:false,
					xtype: 'combobox',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},{
					fieldLabel: '安装结果',
					labelWidth: 65,
					width : '25%',
					name: 'iinstallState',
					displayField: 'name',
					valueField: 'id',
					store: installresult,
					queryMode: 'local',
					editable:false,
					xtype: 'combobox',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},istarttime,'至',iendtime]
			}, {
				baseCls: 'customize_gray_back',
				xtype: 'toolbar',
				border: false,
				dock: 'top',
				items: [{
					name :'rname',
					labelAlign : 'right',
					width : '27%',
					xtype: 'textfield',
					emptyText: '请输入Agent名称或AgentIP或安装人'
				}, {
					xtype: 'button',
					cls: 'Common_Btn',
					text: '查询',
					handler: function () {
						isPageBar.moveFirst();
						installResultStore.load();
					}
				},{
					xtype: 'button',
					cls:'Common_Btn',
					text: '清空',
					handler: function() {
						installResultformPanel.getForm().findField("rname").setValue('');
						installResultformPanel.getForm().findField("ifinish").setValue('');
						installResultformPanel.getForm().findField("iinstallState").setValue('');
						installResultformPanel.getForm().findField("iendtime").setValue('');
						installResultformPanel.getForm().findField("istarttime").setValue('');
					}
				},{
					xtype: 'button',
					cls:'Common_Btn',
					text: '导出',
					handler: function() {
						var ifinish= installResultformPanel.getForm().findField("ifinish").getValue();
						var iinstallState=installResultformPanel.getForm().findField("iinstallState").getValue();
						var iendtime= installResultformPanel.getForm().findField("iendtime").getValue();
						var istarttime= installResultformPanel.getForm().findField("istarttime").getValue();
						var rname=installResultformPanel.getForm().findField("rname").getValue();
						var time1;
						var time2;
						if (!ifinish){
							ifinish='';
						}
						if (!iinstallState){
							iinstallState='';
						}
						if(!istarttime){
							time1='';
						}else {
							time1 = Date.parse(istarttime);
						}
						if(!iendtime){
							time2='';
						}else {
							time2 = Date.parse(iendtime);
						}
						window.location.href = 'installationResultExport.do?rname='+rname+'&istarttime='+time1
							+'&iendtime='+time2+'&iinstallState='+iinstallState+'&ifinish='+ifinish;
					}
				}]
			}]
		})

		installResultStore.on('beforeload', function(store, options) {
			var ifinish= installResultformPanel.getForm().findField("ifinish").getValue()
			var iinstallState=installResultformPanel.getForm().findField("iinstallState").getValue()
			var iendtime= installResultformPanel.getForm().findField("iendtime").getValue()
			var istarttime= installResultformPanel.getForm().findField("istarttime").getValue()
			var time1;
			var time2;
			if(!istarttime){
				time1='';
			}else {
				time1 = Date.parse(istarttime);
			}
			if(!iendtime){
				time2='';
			}else {
				time2 = Date.parse(iendtime);
			}
			var new_params = {
				rname : installResultformPanel.getForm().findField("rname").getValue(),
				istarttime:time1,
				iendtime:time2,
				iinstallState:iinstallState,
				ifinish:ifinish
			};
			Ext.apply(installResultStore.proxy.extraParams, new_params);
		});

		var installResultPanel = Ext.create('Ext.grid.Panel', {
			cls:'customize_panel_back',
			region : 'center',
			store: installResultStore,
			border: true,
			columnLines: true,
			columns: installResultColumns,
			bbar: isPageBar,
		});

		var installResultWin= Ext.create('Ext.window.Window', {
			title : '安装结果',
			autoScroll : true,
			modal : true,
			resizable : false,
			closeAction : 'destroy',
			width : 1000,
			height : 600,
			layout : 'border',
			items: [installResultformPanel,installResultPanel]
		}).show();
	}

	function unloadResult(initEndTime){

		var installResultColumns  = [{
			text: '序号',
			width: 45,
			xtype: 'rownumberer'
		},
			{
				text: '卸载结果iid',
				dataIndex: 'iid',
				hidden:true,
				width: 100
			},
			{
				text: '名称',
				dataIndex: 'iagent_name',
				width: 50,
				renderer :function(value, metaData, record){
					metaData.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},
			{
				text: '地址',
				dataIndex: 'iagent_ip',
				width: 120
			},
			{
				text: '端口号',
				dataIndex: 'iagent_port',
				width: 80
			},{
				text: '采集系统名称',
				dataIndex: 'iname',
				width: 100,
				hidden:!displaySystemName
			},
			{
				text: '卸载结果',
				dataIndex: 'iinstall_state',
				width: 100,
				renderer :function(value, p, record){
					var ret="";
					if(record.get("ifinish")=="1"){
						if(value=='0'){
							ret='<span class="Green_color State_Color">成功</span>';
						}else if(value=='1'){
							ret='<span class="Red_color State_Color">失败</span>';
						}else if(value=='2'){
							ret='<span class="Blue_color State_Color">超时</span>';
						}
					}
					return ret;
				}
			},
			{
				text: '卸载进度',
				dataIndex: 'ifinish',
				width: 100,
				renderer :function(value, p, record){
					var ret="";
					if(value=='0'){
						ret='<span class="Blue_color State_Color">卸载中</span>';
					}else if(value=='1'){
						ret='<span class="Green_color State_Color">完成</span>';
					}else if(value=='2'){
						ret='<span>待卸载</span>';
					}
					return ret;
				}},
			{
				text: '卸载信息',
				dataIndex: 'iinstall_msg',
				flex: 1,
				renderer :function(value, metaData, record){
					var escape =2;
					return "<a href='#' script='javascript:void(0)' onclick=\"scheduledUpload('"+ record.data.iid+"','"+escape+"'"+")\">"+value+"</a>";
				}
			},
			{
				text: '卸载人',
				dataIndex: 'createName',
				width: 150
			},
			{
				text: '创建时间',
				dataIndex: 'icreatetime',
				width: 150
			}];
		Ext.define('installResultModel', {
			extend: 'Ext.data.Model',
			fields: [
				{name: 'iid',     type: 'string'},
				{name: 'iagent_name',     type: 'string'},
				{name: 'iagent_ip',     type: 'string'},
				{name: 'iagent_port',     type: 'string'},
				{name: 'ifinish',     type: 'string'},
				{name: 'iinstall_state',     type: 'string'},
				{name: 'iinstall_msg',     type: 'string'},
				{name: 'ioperation_user',     type: 'string'},
				{name: 'icreatetime',     type: 'string'},
				{name: 'createName',     type: 'string'}
			]
		});
		var installResultStore = Ext.create('Ext.data.Store', {
			autoLoad: true,
			autoDestroy: true,
			model: 'installResultModel',
			pageSize : 30,
			proxy: {
				type: 'ajax',
				url: 'getAgentUnloadResult.do',
				reader: {
					type: 'json',
					root: 'dataList',
					totalProperty: 'total'
				}
			}
		});
		var isPageBar = Ext.create('Ext.PagingToolbar', {
			baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			store: installResultStore,
			dock: 'bottom',
			displayInfo: true,
			emptyMsg: "没有记录"
		});

		var installprogress = Ext.create('Ext.data.Store', {
			model: 'AgentStateModel',
			data : [
				{name: '全部',    id: ''},
				{name: '完成',      id: '1'},
				{name: '卸载中',    id: '0'},
				{name: '待卸载',    id: '2'}
			]
		});
		var installresult = Ext.create('Ext.data.Store', {
			model: 'AgentStateModel',
			data : [
				{name: '全部',    id: ''},
				{name: '失败',    id: '1'},
				{name: '成功',    id: '0'},
				{name: '超时',    id: '2'}
			]
		});

		var istarttime = Ext.create('Go.form.field.DateTime', {
			fieldLabel: '创建时间',
			name: 'istarttime',
			labelWidth: 65,
			width : '27%',
			xtype: 'field',
			format : 'Y-m-d H:i:s',
			value:initEndTime,
		});
		var iendtime = Ext.create('Go.form.field.DateTime', {
			name: 'iendtime',
			labelWidth: 65,
			width : '20%',
			xtype: 'field',
			format : 'Y-m-d H:i:s',
		});

		var installResultformPanel= Ext.create('Ext.form.Panel', {
			region : 'north',
			layout : 'anchor',
			buttonAlign : 'center',
			border : false,
			baseCls:'customize_gray_back',
			dockedItems:[{
				baseCls:'customize_gray_back',
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [{
					fieldLabel: '卸载进度',
					labelWidth: 65,
					width : '25%',
					name: 'ifinish',
					displayField: 'name',
					valueField: 'id',
					store: installprogress,
					queryMode: 'local',
					editable:false,
					xtype: 'combobox',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},{
					fieldLabel: '卸载结果',
					labelWidth: 65,
					width : '25%',
					name: 'iinstallState',
					displayField: 'name',
					valueField: 'id',
					store: installresult,
					queryMode: 'local',
					editable:false,
					xtype: 'combobox',
					listeners: {
						specialkey: function(field,e){
							if (e.getKey()==Ext.EventObject.ENTER){
								grid_panel.ipage.moveFirst();
							}
						}
					}
				},istarttime,'至',iendtime]
			}, {
				baseCls: 'customize_gray_back',
				xtype: 'toolbar',
				border: false,
				dock: 'top',
				items: [{
					name :'rname',
					labelAlign : 'right',
					width : '27%',
					xtype: 'textfield',
					emptyText: '请输入Agent名称或AgentIP或卸载人'
				}, {
					xtype: 'button',
					cls: 'Common_Btn',
					text: '查询',
					handler: function () {
						isPageBar.moveFirst();
						installResultStore.load();
					}
				},{
					xtype: 'button',
					cls:'Common_Btn',
					text: '清空',
					handler: function() {
						installResultformPanel.getForm().findField("rname").setValue('');
						installResultformPanel.getForm().findField("ifinish").setValue('');
						installResultformPanel.getForm().findField("iinstallState").setValue('');
						installResultformPanel.getForm().findField("iendtime").setValue('');
						installResultformPanel.getForm().findField("istarttime").setValue('');
					}
				},{
					xtype: 'button',
					cls:'Common_Btn',
					text: '导出',
					handler: function() {
						var ifinish= installResultformPanel.getForm().findField("ifinish").getValue();
						var iinstallState=installResultformPanel.getForm().findField("iinstallState").getValue();
						var iendtime= installResultformPanel.getForm().findField("iendtime").getValue();
						var istarttime= installResultformPanel.getForm().findField("istarttime").getValue();
						var rname=installResultformPanel.getForm().findField("rname").getValue();
						var time1;
						var time2;
						if (!ifinish){
							ifinish='';
						}
						if (!iinstallState){
							iinstallState='';
						}
						if(!istarttime){
							time1='';
						}else {
							time1 = Date.parse(istarttime);
						}
						if(!iendtime){
							time2='';
						}else {
							time2 = Date.parse(iendtime);
						}
						window.location.href = 'unloadResultExport.do?rname='+rname+'&istarttime='+time1
							+'&iendtime='+time2+'&iinstallState='+iinstallState+'&ifinish='+ifinish;
					}
				}]
			}]
		})

		installResultStore.on('beforeload', function(store, options) {
			var ifinish= installResultformPanel.getForm().findField("ifinish").getValue()
			var iinstallState=installResultformPanel.getForm().findField("iinstallState").getValue()
			var iendtime= installResultformPanel.getForm().findField("iendtime").getValue()
			var istarttime= installResultformPanel.getForm().findField("istarttime").getValue()
			var time1;
			var time2;
			if(!istarttime){
				time1='';
			}else {
				time1 = Date.parse(istarttime);
			}
			if(!iendtime){
				time2='';
			}else {
				time2 = Date.parse(iendtime);
			}
			var new_params = {
				rname : installResultformPanel.getForm().findField("rname").getValue(),
				istarttime:time1,
				iendtime:time2,
				iinstallState:iinstallState,
				ifinish:ifinish
			};
			Ext.apply(installResultStore.proxy.extraParams, new_params);
		});

		var unloadResultPanel = Ext.create('Ext.grid.Panel', {
			cls:'customize_panel_back',
			region : 'center',
			store: installResultStore,
			border: true,
			columnLines: true,
			columns: installResultColumns,
			bbar: isPageBar,
		});

		var installResultWin= Ext.create('Ext.window.Window', {
			title : '卸载结果',
			autoScroll : true,
			modal : true,
			resizable : false,
			closeAction : 'destroy',
			width : 1000,
			height : 600,
			layout : 'border',
			items: [installResultformPanel,unloadResultPanel]
		}).show();
	}


	function newFunction(){
		var newFunctionColumns  = [{
			text: '序号',
			width: 45,
			xtype: 'rownumberer'
		},
		{
			text: '名称',
			dataIndex: 'iagentname',
			flex:1,
			editor: {
			}
//		width: 50,
		},
		{
			text: '用户名',
			dataIndex: 'iusername',
			flex:1,
			editor: {
			}
//		width: 120
		},{
			text: '地址',
			dataIndex: 'iagentip',
			flex:1
//			width: 120
		},{
			text: '端口',
			dataIndex: 'iagentport',
			flex:1,
			editor: {
				xtype: 'numberfield',
				height: 32,
				maxValue: 65535,
				minValue: 0
			}
//		width: 120
		}
		];
		Ext.define('newFunctionModel', {
			extend: 'Ext.data.Model',
			fields: [
			         {name: 'iagentname',     type: 'string'},
			         {name: 'iusername',     type: 'string'},
			         {name: 'iagentip',     type: 'string'},
			         {name: 'iagentport',     type: 'string'},
			         ]
		});
		var newFunctionStore = Ext.create('Ext.data.Store', {
			autoLoad: false,
			autoDestroy: true,
			model: 'newFunctionModel',
			pageSize : 30,
			proxy: {
				type: 'ajax',
//				url: '**********.do',
				reader: {
					type: 'json',
					root: 'dataList',
					totalProperty: 'total'
				}
			}
		});
		var newFunctionformPanel = Ext.create('Ext.form.Panel', {
			region: "north",
			border : false,
			baseCls:'customize_gray_back',
			dockedItems : [{
				xtype : 'toolbar',
				border : false,
				baseCls:'customize_gray_back customize_stbtn',
				dock : 'top',
				items: [{
					xtype: 'splitbutton',
					cls : 'Common_Btn2',
					textAlign:'center',
					text: '获取主机',
					handler : function() {
						this.showMenu();
					},
					menu: new Ext.menu.Menu({
						plain : true,
						items: [{
							text: 'SNMP获取',
							handler: function(){
								var SNMPFormPanel = Ext.create('Ext.form.Panel', {
									border : false,
									bodyPadding : 10,
									bodyCls:'x-docked-noborder-top',
									title : '',
									items : [{
										xtype : 'textfield',
										name : 'isnmpip',
										anchor : '100%',
										fieldLabel : 'IP地址',
										maxLength:255,
										emptyText:'*********-255'
									}],
									buttonAlign: 'center',
									buttons :[{
										xtype : 'button',
										cls : 'Common_Btn',
										height : 32,
										text : '确定',
										handler : function(){
											var submitMask = new Ext.LoadMask(SNMPWin, {msg:"操作中..."});
											submitMask.show();
											var ss = SNMPFormPanel.getForm().findField("isnmpip").getValue();

											var reg1 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\-(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
											var reg2 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
//											var reg3 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;

											if(!reg1.test(ss)&&!reg2.test(ss)){
												Ext.Msg.alert('提示','地址不对，请输入如下格式的IP地址或者IP地址段:</br>&nbsp;&nbsp;&nbsp;&nbsp;1、192.168.1 (扫描***********-*************)</br>&nbsp;&nbsp;&nbsp;&nbsp;2、***********-254 (扫描**********-*************)</br>IP地址最大值为255');
												submitMask.hide();
												return;
											}

											Ext.Ajax.request({
												url : 'getHostListBySnmp.do',
												method : 'POST',
												params : {
													ipAddr : ss,
												},
												success : function(response, request) {
													submitMask.hide();
													var success = Ext.decode(response.responseText).success;
													var result = Ext.decode(response.responseText).result;
													if(success==true&&result!=null&&result!=''){
														for (var i = 0; i < result.length; i++) {
															var ip = result[i].ip;
															var type=result[i].type;
															if(type==0){
																newFunctionStore.add({'iid':'1','iagentname':'agent名称','iusername':'用户名','iagentip':ip,'iagentport':'15000'});
															}
														}
														SNMPWin.close();
													}else if(success==false){
														secureFilterRs(result, '获取数据失败！');
													}
												},
												failure : function(result, request) {
													submitMask.hide();
													secureFilterRs(result, '获取失败！');
												}
											});
										}
									}, {
										xtype : 'button',
										cls : 'Common_Btn',
										height : 32,
										text : '取消',
										handler : function(){
											SNMPWin.close();
										}
									} ]
								});

								var SNMPWin= Ext.create('Ext.window.Window', {
									title : 'SNMP获取',
									autoScroll : true,
									modal : true,
									resizable : false,
									closeAction : 'destroy',
									width : 300,
									height : 200,
									layout : 'fit',
									items: [SNMPFormPanel]
								}).show();
							}
						},{
							text: 'VMware获取',
							handler: function(){
								var VMwareFormPanel = Ext.create('Ext.form.Panel', {
									border : false,
									bodyPadding : 10,
									bodyCls:'x-docked-noborder-top',
									title : '',
									items : [{
										xtype : 'textfield',
										name : 'ivmwareip',
										anchor : '100%',
										fieldLabel : '虚拟机地址',
										maxLength:255,
									},{
										xtype : 'textfield',
										name : 'iusername',
										anchor : '100%',
										fieldLabel : '用户名',
										maxLength:255,
									},{
										xtype : 'textfield',
										name : 'ipassword',
										anchor : '100%',
										fieldLabel : '密码',
										maxLength:255,
									}],
									buttonAlign: 'center',
									buttons :[{
										xtype : 'button',
										cls : 'Common_Btn',
										height : 32,
										text : '确定',
										handler : function(){
											var submitMask = new Ext.LoadMask(VMwareWin, {msg:"操作中..."});
											submitMask.show();
											var ip = VMwareFormPanel.getForm().findField("ivmwareip").getValue();
											var iusername = VMwareFormPanel.getForm().findField("iusername").getValue();
											var ipassword = VMwareFormPanel.getForm().findField("ipassword").getValue();

											var reg3 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
											if(!reg3.test(ip)){
												Ext.Msg.alert('提示','请输入正确的IP地址');
												submitMask.hide();
												return;
											}

											Ext.Ajax.request({
												url : 'getHostListByVMware.do',
												method : 'POST',
												params : {
													userName : iusername,
													password:ipassword,
													ip:ip
												},
												success : function(response, request) {
													submitMask.hide();
													var success = Ext.decode(response.responseText).success;
													var result = Ext.decode(response.responseText).result;
													if(success==true&&result!=null&&result!=''){
														for (var j = 0; j < result.length; j++) {
															var name = result[j].vmname;
															var host = result[j].vmip;
															newFunctionStore.add({'iagentname':name,'iusername':'用户名','iagentip':host,'iagentport':'15000'});
														}
														VMwareWin.close();
													}else if(success==false){
														secureFilterRs(result, '获取数据失败！');
													}
												},
												failure : function(result, request) {
													submitMask.hide();
													secureFilterRs(result, '获取失败！');
												}
											});
										}
									}, {
										xtype : 'button',
										cls : 'Common_Btn',
										height : 32,
										text : '取消',
										handler : function(){
											VMwareWin.close();
										}
									} ]
								});

								var VMwareWin= Ext.create('Ext.window.Window', {
									title : 'VMware获取',
									autoScroll : true,
									modal : true,
									resizable : false,
									closeAction : 'destroy',
									width : 500,
									height : 300,
									layout : 'fit',
									items: [VMwareFormPanel]
								}).show();
								//newFunctionStore.add({'iid':'1','iagentname':'agent名称','iusername':'用户名','iagentip':'127.0.0.1','iagentport':'15000'});
							}
						}]
					})
				}]
			}],
		});

//		Ext.define('dataCenterModel', {
//		extend : 'Ext.data.Model',
//		fields : [ {
//		name : 'iid',
//		type : 'string'
//		}, {
//		name : 'iname',
//		type : 'string'
//		}]
//		});

//		var dataCenterStore = Ext.create('Ext.data.Store', {
//		autoLoad : true,
//		model : 'dataCenterModel',
//		proxy : {
//		type : 'ajax',
//		url : 'getDataCenterList.do',
//		reader : {
//		type : 'json',
//		root : 'dataList'
//		}
//		}
//		});

		Ext.define('connectTypeModel', {
			extend : 'Ext.data.Model',
			fields : [ {
				name : 'iid',
				type : 'string'
			}, {
				name : 'iname',
				type : 'string'
			}]
		});

		var connectTypeStore = Ext.create('Ext.data.Store', {
			model : 'connectTypeModel',
			data : [
			        {iname: 'SSH',    id: 'ssh'},
			        {iname: 'NET',    id: 'net'}
			        ]
		});

		var newFunctionPanel = Ext.create('Ext.grid.Panel', {
			cls:'customize_panel_back',
			region : 'center',
			store: newFunctionStore,
			border: true,
			columnLines: true,
			selModel: Ext.create('Ext.selection.CheckboxModel', {
				checkOnly: true
			}),
			plugins:[Ext.create('Ext.grid.plugin.CellEditing',{
				clicksToEdit : 2
			})],
			columns: newFunctionColumns
		});
		var gridFormPanel=Ext.create('Ext.panel.Panel', {
			region : 'center',
			layout : 'border',
			width : '100%',
			height : '100%',
			items : [ newFunctionformPanel,newFunctionPanel ]
		});

		var installConfigPanel = Ext.create('Ext.form.Panel', {
			region : 'north',
			border : false,
//			bodyPadding : 10,
			bodyCls:'x-docked-noborder-top',
			title : '',
			layout:'form',
			items : [{
				layout:'column',
				items:[{
					xtype : 'textfield',
					name : 'ipsegment',
//					anchor : '30%',
					labelWidth:75,
					width:280,
					padding:'0 0 0 10px',
					fieldLabel : '所属网段',
					maxLength:255,
				},{
					xtype : 'textfield',
					name : 'iinstallScript',
//					anchor : '30%',
					labelWidth:75,
					width:280,
					padding:'0 0 0 10px',
					fieldLabel : '脚本路径',
					allowBlank:false,
					blankText: "脚本路径不能为空",
					maxLength:255,
				},{
					xtype : 'textfield',
					name : 'iparameter',
//					anchor : '30%',
					labelWidth:75,
					width:280,
					padding:'0 0 0 10px',
					fieldLabel : '脚本参数',
					maxLength:255,
				}
				]},{
					layout:'column',
					items:[{
//						xtype : 'textfield',
						name : 'idcid',
//						anchor : '30%',
						labelWidth:75,
						width:280,
						padding:'0 0 0 10px',
						fieldLabel : '数据中心',
						maxLength:255,
						displayField: 'iname',
						valueField: 'id',
						store: dataCenterStore,
						queryMode: 'local',
						editable:false,
						xtype: 'combobox',
					},{
						name : 'iconnectType',
//						anchor : '30%',
						labelWidth:75,
						width:280,
						store: connectTypeStore,
						displayField: 'iname',
						valueField: 'id',
						queryMode: 'local',
						editable:false,
						xtype: 'combobox',
						padding:'0 0 0 10px',
						fieldLabel : '连接方式',
						allowBlank:false,
						blankText: "连接方式不能为空",
						maxLength:255,
					},{
						xtype : 'numberfield',
						name : 'iconnectPort ',
//						anchor : '30%',
						labelWidth:75,
						width:280,
						padding:'0 0 0 10px',
						fieldLabel : '连接端口',
						allowBlank:false,
						blankText: "连接端口不能为空",
						maxValue: 65534,
						minValue: 1
					},]
				}],
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					baseCls:'customize_gray_back',
					dock : 'top',
					items: [{
						xtype: 'button',
						cls:'Common_Btn',
						text: '安装Agent',
						handler: function() {
							var submitMask = new Ext.LoadMask(VMwareWin, {msg:"操作中..."});
							submitMask.show();
							if(installConfigPanel.isValid()){
								var objectVal = installConfigPanel.getForm().getValues();
								var arrayVal = newFunctionPanel.getSelectionModel().getSelection();
								if(arrayVal.length==0){
									submitMask.hide();
									Ext.Msg.alert("提示","请选择要安装Agent的主机");
									return;
								}

								Ext.Ajax.request({
									url : 'autoBatchInstallAgent.do',
									method : 'post',
									params : {
										jsonObject : objectVal,
										jsonArray : arrayVal
									},
									success : function(response, request){
										submitMask.hide();
										var success = Ext.decode(response.responseText).success;
										var message = Ext.decode(response.responseText).message;
										if(!success)
										{
											Ext.Msg.alert('提示','安装失败!');
										}else{
											Ext.Msg.alert('提示', message);
										}
									},
									failure : function(result, request){
										submitMask.hide();
										secureFilterRs(result,"请求返回失败！",request);
									}
								});
							}else{
								submitMask.hide();
								Ext.Msg.alert("提示","请输入必填项目");
								return;
							}
						}
					},'->',{
						xtype: 'button',
						cls:'Common_Btn',
						text: '关闭',
						handler: function() {
							newFunctionWin.close();
						}
					}]
				}]
		});

		var newFunctionWin= Ext.create('Ext.window.Window', {
			title : '获取主机列表',
			autoScroll : true,
			modal : true,
			resizable : false,
			closeAction : 'destroy',
			width : 1000,
			height : 600,
			layout : 'border',
			items: [installConfigPanel,gridFormPanel]
		}).show();
	}

	function pauseAgent(){
		var record = grid_panel.getSelectionModel().getSelection();
		if(record.length==0){
			Ext.Msg.alert('提示', '请选择您要暂停的Agent！');
			return;
		}else{
			var jsonData = "[";
			for ( var i = 0, len = record.length; i < len; i++) {
				var ss = Ext.JSON.encode(record[i].data.iid);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";

			if(jsonData.length>0){
				Ext.MessageBox.wait ("数据处理中...", "进度条");
				Ext.Ajax.request({
					timeout :  1300000,
					url: 'updateAgentStatePause.do',
					params : {
						jsonData : jsonData
					},
					success: function(response, opts) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						Ext.MessageBox.hide ();
						Ext.Msg.alert('提示',message);
						agentStore.reload();
					},
					failure : function(response) {
					}
				});
			}
		}
	}

	function recoverAgent(){
		var record = grid_panel.getSelectionModel().getSelection();
		if(record.length==0){
			Ext.Msg.alert('提示', '请选择您要恢复的Agent！');
			return;
		}else{
			var jsonData = "[";
			for ( var i = 0, len = record.length; i < len; i++) {
				var ss = Ext.JSON.encode(record[i].data.iid);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";

			if(jsonData.length>0){
				Ext.MessageBox.wait ("数据处理中...", "进度条");
				Ext.Ajax.request({
					timeout :  1300000,
					url: 'updateAgentStateRecover.do',
					params : {
						jsonData : jsonData
					},
					success: function(response, opts) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						Ext.MessageBox.hide ();
						Ext.Msg.alert('提示',message);
						agentStore.reload();
					},
					failure : function(response) {
					}
				});
			}
		}
	}
});

function closewin(){
	sysWin.close();
}
function updateNew()
{
	Ext.Msg.confirm("确认操作", "Agent信息更新，您确认要更新吗？", function(id) {
		if (id == 'yes'){
			commonMask.show();
			Ext.Ajax.request({
				url : 'getComputerInfoBySelect.do',
				method : 'post',
				params : {
					ids : iid_du
				},
				success : function(response, request){

					var success = Ext.decode(response.responseText).success;
					if(!success)
					{
						Ext.Msg.alert('提示','更新失败!');
					}
					closewin();
					commonMask.hide();
					store.reload();
				},
				failure : function(result, request){
					commonMask.hide();
					secureFilterRs(result,"请求返回失败！",request);
				}
			});
		}
	});
}
function retNotView(value){
	var coun ="";
	if (value.trim().length>0){
		for (var i=0;i<value.length;i++){
			coun=coun+"*";
		}
	}
	if(value.trim()==""){
		coun="";
	}
	return coun ;
}

function showActInfo(iid) {
	agentId = iid;

	var actStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'ActModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentActInfo.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	var agent_act_formpanel = Ext.create('Ext.form.Panel', {
		region: "north",
		border: false,
		items: [{
			xtype: 'toolbar',
			border: false,
			dock: 'top',
			items: [ '->', {
				xtype: 'button',
				cls: 'Common_Btn',
				text: '导出',
				handler: function () {
					window.location.href = 'exportAgentActInfo.do?agentId=' + iid;
				}
			}]
		}]
	});

	actStore.on('beforeload', function (store, options) {
		var new_params = {
			agentId: agentId
		};
		Ext.apply(actStore.proxy.extraParams, new_params);
	});
	var agentActColumns = [{
		text: '序号',
		width: 45,
		xtype: 'rownumberer'
	},
		{
			text: 'agentId',
			dataIndex: 'agentId',
			width: 50,
			hidden: true
		},
		{
			text: 'AgentIP',
			dataIndex: 'agentIp',
			flex: 2
		},
		{
			text: 'Agent组',
			dataIndex: 'agentGroupName',
			flex: 2
		},
		{
			text: '模块类型',
			dataIndex: 'proType',
			flex: 1,
			renderer : function (value, p, record, rowIndex) {
				if (value != '') {
					if(value == 1){
						return '作业调度';
					}
				}
				return '';
			}
		}, {
			text: '工程名称',
			dataIndex: 'prjName',
			flex: 2
		}, {
			text: '工作流名称',
			dataIndex: 'flowName',
			flex: 2
		}, {
			text: '作业名称',
			dataIndex: 'actName',
			flex: 2
		}];
	var agent_act_panel = Ext.create('Ext.ux.ideal.grid.Panel', {
		cls:'customize_panel_back',
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
		border:true,
		region : 'center',
		store: actStore,
		columnLines: true,
		columns: agentActColumns
	});

	execActWin = Ext.create('Ext.window.Window', {
		title: 'Agent执行作业',
		autoScroll: true,
		modal: true,
		resizable: false,
		closeAction: 'destroy',
		width: contentPanel.getWidth() - 50,
		height: contentPanel.getHeight() - 20,
		layout: 'border',
		items: [agent_act_formpanel, agent_act_panel]
	}).show();
}

function showActivityNum(iid) {
	agentId = iid;

	var agentActStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'AgentActModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentActList.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	var proTypeDataStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'proTypeModel',
		proxy : {
			type : 'ajax',
			url : 'getProType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var agent_act_formpanel = Ext.create('Ext.form.Panel', {
		region: "north",
		border : false,
		items : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [{
				fieldLabel: '模块类型',
				labelAlign : 'right',
				width : '30%',
				labelWidth : 70,
				name: 'proType',
				displayField: 'proName',
				valueField: 'proId',
				store: proTypeDataStore,
				queryMode: 'local',
				editable:false,
				xtype: 'combobox'
			},{
				xtype: 'button',
				cls:'Common_Btn',
				text: '查询',
				handler: function() {
					agentActStore.load();
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				text: '清空',
				handler: function() {
					agent_act_formpanel.getForm().findField("proType").setValue('');
				}
			},'->',{
				xtype: 'button',
				cls:'Common_Btn',
				text: '重置',
				handler: function() {
					resetNownum(agentId);
				}
			}]
		}]
	});

	agentActStore.on('beforeload', function(store, options) {
		var new_params = {
				agentId: agentId,
				proTypeString : agent_act_formpanel.getForm().findField("proType").getValue()
		};
		Ext.apply(agentActStore.proxy.extraParams, new_params);
	});
	var agentActColumns = [{
		text: '序号',
		width: 45,
		xtype: 'rownumberer'
	},
	{
		text: '流程ID',
		dataIndex: 'iflowid',
		width: 100
	},
	{
		text: '工程名',
		dataIndex: 'iprojectname',
		flex: 1
	},
	{
		text: '流程名',
		dataIndex: 'iflowname',
		width: 200
	},
	{
		text: '活动名',
		dataIndex: 'iactname',
		width: 200
	},{
		text: '模块类型',
		dataIndex: 'proType',
		width: 80
	}];
	var agent_act_panel = Ext.create('Ext.grid.Panel', {
		cls:'customize_panel_back',
		region : 'center',
		store: agentActStore,
		border: true,
		columnLines: true,
		columns: agentActColumns
	});

	execActWin = Ext.create('Ext.window.Window', {
		title : 'Agent运行活动',
		autoScroll : true,
		modal : true,
		resizable : false,
		closeAction : 'destroy',
		width : contentPanel.getWidth()-50,
		height : contentPanel.getHeight()-20,
		layout : 'border',
		items: [agent_act_formpanel,agent_act_panel]
	}).show();

	function resetNownum(agentId){
		var count = agentActStore.getCount();
		var jsonData ="";
		for(var i=0;i<count;i++){
			if(i==0){
				jsonData = jsonData + agentActStore.getAt(i).get('proType');
			}else{
				jsonData = jsonData + "," + agentActStore.getAt(i).get('proType');
			}
		}

		Ext.Ajax.request({
			url: 'resetNownum.do',
			params : {
				agentId : agentId,
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.alert("提示", message);
				} else {
					Ext.Msg.alert("提示", message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,"请求返回失败！",request);
			}
		});
	}
}
//判断 设备或者虚拟机 展示判断 jiaMing 2022-5-18 start
//1-物理机，2-虚拟机，3-容器
function iequipmentorvmFn(iequVm){
	var reVal = "";

	if(iequVm == 0){
        reVal ="";
	}

    if(iequVm == 1){
        reVal ="物理机";
    }

    if(iequVm == 2){
        reVal ="虚拟机";
    }

    if(iequVm == 3){
        reVal ="容器";
    }

	return reVal;

}
//判断 设备或者虚拟机 展示判断 jiaMing 2022-5-18 end

function onclickBH(iid){
	iid_du=iid;
	sysWin = new Ext.window.Window({
		title : '对比信息',
		width : 600,
		height : 150,
		autoScroll : true,
		modal : true,
		resizable : false
	});
	sysWin.show();
	Ext.Ajax.request({
		url : "selectAgentInfo.do",
		method : 'post',
		params : {
			iid : iid
		},
		callback:function(e,success,response,o) {
			var str=Ext.decode(response.responseText).success;
			str = str.replace(/;/g,"！<br>");
//			str = "<div style='width:320px;height:350px;overflow:auto;'>"+str+"</div>";
			sysWin.update(str);
		}
	});

}

function  exitNow(){
	var data = [];
	agentSelected.each(function(record,ind,len){
		data.push(record);
	});
	if (data.length == 0) {
		Ext.Msg.alert('提示', '请先选择您要异常处理的Agent!');
		return;
	}else{
		for(var i=0;i<data.length;i++){
			if(data[i].data.iagentstate!='1'){
				Ext.Msg.alert('提示', '请选择状态异常的Agent!');
				return;
			}
		}
	}
}
function  safeMode(){
	var data = [];
	agentSelected.each(function(record,ind,len){
		data.push(record);
	});
	if (data.length == 0) {
		Ext.Msg.alert('提示', '请先选择您要异常处理的Agent!');
		return;
	}else{
		for(var i=0;i<data.length;i++){
			if(data[i].data.iagentstate!='1'){
				Ext.Msg.alert('提示', '请选择状态异常的Agent!');
				return;
			}
		}
	}

}


function onAgentOpt(agentOpt,agentId,agentIp,agentPort){
	var opt="";
	var url="";
	var data =cord;
	var records;

	// if(daemonsSwitch){
	// 	//走守护线程方法 新增
	// 	url = "optAgentMsgNew.do";
	// }else{
	// 	//走Agent方法  原有
	// 	url = "optAgentMsg.do";
	// }
	url = "optAgentMsg.do";
	if(agentOpt =="stop" ){
		opt="停止";
	}else if(agentOpt =="start" ){
		opt="启动";
	}
	if(notDisplayPassword){

		Ext.Array.each(data, function(record) {
			if(record.data.iid==agentId){
				records=record;
				return;
			}
		});
		var osName = records.get('ios_name');
		if(osName=='' || osName==undefined || osName=='没有设置' || osName.length==0){
			Ext.Msg.alert('提示','请配置设备'+agentIp+'的操作系统类型!');
			return;
		}
		//弹出页面 进行处理 业务
		showAgentstopAndstart(records,agentId,agentOpt,url);
	}else{
		//走原有逻辑
		Ext.Msg.confirm("提示","地址为"+agentIp+"，端口为"+agentPort+"，确认要"+opt+"当前Agent？", function(button, text) {
			if (button == "yes") {
				Ext.Ajax.request({
					url : url,
					method : 'post',
					params : {
						agentId:agentId,
						agentOpt:agentOpt
					},
					success : function(response, request) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							Ext.Msg.alert('提示', message);
							agentStore.reload();
						} else {
							Ext.Msg.alert('提示', message);
						}
					}
				});
			}
		});
	}
}

/*function banAgent(agentOpt,agentId,agentIp,agentPort){
	var opt="";
	if(agentOpt =="stop" ){
		opt="禁用";
	}else if(agentOpt =="start" ){
		opt="启用";
	}
	Ext.Msg.confirm("提示","地址为"+agentIp+"，端口为"+agentPort+"，确认要"+opt+"当前Agent？", function(button, text) {
		if (button == "yes") {
			Ext.Ajax.request({
				url : "banAgent.do",
				method : 'post',
				params : {
					agentId:agentId,
					agentOpt:agentOpt
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
						agentStore.reload();
					} else {
						Ext.Msg.alert('提示', message);
					}
				}
			});
		}
	});

}*/

function showAgentstopAndstart(data,agentId,agentOpt,url){
	if(startstop_window){
		startstop_window.close();
	}
	var uspStore = Ext.create('Ext.data.Store', {
		fields: ['iid', 'iname'],
		data : [
			{"iid":"1", "iname":"是"},
			{"iid":"2", "iname":"否"}
		]
	});

	var retStore = Ext.create('Ext.data.Store', {
		fields:['iid','iagentip','iagentport','isusp','iagentos'],
		data:data,
		proxy: {
			type: 'memory',
			reader: {
				type: 'json'
			}
		}
	});

	var cellEditing_install = Ext.create('Ext.grid.plugin.CellEditing',{
		clicksToEdit : 2
	});

	var startstopForm = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		margins:grid_margin,
		cls:'customize_panel_back',
		bodyPadding : 20,
		autoScroll:true,
		items : [{
			labelWidth: 70,
			width : '95%',
			name: 'ioperationuser',
			xtype: 'textfield',
			anchor : '90%',
			margin:'10',
			fieldLabel:"操作用户<font color=red>*</font>"
		},{
			fieldLabel: '操作密码',
			labelWidth: 70,
			width : '95%',
			name: 'ioperationpassword',
			xtype: 'textfield',
			anchor : '90%',
			margin:'10',
			inputType:'password'
		}]
	});

	var startstopGrid =  Ext.create('Ext.grid.Panel',{
		cls:'customize_panel_back',
		region : 'center',
		columns: [
			{ text: '序号',    width:40,xtype : 'rownumberer'},
			{ text: 'iid',  dataIndex: 'iid' ,width:40,hidden:true},
			{ text: '设备IP',  dataIndex: 'iagentip' ,flex:1},
			{ text: '设备端口',  dataIndex: 'iagentport' ,flex:1},
		],
		store:retStore,
		plugins : [ cellEditing_install ]
	});

	var startstopPanel = Ext.create('Ext.panel.Panel', {
		region : 'center',
		layout : 'border',
		width : '100%',
		height : '100%',
		items : [ startstopForm, startstopGrid ]
	});
	startstop_window = Ext.create('Ext.window.Window', {
		autoScroll : true,
		title:"待启停设备信息",
		modal : true,
		layout:'border',
		resizable : true,
		closeAction: 'destroy',
		constrain: true,
		width : 800,
		height : 600,
		items :[startstopPanel],
		buttonAlign:"center",
		buttons: [{
			text: '启动Agent',
			handler: function() {
				startAndStopAgent(0)
			}
		},{
			text: '停止Agent',
			handler: function() {
				startAndStopAgent(1)
			}
		},{
			text: '关闭',
			handler: function() {
				startstop_window.close();
			}
		}]
	}).show();
	function startAndStopAgent(type){
		var storedata = startstopGrid.getStore();
		var count = storedata.getCount();
		var installJson=[];

		var ioperationuser = startstopForm.getForm().findField('ioperationuser').getValue();
		if(ioperationuser == null || ioperationuser.trim() == ''){
			Ext.Msg.alert('提示','请配置操作用户!');
			return;
		}

		var ioperationpassword = startstopForm.getForm().findField('ioperationpassword').getValue()==null?"":startstopForm.getForm().findField('ioperationpassword').getValue()
		if((!iscib || !czFlag) && ioperationpassword.trim() == ""){
			Ext.Msg.alert('提示','请配置操作密码!');
			return;
		}
		for(var i=0;i<count;i++){
			var record = storedata.getAt(i);
			installJson.push(record.data);
		}
		Ext.Ajax.request({
			url: url,
			timeout:900000,//90秒
			params: {
				installJson: Ext.encode(installJson),
				agentId:agentId,
				agentOpt:agentOpt,
				ioperationuser :ioperationuser,
				ioperationpassword :startstopForm.getForm().findField('ioperationpassword').getValue()==null?"":startstopForm.getForm().findField('ioperationpassword').getValue()
			},
			method: 'POST',
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.alert('提示', message);
					agentStore.reload();
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			failure: function(result, request) {
				secureFilterRs(result, "操作失败！",request);
			}
		});
		startstop_window.close();
	}
}


var AGENT_MONITOR = {}

AGENT_MONITOR.win = Ext.create ('Ext.Window',
		{
	width : 800,
	height : 600,
	minHeight : 400,
	minWidth : 550,
	maximizable : true,
	closeAction : 'destroy',
	modal : true,
	layout : 'fit',
	tbar : [
	        {
	        	xtype : 'datefield',
	        	name : 'query_date',
	        	id : 'query_date',
	        	fieldLabel : '日期 ',
	        	format : 'Y-m-d',
	        	labelWidth : 40, // label only
	        	width : 180, // label + input
	        	value : new Date ()
	        },
	        {
	        	text : '查询',
	        	handler : function ()
	        	{
	        		var query_date = Ext.ComponentQuery.query ('#query_date')[0];
	        		if (Ext.ComponentQuery.query ('#query_date')[0].isValid ())
	        		{
	        			store1.load (
	        					{
	        						params :
	        						{
	        							ip : global_ip,
	        							port : global_port,
	        							time : query_date.getRawValue ()
	        						}
	        					});
	        		}
	        		else
	        		{
	        			alert ("输入的日期不合法");
	        		}
	        	}
	        }, '->', /*
	         * { text: '另存为图片', handler: function() {
	         * Ext.MessageBox.confirm('确认下载',
	         * '您想要以图片的形式保存该曲线图?', function(choice){ if(choice ==
	         * 'yes'){ chart.save({ type: 'image/png' }); } }); } },
	         */
	        {
	        	text : '刷 新',
	        	handler : function ()
	        	{
	        		// Add a short delay to prevent fast sequential clicks
	        		store1.reload ();
	        	}
	        }
	        ],
	        items : AGENT_MONITOR.chart
		});

var AGENT = {}

/** 实时监控显示* */
function showAgentMonitor (ip, port)
{
	AGENT.ip = ip;
	AGENT.port = port;
	Ext.create('Ext.window.Window', {
		title: 'Agent实时监控 - IP:' + ip + ' 端口号: ' + port,
		autoScroll: true,
		modal: true,
		closeAction: 'destroy',
		buttonAlign: 'center',
		draggable: false, // 禁止拖动
		resizable: false, // 禁止缩放
		width: 700,
		height: 495,
		loader: {
			url: "page/agentMaintain/monitor/curve.jsp?ip="+ ip + '&port=' + port,
			autoLoad: true,
			autoDestroy: true,
			scripts: true
		}
	}).show();
}
/** 下载列表显示* */
function showAgentLogList (ip, port)
{
	AGENT.ip = ip;
	AGENT.port = port;
	Ext.create('Ext.window.Window', {
		title: 'Agent实时监控 - IP:' + ip + ' 端口号: ' + port,
		autoScroll: true,
		modal: true,
		closeAction: 'destroy',
		buttonAlign: 'center',
		draggable: false, // 禁止拖动
		resizable: false, // 禁止缩放
		width: 700,
		height: 495,
		loader: {
			url: "page/agentMaintain/logdown/logList.jsp",
			autoLoad: true,
			autoDestroy: true,
			scripts: true
		}
	}).show();
}

function scheduledstart(iid,msglog){
	Ext.Ajax.request({
		url: 'getInstallResult.do',
		async:false,
		params: {
			iid: iid
		},
		method: 'POST',
		success: function(response, opts) {
			var  _o = Ext.decode(response.responseText); //解析
			var arr =_o.resList;
			var unescape= arr[0].iinstall_msg
			str1=unescape.split("/n");
			var installmsg="";
			for (let i = 0; i <str1.length ; i++) {
				installmsg+=str1[i]+" \n\r"
			}
			var funcDesc = Ext.create('Ext.form.field.TextArea', {
				columnWidth: 1,
				labelWidth: 65,
				padding: '0 4 0 8',
				height: 300,
				maxLength: 20000,
				name: 'iinstallMsg',
				autoScroll: true,
				value:installmsg
			});

			var installmsgform = Ext.create('Ext.form.FormPanel', {
				border: false,
				bodyPadding: 10,
				bodyCls: 'x-docked-noborder-top',
				autoScroll: true,
				autoDestroy: false,
				buttonAlign: 'center',
				items:
					[{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								layout: "form", // 从左往右的布局column,从上往下form
								border: false,
								columnWidth: 1,
								items:
									[{
										columnWidth: 1,
										border: false,
										items: [{
											fieldLabel: 'AGENT_IP',
											width:400,
											name: 'agentip',
											xtype: 'field',
											editable: false,
											typeAhead: false,
											anchor: '100%',
											readOnly:true,
											value:arr[0].iagent_ip,
										}]
									}]
							},funcDesc]
					}],
			});
			var win1 = Ext.create('Ext.window.Window', {
				title : '安装日志信息',
				autoScroll : false,
				autoDestroy : true,
				closeable : true,
				modal : true,
				width : 600,
				height: 470,
				resizable : false,
				plain : true,
				layout : 'fit',
				draggable:true,
				items : [installmsgform]
			}).show();
		},
		failure: function(result, request) {
			Ext.Msg.alert('提示', "请求失败");
		}
	});
}

function scheduledUpload(iid,msglog){
	Ext.Ajax.request({
		url: 'getUploadResult.do',
		async:false,
		params: {
			iid: iid
		},
		method: 'POST',
		success: function(response, opts) {
			var  _o = Ext.decode(response.responseText); //解析
			var arr =_o.resList;
			var unescape= arr[0].iinstall_msg
			str1=unescape.split("/n");
			var installmsg="";
			for (let i = 0; i <str1.length ; i++) {
				installmsg+=str1[i]+" \n\r"
			}
			var funcDesc = Ext.create('Ext.form.field.TextArea', {
				columnWidth: 1,
				labelWidth: 65,
				padding: '0 4 0 8',
				height: 300,
				maxLength: 20000,
				name: 'iinstallMsg',
				autoScroll: true,
				value:installmsg
			});

			var installmsgform = Ext.create('Ext.form.FormPanel', {
				border: false,
				bodyPadding: 10,
				bodyCls: 'x-docked-noborder-top',
				autoScroll: true,
				autoDestroy: false,
				buttonAlign: 'center',
				items:
					[{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								layout: "form", // 从左往右的布局column,从上往下form
								border: false,
								columnWidth: 1,
								items:
									[{
										columnWidth: 1,
										border: false,
										items: [{
											fieldLabel: 'AGENT_IP',
											width:400,
											name: 'agentip',
											xtype: 'field',
											editable: false,
											typeAhead: false,
											anchor: '100%',
											readOnly:true,
											value:arr[0].iagent_ip,
										}]
									}]
							},funcDesc]
					}],
			});
			var win1 = Ext.create('Ext.window.Window', {
				title : '安装日志信息',
				autoScroll : false,
				autoDestroy : true,
				closeable : true,
				modal : true,
				width : 600,
				height: 470,
				resizable : false,
				plain : true,
				layout : 'fit',
				draggable:true,
				items : [installmsgform]
			}).show();
		},
		failure: function(result, request) {
			Ext.Msg.alert('提示', "请求失败");
		}
	});
}

function showUser(iagentid,ip) {
	if(iagentid==null || iagentid=="" ){
		Ext.Msg.alert('提示', "请先保存记录！");
		return;
	}
	Ext.define('AgentUserModel', {
		extend: 'Ext.data.Model',
		fields: [
		         {name: 'iuser',     type: 'string'},
		         {name: 'idesc',     type: 'string'}
		         ]
	});


	var agentUserStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'AgentUserModel',
		proxy: {
			type: 'ajax',
			url: 'getUserForAgent.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	var agent_user_formpanel = Ext.create('Ext.form.Panel', {
		region: "north",
		border : false,
		items : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [{
				name :'sname',
				labelAlign : 'right',
				width : '50%',
				xtype: 'textfield',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							var sname = agent_user_formpanel.getForm().findField("sname").getValue();
							agentUserStore.load({
								params: {
									iagentid:iagentid,
									sname:sname
								}
							});
						}
					}
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				text: '查询',
				handler: function() {
					agentUserStore.load();
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				text: '清空',
				handler: function() {
					agent_user_formpanel.getForm().findField("sname").setValue('');
				}
			},'->',{
				xtype: 'button',
				cls:'Common_Btn',
				text: '关闭',
				handler: function() {
					agentUserWin.close();
				}
			}]
		}]
	});
	agentUserStore.on('beforeload', function(store, options) {
		var new_params = {
				iagentid: iagentid,
				sname : agent_user_formpanel.getForm().findField("sname").getValue()
		};
		Ext.apply(agentUserStore.proxy.extraParams, new_params);
	});
	var agentUserColumns  = [{
		text: '序号',
		width: 45,
		xtype: 'rownumberer'
	},
	{
		text: '用户名',
		dataIndex: 'iuser',
		width: 100
	},
	{
		text: '用户描述',
		dataIndex: 'idesc',
		flex: 1
	}];

	var agent_user_panel = Ext.create('Ext.grid.Panel', {
		cls:'customize_panel_back',
		region : 'center',
		store: agentUserStore,
		border: true,
		columnLines: true,
		columns: agentUserColumns
	});


	agentUserWin = Ext.create('Ext.window.Window', {
		title : ip+'-用户列表',
		autoScroll : true,
		modal : true,
		resizable : false,
		closeAction : 'destroy',
		width : 600,
		height : 600,
		layout : 'border',
		items: [agent_user_formpanel,agent_user_panel]
	}).show();


}

function post(url, params) {

    // 创建form元素

    var temp_form = document.createElement("form");

    // 设置form属性

    temp_form .action = url;     

    temp_form .target = "_self";

    temp_form .method = "post";     

    temp_form .style.display = "none";

    // 处理需要传递的参数

    for (var x in params) {

        var opt = document.createElement("textarea");     

        opt.name = x;     

        opt.value = params[x];     

        temp_form .appendChild(opt);     

    }     

    document.body.appendChild(temp_form);

    // 提交表单     

    temp_form .submit();   

}
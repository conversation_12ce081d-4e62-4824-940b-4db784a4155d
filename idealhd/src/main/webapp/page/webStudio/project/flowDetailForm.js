Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	
	FlowDetailForm.test = function onFormListener(projetctiid) {
		var projectId = projetctiid;
		var gridColumns =[
				{text : '序号',width : 60,xtype : 'rownumberer'}, 
		        { text: '工作流ID',  dataIndex: 'iid' },
		        { text: '工作流名称', dataIndex: 'iname', flex: 1 }
		    ];
		
		Ext.define('flowModel', {
		extend : 'Ext.data.Model',
		idProperty: 'iid',
			fields : [ {
				name : 'iid',
				type : 'long'
			}, {
				name : 'iname',
				type : 'string'
			}]
		});
		
		var flowStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'flowModel',
		pageSize : 10,
		proxy : {
					type : 'ajax',
					url : 'seeFlowDetail.do?projectId='+projectId,
					method : 'POST',	
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
		});
	
		
		var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		    store: flowStore,
			selModel : Ext.create('Ext.ux.ideal.selection.CheckboxModel'),
		    columns: gridColumns,
		    cls:'customize_panel_back',
		    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			buttonAlign: 'center',
				            buttons: [
				        {
				            text: '确定',
				            handler: function () {   
				        		if(grid.getSelectionModel().getSelection().length<1){
				        			Ext.Msg.alert("提示","未选择工作流");
				        			return;
				        		}else{
										var idName = null;
										for(var i = 0 ; i < grid.getSelectionModel().getSelection().length ; i++){
											var flowReallyName = grid.getSelectionModel().getSelection()[i].get("iname");
											if(idName == null){
												idName  = flowReallyName + ",";
											}else{
												idName  = idName + flowReallyName + ",";
											}		
										}
										if(idName.slice(-1) == ","){
												idName=idName.substring(0,idName.length-1);
										}
										flowAllName = idName ;
										Ext.getCmp('selectFlow').setValue(flowAllName);
										win.close(this);
									 }   
				            }
				        },
						{
				            text: '关闭',
				            handler: function () {                
				                 win.close(this);
				            }
				        }
			      	],
		    height: 200,
		    width: 400,
		    renderTo: Ext.getBody()
		});

        var win = Ext.create("Ext.window.Window", {
            title: "工作流",       //标题
            draggable: false,
			padding : panel_margin,
            height: 500,                          //高度
            width: 500,                           //宽度
            layout: "fit",                        //窗口布局类型
            modal: true, //是否模态窗口，默认为false
            resizable: false,
            items: [grid]
        });
		win.show();
	}

	
});
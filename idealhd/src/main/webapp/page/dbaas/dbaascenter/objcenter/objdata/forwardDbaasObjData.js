Ext.onReady(function() {
	var leftresid=0;
			Ext.tip.QuickTipManager.init();
			// 清理主面板的各种监听时间
			Ext.define('dataModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'sysname',
					type : 'string'
				}, {
					name : 'resourceinfo',
					type : 'string'
				}, {
					name : 'dbtypeid',
					type : 'string'
				}, {
					name : 'objtype',
					type : 'string'
				}, {
					name : 'name',
					type : 'string'
				}, {
					name : 'type',
					type : 'string'
				} , {
					name : 'istate',
					type : 'string'
				}, {
					name : 'size',
					type : 'string'
				}]
			});

			var leftpanelStore = Ext.create('Ext.data.Store',
					{
						autoLoad : true,
						autoDestroy : true,
						pageSize : 100,
						model : 'dataModel',
						proxy : {
							type : 'ajax',
							url : 'getAllResList.do',
							reader : {
								type : 'json',
								root : 'dataList',
								totalProperty : 'total'
							}
						}
					});
			leftpanelStore.on('beforeload', function(store,
					options) {
				var new_params = {
						dataType:forwardDbaasObjData_dbType,
						forwardDbaasObjData_iid : forwardDbaasObjData_iid,
						forwardDbaasObjData_uname:forwardDbaasObjData_uname,
						dbIp:ipField.getValue(),
						sysname:sysname.getValue()
				};
				Ext.apply(leftpanelStore.proxy.extraParams,new_params);
			});
			// 分页工具
			var leftpanelPageBar = Ext.create('Ext.PagingToolbar', {
				store : leftpanelStore,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				border : false,
				emptyMsg : '找不到任何记录'
			});

			var leftpanelSelModel = Ext.create('Ext.selection.CheckboxModel', {
//				checkOnly : true
			});

			var leftpanelColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 50,
				resizable : true
			}, {
				text : '主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			}, {
				text : '业务系统',
				dataIndex : 'sysname',
				minWidth : 120,
				flex : 1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '资源信息',
				dataIndex : 'resourceinfo',
				minWidth : 120,
				flex : 1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '资源状态',
				dataIndex : 'istate',
				minWidth : 70,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : 'dbtypeid',
				dataIndex : 'dbtypeid',
				hidden:true
			}];
			
			var leftpanelGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : leftpanelStore,
				selModel : leftpanelSelModel,
				padding : panel_margin,
				border : false,
				bbar : leftpanelPageBar,
				columnLines : true,
				columns : leftpanelColumns
			});

			var centerpanelStore = Ext.create('Ext.data.Store',
					{
						autoLoad : false,
						autoDestroy : true,
						pageSize : 100,
						model : 'dataModel',
						proxy : {
							type : 'ajax',
							url : 'getAllObjDataListForDbType.do',
							reader : {
								type : 'json',
								root : 'dataList',
								totalProperty : 'total'
							}
						}
					});
			centerpanelStore.on('beforeload', function(store,
					options) {
				var new_params = {
						dataType:forwardDbaasObjData_dbType,
						forwardDbaasObjData_iid : forwardDbaasObjData_iid,
						forwardDbaasObjData_uname:forwardDbaasObjData_uname
				};
				Ext.apply(centerpanelStore.proxy.extraParams,new_params);
			});
			// 分页工具
			var centerpanePageBar = Ext.create('Ext.PagingToolbar', {
				store : centerpanelStore,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				border : false,
				emptyMsg : '找不到任何记录'
			});

			var centerpaneSelModel = Ext.create('Ext.selection.CheckboxModel', {
//				checkOnly : true
			});
			var centerpaneColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 50,
				resizable : true
			}, {
				text : '主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			}, {
				text : '对象类型',
				dataIndex : 'objtype',
				minWidth : 120,
				flex : 1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : 'dbtypeid',
				dataIndex : 'dbtypeid',
				hidden:true
			}];
			var centerpanelGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : centerpanelStore,
				selModel : centerpaneSelModel,
				padding : panel_margin,
				border : false,
				bbar : centerpanePageBar,
				columnLines : true,
				columns : centerpaneColumns
			});
			
			leftpanelGrid.on("itemclick",function(obj, record, index, eOpts){		
				leftresid = record.get('iid'); 
				centerpanelStore.load({
	        	    params: {
	        	    	resid:leftresid,
	        	    	page : 1,
	        	    	limit : 100,
	        	    	start:0
	        	    }
	        	});
				
				rightpanelStore.load({
	        	    params: {
	        	    	page : 1,
	        	    	limit : 100,
	        	    	start:0,
	        	    	objtype:0,
	        	    	resid:0
	        	    }
	        	});
		    });
			
			var rightpanelStore = Ext.create('Ext.data.Store',
					{
						autoLoad : false,
						autoDestroy : true,
						pageSize : 100,
						model : 'dataModel',
						proxy : {
							type : 'ajax',
							url : 'getAllObjDataListForObjectData.do',
							reader : {
								type : 'json',
								root : 'dataList',
								totalProperty : 'total'
							}
						}
					});
			
			// 分页工具
			var rightpanePageBar = Ext.create('Ext.PagingToolbar', {
				store : rightpanelStore,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				border : false,
				emptyMsg : '找不到任何记录'
			});

			var rightpaneSelModel = Ext.create('Ext.selection.CheckboxModel', {
			});
			var rightpaneColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 50,
				resizable : true
			}, {
				text : '主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			}, {
				text : '属性名称',
				dataIndex : 'name',
				minWidth : 200,
				flex : 1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '属性类型',
				dataIndex : 'type',
				minWidth : 200,
				flex : 1,
				hidden : true,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '大小',
				dataIndex : 'size',
				minWidth : 40,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},{
				text : '操作',
				xtype : 'actiontextcolumn',
				width : 60,
				align : 'left',
				items : [{
					text : '详情',
					iconCls : 'monitor_search',
					handler : function(grid, rowIndex) {
						var record=grid.getStore().data.items[rowIndex];
						var type = record.data.type;
						var name = record.data.name;
						showWindow(name,type,leftresid);
					}
				}]
			} ];
			var rightpanelGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : rightpanelStore,
				selModel : rightpaneSelModel,
				padding : panel_margin,
				border : false,
				bbar : rightpanePageBar,
				columnLines : true,
				columns : rightpaneColumns
			});
			
			centerpanelGrid.on("itemclick",function(obj, record, index, eOpts){		
				var type = record.get('objtype');     
				var iid = record.get('iid');     
				rightpanelStore.load({
	        	    params: {
	        	    	page : 1,
	        	    	limit : 100,
	        	    	start:0,
	        	    	objtype:iid,
	        	    	resid:leftresid
	        	    }
	        	});
//				rightpanelGrid.setTitle( data[type] );
		    });
			
			Ext.define('sysModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'sysName',
					type : 'string'
				} ]
			});
			var sysDataByUserIdStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'sysModel',
				proxy : {
					type : 'ajax',
					url : 'getAppSysManageByUserId.do?projectFlag=1',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var sysname = Ext.create("Ext.form.field.ComboBox", {
				labelWidth : 65,
				labelAlign : 'left',
				name : 'sysname',
				width : '15%',
				triggerAction : 'all',
				editable : true,
				queryMode : 'local',
				emptyText : "--请选择业务系统--",
				displayField : 'sysName',
				valueField : 'sysName',
				store : sysDataByUserIdStore,
				listeners : {
					beforequery : function(e) {
						var combo = e.combo;
						if (!e.forceAll) {
							var value = e.query;
							combo.store.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toUpperCase().indexOf(
										value.toUpperCase()) != -1);
							});
							combo.expand();
							return false;
						}
					}
				}
			});
		    var ipField = Ext.create("Ext.form.field.Text", {
		        labelWidth: 25,
		        labelAlign: 'left',
		        emptyText: "--请输入IP--",
		        name: 'dataBaseNameParam',
		        width: '10%',
		        listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	leftpanelPageBar.moveFirst();
		                }
		            }
		        }
		    });
			var form = Ext.create('Ext.form.Panel', {
				border : false,
				region : 'north',
				baseCls : 'customize_gray_back',
				dockedItems : [ {
					xtype : 'toolbar',
					dock : 'top',
					baseCls : 'customize_gray_back',
					border : false,
					items : [sysname,ipField,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function() {
							QueryMessage();
						}
					}, {
						xtype : 'button',
						cls : 'Common_Btn',
						text : '清空',
						handler : function() {
							clearQueryWhere();
						}
					}, '->', {
						text : '数据拉取',
						cls : 'Common_Btn',
						handler : function() {
							getRemoteResource();
						}
					}, {
						text : '返回',
						cls : 'Common_Btn',
						handler : function() {
							contentPanel.getLoader().load({
								url : "dbaasObjectCenterUserManage.do",
								scripts : true,
								params : {
								}
							});
						}
					}]
				} ]
			});
			
			var leftpanel = Ext.create('Ext.panel.Panel', {
				region:'west',
				layout : 'border',
				cls:'customize_panel_back panel_space_right',
				width:'40%',
				items : [leftpanelGrid]
			});
			var centerpanel = Ext.create('Ext.panel.Panel', {
				region:'center',
				layout : 'border',
				cls:'customize_panel_back panel_space_right',
				items : [centerpanelGrid]
			});
			var rightpanel = Ext.create('Ext.panel.Panel', {
				region:'east',
				layout : 'border',
				cls:'customize_panel_back',
				width:'40%',
				items : [rightpanelGrid]
			});
			
			var panel = Ext.create('Ext.panel.Panel', {
				layout : 'border',
				region:'center',
				items : [ leftpanel,centerpanel,rightpanel]
			});
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "dbaasObjectCenterData_manage",
				layout : 'border',
				bodyCls : 'service_platform_bodybg',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				bodyPadding : grid_margin,
				border : true,
				items : [ form, panel ]
			});

			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};

			/** 窗口尺寸调节* */
			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
			});

			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(mainPanel);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
			function clearQueryWhere() {
				sysname.setValue('');
				ipField.setValue('');
			}
			function QueryMessage() {
				if (Ext.isIE) {
					CollectGarbage();
				}
				leftpanelPageBar.moveFirst();
			}

			function getRemoteResource(){
				var data = rightpanelGrid.getView().getSelectionModel().getSelection();
				if (data.length == 0) {
					Ext.Msg.alert('提示', '请先选择您要操作的行进行数据采集!');
					return;
				} else {
					var jsonData = '[';
					var acount=0;
					Ext.Array.each(data, function(record) {
						var tablename = record.get('name');
						if(acount==0){
							jsonData += '{"tablename":"'+tablename+'"}';
						}else{
							jsonData += ","+'{"tablename":"'+tablename+'"}';
						}
						acount=1;
					});
					jsonData += ']';
					Ext.Msg.wait('处理中，请稍后...','提示');
					Ext.Ajax.request({
							url : 'getRemoteResourceToObject.do',
							params : {
								jsonData:jsonData,
								resid:leftresid,
								userdirid:forwardDbaasObjData_iid
							},
							method : 'POST',
							success : function(response,opts) {
								Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
							},
							failure : function(result,request) {secureFilterRs(result,	"操作失败！");
							}
						});
					
				}
			}
			
		});

function setMessage(msg) {
	Ext.Msg.alert('提示', msg);
}
function showWindow(name,type,leftresid){
    var DetailWinTi;
    if(type==1) {
       DetailWinTi = Ext.create('widget.window', {
            title: '详情',
            closable: true,
            closeAction: 'destroy',
            width : contentPanel.getWidth()*0.8,
            height : contentPanel.getHeight(),
            minWidth: 300,
            minHeight: 300,
            layout: 'fit',
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'viewDetailForDateTypePage.do',
                params: {
                    name: name,
                    type:type,
                    iResId:leftresid
                },
                autoLoad: true,
                scripts: true
            }
        });
    }else {
        
        var html="";
        
        Ext.Ajax.request({
            url: 'viewDetailForDateType.do',
            async:false,
            params: {
                iResId:leftresid,type:type,name:name
            },
            method: 'POST',
            success: function(response, opts) {
               var result= Ext.decode(response.responseText).dataList;
                for(var i=0;i<result.length;i++){
                    html=html+"<p>"+result[i].name+"</p>";
              }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
        
        var textPanel = Ext.create('Ext.form.field.Display', {
        });
        textPanel.setValue(html);
        var froms=Ext.create('Ext.form.Panel', {
            baseCls:'customize_panel_back',// panel_space_bottom
            border: true,
            width : '97%',
            items: [textPanel]
        });
        
        DetailWinTi = Ext.widget('window', {
            title: '详情',
            closeAction: 'hide',
            constrain: true,
            resizable: false,
            width : contentPanel.getWidth()*0.8,
            height : contentPanel.getHeight(),
            minWidth: 300,
            minHeight: 300,
            autoScroll: true,
            modal: true,
            items: froms,
            defaultFocus: 'firstName'
        });
    }
	DetailWinTi.show();
}
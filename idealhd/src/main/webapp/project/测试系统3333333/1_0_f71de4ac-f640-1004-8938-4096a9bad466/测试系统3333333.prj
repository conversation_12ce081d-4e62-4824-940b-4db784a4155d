<?xml version="1.0" encoding="UTF-8"?>
<Project ID="0" Name="测试系统3333333" NameSpace="" imodelid="1" Validate="false"><Version Major="1" Minor="0" SubMinor="0" /><ProjectType ProType="1.0" /><ProjectFrom ProFrom="1" Isystemtypeuuid="" Dailytype="1" /><ImportPrjs /><EnvVariants><EnvVariant ID="3" Name="DZ" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="4" Name="GD" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="5" Name="QL" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="6" Name="RQ" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant></EnvVariants><Resources><Resource ID="1" Name="AgentResource" NameSpace="" imodelid="0"><ResourceInfo Name="AgentResource" AdaptorName="generalacts" ConfigClassName="" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResourceView" Description="" IconPath="icon6.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResource" Tip="Agent resource"><Version Major="1" Minor="0" SubMinor="0" /></ResourceInfo><DefaultConfig>
  <__flowId>0</__flowId>
  <__map>
    <entry>
      <string>isremotegroup</string>
      <boolean>false</boolean>
    </entry>
    <entry>
      <string>hostBakup</string>
      <string />
    </entry>
    <entry>
      <string>isremote</string>
      <boolean>true</boolean>
    </entry>
    <entry>
      <string>port</string>
      <string>15000</string>
    </entry>
    <entry>
      <string>connType</string>
      <int>0</int>
    </entry>
    <entry>
      <string>host</string>
      <string>*************</string>
    </entry>
    <entry>
      <string>portBakup</string>
      <string />
    </entry>
    <entry>
      <string>groupname</string>
      <null />
    </entry>
  </__map>
</DefaultConfig></Resource><Resource ID="93" Name="AgentResource_172" NameSpace="" imodelid="0"><ResourceInfo Name="AgentResource" AdaptorName="generalacts" ConfigClassName="" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResourceView" Description="" IconPath="icon6.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResource" Tip="Agent resource"><Version Major="1" Minor="0" SubMinor="0" /></ResourceInfo><DefaultConfig>
  <__flowId>0</__flowId>
  <__map>
    <entry>
      <string>isremotegroup</string>
      <boolean>false</boolean>
    </entry>
    <entry>
      <string>hostBakup</string>
      <string />
    </entry>
    <entry>
      <string>isremote</string>
      <boolean>true</boolean>
    </entry>
    <entry>
      <string>port</string>
      <string>15000</string>
    </entry>
    <entry>
      <string>connType</string>
      <int>0</int>
    </entry>
    <entry>
      <string>host</string>
      <string>************</string>
    </entry>
    <entry>
      <string>portBakup</string>
      <string />
    </entry>
    <entry>
      <string>groupname</string>
      <null />
    </entry>
  </__map>
</DefaultConfig></Resource></Resources><Functions><InternalFunction ID="91" Name="getdate" NameSpace="测试系统3333333" imodelid="0" ReturnType="String"><SourceCode>var fm=new java.text.SimpleDateFormat("yyyyMMdd");
var systime=fm.format(new java.util.Date);
return systime;</SourceCode></InternalFunction></Functions><IEAISchemaDefs /><Workflows><Workflow ID="2346" Name="F0010_综合对账" NameSpace="测试系统3333333" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[F0010_综合对账]]></Description><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="2" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="0" TargetID="2" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="292.0" y="144.0" Width="89.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="521.0" y="229.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><DefaultConfig>
  <__flowId>2346</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="2" Name="F0010_综合对账" ParentEntryFlag="-1" impor="false" ActID="F0010_综合对账" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2346</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="3" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>2</ID></PreActivities><InputExpressions /></EndActElement></ActElements></Workflow></Workflows></Project>

package com.ideal.service.rpamoude.rpatask;
import java.util.List;
import java.util.Map;

import com.ideal.ieai.server.dm.bean.IcSPDBTaskInfoBean;
import com.ideal.ieai.server.dm.bean.TaskHistoryQueryBean;
import com.ideal.ieai.server.dm.repository.ring.IeaiRingManage;
import com.ideal.ieai.server.dm.repository.ring.IeaiRingModel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.doublePersonReview.DoublePersonReviewZBeanForQuery;
import com.ideal.ieai.server.rpamoudle.rpatask.RpaTaskManager;

public class RpaTaskSerice
{

    private static  RpaTaskSerice  intance = new RpaTaskSerice();
    public static  RpaTaskSerice getInstance ()
    {
        if ( intance == null)
        {
             intance = new RpaTaskSerice();
        }
        return  intance;
    }
    private RpaTaskManager      rpaTaskManager    = RpaTaskManager.getInstance();
    private IeaiRingManage           ieaiRingManage = IeaiRingManage.getInstance();

    public List getTaskNameList (String userId,String prjName,int type, int recorCount) throws RepositoryException
    {
        return rpaTaskManager.getTaskNameList(userId,prjName,type,recorCount);
    }
    public Map getSysName (String userId,Long taskId,int type) throws RepositoryException
    {
        return rpaTaskManager.getSysName(userId,taskId,type);
    }
    
    public Map getTaskList ( String userId, long startTime, long endTime, String flowInsName, String systemCode,
            String projectName, int nowPage, int numPage, String flowState, int ieaiType, int selType,
            String flowName, long flowid, String startUser, String iserverIp, int type )  
    {
        return rpaTaskManager.getFlowInfo(userId, startTime, endTime, flowInsName, systemCode, projectName, nowPage,
            numPage, flowState, ieaiType, selType, flowName, flowid, startUser, iserverIp, type);
    }

    public boolean updateButterflyVersion ( String butterflyversion, String flowId,int type ) throws RepositoryException
    {
        return rpaTaskManager.updateButterflyVersion(butterflyversion,flowId,type);
    }
    
    public Map getDmNotRunList ( DoublePersonReviewZBeanForQuery doublePersonReviewZBeanForQuery, int type )
            throws RepositoryException
    {
        return rpaTaskManager.getDmNotRunList(doublePersonReviewZBeanForQuery,type);
    }
    
    public Map getDmNotRunListForEm ( DoublePersonReviewZBeanForQuery doublePersonReviewZBeanForQuery, int type )
            throws RepositoryException
    {
        return rpaTaskManager.getDmNotRunListForEm(doublePersonReviewZBeanForQuery,type);
    }
    
    public Map getDmHistoryList ( TaskHistoryQueryBean param, int dbType  ) throws RepositoryException{
        return rpaTaskManager.getDmRunning(param, dbType );
    }
    
    public Map getDmRunningList ( TaskHistoryQueryBean param, int dbType ) throws RepositoryException{
        return rpaTaskManager.getDmRunning(param, dbType );
    }
    
    public Map getEmRunningList ( TaskHistoryQueryBean param, int dbType ) throws RepositoryException
    {
        return rpaTaskManager.getEmRunning(param, dbType);
    }
    public long getIeaiRingOne (  int dbType  ) throws RepositoryException{
        IeaiRingModel model = new IeaiRingModel();
        model = ieaiRingManage.getIeaiRingOneN(dbType+"", "taskMonistor");
        return model.getIringflag();
    }
    
    public void updateRingFlag (int dbType ) throws RepositoryException{
        ieaiRingManage.updateRingFlag(dbType+"", "taskMonistor", 0l);
    }
    
    public IcSPDBTaskInfoBean getAuditInfoHis ( Long workItemId, int type ) throws RepositoryException
    {
        return rpaTaskManager.getAuditInfoHis(workItemId, type);
    }
    public Map getDmAbnormalList ( TaskHistoryQueryBean param, int dbType ) throws RepositoryException{
        return rpaTaskManager.getDmAbnormal(param, dbType );
    }
}

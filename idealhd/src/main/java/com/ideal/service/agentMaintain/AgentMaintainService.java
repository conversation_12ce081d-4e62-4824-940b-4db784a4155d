package com.ideal.service.agentMaintain;

import com.dhcc.itsm.cmdb.webservice.server.service.ci.CiSimple;
import com.ideal.common.utils.IPTools;
import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.PoiUtil;
import com.ideal.common.utils.SSHUtils;
import com.ideal.common.utils.SessionData;
import com.ideal.common.utils.TelnetUtils;
import com.ideal.common.utils.Tools;
import com.ideal.controller.resourcemanage.exception.ServiceException;
import com.ideal.entity.CmdbAgentDTO;
import com.ideal.entity.CmdbSaveAgentDTO;
import com.ideal.entity.CmdbSaveAgentRequestDTO;
import com.ideal.ieai.cbb.strategy.manager.StrategyBean;
import com.ideal.ieai.cbb.strategy.service.ExecutionStrategyHandler;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.server.ResultBean;
import com.ideal.ieai.server.common.DNSAnalyze;
import com.ideal.ieai.server.compare.common.CompareException;
import com.ideal.ieai.server.czb.ywgl.CMDBManager;
import com.ideal.ieai.server.datacollect.repository.collectalarm.CollectalarmManage;
import com.ideal.ieai.server.datacollect.repository.collectalarm.DataCollectAlarmModel;
import com.ideal.ieai.server.engine.agent.ActivityRExecHelper;
import com.ideal.ieai.server.engine.agent.AgentXmlRpcClient;
import com.ideal.ieai.server.eswitch.common.EswitchException;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.platform.warnmanage.UserModel;
import com.ideal.ieai.server.proxy.execremote.PerformDataProcessService;
import com.ideal.ieai.server.pubtoken.PublicTokenManager;
import com.ideal.ieai.server.pubtoken.PublicTokenResponse;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.agent.AgentDataBean;
import com.ideal.ieai.server.repository.agent.AgentDownBean;
import com.ideal.ieai.server.repository.agent.AgentMonitorManager;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.RpcSslType;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentInfoModel;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentMaintainAct;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentOpModel;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.ComputerDataModel;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.InstallResult;
import com.ideal.ieai.server.repository.hd.hcCollect.HcCollectTaskManager;
import com.ideal.ieai.server.repository.hd.ic.supperhc.linkplatform.HcLinkPlatformManager;
import com.ideal.ieai.server.repository.hd.parameterConfig.ParameterConfigManager;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.InfoExeclServices;
import com.ideal.ieai.server.repository.monitor.MonitorSystem;
import com.ideal.ieai.server.repository.monitor.SendAgentInfoThread;
import com.ideal.ieai.server.repository.monitor.SendCibSyslog;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.rollBackTool.RollBackTool;
import com.ideal.ieai.server.repository.sus.resource.ResourceGroupManager;
import com.ideal.ieai.server.repository.sus.resource.models.ResourceGroupDTO;
import com.ideal.ieai.server.util.BeanFormatter;
import com.ideal.ieai.server.util.WarningInterfaceUtilsPlatform;
import com.ideal.ieai.server.warn.HttpWarnMessage;
import com.ideal.service.agentUpDataInfo.AgentInstallTread;
import com.ideal.service.agentUpDataInfo.AgentStartStopTread;
import com.ideal.service.agentUpDataInfo.AgentUnloadTread;
import com.ideal.service.czb.ywgl.PLUPdwService;
import com.ideal.service.ic.hccomputeroperate.HcComputerOperateService;
import com.ideal.service.platform.computer.ComputerService;
import com.ideal.service.pubtoken.PublicTokenService;
import com.ideal.service.sus.resourcemanage.ResourceServerService;
import com.ideal.util.ImportUtil;
import com.ideal.util.StringUtil;
import com.ideal.util.ValidateUtil;
import com.ideal.util.sm4.SM4Utils;
import net.sf.json.JSONArray;
import org.apache.commons.httpclient.ProtocolException;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xmlrpc.XmlRpcClient;
import org.apache.xmlrpc.XmlRpcException;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspException;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;
import java.util.Vector;
import java.util.concurrent.Semaphore;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

public class AgentMaintainService
{

    private static final Logger        dbsRollbacklog          = Logger.getLogger("dbSRblog");
    private static final Logger        logger                  = Logger.getLogger(AgentMaintainService.class);
    private final AgentMaintainManager manager                 = AgentMaintainManager.getInstance();
    private final AgentMonitorManager  monitor                 = AgentMonitorManager.getInstance();
    private static final Semaphore     s                       = new Semaphore(
            Environment.getInstance().getBatchStartStopThreadNum());
    private static final long          AGENT_ID_NULL           = -1;
    private static final String[]      AGENT_UPLOAD_DATA       = { "序号", "名称", "地址", "端口", "守护端口", "AZ名称",
            "proxyIp","是否云上设备","所属团队","业务系统","业务地址","开启SSL" };

    private static final String[]      AGENT_UPLOAD_DATA_FJNX       = { "序号", "业务系统", "IP地址", "端口" ,"用途" ,"开启SSL"};
    private static final String        EXECSQL_TEXT            = "exeSqls";
    private static final String        ROLLBACKSQL_TEXT        = "rollbackSqls";
    private static final String        MESSAGE_TEXT            = "message";
    private static final String        SUCCESS_TEXT            = "success";
    private static final String        UPLOADAGENT             = "uploadAgent";
    private static final String        IAGENTINFO_ID           = "IAGENTINFO_ID";
    private static final String        ERRORMESSAGE            = "组织信息sql时出现错误！";
    private static final String        ERRORMESSAGEONE         = "执行信息变更sql时出现错误！";
    private static final String        UPDATECOMPUTERAGENT     = "updateComputerAgent";
    private static final String        SAVECIBAGENTINFO        = "saveCibAgentInfo";
    private static final String        SAVECIBAGENTINFOXYYG    = "saveCibAgentInfoXyyg";

    private static final String        ERRORMESSAGEONEMATER    = "mater执行信息变更sql时出现错误！";
    private static final String        ERRORMESSAGEONEBACKUP   = "backup执行信息变更sql时出现错误！";
    private static final String        ERRORMESSAGEONEMAKEUP   = "makeup执行信息变更sql时出现错误！";
    
    
    private static final String        SUCCESS                 = "success";
    private static final String        ROLLBACKSQLS            = "rollbackSqls";
    private static final String        EXESQLS                 = "exeSqls";

    private static final String[]      UPLOAD_START_STOP_AGENT = { "地址", "端口", "操作系统", "操作用户", "操作密码" };   
    private static final String        AGENTCMDBJOB            = "com.ideal.ieai.server.czb.job.CzAgentCmdbJob";
    private static final String[]      DAEMOS_START_STOP_AGENT = { "IP地址", "端口",};


    private static final String[] AGENT_UPLOAD_DATA_DES       = { "序号","地址", "端口", "描述"};
    private static final String MESSAGE            = "message";
    private static final String   MESSAGEERROR           = "没有基线源, 无法保存！";
    private static final String   SAVECOMPAREBASEBYEXCEL = "saveCollectInfoByExcel";
    private static final String   DBCONNS                = "dbConns";
    private static final String   ORGSQL                 = "组织信息sql时出现错误！";
    private static final String   SQLERROR               = "执行信息变更sql时出现错误！";
    private static final String   BASECONN               = "baseConn";

    /**
     * 福建农信云管系统环境类型与server对应关联关系
     */
    public static final Map<String, String> ENV_MAP = new HashMap<String, String>();
    static
    {
        ENV_MAP.put("10", "技术联调固化一");
        ENV_MAP.put("20", "第1轮测试固化一");
        ENV_MAP.put("30", "验证测试固化");
        ENV_MAP.put("40", "准生产测试固化");
        ENV_MAP.put("50", "性能压测环境");
        ENV_MAP.put("60", "POC测试环境");
        ENV_MAP.put("61", "POC测试环境");
        ENV_MAP.put("70", "业务培训环境");
        ENV_MAP.put("80", "业务培训环境");
    }

    private static AgentMaintainService agentMaintainService = null;


    public static AgentMaintainService getInstance(){
        if (agentMaintainService == null){
            agentMaintainService = new AgentMaintainService();
        }
        return agentMaintainService;
    }

    private String hour2str ( int hour )
    {
        if (hour < 10)
        {
            return "0" + hour;
        } else
        {
            return String.valueOf(hour);
        }
    }

    private long str2long ( String date, String time )
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String alltime = date + time;
        long ret = 0L;
        try
        {
            ret = sdf.parse(alltime).getTime() + 8 * 60 * 60 * 1000;
        } catch (ParseException e)
        {
            logger.error("str2long", e);
        }
        return ret;
    }

    public byte[] downLog ( String ip, int port, String filename ) throws Exception
    {
        byte[] fileByte = new byte[0];
        try
        {
            fileByte = ActivityRExecHelper.getAgentLog(ip, port, filename);
        } catch (Exception e)
        {
            logger.error("downLog", e);
            throw e;
        }
        return fileByte;
    }

    public List<AgentDownBean> downList ( String ip, int port ) throws EswitchException
    {
        Hashtable info = new Hashtable();
        try
        {
            info = ActivityRExecHelper.execAgentLog(ip, port);
        } catch (Exception e)
        {
            logger.error("downList", e);
            throw new EswitchException(e);
        }

        List<AgentDownBean> logList = new ArrayList<AgentDownBean>();
        AgentDownBean temp = null;
        for (int i = 1; i < info.size(); i++)
        {
            Hashtable tablea = (Hashtable) info.get(String.valueOf(i));
            temp = new AgentDownBean();
            temp.setLogname((String) tablea.get("logname"));
            temp.setLogsize((String) tablea.get("size"));
            temp.setIp(ip);
            temp.setPort(String.valueOf(port));
            logList.add(temp);
        }
        return logList;
    }

    public List<AgentDataBean> listMonitorHourData ( int modelType, String ip, int port, String date, String hour )
    {
        long start = str2long(date, " " + hour + ":00:00");
        long end = str2long(date, " " + hour + ":00:00") + 60 * 60 * 1000;
        return monitor.hourlist(modelType, ip, port, start, end);
    }

    public List<AgentDataBean> listMonitorData ( int modelType, String ip, int port, String date )
    {

        long start = str2long(date, " 00:00:00");
        long end = str2long(date, " 00:00:00") + 24 * 60 * 60 * 1000;

        List<AgentDataBean> list = monitor.list(modelType, ip, port, start, end);

        int startnum = 0;
        List<AgentDataBean> data = new ArrayList<AgentDataBean>();
        AgentDataBean temp = null;
        for (AgentDataBean agentDataBean : list)
        {
            int num = Integer.parseInt(agentDataBean.getXtime());
            for (int i = 0; i < num - startnum; i++)
            {
                temp = new AgentDataBean();
                temp.setIactnum(0L);
                temp.setXtime(hour2str(startnum + i + 1));
                data.add(temp);
            }
            startnum = num + 1;
            data.add(agentDataBean);
        }
        for (int i = 0; i < 24 - startnum; i++)
        {
            temp = new AgentDataBean();
            temp.setIactnum(0L);
            temp.setXtime(hour2str(startnum + i));
            data.add(temp);
        }

        return data;
    }

    public Map getAgentMaintainList ( String iagentName, String iagentIp, String iagentDesc, String iagentState,
            String iagentosname, String proId, String icreateuser, String istarttime, String iendtime, int start,
            int limit, int sysType, Map<String, String> sortMap, String iagentcomputername, String icustommess,
            String iagentversion, String idcid, String os_smalltype,String proxyName,String systeminfo,String centername,String sysadmin_a,String appadmin_a,String serverName,String iequipmentOrVmId,String iteam, String fjnxSysName,String envName,long userId) throws Exception
    {
        if(Environment.getInstance().getBankSwitchIsFjnx()){
            Agent agent = new Agent();
            agent.setIagentip(iagentIp);
            agent.setIagentname(iagentName);
            agent.setIagentdesc(iagentDesc);
            if (iagentState != null && !"".equals(iagentState.trim())){
                agent.setIagentState(Integer.valueOf(iagentState));
            }else{
                //约定agent状态为空时设置-101，查询时等于-101，状态条件过滤不查询
                agent.setIagentState(-101);
            }
            agent.setIosName(iagentosname);
            if (proId != null && !"".equals(proId.trim())){
                agent.setProId(Long.valueOf(proId));
            }
            agent.setIcomName(iagentcomputername);
            agent.setIagentVersion(iagentversion);
            agent.setSysteminfo(systeminfo);
            agent.setCentername(centername);
            agent.setSysadmin_a(sysadmin_a);
            agent.setAppadmin_a(appadmin_a);
            if (iequipmentOrVmId != null && !"".equals(iequipmentOrVmId.trim())){
                agent.setIequipmentorvmid(Long.valueOf(iequipmentOrVmId));
            }
            agent.setSystemName(fjnxSysName);
            agent.setEnvName(envName);
            agent.setIcustom_cmd(icustommess);
            agent.setOs_smalltype(os_smalltype);
            agent.setProxyName(proxyName);
            agent.setServerName(serverName);
            agent.setIteam(iteam);
            return manager.getAgentMaintainList(agent, icreateuser, istarttime, iendtime,
                    start, limit, sysType, sortMap, idcid, true,null,userId);
        }else{
            return manager.getAgentMaintainList(iagentName, iagentIp, iagentDesc, iagentState, iagentosname, proId,
                    icreateuser, istarttime, iendtime, start, limit, sysType, sortMap, iagentcomputername, icustommess,
                    iagentversion, idcid, os_smalltype, true,proxyName, systeminfo, centername, sysadmin_a, appadmin_a,null,serverName, iequipmentOrVmId,iteam
            );
        }
    }

    public Map getAgentForStateList (String iagentIp,String proId,int start,
                                      int limit, int sysType, Map<String, String> sortMap) throws Exception
    {
        return manager.getAgentForStateList(iagentIp, proId, start, limit, sysType, sortMap);
    }

    public Map getAgentMonitorList ( String iagentName, String iagentIp, String iagentDesc, String iagentState,
            String iagentosname, String proId, String icreateuser, String istarttime, String iendtime, int start,
            int limit, int sysType, Map<String, String> sortMap, String iagentcomputername, String icustommess,
            String iagentversion, String cname, String ctype, String systeminfo, String idcid, String iids )
            throws Exception
    {
        // updateInactiveNum(host, port);
        return manager.getAgentMonitorList(iagentName, iagentIp, iagentDesc, iagentState, iagentosname, proId,
            icreateuser, istarttime, iendtime, start, limit, sysType, sortMap, iagentcomputername, icustommess,
            iagentversion, cname, ctype, systeminfo, idcid, iids);
    }

    public Map getAgentNumDeital () throws Exception
    {
        return manager.getAgentNumDeital();
    }

    public Map<String, List> getAgentNumExt ()
    {
        Map<String, List> res = new HashMap<String, List>();
        try
        {
            res = manager.getAgentNumExt();
        } catch (Exception e)
        {
            logger.error("AgentMaintainService.getAgentNumExt has an Exception:" + e.getMessage(), e);
        }
        return res;
    }

    public Map<String, Object> getAgentInstallResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int start, int limit, int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        List<Map<String, Object>> datalist = null;
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        int li = 0;
        String s1 = rname;
        if (rname != null && !"".equals(rname.trim()))
        {
            sqlWhere += " and (lower(a.iagent_name) LIKE ? or a.iagent_ip like ? or a.create_name like ? )";
        }

        if (StringUtils.isNotBlank(ifinish))
        {
            sqlWhere += " and a.ifinish=? ";
        }
        if (StringUtils.isNotBlank(iinstallState))
        {
            sqlWhere += " and a.iinstall_state=? ";
        }


        if (StringUtils.isNotBlank(istarttime))
        {
            sqlWhere += " and a.icreatetime >= ? ";
        }

        if (StringUtils.isNotBlank(iendtime))
        {
            sqlWhere += " and a.icreatetime <= ? ";
        }

        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc limit ?,?";
            li = limit;
        } else
        {
            sql = "select * from (select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere + ") where ro>? and ro<=?";
            li = start + limit;
        }
        //datalist = manager.getAgentInstallResult(rname, sql, start, li, sysType);
        datalist = manager.getAgentInstallResult (  istarttime, iendtime,  iinstallState, ifinish, rname,  sql,  start,  li,  sysType );

        String sqlCount = " SELECT COUNT(*) AS NUM FROM ieai_agentinstall_result a where 1=1 " + sqlWhere;
        //int total = manager.getCount(sqlCount, sysType);
        int total = manager.getCount ( istarttime, iendtime,  iinstallState, ifinish, rname, sqlCount,  sysType ) ;
        res.put("dataList", datalist);
        res.put("total", total);
        return res;
    }

    public Map getAgentMaintainUpgradeInfo ( int start, int limit, int sysType ) throws Exception
    {
        return manager.getAgentMaintainUpgradeInfo(start, limit, sysType);
    }

    public List getAgentActList ( long agentId, String proTypeString, int sysType ) throws Exception
    {
        List actListBack = new ArrayList();
        List actList = new ArrayList();
        actList = manager.getAgentActList(agentId, sysType);
        Map map = manager.getProTypeMap(sysType);
        for (int i = 0; i < actList.size(); i++)
        {
            Map actM = (Map) actList.get(i);
            int proType = manager.getProTypeByProName((String) actM.get("iprojectname"), sysType);
            if (proTypeString != null && !"".equals(proTypeString) && !String.valueOf(proType).equals(proTypeString))
            {
                continue;
            }
            Iterator iter = map.entrySet().iterator();
            while (iter.hasNext())
            {
                Map.Entry<Long, String> entry = (Entry<Long, String>) iter.next();
                long key = entry.getKey();
                String value = entry.getValue();
                if (String.valueOf(proType).equals(String.valueOf(key)))
                {
                    actM.put("proType", value);
                    break;
                }
            }
            actListBack.add(actM);
        }
        return actListBack;
    }

    public List getAgentMaintainClusterList ( int sysType ) throws Exception
    {
        return manager.getAgentMaintainClusterList(sysType);
    }

    public Map saveAgentMaitainInfos ( String jsonData, int sysType, HttpServletRequest request ) throws Exception
    {
        Map res = new HashMap();
        List<Map<String, Object>> agents = null;
        String userName = SessionData.getSessionData(request).getUserName();
        long userid = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        try
        {
            agents = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "解析Agent数据出现问题！");
            return res;
        }
        List<Agent> repAgents = new ArrayList<Agent>();
        Connection checkConn = null;
        List<Connection> dbConns = null;
        Connection baseConn = null;
        try {
            checkConn = DBResource.getConnection("saveAgentMaitainInfos", logger, sysType);
            Map<String, String> tmpContains = new HashMap<String, String>();
            for (int i = 0; i < agents.size(); i++) {
                Map<String, Object> agentMap = agents.get(i);
                Agent agent = _convertToAgent(agentMap);
                String key = agent.getIagentip() + agent.getIagentport();
                if (!tmpContains.containsKey(key)) {
                    tmpContains.put(key, key);
                } else {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "Agent IP 有重复：" + agent.getIagentip());
                    return res;
                }
                if (manager.isAgentIpExistsAndUpdateClusterId(agent, checkConn, sysType)) {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "数据库中已经存在IP为：" + agent.getIagentip() + " 的Agent信息。");
                    return res;
                }
                //判断是否启动守护进程agent监控
                if (manager.isStartAgentMonitor(agent, checkConn, sysType)) {
                    if (manager.isdiffertentIpOrPort(agent, checkConn, sysType)) {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "修改的IP为 ：" + agent.getIagentip() + " 端口为  ：" + agent.getIagentport() + " 的Agent，已启动守护进程agent监控，修改IP需先关闭守护进程agent监控");
                        return res;
                    }
                }
                if(Environment.getInstance().getBankSwitchIsFjnx()){
                    Long systemId = ProjectManager.getInstance().getPrjIdByName(agent.getIbusinesssys());
                    if(systemId<=0){
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "业务系统:"+agent.getIbusinesssys()+" 不存在,请检查！");
                        return res;
                    }
                }
                // 修改agent时验证是否更改，判断新巡检配置中是否已经使用了查agent
                Boolean canSave = true;
                // 添加开关控制保存agent时是否验证是否已经在新巡检中使用
                Boolean checkSave = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.HC_AGENT_SAVE_CHECK_WEBPORT_CHANGE_SWITCH, false);
                Boolean isNewCheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
                if (checkSave && isNewCheck) {
                    HcLinkPlatformManager hcmanager = new HcLinkPlatformManager();
                    canSave = hcmanager.diffWebPortCheckForAgent(null, agent.getIid(), agent.getIagentwebport().toString());
                    if (!canSave) {
                        String mss = "修改的Agent IP【" + agent.getIagentip()
                                + "】的WEB端口号已经绑定并且启动了巡检操作，更改端口号需要先停止关联此Agent的设备巡检 。";
                        List<String> startComputerForAgent = hcmanager.getStartComputerForAgent(agent.getIid(), null);
                        if (startComputerForAgent != null && !startComputerForAgent.isEmpty()) {
                            mss = mss + "关联设备如下：</br>" + StringUtils.join(startComputerForAgent, ",");
                        }
                        res.put("success", false);
                        res.put("message", mss);
                        return res;
                    }
                }
                // 新增时保存纳管用户和纳管时间
                if (agent.getIid() == -1) {
                    agent.setIcreateuser(userid);
                    agent.setIcreatetime(System.currentTimeMillis() + "");
                }
                repAgents.add(agent);
            }
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            baseConn = (Connection) connInfo.get("baseConn");
            if (null != baseConn) { // 没有基线源，不可以
                dbConns = (List<Connection>) connInfo.get("dbConns");
                Map orgSql = manager.organizeAgentMaitainInfoSql(repAgents, baseConn, dbConns);
                if ((Boolean) orgSql.get(SUCCESS_TEXT)) {
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                            (List<String>) orgSql.get("rollbackSqls"))) {
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "数据保存成功！");
                        logger.info("用户名:" + userName + "操作:Agent管理保存操作。");

                        // 发送Agent设备同步信息 新增一个是否开启该功能的开关
                        if (ServerEnv.getServerEnv().isSendAgentInfoSwitch()) {
                            // 组织Agent信息，内容详见pdf文档
                            try {
                                if (!repAgents.isEmpty()) {
                                    for (int h = 0; h < repAgents.size(); h++) {
                                        CiSimple agent = getAgentMainInfo(repAgents.get(h));
                                        if (null != agent) {
                                            logger.info("调用发送CMDB-Agent管理集成线程！");
                                            SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                            thread.start();
                                            logger.info("调用发送CMDB-Agent管理集成线程结束！");
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Agent信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                            }
                        }
                        return res;
                    } else {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                        return res;
                    }
                } else {
                    if(orgSql.get(MESSAGE_TEXT) != null){
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, orgSql.get(MESSAGE_TEXT));
                        return res;
                    }else{
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                        return res;
                    }

                }
            } else {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                return res;
            }
        }catch (Exception e){
            logger.error("", e);
        }finally {
            DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
            DBResource.closeConnection(checkConn, "saveAgentMaitainInfos", logger);
            if(dbConns != null) {
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "deleteAgentMaintainInfos", logger);
                }
            }
        }
        return null;
    }


    public Map fjnxSaveAgentMaitainInfos ( String jsonData, int sysType) throws Exception
    {
        Map res = new HashMap();
        List<Map<String, Object>> agents = null;
        try
        {
            agents = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "解析Agent数据出现问题！");
            return res;
        }
        List<Agent> repAgents = new ArrayList<Agent>();
        Connection checkConn = null;
        List<Connection> dbConns = null;
        Connection baseConn = null;
        try {
            checkConn = DBResource.getConnection("saveAgentMaitainInfos", logger, sysType);
            Map<String, String> tmpContains = new HashMap<String, String>();
            for (int i = 0; i < agents.size(); i++) {
                Map<String, Object> agentMap = agents.get(i);
                Agent agent = new Agent();
                agent.setIid(-1);
                agent.setEnvName(String.valueOf(agentMap.get("envName")));
                agent.setIagentip(String.valueOf(agentMap.get("agentIp")));
                agent.setIagentport(agentMap.get("agentPort") != null && !"".equals(String.valueOf(agentMap.get("agentPort")).trim())
                        ? Long.parseLong(String.valueOf(agentMap.get("agentPort")))
                        : 15000);
                agent.setIbusinesssys(String.valueOf(agentMap.get("sysName")));
                agent.setIregion(agentMap.get("iregion") == null ? "" : String.valueOf(agentMap.get("iregion")));
                agent.setIssued(agentMap.get("issued") == null ? 0 : Integer.valueOf(agentMap.get("issued").toString()));
                agent.setIagentState(
                        agentMap.get("iagentState") == null ? -1 : Integer.valueOf(agentMap.get("iagentState").toString()));
                Object idcid = agentMap.get("idcid");
                agent.setIdcid(idcid == null || "".equals(idcid.toString()) ? -1 : Long.valueOf(idcid.toString()));

                agent.setIagentwebport(
                        agentMap.get("iagentwebport") != null && !"".equals(String.valueOf(agentMap.get("iagentwebport")).trim())
                                ? Long.parseLong(String.valueOf(agentMap.get("iagentwebport")))
                                : 0);
                agent.setIagentguardport(
                        agentMap.get("iagentguardport") != null && !"".equals(String.valueOf(agentMap.get("iagentguardport")).trim())
                                ? Long.parseLong(String.valueOf(agentMap.get("iagentguardport")))
                                : 0);
                agent.setIsoncloud( agentMap.get("isoncloud") == null?"":String.valueOf(agentMap.get("isoncloud")));
                agent.setIbusinessip( agentMap.get("ibusinessip") == null?"":String.valueOf(agentMap.get("ibusinessip")));
                //ssl add by sunxf 2022.10.25
                agent.setIsSSL(agentMap.get("isSSL") == null?1:Integer.valueOf(agentMap.get("isSSL").toString()));
                agent.setIequipmentorvmid(  agentMap.get("iequipmentorvm") != null && !"".equals(String.valueOf(agentMap.get("iequipmentorvm")).trim())
                        ? Long.parseLong(String.valueOf(agentMap.get("iequipmentorvm")))
                        : null);

                String key = agent.getIagentip() + agent.getIagentport();
                if (!tmpContains.containsKey(key)) {
                    tmpContains.put(key, key);
                } else {
//                    res.put(SUCCESS_TEXT, false);
//                    res.put(MESSAGE_TEXT, "Agent IP 有重复：" + agent.getIagentip());
//                    return res;
                    logger.info("Agent IP 有重复：" + agent.getIagentip());
                    continue;
                }
                if (manager.isAgentIpExistsAndUpdateClusterId(agent, checkConn, sysType)) {
//                    res.put(SUCCESS_TEXT, false);
//                    res.put(MESSAGE_TEXT, "数据库中已经存在IP为：" + agent.getIagentip() + " 的Agent信息。");
//                    return res;
                    logger.info("数据库中已经存在IP为：" + agent.getIagentip() + " 的Agent信息。");
                    continue;
                }
                //判断是否启动守护进程agent监控
                if (manager.isStartAgentMonitor(agent, checkConn, sysType)) {
                    if (manager.isdiffertentIpOrPort(agent, checkConn, sysType)) {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "修改的IP为 ：" + agent.getIagentip() + " 端口为  ：" + agent.getIagentport() + " 的Agent，已启动守护进程agent监控，修改IP需先关闭守护进程agent监控");
                        return res;
                    }
                }
                if(Environment.getInstance().getBankSwitchIsFjnx()){
                    Long systemId = ProjectManager.getInstance().getPrjIdByName(agent.getIbusinesssys());
                    if(systemId<=0){
                        agent.setIbusinesssys("一代反假币系统");
                        Long sysId = ProjectManager.getInstance().getPrjIdByName(agent.getIbusinesssys());
                        if(sysId<=0){
                            continue;
                        }
                    }
                }
                // 修改agent时验证是否更改，判断新巡检配置中是否已经使用了查agent
                Boolean canSave = true;
                // 添加开关控制保存agent时是否验证是否已经在新巡检中使用
                Boolean checkSave = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.HC_AGENT_SAVE_CHECK_WEBPORT_CHANGE_SWITCH, false);
                Boolean isNewCheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
                if (checkSave && isNewCheck) {
                    HcLinkPlatformManager hcmanager = new HcLinkPlatformManager();
                    canSave = hcmanager.diffWebPortCheckForAgent(null, agent.getIid(), agent.getIagentwebport().toString());
                    if (!canSave) {
                        String mss = "修改的Agent IP【" + agent.getIagentip()
                                + "】的WEB端口号已经绑定并且启动了巡检操作，更改端口号需要先停止关联此Agent的设备巡检 。";
                        List<String> startComputerForAgent = hcmanager.getStartComputerForAgent(agent.getIid(), null);
                        if (startComputerForAgent != null && !startComputerForAgent.isEmpty()) {
                            mss = mss + "关联设备如下：</br>" + StringUtils.join(startComputerForAgent, ",");
                        }
                        res.put("success", false);
                        res.put("message", mss);
                        return res;
                    }
                }
                // 新增时保存纳管用户和纳管时间
                if (agent.getIid() == -1) {
                    //agent.setIcreateuser(userid);
                    agent.setIcreatetime(System.currentTimeMillis() + "");
                }
                repAgents.add(agent);
            }
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            baseConn = (Connection) connInfo.get("baseConn");
            if (null != baseConn) { // 没有基线源，不可以
                dbConns = (List<Connection>) connInfo.get("dbConns");
                Map orgSql = manager.organizeAgentMaitainInfoSql(repAgents, baseConn, dbConns);
                if ((Boolean) orgSql.get(SUCCESS_TEXT)) {
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                            (List<String>) orgSql.get("rollbackSqls"))) {
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "数据保存成功！");

                        for(Agent agentInfo : repAgents){
                            try {
                                //根据ip端口查询agentid
                                List list = new ArrayList();
                                Map map = new HashMap();
                                map.put("id",0);
                                String envName = ENV_MAP.get(agentInfo.getEnvName());
                                if(StringUtils.isNotEmpty(envName)){
                                    map.put("envType",envName);
                                }else{
                                    map.put("envType","其他环境");
                                }
                                //map.put("envType",agentInfo.getEnvName());
                                list.add(map);
                                String result = list.toString();
                                Agent agent = getAgentMaintainInfoByIpPort(agentInfo.getIagentip(),agentInfo.getIagentport().intValue());
                                if(agent!=null){
                                    saveAgentEnvsMultiple(result, agent.getIid());
                                }
                            } catch (Exception e) {
                                logger.error("保存环境类型失败:"+e.getMessage());
                            }
                        }

                        // 发送Agent设备同步信息 新增一个是否开启该功能的开关
                        if (ServerEnv.getServerEnv().isSendAgentInfoSwitch()) {
                            // 组织Agent信息，内容详见pdf文档
                            try {
                                if (!repAgents.isEmpty()) {
                                    for (int h = 0; h < repAgents.size(); h++) {
                                        CiSimple agent = getAgentMainInfo(repAgents.get(h));
                                        if (null != agent) {
                                            logger.info("调用发送CMDB-Agent管理集成线程！");
                                            SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                            thread.start();
                                            logger.info("调用发送CMDB-Agent管理集成线程结束！");
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Agent信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                            }
                        }
                        return res;
                    } else {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                        return res;
                    }
                } else {
                    if(orgSql.get(MESSAGE_TEXT) != null){
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, orgSql.get(MESSAGE_TEXT));
                        return res;
                    }else{
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                        return res;
                    }

                }
            } else {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                return res;
            }
        }catch (Exception e){
            logger.error("", e);
        }finally {
            DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
            DBResource.closeConnection(checkConn, "saveAgentMaitainInfos", logger);
            if(dbConns != null) {
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "deleteAgentMaintainInfos", logger);
                }
            }
        }
        return null;
    }

    /**
     * 
     * <li>Description:保存或更新agent和设备信息</li> 
     * <AUTHOR>
     * 2021年6月1日 
     * return Map
     * @throws RepositoryException 
     */
    public Map saveAgentAndEquipInfos () throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        CMDBManager manager = new CMDBManager();
        try
        {
            map = manager.saveAgentAndEquipInfos();
        } catch (Exception e)
        {
            logger.error("AgentMaintainService.saveAgentAndEquipInfos is error", e);
        }
        return map;
    }

    public CiSimple getAgentMainInfo ( Agent agent )
    {
        CiSimple cisimple = new CiSimple();
        if (null != agent)
        {
            cisimple.setAgentIP(null == agent.getIagentip() ? "" : agent.getIagentip());
            cisimple.setAgentName(null == agent.getIagentname() ? "" : agent.getIagentname());
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            cisimple.setSyncTime(sdf.format(d));
            if (null == agent.getIosName())
            {
                cisimple.setOsName("");
            }
            if (null != agent.getIosName())
            {
                if ("没有设置".equals(agent.getIosName()))
                {
                    cisimple.setOsName("");
                } else
                {
                    cisimple.setOsName(agent.getIosName());
                }

            }

        }
        return cisimple;
    }

    public Agent getAgentMaintainInfoById ( Long id ) throws Exception
    {
        Agent res = new Agent();
        Connection baseConn = Tools.getBaseConnectionInfo();
        try
        {
            if (null != baseConn)
            { // 没有基线源，不可以
                res = manager.getAgentMaintainInfo(id, baseConn);
            }
        } catch (Exception e)
        {
            logger.error("AgentMaintainService.getAgentMaintainInfoById has an Exception:" + e.getMessage(), e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConnection(baseConn, "getAgentMaintainInfoById", logger);
        }
        return res;
    }
    //根据ip,port获取Agent信息
    public Agent getAgentMaintainInfoByIpPort (String iagentIp, int iagentPort) throws Exception
    {
        Agent agent = null;
        //todo:根据ip,port获取Agent信息
        agent=manager.getAgentMaintainInfoByIpPort(iagentIp, iagentPort);
        return agent;
    }

    public List<Long> getDeleteBusiness ( Long[] agentIds, int sysType ) throws RepositoryException
    {
        return manager.getdeleteBusiness(agentIds, sysType);
    }

    public Map deleteAgentMaintainInfos ( Long[] deleteIds, int sysType, String userName, int state ) throws Exception
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        long deleteId = 0;
        boolean chenState = false;
        String strIps = "";
        List<Long> businessIds = this.getDeleteBusiness(deleteIds, Constants.IEAI_EMERGENCY_SWITCH);
        if (null != baseConn)
        { // 没有基线源，不可以
            String modelError = "<div style='width:400px;word-wrap: break-word;word-break: break-all;overflow: auto;'>";
            try
            {
                for (int p = 0; p < deleteIds.length; p++)
                {
                    deleteId = deleteIds[p];
                    String flag = manager.findAgentRelationData(baseConn, deleteId);
                    String comFlag = manager.findComRelationData(baseConn, deleteId);
                    if (!"".equals(flag) || !"".equals(comFlag))
                    {
                        if (!"".equals(flag))
                        {
                            modelError = modelError + "<br>" + "<font color='red'>绑定模块</font>:业务系统维护;工程类型:定时任务;业务系统名称:"
                                    + flag + ";";
                        }
                        if (!"".equals(comFlag))
                        {
                            modelError = modelError + "<br>" + "<font color='red'>绑定模块</font>:设备维护;设备IP:" + comFlag
                                    + ";";
                        }
                        chenState = true;
                        String iip = manager.findIpsById(baseConn, deleteId);
                        strIps = strIps + iip + ",";

                    }
                }
                modelError = modelError + "</div>";
            } catch (Exception e)
            {
                logger.error(e);
                DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
                throw new RepositoryException(ServerError.ERR_DB_DELETE);

            }
            if (chenState)
            {
                // 不调用基础库连接后进行关闭
                DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "关于Agent:" + strIps + "处于绑定中，确定是否解除绑定！" + modelError);
                return res;
            }
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

            // 删除之前先获取一下Agent的信息
            List<Agent> agentList = new ArrayList<Agent>();
            for (Long iid : deleteIds)
            {
                // 根据deleteIds查找Agent信息
                Agent iagent = getAgentMaintainInfoById(iid);
                agentList.add(iagent);
            }

            // 删除前，停掉巡检流程：
            Boolean candel = true;
            Boolean stopResult = true;
            Boolean stopNewHcResult = true;
            HcComputerOperateService service2 = new HcComputerOperateService();
            UserInfo userInfo = new UserInfo();
            userInfo.setFullName(userName);
            stopResult = service2.stopHcForDelAgent(deleteIds, userInfo);

            // 新巡检停止方法：
            boolean suppercheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
            if (suppercheck)
            {
                stopNewHcResult = service2.stopNewHcForDelAgent(deleteIds);
            }

            candel = stopResult && stopNewHcResult;
            if (!candel)
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "关于停止待删除Agent所关联设备的巡检状态失败！请手动停止关联设备的巡检后在尝试删除！");
                return res;
            }

            Map orgSql = manager.organizeAgentMaitainInfoDeleteSql(deleteIds, businessIds, baseConn, dbConns, state);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {

                    // 同时删除资源组
                    // ResourceGroupManager.getInstance().delServerInfoByAgent(deleteIds);

                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "数据删除成功！");
                    logger.info("用户名:" + userName + "操作:Agent管理删除操作。");

                    // CMDB--Agent管理集成 Agent设备同步信息 新增一个是否开启该功能的开关
                    if (ServerEnv.getServerEnv().isSendAgentInfoSwitch())
                    {
                        try
                        {

                            if (!agentList.isEmpty())
                            {
                                for (Agent iagent : agentList)
                                {

                                    CiSimple agent = getAgentMainInfo(iagent);

                                    if (null != agent)
                                    {
                                        logger.info("调用发送Agent信息线程！");

                                        SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                        thread.start();
                                        logger.info("调用发送Agent信息线程结束！");
                                    }

                                }

                            }

                        } catch (Exception e)
                        {
                            logger.error("Agent删除信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                        }
                    }

                    return res;
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "deleteAgentMaintainInfos", logger);
                }
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                return res;
            }

        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }

    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public Map deleteTimeTaskAgentMaintainInfos ( Long[] deleteIds, String userName, int state ) throws Exception
    {
        Map res = new HashMap();

        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        long deleteId = 0;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List exec = new ArrayList();
        List rollback = new ArrayList();
        List<String> exeSqlstimetask = new ArrayList<String>();
        List<String> rollbackSqlstimetask = new ArrayList<String>();
        List<Long> businessIds = this.getDeleteBusiness(deleteIds, Constants.IEAI_EMERGENCY_SWITCH);
        List<Long> computerIdList = new ArrayList<Long>();
        if (null != baseConn)
        { // 没有基线源，不可以

            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            List<Map<Long, Long[]>> allListsys = new ArrayList<Map<Long, Long[]>>();
            List<Map<Long, Long[]>> allListcom = new ArrayList<Map<Long, Long[]>>();
            for (int p = 0; p < deleteIds.length; p++)
            {
                Map<Long, Long[]> allMap = new HashMap<Long, Long[]>();
                Map<Long, Long[]> allComMap = new HashMap<Long, Long[]>();
                deleteId = deleteIds[p]; // agentId
                List<Long> systemList = manager.findAgentRelationId(baseConn, deleteId);
                Long[] systemIds = null;
                if (null != systemList && !systemList.isEmpty())
                {
                    systemIds = new Long[systemList.size()];
                    for (int h = 0; h < systemList.size(); h++)
                    {
                        systemIds[h] = systemList.get(h);
                    }
                }
                if (null != systemIds)
                {
                    allMap.put(deleteId, systemIds);
                    allListsys.add(allMap);
                }

                // 查找到的agent已经绑定的业务系统systemList，根据业务系统查找已经绑定了该agent的定时任务(调度IP),记录定时任务id，ip信息，然后判断开头，结尾，中间，然后更新该条定时任务
                // 的调度IP
                if (null != systemIds && systemIds.length > 0)
                {
                    String gids = StringUtils.join(systemIds, ",");
                    manager.findTimetaskList(deleteId, gids, exeSqlstimetask, rollbackSqlstimetask);
                }

                List<Long> comIdList = manager.findComRelationIds(baseConn, deleteId);
                Long[] comIds = null;
                if (null != comIdList && !comIdList.isEmpty())
                {
                    comIds = new Long[comIdList.size()];
                    for (int k = 0; k < comIdList.size(); k++)
                    {
                        comIds[k] = comIdList.get(k);

                        computerIdList.add(comIdList.get(k));
                    }
                }

                if (null != comIds)
                {
                    allComMap.put(deleteId, comIds);
                    allListcom.add(allComMap);
                }

            }
            Map sysRelSql = new HashMap();
            if (!allListsys.isEmpty())
            {
                sysRelSql = manager.organizeBSSqlSystemRelationListMapDel(allListsys, userName, baseConn);
            }

            Map comSql = new HashMap();
            if (!allListcom.isEmpty())
            {

                comSql = manager.organizeComSQLMapDel(allListcom, userName, baseConn);
            }

            // 删除之前先获取一下Agent的信息
            List<Agent> agentList = new ArrayList<Agent>();
            for (Long iid : deleteIds)
            {
                // 根据deleteIds查找Agent信息
                Agent iagent = getAgentMaintainInfoById(iid);
                agentList.add(iagent);
            }





            Map orgSql = manager.organizeAgentMaitainInfoDeleteSql(deleteIds, businessIds, baseConn, dbConns, state);

            // 删除定时任务的调度IP
            exec.addAll(exeSqlstimetask);
            rollback.addAll(rollbackSqlstimetask);

            if (null != sysRelSql && sysRelSql.containsKey(SUCCESS_TEXT))
            {
                if ((Boolean) sysRelSql.get(SUCCESS_TEXT))
                {

                    exec.addAll((List<String>) sysRelSql.get(EXECSQL_TEXT));
                    rollback.addAll((List<String>) sysRelSql.get(ROLLBACKSQL_TEXT));
                } else
                {

                    DBResource.closeConnection(baseConn, method, logger);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, logger);
                    }
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "组织业务系统维护绑定agent的sql时出现错误！");
                    return res;
                }
            }

            if (null != comSql && comSql.containsKey(SUCCESS_TEXT))
            {
                if ((Boolean) comSql.get(SUCCESS_TEXT))
                {
                    exec.addAll((List<String>) comSql.get(EXECSQL_TEXT));
                    rollback.addAll((List<String>) comSql.get(ROLLBACKSQL_TEXT));
                } else
                {

                    DBResource.closeConnection(baseConn, method, logger);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, logger);
                    }
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "组织设备维护绑定agent的sql时出现错误！");
                    return res;
                }
            }


            if (null != orgSql && orgSql.containsKey(SUCCESS_TEXT))
            {
                if ((Boolean) orgSql.get(SUCCESS_TEXT))
                {
                    exec.addAll((List<String>) orgSql.get(EXECSQL_TEXT));
                    rollback.addAll((List<String>) orgSql.get(ROLLBACKSQL_TEXT));
                } else
                {

                    DBResource.closeConnection(baseConn, method, logger);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, logger);
                    }
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                    return res;
                }

            }

            //增加福建农信开关，用于删除设备和设备与业务系统关系。下钩子，********
            if(Environment.getInstance().getBankSwitchIsFjnx() && !computerIdList.isEmpty()){
//                Map computerAndSysRelationorgSql = organizeComputerAndRelationSQLMapDel(computerIdList, 7, baseConn);
                Map computerAndSysRelationorgSql = new ComputerService().orgDeleteSqlForCpList(computerIdList.toArray(new Long[computerIdList.size()]) ,baseConn);
                if (null != computerAndSysRelationorgSql && computerAndSysRelationorgSql.containsKey(SUCCESS_TEXT))
                {
                    if ((Boolean) computerAndSysRelationorgSql.get(SUCCESS_TEXT))
                    {
                        exec.addAll((List<String>) computerAndSysRelationorgSql.get(EXECSQL_TEXT));
                        rollback.addAll((List<String>) computerAndSysRelationorgSql.get(ROLLBACKSQL_TEXT));
                    } else
                    {

                        DBResource.closeConnection(baseConn, method, logger);
                        for (Connection conn : dbConns)
                        {
                            DBResource.closeConnection(conn, method, logger);
                        }
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息同步删除设备信息及设备绑定业务系统信息时出现错误！");
                        return res;
                    }

                }
            }


            if (DBUtil.hitDatabase(dbConns, exec, rollback))
            {
                // 同时删除资源组
                res.put(SUCCESS_TEXT, true);
                res.put(MESSAGE_TEXT, "数据删除成功！");
                logger.info("用户名:" + userName + "操作:Agent管理删除操作。");

                // CMDB--Agent管理集成 Agent设备同步信息 新增一个是否开启该功能的开关
                if (ServerEnv.getServerEnv().isSendAgentInfoSwitch())
                {
                    try
                    {

                        if (!agentList.isEmpty())
                        {
                            for (Agent iagent : agentList)
                            {

                                CiSimple agent = getAgentMainInfo(iagent);

                                if (null != agent)
                                {
                                    logger.info("调用发送Agent信息线程！");

                                    SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                    thread.start();
                                    logger.info("调用发送Agent信息线程结束！");
                                }

                            }

                        }

                    } catch (Exception e)
                    {
                        logger.error("Agent删除信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                    }
                }

                //增加删除设备的同时，删除设备与批量任务的绑定关系
                HcCollectTaskManager.getInstance().deleteTargetRelByTaskId(computerIdList.toArray(new Long[computerIdList.size()]));

                return res;
            } else
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "执行sql时出现错误！");
                return res;
            }

        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }
    }


   /* public Map<String, Object> organizeComputerAndRelationSQLMapDel ( List<Long> computerIdList,int prjType,
                                                      Connection baeConn ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        try
        {
            for(Long computerId : computerIdList){
                String execSql = "DELETE FROM IEAI_SYS_RELATION WHERE PRJTYPE="+prjType+" AND COMPUTERID="+computerId;
                SysRelationBean sysRelationBean = ProjectManager.getInstance().getHcSystemRelationListForBack(computerId,baeConn);
                String sql_back ="  INSERT INTO   IEAI_SYS_RELATION  (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE,SYSSTATE,SYSPARAM,SDID)"
                        + "values  (" + sysRelationBean.getSystemId() + "," + computerId+ ",'" +sysRelationBean.getIp()+ "',"+sysRelationBean.getComputerState()+","
                        + sysRelationBean.getSysState() + " ,'" + sysRelationBean.getCenterName() + "','"+sysRelationBean.getSysName()+"',"
                        +sysRelationBean.getPrjType()+","+sysRelationBean.getSysState()+",'"+sysRelationBean.getSysParam()+"',"+sysRelationBean.getSdid()+     ")";

                String execComputerSql = "delete from IEAI_COMPUTER_LIST where cpid = " + computerId;
                String computerSql_bak = " insert into ieai_computer_list(cpid,ip,cpname,centerName,CPSTATE,cpport,cpos,filename,cpuser,cppasswd,createtime,createuser,UPUSER,UPTIME,CHECKTIME,ACTNUM,ACTNUMMAX,Ictid,cmid,cputhreshold,memorythreshold,iowaitthreshold,FLOWID,PRIORITY,des,ipalias,agentpath,agentip,CSTATE,IAGENTUP_ID,wswitch,cswitch) "
                        + " values ({id},'{ip}','{name}','{centerName}',{cpstate},{port},{osid},'{fileName}','{user}','{passwd}',{createTime},'{createUser}','{upUser}',{upTime},{checkTime},{actNum},{actNumMax},{ctid},{cmid},{cputhreshold},{memorythreshold},{iowaitthreshold},{flowId},{priority},'{des}',{ipAlias},'{agentPath}','{agentIp}',{cstate},{agentupId},{wswitch},{cswitch})";

                ComputerModel computerModel = manager.getComputerModelByCompuerId(computerId,baeConn);
                BeanFormatter<ComputerModel> info = new BeanFormatter<ComputerModel>(computerSql_bak);
                String insertinfo = info.format(computerModel);

                exeSqls.add(execSql);
                rollbackSqls.add(sql_back);

                exeSqls.add(execComputerSql);
                rollbackSqls.add(computerSql_bak);
            }
        } catch (Exception ex)
        {
            throw new RepositoryException(ServerError.ERR_DB_INSERT,ex);
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }*/

    public Map deleteUpgradeAgentFlag ( Long[] ids, String userName ) throws EswitchException
    {
        Map res = new HashMap();

        if (ids.length > 0)
        {
            try
            {
                manager.deleteUpgradeAgentFlag(ids);
                manager.deleteUpgradeAgentFlagForMonitor(ids);
                res.put(SUCCESS_TEXT, true);
                res.put(MESSAGE_TEXT, "清除成功！");
                logger.info("用户名:" + userName + "操作:Agent管理清除升级标记操作。");
            } catch (DBException e)
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "清除升级标记过程中出现问题！" + e.getMessage());
            } catch (RepositoryException e)
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "清除升级标记过程中出现问题！" + e.getMessage());
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有选择Agent!");
        }

        return res;
    }

    public Vector<Map<String, String>> installAgent ( List<ComputerDataModel> list, int sysType ) throws Exception
    {
        Vector<Map<String, String>> retList = new Vector<Map<String, String>>();
        long agentId = 0;
        Vector<AgentInstallTread> threads = new Vector<AgentInstallTread>();
        for (int i = 0; i < list.size(); i++)
        {
            agentId = list.get(i).getIid();
            boolean force = list.get(i).isForce();
            int isusp = list.get(i).getIsusp();
            Map<String, String> map = AgentMaintainManager.getInstance().getAgentInstallInfo(agentId, sysType);
            map.put("force", force ? "force" : "");
            map.put("ioperation_user", list.get(i).getIoperation_user());
            if (isusp == 1)
            {
                map.put("ioperation_password", getUspPassword(map));
            } else
            {
                map.put("ioperation_password", list.get(i).getIoperation_password());
            }
            AgentInstallTread thread = new AgentInstallTread(map, retList);
            threads.add(thread);
            thread.start();
        }
        for (AgentInstallTread ithread : threads)
        {
            ithread.join(180000);// 设置超时时间3分钟
            if (ithread.isAlive())
            {
                ithread.closeExec();
                ithread.join(10000);
                if (ithread.isAlive())
                {
                    ithread.interrupt();
                    Map<String, String> retMap = new HashMap<String, String>();
                    retMap.put("ip", ithread.getMap().get("iagent_ip"));
                    retMap.put(SUCCESS_TEXT, "false");
                    retMap.put("errMsg", "安装超时,请检查安装日志");
                    retList.add(retMap);
                }
            }
        }
        logger.info(retList.toString());
        return retList;
    }

    private String getUspPassword ( Map<String, String> map )
    {
        String str = "";
        try
        {

            if (Environment.getInstance().getBankSwitchIscib())
            {
                str = getCibPassword(map);
            } else if (Environment.getInstance().getBankSwitchIsCzb())
            {
                PLUPdwService pd = new PLUPdwService();
                String ip = map.get("iagent_ip");
                String userName = map.get("ioperation_user");
                str = pd.getPldpdw(ip, userName);
            }

        } catch (Exception e)
        {
            logger.error(map.get("iagent_ip") + "获取堡垒机密码失败");
        }
        return str;
    }

    public Map upgradeAgent ( Long[] ids, int sysType, String userName ) throws Exception
    {
        Map res = new HashMap();

        List<Agent> agents;
        try
        {
            agents = manager.getAgentsByIds(Arrays.asList(ids), sysType);
        } catch (Exception e1)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "获得Agent信息出现错误！" + e1.getMessage());
            return res;
        }

        String adpDefUuid = manager.getAgentMaintainAdaptorUuid();
        if ("".equals(adpDefUuid))
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "获取Agent维护接口(agentmaintainadapator)出现问题！");
            return res;
        }

        List<Agent> canNotConnect = new ArrayList<Agent>();
        List<Agent> upgradings = new ArrayList<Agent>();
        List<Agent> updateAgents = new ArrayList<Agent>();
        String agentIp = "";
        for (Agent agent : agents)
        {
            agentIp = agent.getIagentip();
            if (ValidateUtil.validateIp(agentIp))
            {
                agentIp = DNSAnalyze.getIPByName(agentIp);
                agent.setIagentip(agentIp);
            }

            // 正在升级的agent不能再次升级
            try
            {
                int state = manager.getAgentState(agent.getIagentip(), sysType);
                if (state == Constants.REMOTE_AGENT_STATE_UPGRADING)
                {
                    upgradings.add(agent);
                } else
                {
                    updateAgents.add(agent);
                }
            } catch (Exception e1)
            {
                logger.error("upgradeAgent", e1);
                canNotConnect.add(agent);
            }
        }

        AgentUpgradeThread upgradeThread = new AgentUpgradeThread(updateAgents, adpDefUuid, sysType);
        upgradeThread.start();

        if (!canNotConnect.isEmpty() || !upgradings.isEmpty())
        {
            List ips = new ArrayList();
            List upgradingIps = new ArrayList();
            if (!canNotConnect.isEmpty())
            {
                for (Agent agent : canNotConnect)
                {
                    ips.add(agent.getIagentip());
                }
            }

            if (!upgradings.isEmpty())
            {
                for (Agent agent : upgradings)
                {
                    upgradingIps.add(agent.getIagentip());
                }
            }
            String msg = "升级Agent任务已经发送。";
            if (!canNotConnect.isEmpty())
            {
                msg += "以下agent无法连接：" + StringUtils.join(ips, ",");
            }
            if (!upgradings.isEmpty())
            {
                msg += "以下agent正处于升级状态，无法执行此次升级操作：" + StringUtils.join(upgradingIps, ",");
            }
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, msg);
        } else
        {
            res.put(SUCCESS_TEXT, true);
            res.put(MESSAGE_TEXT, "升级Agent任务已经发送！");
            logger.info("用户名：" + userName + ",操作：Agent管理中的升级操作。");
        }
        return res;

    }

    public Map fetchAgentInfo ( int sysType, String userName ) throws Exception
    {

        Map res = new HashMap();

        String adpDefUuid = manager.getAgentMaintainAdaptorUuid();
        if ("".equals(adpDefUuid))
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "获取Agent维护接口(agentmaintainadapator)出现问题！");
            return res;
        }

        List<Agent> canNotConnect = new ArrayList<Agent>();

        List<Agent> agents = manager.getAllAgentMaintainList(true, sysType); // 正在升级的agent不获取agent信息

        for (Agent agent : agents)
        {
            AgentMaintainAct agentMaintainAct = manager.createAgentMaintainAct(agent, "monitor", adpDefUuid, "",
                sysType);
            String ip = agentMaintainAct.getAgentHost();
            int port = agentMaintainAct.getAgentPort();

            Hashtable table = new Hashtable();

            boolean isSaveErrorInfo = ServerEnv.getInstance().getBooleanConfig("agent.error.saveinfo",
                Environment.FALSE_BOOLEAN);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);

            Vector params = new Vector();
            params.addElement(agentMaintainAct.getId());
            String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                agentMaintainAct.getServerHost());
            params.addElement(serverIp);
            params.addElement(agentMaintainAct.getServerPort());
            table.put("flowId", String.valueOf(agentMaintainAct.getFlowId()));// agent监控不需要再监控，此处赋值1可否？
            table.put("adaptorConfig", agentMaintainAct.getAdaptorConfig());// ?默认值
            table.put("agentPort", port);
            table.put("projectName", agentMaintainAct.getProjectName());
            table.put("adaptorDefUUID", agentMaintainAct.getAdaptorDefUUID());
            table.put("_scopeId", agentMaintainAct.getScopeId());// ?
            table.put("_actStateDataVersion", agentMaintainAct.getActStateDataVersion());// ?
            table.put("timeout", "60000");// ?
            table.put("agentHost", ip);
            table.put("actId", agentMaintainAct.getActId());// 是否是固定值AgentMonitor?
            table.put("adaptorDefName", agentMaintainAct.getAdaptorDefName());// agentmaintoradaptor
            table.put("startTime", agentMaintainAct.getStartTime());// 定时任务实例启动时间
            table.put("actName", agentMaintainAct.getActName()); // 默认值
            table.put("status", agentMaintainAct.getStatus());// ? 默认值
            table.put("levelOfWeight", String.valueOf(agentMaintainAct.getLevelOfWeight()));// ? 默认值
            table.put("Id", agentMaintainAct.getId());
            table.put("flowName", agentMaintainAct.getFlowName());
            table.put("flowPoolNum", "0");// ?默认值
            table.put("serverHost", serverIp);
            table.put("serverPort", agentMaintainAct.getServerPort());
            table.put("isSafeFirst", agentMaintainAct.isSafeFirst()); // ?默认值
            table.put("levelOfPRI", String.valueOf(agentMaintainAct.getLevelOfPRI()));// ?默认值
            Hashtable t = new Hashtable();
            t.put("icustom_cmd", agent.getIcustom_cmd() == null ? "" : agent.getIcustom_cmd());
            table.put("input", t);// ?默认值
            int ssltype= RpcSslType.getInstance().exec(ip, port);

            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(table, ip, port, sysType);
                ip = pm.getIp();
                port = pm.getPort();
                ssltype=pm.getSsl();
            }
            params.addElement(table);
            XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, ssltype);

            manager.createRequestWrapperWork(agentMaintainAct);

            try
            {
                if (manager.checkOutTime(agent.getIagentip(), sysType))
                {
                    if (isSaveErrorInfo)
                    {
                        manager.saveAgentErrorInfo(agent, "O");
                    }
                    warnAgentOutTime(agent);
                    manager.updateAgentState(agent.getIagentip(), agentMaintainAct.getAgentPort(),
                        Constants.REMOTE_AGENT_STATE_OUTTIME, false);
                }
                manager.saveExcuteTime(agent.getIagentip(), sysType);
                XmlRpcClient finalRpcClient = rpcClient;
                new Thread(
                    new Runnable() {
                        @Override
                        public void run() {
                            updateInactiveNum(finalRpcClient, agent.getIagentip(), agent.getIagentport(), serverIp);
                        }
                    }
                ).start();
                try {
                    rpcClient.execute("IEAIAgent.executeAct", params);
                }catch (ProtocolException pe){
                    logger.info("fetchAgentInfoQuery is ProtocolException",pe);
                    //在ssltype为0的情况下，普通请求失败，则尝试使用ssl加密方式重新连接
                    if(ssltype==0 ){
                        logger.info("agent connect fail,retry ssl connect:"+ip+","+port);
                        rpcClient = new AgentXmlRpcClient(ip, port, 1);
                        rpcClient.execute("IEAIAgent.executeAct", params);
                        logger.info("agent retry ssl connect successful:"+ip+","+port);
                        //当尝试使用ssl加密方式连接成功时，更改ssl加密方式为1.
                        RpcSslType.getInstance().setSslType(1, agent.getIagentip(),agent.getIagentport());
                        logger.info("update ssl successful:"+ip+","+port);
                    }
                }
            } catch (Exception e)
            {
                if (isSaveErrorInfo)
                {
                    manager.saveAgentErrorInfo(agent, "E");
                }
                warnAgent(agent);
                logger.error("fetchAgentInfo", e);
                canNotConnect.add(agent);
                manager.checkRLAgentState(agent.getIagentip(), 0, sysType);
                logger.info("异常 ip "+agent.getIagentip() + ":" + agent.getIagentport());
                // 判断agent原来状态，如果原来正常则调用多写方法，并且将实时并发数变为0
                if (Constants.REMOTE_AGENT_STATE_NORMAL == manager.getAgentStateMsg(agent.getIagentip(),
                    agent.getIagentport(), sysType))
                {
                    manager.updateConcurrency(agent.getIagentip(), agent.getIagentport());
                    manager.updateAgentState(agent.getIagentip(), agent.getIagentport(),
                        Constants.REMOTE_AGENT_STATE_EXCEPTION, false);
                } else
                {
                    manager.updateAgentStateSing(agent.getIagentip(), agent.getIagentport(),
                        Constants.REMOTE_AGENT_STATE_EXCEPTION, false);
                }
                // 读取配置文件，获取agent失败时，是否发送消息
                if (ServerEnv.getServerEnv().isSendAgentErrorMessage())
                {
                    if (manager.checkIfSendMsg(agent.getIagentip(), sysType))
                    {
                        HttpWarnMessage warnMsg = new HttpWarnMessage();// 组装报警平台所需的json报文
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
                        warnMsg.sendAgentExceptionMessage((short) sysType,
                            StringUtils.isBlank(agent.getIcomName()) ? agent.getIagentip() : agent.getIcomName(),
                            format.format(new Date()), agent.getIagentip());
                    }

                }

            }

        }

        if (!canNotConnect.isEmpty())
        {
            List ips = new ArrayList();
            for (Agent agent : canNotConnect)
            {
                ips.add(agent.getIagentip() + ":" + agent.getIagentport());
                logger.info("异常 ip "+agent.getIagentip() + ":" + agent.getIagentport());
            }
            res.put(SUCCESS_TEXT, false);
            StringBuffer sb = new StringBuffer("<div class=\"win_prompt\">");
            sb.append("<p>获取Agent信息请求已发送，但以下Agent无法连接：</p>");
            sb.append("<div class=\"pro_ip\">");
            for (int i = 0; i < ips.size(); i++)
            {
                sb.append("<span>" + ips.get(i).toString() + "</span>");
            }
            sb.append("</div></div>");
            res.put(MESSAGE_TEXT, sb.toString());
            // res.put(MESSAGE_TEXT, "获取Agent信息请求已经发送，但以下Agent无法连接：" + StringUtils.join(ips, ","));
        } else
        {
            res.put(SUCCESS_TEXT, true);
            res.put(MESSAGE_TEXT, "获取Agent信息请求已经发送！");
        }
        // OperationService.getInstance().calladdoperRecord("Agent管理",userName,"获取全部Agent信息",Constants.IEAI_EMERGENCY_SWITCH);
        logger.info("用户名：" + userName + ",操作：Agent管理中的获取信息操作。");

        return res;

    }

    public void warnAgent ( Agent agent )
    {
        String message = "ip为" + agent.getIagentip() + ",端口为" + agent.getIagentport() + "连接异常。";
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentnotconnect");
        ieaiWarnModel.setIlevelcode("five");
        ieaiWarnModel.setIip(agent.getIagentip());
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhostname(agent.getIagentname());
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        boolean nmnxWarn = Environment.getInstance().isWarnNmnxSwitch();
        if (nmnxWarn)
        {
            // 如果是内蒙农信告警,通过Agentip获取所需要发送的告警人
            List<UserModel> list = manager.getUserListByIip(agent.getIagentip(), Constants.IEAI_OPM);
            ieaiWarnModel.setUserList(list);
        }

        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
        if (Environment.getInstance().getCibTivoliSwitch())
        {
            SendCibSyslog syslog = new SendCibSyslog();
            StringBuilder sb = new StringBuilder();
            sb.append("severity=WARNING hostname=").append(agent.getIagentname() == null ? "" : agent.getIagentname())
                    .append(" ").append("ip=" + agent.getIagentip()).append(" ")
                    .append("msg=\"" + agent.getIagentip() + "Agent连接异常" + "\"");
            logger.info(sb.toString());
            syslog.sendsysLog(sb.toString());
        }
    }

    /**
     *
     * <li>Description:获取选中的Agent信息</li>
     * <AUTHOR>
     * 2016年10月20日
     * @param ids
     * @param sysType
     * @param userName
     * @return
     * @throws Exception
     * return Map
     */
    public Map fetchAgentInfoQuery ( Long[] ids, int sysType, String userName, String monitorType ) throws Exception
    {

        Map res = new HashMap();

        String adpDefUuid = manager.getAgentMaintainAdaptorUuid();
        if ("".equals(adpDefUuid))
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "获取Agent维护接口(agentmaintainadapator)出现问题！");
            return res;
        }

        List<Agent> canNotConnect = new ArrayList<Agent>();

        List<Agent> agents = manager.getAgentMaintainList(ids, true, sysType); // 正在升级的agent不获取agent信息

        for (Agent agent : agents)
        {
            AgentMaintainAct agentMaintainAct = manager.createAgentMaintainAct(agent, monitorType, adpDefUuid, "",
                sysType);
            String ip = agentMaintainAct.getAgentHost();
            int port = agentMaintainAct.getAgentPort();
            Hashtable table = new Hashtable();
            boolean isSaveErrorInfo = ServerEnv.getInstance().getBooleanConfig("agent.error.saveinfo",
                Environment.FALSE_BOOLEAN);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            Vector params = new Vector();
            params.addElement(agentMaintainAct.getId());
            String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                agentMaintainAct.getServerHost());
            params.addElement(serverIp);
            params.addElement(agentMaintainAct.getServerPort());
            table.put("flowId", String.valueOf(agentMaintainAct.getFlowId()));// agent监控不需要再监控，此处赋值1可否？
            table.put("adaptorConfig", agentMaintainAct.getAdaptorConfig());// ?默认值
            table.put("agentPort", port);
            table.put("projectName", agentMaintainAct.getProjectName());
            table.put("adaptorDefUUID", agentMaintainAct.getAdaptorDefUUID());
            table.put("_scopeId", agentMaintainAct.getScopeId());// ?
            table.put("_actStateDataVersion", agentMaintainAct.getActStateDataVersion());// ?
            table.put("timeout", "60000");// ?
            table.put("agentHost", ip);
            table.put("actId", agentMaintainAct.getActId());// 是否是固定值AgentMonitor?
            table.put("adaptorDefName", agentMaintainAct.getAdaptorDefName());// agentmaintoradaptor
            table.put("startTime", agentMaintainAct.getStartTime());// 定时任务实例启动时间
            table.put("actName", agentMaintainAct.getActName()); // 默认值
            table.put("status", agentMaintainAct.getStatus());// ? 默认值
            table.put("levelOfWeight", String.valueOf(agentMaintainAct.getLevelOfWeight()));// ? 默认值
            table.put("Id", agentMaintainAct.getId());
            table.put("flowName", agentMaintainAct.getFlowName());
            table.put("flowPoolNum", "0");// ?默认值
            table.put("serverHost", serverIp);
            table.put("serverPort", agentMaintainAct.getServerPort());
            table.put("isSafeFirst", agentMaintainAct.isSafeFirst()); // ?默认值
            table.put("levelOfPRI", String.valueOf(agentMaintainAct.getLevelOfPRI()));// ?默认值
            table.put("isczb", Environment.getInstance().getBankSwitchIsCzb());// 是否是czb
            Hashtable t = new Hashtable();
            String customCmd = agent.getIcustom_cmd() == null ? "" : agent.getIcustom_cmd();
            // 增加光大SMDB集成，当类型为SMDB时，获取固定的脚本执行。
            if (monitorType.equals(Constants.AGENT_MAINTAIN_SMDB))
            {
                if (!StringUtil.isBlank(agent.getIosName()) && (agent.getIosName().indexOf("Win") > -1 || agent.getIosName().indexOf("win") > -1))
                {
                    customCmd = PersonalityEnv.getSmdbShellnameWinValue();
                } else
                {
                    customCmd = PersonalityEnv.getSmdbShellnameLinuxValue();
                }
            }
            t.put("icustom_cmd", customCmd);
            table.put("input", t);// ?默认值
            int ssltype= RpcSslType.getInstance().exec(ip, port);

            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(table, ip, port, sysType);
                ip = pm.getIp();
                port = pm.getPort();
                ssltype=pm.getSsl();
            }
            XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, ssltype);
            params.addElement(table);

            manager.createRequestWrapperWork(agentMaintainAct);

            try
            {
                if (manager.checkOutTime(agent.getIagentip(), sysType))
                {
                    if (isSaveErrorInfo)
                    {
                        manager.saveAgentErrorInfo(agent, "O");
                    }
                    warnAgentOutTime(agent);
                    manager.updateAgentState(agent.getIagentip(), agent.getIagentport(),
                        Constants.REMOTE_AGENT_STATE_OUTTIME, false);
                }
                manager.saveExcuteTime(agent.getIagentip(), sysType);
                XmlRpcClient finalRpcClient = rpcClient;
                new Thread(
                    new Runnable() {
                        @Override
                        public void run() {
                            updateInactiveNum(finalRpcClient, agent.getIagentip(), agent.getIagentport(), serverIp);
                        }
                    }
                ).start();
                try {
                    rpcClient.execute("IEAIAgent.executeAct", params);
                }catch (ProtocolException pe){
                    logger.info("fetchAgentInfoQuery is ProtocolException",pe);
                    //在ssltype为0的情况下，普通请求失败，则尝试使用ssl加密方式重新连接
                    if(ssltype==0 ){
                        logger.info("agent connect fail,retry ssl connect:"+ip+","+port);
                        rpcClient = new AgentXmlRpcClient(ip, port, 1);
                        rpcClient.execute("IEAIAgent.executeAct", params);
                        logger.info("agent retry ssl connect successful:"+ip+","+port);
                        //当尝试使用ssl加密方式连接成功时，更改ssl加密方式为1.
                        RpcSslType.getInstance().setSslType(1, agent.getIagentip(),agent.getIagentport());
                        logger.info("update ssl successful:"+ip+","+port);
                    }
                }
            } catch (Exception e)
            {
                logger.info("isSaveErrorInfo=" + isSaveErrorInfo);
                if (isSaveErrorInfo)
                {
                    manager.saveAgentErrorInfo(agent, "E");
                }
                warnAgent(agent);
                logger.error("fetchAgentInfoQuery is error！", e);
                canNotConnect.add(agent);
                manager.checkRLAgentState(agent.getIagentip(), 0, sysType);
                // 判断agent原来状态，如果原来正常则调用多写方法，并且将实时并发数变为0
                if (Constants.REMOTE_AGENT_STATE_NORMAL == manager.getAgentStateMsg(agent.getIagentip(),
                    agent.getIagentport(), sysType))
                {
                    manager.updateConcurrency(agent.getIagentip(), agent.getIagentport());
                    manager.updateAgentState(agent.getIagentip(), agent.getIagentport(),
                        Constants.REMOTE_AGENT_STATE_EXCEPTION, false);
                } else
                {
                    manager.updateAgentStateSing(agent.getIagentip(), agent.getIagentport(),
                        Constants.REMOTE_AGENT_STATE_EXCEPTION, false);
                }

                // 读取配置文件，获取agent失败时，是否发送消息
                if (ServerEnv.getServerEnv().isSendAgentErrorMessage())
                {
                    if (manager.checkIfSendMsg(agent.getIagentip(), sysType))
                    {
                        HttpWarnMessage warnMsg = new HttpWarnMessage();// 组装报警平台所需的json报文
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
                        warnMsg.sendAgentExceptionMessage((short) sysType,
                            StringUtils.isBlank(agent.getIcomName()) ? agent.getIagentip() : agent.getIcomName(),
                            format.format(new Date()), agent.getIagentip());
                    }
                }

                // 判断是否发送报警平台并且保存小铃铛信息
                boolean isSendWarnMsgToTevilo = ServerEnv.getInstance().getBooleanConfig("agent.warn.message.tevilo",
                    Environment.FALSE_BOOLEAN);
                if (isSendWarnMsgToTevilo)
                {
                    addDataCollectAlarmAndSendMessage(agent);
                }
            }

            // agent守护进程状态监控开关
            boolean monitorDaemonsStateSwitch = ServerEnv.getInstance()
                    .getBooleanConfig("agent.monitor.daemons.state.switch", Environment.FALSE_BOOLEAN);

            if (monitorDaemonsStateSwitch)
            {
                int guardPort = agentMaintainAct.getAgentGuardPort();
                // 守护进程端口不为0才监控，否则不处理
                if (guardPort != 0)
                {
                    ssltype= RpcSslType.getInstance().exec(agentMaintainAct.getAgentHost(),
                            agentMaintainAct.getAgentPort());

                    if (proxySwitch)
                    {
                        PerformDataProcessService ps = new PerformDataProcessService();
                        ProxyModel pm = ps.getPerformDataJsonDataOpt(table, agentMaintainAct.getAgentHost(),
                            agentMaintainAct.getAgentPort(), agentMaintainAct.getAgentGuardPort(), sysType);
                        rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
                    } else
                    {
                        rpcClient = new AgentXmlRpcClient(agentMaintainAct.getAgentHost(),
                                agentMaintainAct.getAgentGuardPort(), ssltype);

                    }
                    monitorAgentDaemonsState(agent, table, rpcClient);
                }
            }
        }

        if (!canNotConnect.isEmpty())
        {
            List ips = new ArrayList();
            for (Agent agent : canNotConnect)
            {
                ips.add(agent.getIagentip() + ":" + agent.getIagentport());
            }
            res.put(SUCCESS_TEXT, false);
            StringBuffer sb = new StringBuffer("<div class=\"win_prompt\">");
            sb.append("<p>获取Agent信息请求已发送，但以下Agent无法连接：</p>");
            sb.append("<div class=\"pro_ip\">");
            for (int i = 0; i < ips.size(); i++)
            {
                sb.append("<span>" + ips.get(i).toString() + "</span>");
            }
            sb.append("</div></div>");
            res.put(MESSAGE_TEXT, sb.toString());
        } else
        {
            res.put(SUCCESS_TEXT, true);
            res.put(MESSAGE_TEXT, "获取Agent信息请求已经发送！");
        }
        logger.info("用户名：" + userName + ",操作：Agent管理中的获取信息操作。");

        return res;

    }

    public void monitorAgentDaemonsState ( Agent agent, Hashtable table, XmlRpcClient rpcClient )
    {
        // Agent状态监控线程的守护进程的状态检查，如果出现异常需要修改守护进程的状态并发送报警信息
        try
        {
            String opt = "hostname";// 主机的hostname
            Vector params = new Vector();
            params.addElement(opt);
            params.addElement(table);
            agent.setAgentMoitorState(Constants.REMOTE_AGENT_STATE_NORMAL);// 正常状态
            rpcClient.execute("IEAIAgent.optAgent", params);
        } catch (Exception e)
        {
            logger.error("fetchAgentInfoQuery is error！", e);
            agent.setAgentMoitorState(Constants.REMOTE_AGENT_STATE_EXCEPTION);// 出现异常修改守护进程状态

            // 判断是否发送报警平台并且保存小铃铛信息
            boolean isSendWarnMsgToTevilo = ServerEnv.getInstance().getBooleanConfig("agent.warn.message.tevilo",
                Environment.FALSE_BOOLEAN);
            if (isSendWarnMsgToTevilo)
            {
                addDataCollectAlarmAndSendMessage(agent);
            }
        }
        manager.updateAgentMonitorState(agent.getIagentip(), agent.getIagentport(), agent.getAgentMoitorState());
    }

    public void sendVcsCheck ( Agent agent, int sysType, String adpDefUuid, String chkType ) throws Exception
    {

        if ("".equals(adpDefUuid))
        {
            throw new Exception("Agent维护接口（agentmaintainadaptor）不存在");
        }

        AgentMaintainAct agentMaintainAct = manager.createAgentMaintainAct(agent, "vcs", adpDefUuid, chkType, sysType);
        String ip = agentMaintainAct.getAgentHost();
        int port = agentMaintainAct.getAgentPort();
        Hashtable table = new Hashtable();
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);

        Vector params = new Vector();
        params.addElement(agentMaintainAct.getId());
        params.addElement(agentMaintainAct.getServerHost());
        params.addElement(agentMaintainAct.getServerPort());
        table.put("flowId", String.valueOf(agentMaintainAct.getFlowId()));// agent监控不需要再监控，此处赋值1可否？
        table.put("adaptorConfig", agentMaintainAct.getAdaptorConfig());// ?默认值
        table.put("agentPort", port);
        table.put("projectName", agentMaintainAct.getProjectName());
        table.put("adaptorDefUUID", agentMaintainAct.getAdaptorDefUUID());
        table.put("_scopeId", agentMaintainAct.getScopeId());// ?
        table.put("_actStateDataVersion", agentMaintainAct.getActStateDataVersion());// ?
        table.put("timeout", "60000");// ?
        table.put("agentHost", ip);
        table.put("actId", agentMaintainAct.getActId());// 是否是固定值AgentMonitor?
        table.put("adaptorDefName", agentMaintainAct.getAdaptorDefName());// agentmaintoradaptor
        table.put("startTime", agentMaintainAct.getStartTime());// 定时任务实例启动时间
        table.put("actName", agentMaintainAct.getActName()); // 默认值
        table.put("status", agentMaintainAct.getStatus());// ? 默认值
        table.put("levelOfWeight", String.valueOf(agentMaintainAct.getLevelOfWeight()));// ? 默认值
        table.put("Id", agentMaintainAct.getId());
        table.put("flowName", agentMaintainAct.getFlowName());
        table.put("flowPoolNum", "0");// ?默认值
        table.put("serverHost", agentMaintainAct.getServerHost());
        table.put("serverPort", agentMaintainAct.getServerPort());
        table.put("isSafeFirst", agentMaintainAct.isSafeFirst()); // ?默认值
        table.put("levelOfPRI", String.valueOf(agentMaintainAct.getLevelOfPRI()));// ?默认值
        int ssltype= RpcSslType.getInstance().exec(ip, port);

        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(table, ip, port, sysType);
            ip = pm.getIp();
            port = pm.getPort();
            ssltype=pm.getSsl();
        }
        params.addElement(table);

        XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, ssltype);
        manager.createRequestWrapperWork(agentMaintainAct);

        manager.createAgentMaintainTask(agentMaintainAct, "vcs");

        rpcClient.execute("IEAIAgent.executeAct", params);

    }

    public Map getAgentUpgradeMonitorList ( int start, int limit, String istarttime, String iendtime, String iagentip,
            Integer istate ) throws Exception
    {
        return manager.getAgentUpgradeMonitorList(start, limit, istarttime, iendtime, iagentip, istate);
    }

    public Map agentBindUpgradeInfo ( Long[] agentIds, Long agentUpId, String userName )
            throws EswitchException, RepositoryException, DBException
    {
        Map res = new HashMap();

        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

        if (null != baseConn)
        { // 没有基线源，不可以
            Map orgSql = manager.organizeAgentBindUpgradeInfoSql(agentIds, agentUpId, baseConn, dbConns);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "绑定成功！");
                    // OperationService.getInstance().calladdoperRecord("Agent管理",userName,"绑定准备信息",Constants.IEAI_EMERGENCY_SWITCH);
                    logger.info("用户名：" + userName + ",操作：Agent管理中的绑定准备信息操作。");
                    return res;
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "执行绑定Agent升级信息sql时出现错误！");
                    return res;
                }
            } else
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "组织绑定Agent升级信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }
    }

    private Agent _convertToAgent ( Map<String, Object> rgMap )
    {
        Agent res = new Agent();
        Object idd = rgMap.get("iid");
        String iagentname = ((String) rgMap.get("iagentname")).trim();
        if (rgMap.get("iosbasevalue") != null && !"".equals(String.valueOf(rgMap.get("iosbasevalue")).trim()))
        {
            String iosbasevalue = ((String) rgMap.get("iosbasevalue")).trim();
            res.setIosbasevalue(iosbasevalue);
        }

        iagentname = iagentname.replace("，", ",");
        iagentname = iagentname.replaceAll("\\s*", "");
        if (idd == JSONObject.NULL || "".equals(String.valueOf(idd)))
        {
            res.setIid(AGENT_ID_NULL);
        } else
        {
            res.setIid(Long.parseLong(String.valueOf(rgMap.get("iid"))));
        }
        res.setIagentname(iagentname);
        res.setIagentdesc(((String) rgMap.get("iagentdesc")).trim());
        if (rgMap.get("ios_name") != null && !"".equals(String.valueOf(rgMap.get("ios_name")).trim()))
        {
            res.setIosName(((String) rgMap.get("ios_name")).trim());
        }
        if (rgMap.get("iagentnetid") != null)
        {
            res.setIagentnetid(((String) rgMap.get("iagentnetid")).trim());
        }
        res.setIagentip(((String) rgMap.get("iagentip")).trim());

        res.setIpalias(IPTools.getDigitalIP(((String) rgMap.get("iagentip")).trim()));//根据IP 设置 转译后的数字IPv4,IPv6

        res.setIagentport(rgMap.get("iagentport") != null && !"".equals(String.valueOf(rgMap.get("iagentport")).trim())
                ? Long.parseLong(String.valueOf(rgMap.get("iagentport")))
                : 15000);
        res.setIagentclustername(((String) rgMap.get("iagentclustername")).trim());
        res.setIenvtype("测试".equals(((String) rgMap.get("ienvtype")).trim()) ? 0 : 1);
        res.setIregion(rgMap.get("iregion") == null ? "" : String.valueOf(rgMap.get("iregion")));
        res.setIssued(rgMap.get("issued") == null ? 0 : Integer.valueOf(rgMap.get("issued").toString()));
        res.setIagentState(
            rgMap.get("iagentState") == null ? -1 : Integer.valueOf(rgMap.get("iagentState").toString()));
        Object idcid = rgMap.get("idcid");
        res.setIdcid(idcid == null || "".equals(idcid.toString()) ? -1 : Long.valueOf(idcid.toString()));

        res.setIagentwebport(
            rgMap.get("iagentwebport") != null && !"".equals(String.valueOf(rgMap.get("iagentwebport")).trim())
                    ? Long.parseLong(String.valueOf(rgMap.get("iagentwebport")))
                    : 0);
        res.setIagentguardport(
            rgMap.get("iagentguardport") != null && !"".equals(String.valueOf(rgMap.get("iagentguardport")).trim())
                    ? Long.parseLong(String.valueOf(rgMap.get("iagentguardport")))
                    : 0);
//        boolean isght= Environment.getInstance().getGhtBankSwitch();
//        if(isght) {
            res.setIsoncloud( rgMap.get("isoncloud") == null?"":String.valueOf(rgMap.get("isoncloud")));     
//        }
            res.setIbusinessip( rgMap.get("ibusinessip") == null?"":String.valueOf(rgMap.get("ibusinessip"))); 
            res.setIbusinesssys( rgMap.get("ibusinesssys") == null?"":String.valueOf(rgMap.get("ibusinesssys")));
        //ssl add by sunxf 2022.10.25
        res.setIsSSL(rgMap.get("isSSL") == null?0:Integer.valueOf(rgMap.get("isSSL").toString()));
        res.setIequipmentorvmid(  rgMap.get("iequipmentorvm") != null && !"".equals(String.valueOf(rgMap.get("iequipmentorvm")).trim())
                ? Long.parseLong(String.valueOf(rgMap.get("iequipmentorvm")))
                : null);
        if(rgMap.containsKey("iteam")){
            if (StringUtils.isNotBlank(rgMap.get("iteam").toString())){
                res.setIteam(rgMap.get("iteam").toString());
            }
        }

        try {
            if(Environment.getInstance().getBankSwitchIsFjnx()){
                res.setIenvtype( rgMap.get("ienvtype") == null? null:1);
            }
        } catch (UnknownHostException e) {
            logger.error("getBankSwitchIsFjnx() is error");
        }
        return res;
    }

    /**
     * 
     * <li>Description:Agent配置导入</li>
     * <AUTHOR>
     * 2016年8月18日 
     * @param filename
     * @param fis
     * @param type
     * @return
     * return boolean
     */
    public Map<String, Object> uploadAgent ( String filename, InputStream fis, int type, HttpServletRequest request )
    {
        List<Agent> list = new ArrayList();
        Map res = new HashMap();
        Map map = new HashMap();
        String cellNumName = null;
        String CellValueString = null;
        Row row = null;
        Cell cell = null;
        Workbook workbook = null;
        Map<String, List<String>> tmpIpMaps = new HashMap<String, List<String>>();
        Connection checkConn = null;

        String userName = SessionData.getSessionData(request).getUserName();
        long iuserid = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        try
        {
            boolean iscib = Environment.getInstance().getBankSwitchIscib();
            InfoExeclServices service = new InfoExeclServices();
            try
            {
                workbook = service.fis2POI(filename, fis);
            } catch (Exception e)
            {
                throw new EswitchException("Excel文件版本格式转换发生异常！");
            }
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getLastRowNum() + 1 - sheet.getFirstRowNum();
            //{ "序号", "名称", "地址", "端口", "守护端口", "数据中心", "AZ名称","proxyIp" };
            Map<String, Object> obmap = new HashMap<String, Object>()
            {
                {
                    put(AGENT_UPLOAD_DATA[1], 0);
                    put(AGENT_UPLOAD_DATA[2], 0);
                    put(AGENT_UPLOAD_DATA[3], 0);
                    put(AGENT_UPLOAD_DATA[4], 0);
//                    put(AGENT_UPLOAD_DATA[5], 0);
                    boolean isght= Environment.getInstance().getGhtBankSwitch();
                    boolean ispf= Environment.getInstance().getPfBankSwitch();
                    boolean lnbankSwitch = Environment.getInstance().getBooleanConfig("ln.bank.of.china.switch", false);
                    if(isght||ispf) {
                        put(AGENT_UPLOAD_DATA[7],0 );
                    }
                    if (lnbankSwitch){
                        put(AGENT_UPLOAD_DATA[8],0 );
                        put(AGENT_UPLOAD_DATA[9],0 );
                        put(AGENT_UPLOAD_DATA[10],0 );
                    }
                    put(AGENT_UPLOAD_DATA[11], 0);
                }
            };
            // 检查Excel格式长度
            row = sheet.getRow(0);
            // 格式通过后进行数据导入
            for (int k = 0; k <= rows; k++)
            {
                Agent agent = new Agent();
                agent.setIid(AGENT_ID_NULL);
                row = sheet.getRow(k);
                if (row != null && !isRowEmpty(row))
                {
                    int colum = row.getLastCellNum();
                    for (int j = 0; j < colum; j++)
                    {
                        cell = row.getCell(j);
                        if (k == 0)
                        {
                            if (cell != null)
                            {
                                map.put(j, getStringCellValue(cell));
                                if (obmap.containsKey(getStringCellValue(cell).trim()))
                                {
                                    obmap.put(getStringCellValue(cell).trim(), 1);
                                }
                            }
                        } else
                        {
                            StringBuilder nullValue = new StringBuilder();
                            for (int m = 0; m < AGENT_UPLOAD_DATA.length; m++)
                            {
                                String value = "";
                                if (obmap.containsKey(AGENT_UPLOAD_DATA[m]))
                                {
                                    value = obmap.get(AGENT_UPLOAD_DATA[m]).toString();
                                }
                                if ("0".equals(value))
                                {
                                    if ("".equals(nullValue))
                                    {
                                        nullValue.append(AGENT_UPLOAD_DATA[m]);
                                    } else
                                    {
                                        nullValue.append(",").append(AGENT_UPLOAD_DATA[m]);
                                    }
                                }
                            }
                            if (!"".equals(nullValue.toString()))
                            {
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, "excel中不存在列名为：" + nullValue + "的列，请查看后再操作！");
                                return res;
                            }
                            getStringCellValue(cell);
                            cellNumName = (String) map.get(j);
                            CellValueString = getStringCellValue(cell);
                            int colNum = j + 1;
                            int rowNum = k + 1;
                            if (AGENT_UPLOAD_DATA[0].equals(cellNumName))
                            {
                                if ("".equals(CellValueString))
                                {
                                    break;
                                }
                            }
                            if (AGENT_UPLOAD_DATA[1].equals(cellNumName))
                            {
                                if (iscib)
                                {
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        int count = AgentMaintainManager.getInstance().checkAgentName(CellValueString,
                                            type);
                                        if (count < 1)
                                        {
                                            agent.setIagentname("");
                                        } else
                                        {
                                            agent.setIagentname(CellValueString);
                                        }
                                    } else
                                    {
                                        agent.setIagentname("");
                                    }
                                } else
                                {
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        agent.setIagentname(CellValueString);
                                    } else
                                    {
                                        throwException(cellNumName, colNum, rowNum);
                                    }
                                }
                            }
                            if (AGENT_UPLOAD_DATA[2].equals(cellNumName))
                            {
                                if (PersonalityEnv.isAgentDnsSwitchValue())
                                {
                                    boolean isIP = isIP(CellValueString);
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        if (isIP)
                                        {
                                            agent.setIagentip(CellValueString);
                                            agent.setIpalias(IPTools.getDigitalIP(CellValueString));//根据IP 设置 转译后的数字IPv4,IPv6
                                        } else
                                        {
                                            throwCheckIPException(CellValueString, colNum, rowNum);
                                        }
                                    }
                                } else
                                {
                                    boolean isDNS = isDNS(CellValueString);
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        if (!isDNS)
                                        {
                                            agent.setIagentip(CellValueString);
                                            agent.setIpalias(IPTools.getDigitalIP(CellValueString));//根据IP 设置 转译后的数字IPv4,IPv6
                                        } else
                                        {
                                            throwCheckDNSException(CellValueString, colNum, rowNum);
                                        }
                                    } else
                                    {
                                        throwException(cellNumName, colNum, rowNum);
                                    }
                                }
                            }
                            if (AGENT_UPLOAD_DATA[3].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    int portVal = new Double(CellValueString).intValue();
                                    if (0 < portVal && portVal < 65535)
                                    {
                                        agent.setIagentport(Long.parseLong(String.valueOf(portVal)));
                                    } else
                                    {
                                        throwPortException(colNum, rowNum);
                                    }
                                } else
                                {
                                    throwException(cellNumName, colNum, rowNum);
                                }
                            }
                            if (AGENT_UPLOAD_DATA[4].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    int portVal = new Double(CellValueString).intValue();
                                    if (0 <= portVal && portVal < 65535)
                                    {
                                        agent.setIagentguardport(Long.parseLong(String.valueOf(portVal)));
                                    } else
                                    {
                                        throwPortException(colNum, rowNum);
                                    }
                                } else
                                {
                                    throwException(cellNumName, colNum, rowNum);
                                }
                            }
                            /*if (AGENT_UPLOAD_DATA[5].equals(cellNumName))
                            {
                                if (isCellValueString(CellValueString))
                                {
                                    agent.setIdcid(getDataCenterByName(CellValueString));
                                }
                            }*/
                            if (AGENT_UPLOAD_DATA[5].equals(cellNumName))
                            {
                                if (isCellValueString(CellValueString))
                                {
                                    agent.setIagentazname(CellValueString);
                                }
                            }
                            if (AGENT_UPLOAD_DATA[6].equals(cellNumName))
                            {
                                if (isCellValueString(CellValueString))
                                {
                                    agent.setIagentnetid(CellValueString);
                                }
                            }if (AGENT_UPLOAD_DATA[7].equals(cellNumName))
                            {
                                if(StringUtils.isNotBlank(CellValueString)){
                                    if("是".equals(CellValueString)){
                                        agent.setIsoncloud("yes");
                                    }else if("否".equals(CellValueString)){
                                        agent.setIsoncloud("no");
                                    }else{
                                        throwCommonException(colNum, rowNum,"列中数据格式错误,请确认为是或否！");
                                    }
                                }
                            }
                            if (AGENT_UPLOAD_DATA[8].equals(cellNumName))
                            {
                                agent.setIteam(CellValueString==null?"":CellValueString);
                            }
                            if (AGENT_UPLOAD_DATA[9].equals(cellNumName))
                            {
                                agent.setIbusinesssys(CellValueString==null?"":CellValueString);
                            }
                            if (AGENT_UPLOAD_DATA[10].equals(cellNumName))
                            {
                                agent.setIbusinessip(CellValueString==null?"":CellValueString);
                            }
                            if (AGENT_UPLOAD_DATA[11].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    if("是".equals(CellValueString)){
                                        agent.setIsSSL(1);
                                    }else if("否".equals(CellValueString)){
                                        agent.setIsSSL(0);
                                    } else
                                    {
                                        throwCommonException(colNum, rowNum,"列中数据格式错误,请确认为是或否！");
                                    }
                                } else
                                {
                                    throwException(cellNumName, colNum, rowNum);
                                }
                            }
                            agent.setIagentState(-1);
                        }
                    }
                    if (k != 0)
                    {
                        if ("序号".equals(cellNumName) && "".equals(CellValueString))
                        {
                        } else
                        {
                            // 判断azName是否为空，如果为空 则到表里进行查询
                            String azName = agent.getIagentazname();
                            if (!isCellValueString(azName))
                            {
                                azName = manager.getAznameforPf(agent.getIagentip(), type);
                            }
                            agent.setIagentazname(azName);
                            list.add(agent);
                        }
                    }
                }
            }
            try
            {
                checkConn = DBResource.getConnection(UPLOADAGENT, logger, type);
                List listSim = new ArrayList();
                for (int i = 0; i < list.size(); i++)
                {
                    Agent agents = list.get(i);
                    checkAgent(listSim, agents);

                    if (manager.isAgentIpExistsAndUpdateClusterId(agents, checkConn, type))
                    {
                        boolean importAgentToComputer = Environment.getInstance()
                                .getBooleanConfig("import.agent.to.computer", false);
                        if (importAgentToComputer)
                        {
                            Map mapAgent = AgentMaintainManager.getInstance().getAgentInfoByIP(agents.getIagentip(),
                                agents.getIagentport(), type, checkConn);
                            String iagentinfoid = mapAgent.get(IAGENTINFO_ID) == null ? ""
                                    : (String) mapAgent.get(IAGENTINFO_ID);
                            agents.setIid("".equals(iagentinfoid) ? -1 : Long.valueOf(iagentinfoid));
                            updateComputerAgent(agents);
                        }
                        /**
                         * 是否更新Agent开关
                         */
                        boolean ifupdataAgent = Environment.getInstance().getBooleanConfig("import.agent.if.update",
                            false);
                        if (ifupdataAgent)
                        {
                            Map mapAgent = AgentMaintainManager.getInstance().getAgentInfoByIP(agents.getIagentip(),
                                agents.getIagentport(), type, checkConn);
                            String iagentinfoid = mapAgent.get(IAGENTINFO_ID) == null ? "-1"
                                    : (String) mapAgent.get(IAGENTINFO_ID);
                            agents.setIcreateuser(iuserid);
                            agents.setIid(Long.valueOf(iagentinfoid));
                            if (iscib && "".equals(agents.getIagentname()))
                            {
                                String ioldAgentName = AgentMaintainManager.getInstance()
                                        .getOldAgentName(Long.valueOf(iagentinfoid), checkConn);
                                agents.setIagentname(ioldAgentName);
                            }
                            listSim.add(agents);

                        }
                        continue;
                    }
                    // 增加纳管用户
                    agents.setIcreateuser(iuserid);
                    listSim.add(agents);

                }
                // 关闭检查Agent 是否存在IP连接
                if (checkConn != null)
                {
                    DBResource.closeConnection(checkConn, UPLOADAGENT, logger);
                }

                Map<String, Object> connInfo = Tools.getConnectionInfo();
                Connection baseConn = (Connection) connInfo.get("baseConn");
                List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

                if (null != baseConn)
                { // 没有基线源，不可以
                    Map orgSql = manager.organizeAgentMaitainInfoSql(listSim, baseConn, dbConns);
                    if ((Boolean) orgSql.get(SUCCESS_TEXT))
                    {
                        if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                            (List<String>) orgSql.get("rollbackSqls")))
                        {
                            DBResource.closeConnection(baseConn, UPLOADAGENT, logger);
                            res.put(SUCCESS_TEXT, true);
                            res.put(MESSAGE_TEXT, "数据保存成功！");
                            // OperationService.getInstance().calladdoperRecord("Agent管理",userName,"保存",Constants.IEAI_EMERGENCY_SWITCH);
                            logger.info("用户名:" + userName + "操作:Agent管理保存操作。");
                            return res;
                        } else
                        {
                            DBResource.closeConnection(baseConn, UPLOADAGENT, logger);
                            res.put(SUCCESS_TEXT, false);
                            res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                            return res;
                        }
                    } else
                    {
                        // 执行有异常关闭连接
                        DBResource.closeConnection(baseConn, UPLOADAGENT, logger);
                        for (Connection conn : dbConns)
                        {
                            DBResource.closeConnection(conn, UPLOADAGENT, logger);
                        }
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                        return res;
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                    return res;
                }
            } catch (Exception e)
            {
                String mes = "插入数据库发生异常！";
                throw new EswitchException(mes);

            }
        } catch (Exception ee)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, ee.getMessage());
        }
        try
        {
            fis.close();
        } catch (Exception e)
        {
            logger.error(UPLOADAGENT, e);
        }
        return res;
    }

    /**
     * Description:excel列校验不符合条件抛出异常提示，提示动态传入
     * param: [colNum, rowNum, msg]
     * return void
     * <AUTHOR>
     * 2024/1/23 14:41
     * @Reviser:修改人
     * @ReviseDate:修改时间
     * @Revision:修改内容
     **/
    public void throwCommonException ( int colNum, int rowNum, String msg ) throws EswitchException
    {
        String mes = "在" + rowNum + "行," + colNum + msg;
        throw new EswitchException(mes);
    }

    public Map updateComputerAgent ( Agent agent ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeUpdateComputerAgentSql(agent, baseConn);
            if ((Boolean) orgSql.get("success"))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {
                    DBResource.closeConnection(baseConn, UPDATECOMPUTERAGENT, logger);
                    res.put("success", true);
                    res.put("message", "数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, UPDATECOMPUTERAGENT, logger);
                    res.put("success", false);
                    res.put("message", ERRORMESSAGEONE);
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, UPDATECOMPUTERAGENT, logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, UPDATECOMPUTERAGENT, logger);
                }
                res.put("success", false);
                res.put("message", ERRORMESSAGE);
                return res;
            }
        } else
        {
            res.put("success", false);
            res.put("message", "没有基线源, 无法保存！");
            return res;
        }
    }

    public Map organizeUpdateComputerAgentSql ( Agent agent, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<>();
        List<String> rollbackSqls = new ArrayList<>();
        try
        {
            Map resultMap = AgentMaintainManager.getInstance().getComputerByIP(agent.getIagentip(), baseConn);
            if (resultMap.size() > 0)
            {
                rollbackSqls.add("update IEAI_COMPUTER_LIST set IAGENTINFO_ID=" + resultMap.get(IAGENTINFO_ID)
                        + " where CPID=" + resultMap.get("CPID"));
                exeSqls.add("update IEAI_COMPUTER_LIST set IAGENTINFO_ID=" + agent.getIid() + " where CPID="
                        + resultMap.get("CPID"));
            } else
            {
                long iid = IdGenerator.createId("ieai_computer_list", baseConn);
                rollbackSqls.add("delete from IEAI_COMPUTER_LIST where cpid = " + iid);
                exeSqls.add(
                    "insert into IEAI_COMPUTER_LIST(CPID,IP,CPNAME,IAGENTINFO_ID,CSWITCH,cpport,cpos,ictid,cmid,cputhreshold,memorythreshold,iowaitthreshold,wswitch,agentip,ipalias,createtime) values("
                            + iid + ",'" + agent.getIagentip() + "','" + agent.getIagentip() + "'," + agent.getIid()
                            + "," + 0 + "," + -1 + "," + 0 + "," + -1 + "," + -1 + "," + -1 + "," + -1 + "," + -1 + ","
                            + 0 + ",'" + agent.getIagentip() + "',"
                            + AgentMaintainManager.getAlias(agent.getIagentip()).longValue()
                            + ",FUN_GET_DATE_NUMBER_NEW(" + Constants.getCurrentSysDate() + ",8))");
            }
        } catch (RepositoryException e)
        {
            logger.error("organizeUpdateComputerAgentSql error", e);
            isSuccess = false;
        } catch (Exception e)
        {
            logger.error("organizeUpdateComputerAgentSql error", e);
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    public void checkAgent ( List list, Agent agent )
    {
        if (list != null && !list.isEmpty())
        {
            for (int i = 0; i < list.size(); i++)
            {
                Agent agentOne = (Agent) list.get(i);
                if (agentOne.getIagentip().equals(agent.getIagentip())
                        && agentOne.getIagentport().equals(agent.getIagentport()))
                {
                    list.remove(agentOne);
                    return;
                }
            }
        }
    }

    private long getDataCenterByName ( String centerName )
    {
        long dataCenterId = -1l;
        Connection conn = null;
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            conn = DBResource.getJDBCConnection("getDataCenterByName", logger, sysType);
            dataCenterId = AgentMaintainManager.getInstance().getDataCenterByName(conn, centerName);
        } catch (RepositoryException e)
        {
            logger.error("getDataCenterByName is error ", e);
        } finally
        {
            DBResource.closeConnection(conn, "getDataCenterByName", logger);
        }
        return dataCenterId;
    }

    public boolean isCellValueString ( String str )
    {
        return str != null && !"".equals(str);
    }

    private boolean isRowEmpty ( Row row )
    {

        if (row != null)
        {
            for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++)
            {
                Cell cell = row.getCell(i);
                if (null != cell && cell.getCellType() != CellType.BLANK)
                {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 
     * <li>Description:(获取单元格数据内容为字符串类型的数据)</li> 
     * <AUTHOR>
     * 2016年8月18日 
     * @param cell
     * @return
     * return String
     */
    private String getStringCellValue ( Cell cell )
    {
        String strCell = "";
        if (cell != null)
        {
            switch (cell.getCellType())
            {
                case FORMULA:
                    try
                    {
                        strCell = String.valueOf(cell.getNumericCellValue());
                    } catch (IllegalStateException e)
                    {
                        try
                        {
                            strCell = String.valueOf(cell.getStringCellValue());
                        } catch (IllegalStateException ee)
                        {

                            try
                            {
                                strCell = String.valueOf(cell.getCellFormula());
                            } catch (IllegalStateException eee)
                            {
                                strCell = "";
                            }
                        }

                    }
                    break;
                case STRING:
                    strCell = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    strCell = String.valueOf(new Double(cell.getNumericCellValue()).intValue());
                    break;
                case BOOLEAN:
                    strCell = String.valueOf(cell.getBooleanCellValue());
                    break;
                case BLANK:
                    strCell = "";
                    break;
                default:
                    strCell = cell.getStringCellValue() == null ? "" : cell.getStringCellValue();
                    break;
            }
        }
        if (strCell.equals("") || strCell == null || cell == null)
        {
            return "";
        }
        return strCell.trim();
    }

    /**
     * 
     * <li>Description:IP格式检查</li> 
     * <AUTHOR>
     * 2016年8月24日 
     * @param addr
     * @return
     * return boolean
     */
    public boolean isIP ( String addr )
    {
        if (addr.length() < 7 || addr.length() > 15 || "".equals(addr))
        {
            return false;
        }
        String rexp = "\\b((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\b";
        Pattern pat = Pattern.compile(rexp);
        Matcher mat = pat.matcher(addr);
        return mat.find();

    }

    public boolean isDNS ( String addr )
    {
        boolean flag = addr.length() > 125;
        return flag;

    }

    /**
     * 
     * <li>Description:检查IP格式异常抛出</li> 
     * <AUTHOR>
     * 2016年8月24日 
     * @param addr
     * @param colNum
     * @param rowNum
     * @throws Exception
     * return void
     */
    public void throwCheckIPException ( String addr, int colNum, int rowNum ) throws Exception
    {
        String mes = "在" + rowNum + "行," + colNum + "列中地址IP" + "“" + addr + "”" + "的格式错误,请确认后再执行！";
        throw new Exception(mes);
    }

    public void throwCheckDNSException ( String addr, int colNum, int rowNum ) throws Exception
    {
        String mes = "在" + rowNum + "行," + colNum + "列中地址" + "“" + addr + "”" + " 不能超过250个字节,请确认后再执行！";
        throw new Exception(mes);
    }

    /**
     * 
     * <li>Description:检查数据为空值时异常抛出</li> 
     * <AUTHOR>
     * 2016年8月24日 
     * @param data
     * @param colNum
     * @param rowNum
     * @throws Exception
     * return void
     */
    public void throwException ( String data, int colNum, int rowNum ) throws EswitchException
    {
        String mes = "在" + rowNum + "行," + colNum + "列中" + "“" + data + "”" + "的数据为空值,请确认后再执行！";
        throw new EswitchException(mes);
    }

    /**
     * 
     * <li>Description:兴业银行检查agent名称是否与系统名称匹配</li> 
     * <AUTHOR>
     * 2020年8月19日
     * @param data
     * @param colNum
     * @param rowNum
     * @throws Exception
     * return void
     */
    public void throwAgentNameExcep ( String data, int colNum, int rowNum ) throws EswitchException
    {
        String mes = "在" + rowNum + "行," + colNum + "列中" + "“" + data + "”" + "的系统名称不存在,请确认后再执行！";
        throw new EswitchException(mes);
    }

    /**
     * 
     * <li>Description:端口号格式检查</li> 
     * <AUTHOR>
     * 2016年8月24日 
     * @param colNum
     * @param rowNum
     * @throws Exception
     * return void
     */
    public void throwPortException ( int colNum, int rowNum ) throws EswitchException
    {
        String mes = "在" + rowNum + "行," + colNum + "列中端口号格式错误,请确认后再执行！";
        throw new EswitchException(mes);
    }

    public void downloadComputerInfoTemplate ( HttpServletResponse response, HttpServletRequest request ,String flag)
    {
        OutputStream out = null;
        try
        {
            String fileName = "Agent信息导入模板.xls";
            if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
                fileName = "Agent信息导入模板.et";
            }
            HSSFWorkbook wb = new HSSFWorkbook();
            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            response.addHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            HSSFSheet sheet = wb.createSheet("Agent信息表");

//            HSSFRow row = sheet.createRow(0);
            int rowIndex = 0;
            HSSFRow row = sheet.createRow(rowIndex++);

            sheet.setDefaultColumnWidth(20);

            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
//            sheet.createRow(5);
//            sheet.createRow(6);
//            sheet.createRow(7);
//            sheet.createRow(rowIndex++);//启动用户也删除掉
//            sheet.createRow(9);
//            sheet.createRow(10);
//            sheet.createRow(11);
//            sheet.createRow(12);
//            sheet.createRow(13);
            sheet.createRow(rowIndex++);
//            sheet.createRow(15);
            sheet.createRow(rowIndex++);

            boolean isght= Environment.getInstance().getGhtBankSwitch();
            boolean ispf= Environment.getInstance().getPfBankSwitch();
            boolean isFJNX= Environment.getInstance().getBankSwitchIsFjnx();
            if(isght||ispf) {
                sheet.createRow(rowIndex++);
            }
            sheet.createRow(rowIndex++);

            HSSFCellStyle style = wb.createCellStyle();

            int cellIndex = 0;
            HSSFCell cell = row.createCell(cellIndex++);
            if(isFJNX){
                cell.setCellValue("序号");
                cell.setCellStyle(style);

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("业务系统");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("IP地址");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("端口");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("用途");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("开启SSL");
            }else
            {
                cell.setCellValue("序号");
                cell.setCellStyle(style);

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("名称");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("地址");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("端口");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("守护端口");

//            cell = row.createCell(5);
//            cell.setCellStyle(style);
//            cell.setCellValue("数据中心");

//            cell = row.createCell(6);
//            cell.setCellStyle(style);
//            cell.setCellValue("操作系统");

//            cell = row.createCell(7);
//            cell.setCellStyle(style);
//            cell.setCellValue("计算机名");

//            cell = row.createCell(cellIndex++);
//            cell.setCellStyle(style);
//            cell.setCellValue("启动用户");

//            cell = row.createCell(9);
//            cell.setCellStyle(style);
//            cell.setCellValue("版本");

//            cell = row.createCell(10);
//            cell.setCellStyle(style);
//            cell.setCellValue("活动数量");

//            cell = row.createCell(11);
//            cell.setCellStyle(style);
//            cell.setCellValue("状态");

//            cell = row.createCell(12);
//            cell.setCellStyle(style);
//            cell.setCellValue("纳管用户");

//            cell = row.createCell(13);
//            cell.setCellStyle(style);
//            cell.setCellValue("纳管时间");

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("AZ名称");

//            cell = row.createCell(15);
//            cell.setCellStyle(style);
//            cell.setCellValue("NetId");

                if(isght||ispf) {
                    cell = row.createCell(cellIndex++);
                    cell.setCellStyle(style);
                    cell.setCellValue("是否云上设备");
                }

                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("开启SSL");
            }

            out = response.getOutputStream();
            wb.write(out);

        } catch (Exception e)
        {
            logger.error("exportComputerInfo", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportComputerInfo", e);
                }
            }
        }
    }

    public List exportComputerInfo (HttpServletResponse response, HttpServletRequest request, String id, int type, AgentInfoModel agent ,String flag)
    {
        OutputStream out = null;
        List list = new ArrayList();
        try
        {
            String fileName = null;
            Workbook wb = null;
            list = AgentMaintainManager.getInstance().queryComputerInfo(id, type,agent);
            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);

            if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
                fileName = dateString + "_设备信息表.et";
                wb = new HSSFWorkbook();
            }else{
                fileName = dateString + "_设备信息表.xlsx";
                wb = new SXSSFWorkbook();
            }
            response.addHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            Sheet sheet = wb.createSheet("Agent信息表");

            Row row = sheet.createRow(0);

            sheet.setDefaultColumnWidth(20);

            int rowIndex = 1;// 这里是从 1 开始
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            boolean isght= Environment.getInstance().getGhtBankSwitch();
            boolean ispf= Environment.getInstance().getPfBankSwitch();
            if(isght||ispf) {
                sheet.createRow(rowIndex++);
            }
            boolean lnbankSwitch = Environment.getInstance().getBooleanConfig("ln.bank.of.china.switch", false);
            if(lnbankSwitch) {
                sheet.createRow(rowIndex++);
                sheet.createRow(rowIndex++);
                sheet.createRow(rowIndex++);
            }
            //Agent管理增加字段业务系统  业务系统：新增采集业务系统字段，字段添加到端口号后面，数据来源于节点绑定页面（系统名称）。导出功能增加此字段，导入不需要增加。此功能为光大特有功能，通过开关控制 jiaMing
            boolean disName = Environment.getInstance().getBooleanConfigNew2("agent.manager.displaySystemName",false);
            if(disName){
                sheet.createRow(rowIndex++);
            }
            sheet.createRow(rowIndex++);// 增加 开启SSL 列


            CellStyle style = wb.createCellStyle();

            int cellIndex = 0;

            Cell cell = row.createCell(cellIndex++);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("名称");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("地址");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("端口");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("守护端口");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("数据中心");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("操作系统");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("计算机名");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("启动用户");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("版本");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("活动数量");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("状态");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("纳管用户");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("纳管时间");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("AZ名称");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("NetId");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("proxyName");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("服务名称");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("设备或者虚拟机");
            if(isght||ispf) {
                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("是否云上设备");   
            }
            if(lnbankSwitch){
                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("所属团队");
                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("业务系统");
                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("业务地址");
            }
            if(disName){
                cell = row.createCell(cellIndex++);
                cell.setCellStyle(style);
                cell.setCellValue("采集系统名称");
            }

            cell = row.createCell(cellIndex++);// 增加 开启SSL 列
            cell.setCellStyle(style);
            cell.setCellValue("开启SSL");

            if (list != null)
            {
                int createCellIndex = 0;
                for (int i = 0; i < list.size(); i++)
                {
                    Row row1 = sheet.createRow(i + 1);
                    Agent model = (Agent) list.get(i);
                    row1.createCell(createCellIndex++).setCellValue(i + 1);
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentname());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentip());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentport());
                    row1.createCell(createCellIndex++)
                            .setCellValue(model.getIagentguardport() == null ? 0l : model.getIagentguardport());
                    //row1.createCell(createCellIndex++).setCellValue(getDataCenterById(model.getIdcid()));
                    row1.createCell(createCellIndex++).setCellValue(model.getCentername());
                    row1.createCell(createCellIndex++).setCellValue(model.getIosName());
                    row1.createCell(createCellIndex++).setCellValue(model.getIcomName());
                    row1.createCell(createCellIndex++).setCellValue(model.getIstartUser());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentVersion());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentActivityNum());
                    createCellIndex++;// 这里 11 是空的.
                    row1.createCell(createCellIndex++).setCellValue(model.getIcreateusername());
                    row1.createCell(createCellIndex++).setCellValue(model.getIcreatetime());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentazname());
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentnetid());
                    row1.createCell(createCellIndex++).setCellValue(model.getProxyName());
                    row1.createCell(createCellIndex++).setCellValue(model.getServerName());
                    if(1 == model.getIequipmentorvmid()){
                        row1.createCell(createCellIndex++).setCellValue("物理机");
                    }else if(2 == model.getIequipmentorvmid()){
                        row1.createCell(createCellIndex++).setCellValue("虚拟机");
                    }else if(3 == model.getIequipmentorvmid()){
                        row1.createCell(createCellIndex++).setCellValue("容器");
                    }else {
                        row1.createCell(createCellIndex++).setCellValue("");
                    }
                    if(isght||ispf) {
                    row1.createCell(createCellIndex++).setCellValue(model.getIsoncloud());
                    }
                    if(lnbankSwitch){
                        row1.createCell(createCellIndex++).setCellValue(model.getIteam());
                        row1.createCell(createCellIndex++).setCellValue(model.getIbusinesssys());
                        row1.createCell(createCellIndex++).setCellValue(model.getIbusinessip());
                    }
                    if(disName){
                        row1.createCell(createCellIndex++).setCellValue(model.getSystemName());
                    }
                    // 增加 开启SSL 列
                    if(0 == model.getIsSSL()){
                        row1.createCell(createCellIndex++).setCellValue("否");
                    }else if(1 == model.getIsSSL()){
                        row1.createCell(createCellIndex++).setCellValue("是");
                    }else {
                        row1.createCell(createCellIndex++).setCellValue("");
                    }

                    String cstate = "";
//                    if (0 == model.getIagentState())
//                    {
//                        cstate = "正常";
//                    } else if (Constants.REMOTE_AGENT_STATE_PAUSE == model.getIagentState())
//                    {
//                        if (ServerEnv.getInstance().getAgentPauseRecoverSwitch())
//                        {
//                            cstate = "暂停";
//                        } else
//                        {
//                            cstate = "异常";
//                        }
//                    } else if (Constants.REMOTE_AGENT_STATE_UPGRADING == model.getIagentState()){
//                        cstate = "升级中";
//                    } else if (Constants.REMOTE_AGENT_STATE_EXCEPTION == model.getIagentState()){
//                        cstate = "异常";
//                    }else if(Constants.REMOTE_AGENT_STATE_OUTTIME == model.getIagentState()){
//                        cstate = "未知";
//                    }else
//                    {
//                        cstate = "新建";
//                    }
                    if ("0".equals(model.getState()))
                    {
                        cstate = "正常";
                    } else if ("4".equals(model.getState()))
                    {
                        if (ServerEnv.getInstance().getAgentPauseRecoverSwitch())
                        {
                            cstate = "暂停";
                        } else
                        {
                            cstate = "异常";
                        }
                    } else if ("2".equals(model.getState())){
                        cstate = "升级中";
                    } else if ("1".equals(model.getState())){
                        cstate = "异常";
                    }else if("3".equals(model.getState())){
                        //cstate = "未知";
                        cstate = "异常";
                    }else
                    {
                        cstate = "新建";
                    }
                    row1.createCell(11).setCellValue(cstate);
                    createCellIndex=0;
                }
            }

            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e)
        {
            logger.error("exportComputerInfo", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportComputerInfo", e);
                }
            }
        }
        return list;
    }

    /**
     * 福建农信Agent导出
     * @param response response
     * @param request request
     * @param id  id
     * @param type type
     * @param agent agent
     * @return List
     */
    public List exportFjnxComputerInfo (HttpServletResponse response, HttpServletRequest request, String id, int type, AgentInfoModel agent )
    {
        OutputStream out = null;
        List list = new ArrayList();
        try
        {
            String fileName = null;
            long userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
            list = AgentMaintainManager.getInstance().queryFJNXComputerInfo(id, type,agent,userId);
            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);

            Workbook wb = new SXSSFWorkbook();
            fileName = dateString + "_设备信息表.xlsx";

            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            Sheet sheet = wb.createSheet("Agent信息表");

            Row row = sheet.createRow(0);

            sheet.setDefaultColumnWidth(20);

            int rowIndex = 1;// 这里是从 1 开始
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);
            sheet.createRow(rowIndex++);

            CellStyle style = wb.createCellStyle();

            int cellIndex = 0;


            Cell cell = row.createCell(cellIndex++);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("业务系统");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("IP地址");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("端口");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("用途");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("操作系统");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("计算机名");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("Agent版本");

            cell = row.createCell(cellIndex++);// 增加 开启SSL 列
            cell.setCellStyle(style);
            cell.setCellValue("开启SSL");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("proxyName");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("纳管用户");

            cell = row.createCell(cellIndex++);
            cell.setCellStyle(style);
            cell.setCellValue("纳管时间");

            if (list != null)
            {
                int createCellIndex = 0;
                for (int i = 0; i < list.size(); i++)
                {
                    Row row1 = sheet.createRow(i + 1);
                    Agent model = (Agent) list.get(i);
                    row1.createCell(createCellIndex++).setCellValue(i + 1);
                    //业务系统
                    row1.createCell(createCellIndex++).setCellValue(model.getIbusinesssys());
                    //Agent IP地址
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentip());
                    //端口
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentport());
                    //用途
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentdesc());
                    //操作系统
                    row1.createCell(createCellIndex++).setCellValue(model.getIosName());
                    //计算机名
                    row1.createCell(createCellIndex++).setCellValue(model.getIcomName());
                    //Agent版本
                    row1.createCell(createCellIndex++).setCellValue(model.getIagentVersion());
                    //开启SSL
                    if(0 == model.getIsSSL()){
                        row1.createCell(createCellIndex++).setCellValue("否");
                    }else if(1 == model.getIsSSL()){
                        row1.createCell(createCellIndex++).setCellValue("是");
                    }else {
                        row1.createCell(createCellIndex++).setCellValue("");
                    }
                    //proxyName
                    row1.createCell(createCellIndex++).setCellValue(model.getProxyName());
                    //纳管用户
                    row1.createCell(createCellIndex++).setCellValue(model.getIcreateusername());
                    //纳管时间
                    row1.createCell(createCellIndex++).setCellValue(model.getIcreatetime());

                    createCellIndex=0;
                }
            }
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e)
        {
            logger.error("exportFjnxComputerInfo", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportFjnxComputerInfo", e);
                }
            }
        }
        return list;
    }

    private String getDataCenterById ( long iid )
    {
        String dataCenterName = "";
        Connection conn = null;
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            conn = DBResource.getJDBCConnection("getDataCenterById", logger, sysType);
            dataCenterName = AgentMaintainManager.getInstance().getDataCenterById(conn, iid);
        } catch (RepositoryException e)
        {
            logger.error("getDataCenterById is error ", e);
        } finally
        {
            DBResource.closeConnection(conn, "getDataCenterById", logger);
        }
        return dataCenterName;
    }

    public String selectAgentInfo ( String iid, int sysType )
    {
        return AgentMaintainManager.getInstance().selectAgentInfo(iid, sysType);
    }

    /**
     * 
     * <li>Description:启停Agent</li> 
     * <AUTHOR>
     * 2017年12月7日 
     * @param agentId
     * @param sysType
     * @return
     * return Map
     */
    public Map optAgentMsg ( long agentId, String agentOpt, int sysType, String userName,String ioperationuser,String ioperationpassword )
    {
        Map map = new HashMap();
        if ("start".equals(agentOpt))
        {
            map = startAgentInfo(agentId, sysType, userName,ioperationuser,ioperationpassword);
        } else if ("stop".equals(agentOpt))
        {
            map = stopAgentInfo(agentId, sysType, userName,ioperationuser,ioperationpassword);
        }
        return map;
    }

    /**
     * 
     * <li>Description:启动agent</li> 
     * <AUTHOR>
     * 2017年12月4日 
     * @param agentId
     * @param sysType
     * return void
     */
    public Map startAgentInfo ( long agentId, int sysType, String userName,String ioperationuser,String ioperationpassword )
    {
        Map res = new HashMap();
        XmlRpcClient _rpcClient = null;
        String agentServiceName = "EntegorAgentServer";
        List<AgentOpModel> list = new ArrayList<AgentOpModel>();
        boolean guardflag = false;
        try
        {
            guardflag = ServerEnv.getInstance().getBooleanConfig("is.guard.opt", false);

            boolean flag = AgentMaintainManager.getInstance().checkAgentCfg(agentId, sysType);

            if (guardflag)
            {
                list = AgentMaintainManager.getInstance().getAgentInfoOper(agentId, sysType);
                String startagentcmd = ParameterConfigManager.getInstance().getParamInfo("startagentcmd");
                String startagentcmdWin = ParameterConfigManager.getInstance().getParamInfo("startagentcmdWin");
                String startcmd = null;
                for (int i = 0; i < list.size(); i++)
                {
                    AgentOpModel aom = new AgentOpModel();
                    aom = list.get(i);
                    String agentip = aom.getiAgentIp();
                    int agendamontport = Integer.parseInt(String.valueOf(aom.getiConnPort()));
                    logger.info("agendamontport======================"+agendamontport);
                    int agentport = Integer.parseInt(String.valueOf(aom.getiAgentPort()));
                    if ((aom.getiAgentOs().toLowerCase().indexOf("windows") > -1))
                    {
                        startcmd = startagentcmdWin == null || "".equals(startagentcmdWin) || "null".equals(startagentcmd)
                                ? " net start " + agentServiceName
                                : startagentcmdWin;
                    } else
                    {
                        startcmd = startagentcmd == null || "".equals(startagentcmd) || "null".equals(startagentcmd)
                                ? " nohup ./Agent_Server >nohup.out 2>&1 & "
                                : startagentcmd;
                    }

                    optAgent(agentip, agentport, agendamontport, sysType, startcmd);
                    Thread.sleep(2000);
                    res = connAgent(userName, agentip, agentport, sysType);
                }
            } else
            {
                if (flag)
                {
                    list = AgentMaintainManager.getInstance().getAgentInfoOp(agentId, sysType);
                    for (int i = 0; i < list.size(); i++)
                    {
                        AgentOpModel aom = new AgentOpModel();
                        aom = list.get(i);
                        if(StringUtils.isNotBlank(ioperationuser)){
                            aom.setiAgentUser(ioperationuser);
                        }
                        if(StringUtils.isNotBlank(ioperationpassword)){
                            aom.setiAgentPasswd(ioperationpassword);
                        }
                        String agentip = aom.getiAgentIp();
                        int agentport = Integer.parseInt(String.valueOf(aom.getiAgentPort()));
                        // 针对windows系统采用net用服务形式启动Agent
                        if ((aom.getiAgentOs().toLowerCase().indexOf("windows") > -1))
                        {

                            AgentState state = new AgentState();
                            Map map = state.runbat("net use \\\\" + aom.getiAgentIp() + "\\ipc$ "
                                    + aom.getiAgentPasswd() + " /user:" + aom.getiAgentUser());
                            if (Integer.parseInt(map.get("exitValue").toString()) == 0)
                            {
                                state.runbat("sc \\\\" + aom.getiAgentIp() + " start " + agentServiceName);
                                state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
                            } else
                            {
                                state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, map.get(MESSAGE_TEXT).toString());
                                return res;
                            }
                            // 针对aix系统采用net用服务形式启动Agent
                        } else if (aom.getiConnType() == 1)
                        {
                            TelnetUtils telnet = new TelnetUtils(aom.getiAgentOs());
                            telnet.login(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                aom.getiAgentPasswd());
                            telnet.sendCommand("cd " + aom.getiAgentPath());
                            telnet.sendCommand(" nohup ./Agent_Server >nohup.out 2>&1 & ");
                            Thread.sleep(2000);
                            telnet.disconnect();
                            // 针对Linux系统采用net用服务形式启动Agent
                        } else if (aom.getiConnType() == 2)
                        {
                            try
                            {
                                SSHUtils ssh = new SSHUtils(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                        aom.getiAgentPasswd());
                                ssh.execNewCmd(
                                    "cd " + aom.getiAgentPath() + ";" + " nohup ./Agent_Server >nohup.out 2>&1 & ");
                                Thread.sleep(2000);
                                ssh.closeSession();
                            } catch (Exception e)
                            {
                                logger.error("startAgentInfo is error", e);
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, "SSH连接失败！");
                                return res;
                            }
                        }
                        res = connAgent(userName, agentip, agentport, sysType);
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "Agent没有配置信息！");
                    return res;
                }
            }

        } catch (Exception e)
        {
            logger.error("startAgentInfo is error", e);
            res.put(SUCCESS_TEXT, false);
            if (guardflag)
            {
                res.put(MESSAGE_TEXT, "操作失败！请检查“守护端口号”,“守护进程”,“操作系统”等！</br>异常信息：" + e.getMessage());
            } else
            {
                res.put(MESSAGE_TEXT, e.getMessage());
            }
        }
        return res;
    }

    public Map connAgent ( String userName, String agentip, int agentport, int sysType ) throws Exception
    {
        Map res = new HashMap();
        XmlRpcClient _rpcClient = null;
        logger.info("用户名：" + userName + ",操作：Agent管理中执行启动Agent操作。");
        Thread.sleep(5000);
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        boolean isProxy = false;
        Hashtable ha = new Hashtable();
        Vector params = new Vector();
        int ssltype= RpcSslType.getInstance().exec(agentip, agentport);

        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentip, agentport, sysType);
            if (pm.isProxy())
            {
                isProxy = true;
            }
            _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
        } else
        {
            _rpcClient = new AgentXmlRpcClient(agentip, agentport, ssltype);
        }
        // 设置传递的参数
        params.addElement(agentip);
        params.addElement(new Integer(agentport));
        if (isProxy)
        {
            params.addElement(ha);
        }
        try
        {
            _rpcClient.execute("IEAIAgent.checkAgentRe", params);
            manager.updateAgentState(agentip, agentport, Constants.REMOTE_AGENT_STATE_NORMAL, true);
            res.put(SUCCESS_TEXT, true);
            res.put(MESSAGE_TEXT, "成功启动Agent！");
        } catch (ConnectException e)
        {
            logger.error("startAgentInfo is error", e);
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "启动Agent失败！");
            return res;
        } catch (XmlRpcException ee)
        {
            if (ee.getMessage().contains("NoSuchMethodException"))
            {
                logger.error(ee.getMessage());
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "请升级Agent后再重新操作！");
                return res;
            }
            throw new Exception(ee);
        }
        return res;
    }

    public void optAgent ( String agentip, int agentport, int agendamontport, int sysType, String opt )
            throws XmlRpcException, IOException
    {
        XmlRpcClient _rpcClient = null;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        boolean isProxy = false;
        Hashtable ha = new Hashtable();
        Vector params = new Vector();
        int ssltype= RpcSslType.getInstance().exec(agentip, agentport);

        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonDataOpt(ha, agentip, agentport, agendamontport, sysType);
            if (pm.isProxy())
            {
                isProxy = true;
            }
            _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
        } else
        {
            _rpcClient = new AgentXmlRpcClient(agentip, agendamontport, ssltype);
        }
        // 设置传递的参数
        params.addElement(opt);
        params.addElement(ha);
        String backmessage = (String) _rpcClient.execute("IEAIAgent.optAgent", params);
        logger.info("backmessage==="+backmessage);
        System.out.println(backmessage);

    }

    /**
     * 
     * <li>Description:停止Agent</li> 
     * <AUTHOR>
     * 2017年12月7日 
     * @param agentId
     * @param sysType
     * @return
     * return Map
     */
    public Map stopAgentInfo ( long agentId, int sysType, String userName,String ioperationuser,String ioperationpassword )
    {
        Map res = new HashMap();
        XmlRpcClient _rpcClient = null;
        String agentServiceName = "EntegorAgentServer";
        List<AgentOpModel> list = new ArrayList<AgentOpModel>();
        boolean guardflag = false;
        try
        {
            guardflag = ServerEnv.getInstance().getBooleanConfig("is.guard.opt", false);

            boolean flag = AgentMaintainManager.getInstance().checkAgentCfg(agentId, sysType);
            if (guardflag)
            {
                String stopagentcmd = ParameterConfigManager.getInstance().getParamInfo("stopagentcmd");
                String stopagentWincmd = ParameterConfigManager.getInstance().getParamInfo("stopagentcmdWin");

                list = AgentMaintainManager.getInstance().getAgentInfoOper(agentId, sysType);
                String stopcmd = null;
                for (int i = 0; i < list.size(); i++)
                {
                    AgentOpModel aom = new AgentOpModel();
                    aom = list.get(i);
                    String agentip = aom.getiAgentIp();
                    int agendamontport = Integer.parseInt(String.valueOf(aom.getiConnPort()));
                    int agentport = Integer.parseInt(String.valueOf(aom.getiAgentPort()));

                    if ((aom.getiAgentOs().toLowerCase().indexOf("windows") > -1))
                    {
                        stopcmd = stopagentWincmd == null || "".equals(stopagentWincmd) || "null".equals(stopagentWincmd)
                                ? " net stop " + agentServiceName
                                : stopagentWincmd;
                    } else
                    {
                        stopcmd = stopagentcmd == null || "".equals(stopagentcmd) || "null".equals(stopagentWincmd) ? "./Agent_Server stop"
                                : stopagentcmd;
                    }

                    optAgent(agentip, agentport, agendamontport, sysType, stopcmd);
                    Thread.sleep(2000);
                    res = disConnAgent(userName, agentip, agentport, sysType);
                }
            } else
            {
                if (flag)
                {
                    list = AgentMaintainManager.getInstance().getAgentInfoOp(agentId, sysType);
                    for (int i = 0; i < list.size(); i++)
                    {
                        AgentOpModel aom = new AgentOpModel();
                        aom = list.get(i);
                        if(StringUtils.isNotBlank(ioperationuser)){
                            aom.setiAgentUser(ioperationuser);
                        }
                        if(StringUtils.isNotBlank(ioperationpassword)){
                            aom.setiAgentPasswd(ioperationpassword);
                        }
                        String agentip = aom.getiAgentIp();
                        int agentport = Integer.parseInt(String.valueOf(aom.getiAgentPort()));
                        // 针对windows系统采用net用服务形式停止Agent
                        if (aom.getiConnType() == 3)
                        {

                            AgentState state = new AgentState();
                            Map map = state.runbat("net use \\\\" + agentip + "\\ipc$ " + aom.getiAgentPasswd()
                                    + " /user:" + aom.getiAgentUser());
                            if (Integer.parseInt(map.get("exitValue").toString()) == 0)
                            {
                                state.runbat("sc \\\\" + agentip + " stop " + agentServiceName);
                                state.runbat("net use \\\\" + agentip + " /del");
                            } else
                            {
                                state.runbat("net use \\\\" + agentip + " /del");
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, map.get(MESSAGE_TEXT).toString());
                                return res;
                            }
                            // 针对aix系统采用telnet连接形式停止Agent
                        } else if (aom.getiConnType() == 1)
                        {
                            TelnetUtils telnet = new TelnetUtils(aom.getiAgentOs());
                            telnet.login(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                aom.getiAgentPasswd());
                            telnet.sendCommand("cd " + aom.getiAgentPath());
                            telnet.sendCommand("./Agent_Server stop");
                            Thread.sleep(2000);
                            telnet.disconnect();
                            // 针对Linux系统采用ssh连接形式停止Agent
                        } else if (aom.getiConnType() == 2)
                        {
                            try
                            {
                                SSHUtils ssh = new SSHUtils(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                        aom.getiAgentPasswd());
                                ssh.execNewCmd("cd " + aom.getiAgentPath() + ";" + " ./Agent_Server stop");
                                Thread.sleep(2000);
                                ssh.closeSession();
                            } catch (Exception e)
                            {
                                logger.error("stopAgentInfo is error", e);
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, "SSH连接失败！");
                                return res;
                            }
                        }
                        res =disConnAgent(userName, agentip, agentport, sysType);
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "Agent没有配置信息！");
                    return res;
                }
            }
        } catch (Exception e)
        {
            logger.error("stopAgentInfo is error", e);
            res.put(SUCCESS_TEXT, false);
            if (guardflag)
            {
                res.put(MESSAGE_TEXT, "操作失败！请检查“守护端口号”,“守护进程”,“操作系统”等！</br>异常信息：" + e.getMessage());
            } else
            {
                res.put(MESSAGE_TEXT, e.getMessage());
            }
        }
        return res;

    }

    public Map disConnAgent ( String userName, String agentip, int agentport, int sysType ) throws Exception
    {
        Map res = new HashMap();
        XmlRpcClient _rpcClient = null;
        logger.info("用户名：" + userName + ",操作：Agent管理中执行停止Agent操作。");
        Thread.sleep(5000);
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        boolean isProxy = false;
        Hashtable ha = new Hashtable();
        int ssltype= RpcSslType.getInstance().exec(agentip, agentport);

        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentip, agentport, sysType);
            if (pm.isProxy())
            {
                isProxy = true;
            }
            _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
        } else
        {
            _rpcClient = new AgentXmlRpcClient(agentip, agentport, ssltype);
        }
        // 设置传递的参数
        Vector params = new Vector();
        params.addElement(agentip);
        params.addElement(new Integer(agentport));
        if (isProxy)
        {
            params.addElement(ha);
        }
        try
        {
            _rpcClient.execute("IEAIAgent.checkAgentRe", params);
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "停止Agent失败！");
        } catch (ConnectException e)
        {
            logger.error("stopAgentInfo is error", e);
            manager.updateAgentState(agentip, agentport, Constants.REMOTE_AGENT_STATE_EXCEPTION, true);
            res.put(SUCCESS_TEXT, true);
            res.put(MESSAGE_TEXT, "停止Agent成功！");
            return res;
        } catch (XmlRpcException ee)
        {
            if (ee.getMessage().contains("java.net.ConnectException"))
            {
                logger.error(ee.getMessage());
                manager.updateAgentState(agentip, agentport, Constants.REMOTE_AGENT_STATE_EXCEPTION, true);
                res.put(SUCCESS_TEXT, true);
                res.put(MESSAGE_TEXT, "停止Agent成功！");
                return res;
            }
            if (ee.getMessage().contains("NoSuchMethodException"))
            {
                logger.error(ee.getMessage());
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "请升级Agent后再重新操作！");
                return res;
            }
            throw new Exception(ee);
        }
        return res;
    }

    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2017年12月9日 
     * @param iagentIds
     * @param
     * @param userName
     * @return
     * return Map
     * @throws RepositoryException 
     * @throws DBException 
     */
    public void sendMsgCfg ( long[] iagentIds, int operType, String userName ) throws Exception
    {
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);
        List<Connection> dbConns = new ArrayList<Connection>();
        int exceptionPosition = 0;
        List<Object> listRollback = new ArrayList<Object>();
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            Connection connection = DBManager.getInstance().getJdbcConnection((int) dBSourceMonitor.getGroupId());
            dbConns.add(connection);
        }
        if (!dbConns.isEmpty())
        {
            try
            {
                for (Connection con : dbConns)
                {
                    try
                    {
                        for (int i = 0; i < iagentIds.length; i++)
                        {
                            long agentId = iagentIds[i];
                            if (listRollback.size() <= iagentIds.length)
                            {
                                List tempList = new ArrayList();
                                tempList = RollBackTool.getInstance().getDataModel(con, "IEAI_AGENTINFO", IAGENTINFO_ID,
                                    agentId, "update");
                                listRollback.addAll(tempList);
                            }
                            manager.sendMsgCfg(agentId, operType, con);
                            con.commit();
                        }
                    } catch (Exception e)
                    {
                        logger.error("sendMsgCfg is error", e);
                    } finally
                    {
                        if (con != null)
                        {
                            con.close();
                        }
                    }
                }
            } catch (Exception e)
            {
                List<Object> loglist = new ArrayList<Object>();
                try
                {
                    RollBackTool.getInstance().dbRollBack(listRollback, exceptionPosition, loglist, "IEAI_AGENTINFO");
                } catch (Exception e1)
                {
                    for (int logi = 0; logi < loglist.size(); logi++)
                    {
                        String rRoll = (String) loglist.get(logi);
                        dbsRollbacklog.error(rRoll);
                    }
                    logger.error("serviceRollBack error!", e1);
                }
                logger.error("sendMsgCfg is error", e);
                throw new RepositoryException(ServerError.ERR_DB_UPDATE);
            }
        }
    }

    // 查询是否有绑定agent组配置
    public int getAgentInfoGroup ( Long[] agentid, int sysType ) throws RepositoryException
    {
        return AgentMaintainManager.getInstance().getAgentInfoGroup(agentid, sysType);
    }

    // 查询模块类型
    public List getProType ( int sysType )
    {
        return AgentMaintainManager.getInstance().getProType(sysType);
    }

    // 重置
    public void resetNownum ( long agentId, String[] jsonData, int sysType )
    {
        // 获取IEAI_AGENTINFO_GROUP的id和模块类别
        Map<Long, Long> mapAgentGrop = manager.getAgentGroup(agentId, sysType);
        // 获取模块类型
        Map mapPro = manager.getProTypeMap(sysType);
        List<Long> list = new ArrayList();
        for (int i = 0; i < jsonData.length; i++)
        {
            Iterator iter = mapPro.entrySet().iterator();
            while (iter.hasNext())
            {
                Map.Entry<Long, String> entry = (Entry<Long, String>) iter.next();
                long key = entry.getKey();
                String value = entry.getValue();
                if (jsonData[i].equals(value))
                {
                    list.add(key);
                    break;
                }
            }
        }
        // 查询模块类型对应的活动数
        HashMap<Long, Long> map = new HashMap<Long, Long>();
        if (!list.isEmpty())
        {
            for (long data : list)
            {
                long i = 1;
                if (map.get(data) != null)
                {
                    i = map.get(data) + 1;
                }
                map.put(data, i);
            }

            for (Map.Entry<Long, Long> entry1 : mapAgentGrop.entrySet())
            {
                long iid = entry1.getKey();// id
                long type = entry1.getValue();// 模块类别
                for (Map.Entry<Long, Long> entry2 : map.entrySet())
                {
                    long types = entry2.getKey();// 模块类别
                    long num = entry2.getValue();// 活动数
                    if (type == types)
                    {
                        manager.updateNownum(type, iid, num);
                    }
                }
            }
        } else
        {
            try
            {
                List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                        .getDBsourceList(Constants.IEAI_IEAI_BASIC);
                for (DBSourceMonitor dBSourceMonitor : dbList)
                {
                    Connection connection = DBManager.getInstance()
                            .getJdbcConnection((int) dBSourceMonitor.getGroupId());
                    manager.updateNownum(connection, agentId);
                }
            } catch (Exception e)
            {
                logger.error("resetNownum method of AgentMaintainService.class Exception:" + e.getMessage());
            }

        }

    }

    private List jsonTranslateAgent ( String iids, String icustomcmd )
    {
        List dataList = new ArrayList();
        String[] iid = iids.split(",");
        for (int i = 0; i < iid.length; i++)
        {
            Agent model = new Agent();
            model.setIid(Long.parseLong(iid[i]));
            model.setIcustom_cmd(icustomcmd);
            dataList.add(model);
        }

        return dataList;
    }

    public Map updateIcustomCmd ( String iids, String icustomcmd )
            throws EswitchException, RepositoryException, DBException
    {
        Map res = new HashMap();
        List list = jsonTranslateAgent(iids, icustomcmd);
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeUpdateIcustomCmdSql(list, baseConn, dbConns);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {
                    DBResource.closeConnection(baseConn, "updateIcustomCmd", logger);
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, "updateIcustomCmd", logger);
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, ERRORMESSAGEONE);
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, "updateIcustomCmd", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "updateIcustomCmd", logger);
                }
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, ERRORMESSAGE);
                return res;
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }
    }

    /**
     * 
     * @Title: syncAgentMaintainInfos   
     * @Description: CMDB同步Agent设备   
     * @param deleteIds      
     * @author: Administrator 
     * @throws Exception 
     * @date:   2018年8月31日 
     */
    public void syncAgentMaintainInfos ( Long[] deleteIds ) throws Exception
    {
        if (null != deleteIds && deleteIds.length > 0)
        {
            for (Long iid : deleteIds)
            {
                // 根据deleteIds查找Agent信息
                Agent iagent = getAgentMaintainInfoById(iid);

                try
                {
                    CiSimple agent = getAgentMainInfo(iagent);

                    if (null != agent)
                    {
                        logger.info("CMDB同步Agent信息！");
                        logger.info(
                            "send messgages:[agentIP=" + agent.getAgentIP() + ",agentName=" + agent.getAgentName()
                                    + ",osName=" + agent.getOsName() + ",syncTime=" + agent.getSyncTime() + "]");
                        MonitorSystem.getInstance().webserviceSendAgentMessage(agent);
                        logger.info("CMDB同步Agent信息结束！");
                    }

                } catch (Exception e)
                {
                    logger.error("CMDB同步Agent信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                    throw e;
                }

            }

        }

    }

    public void batchInstallAgent ( CommonsMultipartFile file, HttpServletRequest request, int systype )
            throws EswitchException, IOException
    {
        String fileName = file.getOriginalFilename();
        InputStream fis = null;
        List<Map<String, String>> infoList = null;
        try
        {
            if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx") || fileName.endsWith(".et"))
            {
                fis = file.getInputStream();
                infoList = getInstallInfosFromExcel(fileName, fis);
                // agent表增加数据
                Map map = saveAgentByBatch(infoList, systype, request);
                if (!(Boolean) map.get(SUCCESS_TEXT))
                {
                    throw new EswitchException((String) map.get(MESSAGE_TEXT));
                }
                String userName = SessionData.getSessionData(request).getUserName();
                batchInstallAgentNjBank(infoList,userName);
            } else
            {
                throw new EswitchException(" 文件必须为Excel!");
            }
        } catch (Exception e)
        {
            logger.error("批量安装出错！", e);
            throw new EswitchException(e.getMessage());
        } finally
        {
            if (null != fis)
            {
                fis.close();
            }
        }

    }

    public void batchUnloadAgent ( CommonsMultipartFile file, HttpServletRequest request, int systype )
            throws EswitchException, IOException
    {
        String fileName = file.getOriginalFilename();
        InputStream fis = null;
        List<Map<String, String>> infoList = null;
        try
        {
            if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx") || fileName.endsWith(".et"))
            {
                fis = file.getInputStream();
                infoList = getInstallInfosFromExcel(fileName, fis);
                String userName = SessionData.getSessionData(request).getUserName();
                batchUnloadAgent(infoList,userName);
            } else
            {
                throw new EswitchException(" 文件必须为Excel!");
            }
        } catch (Exception e)
        {
            logger.error("批量安装出错！", e);
            throw new EswitchException(e.getMessage());
        } finally
        {
            if (null != fis)
            {
                fis.close();
            }
        }

    }


    public void batchInstallAgent ( List<Map<String, String>> infoList, int systype )
    {
        Vector<Map<String, String>> retList = new Vector<Map<String, String>>();
        for (int i = 0; i < infoList.size(); i++)
        {
            Map<String, String> retMap = infoList.get(i);
            retMap.put("newBatch", "1");
            AgentInstallTread thread = new AgentInstallTread(retMap, retList);
            thread.start();
        }
    }

    public void batchInstallAgentNjBank ( List<Map<String, String>> infoList, String userName )
    {
        Vector<Map<String, String>> retList = new Vector<Map<String, String>>();
        for (int i = 0; i < infoList.size(); i++)
        {
            Map<String, String> retMap = infoList.get(i);
            retMap.put("userName",userName);
            retMap.put("newBatch", "1");
            AgentInstallTread thread = new AgentInstallTread(retMap, retList);
            thread.start();
        }
    }

    public void batchUnloadAgent ( List<Map<String, String>> infoList, String userName )
    {
        Vector<Map<String, String>> retList = new Vector<Map<String, String>>();
        for (int i = 0; i < infoList.size(); i++)
        {
            Map<String, String> retMap = infoList.get(i);
            retMap.put("userName",userName);
            retMap.put("newBatch", "1");
            AgentUnloadTread thread = new AgentUnloadTread(retMap, retList);
            thread.start();
        }
    }

    private List<Map<String, String>> getInstallInfosFromExcel ( String fileName, InputStream fis ) throws Exception
    {
        Workbook poiBook = fisToPOI(fileName, fis);
        List<Map<String, String>> installInfos = new ArrayList<Map<String, String>>();
        int numberOfSheets = poiBook.getNumberOfSheets();
        for (int i = 0; i < numberOfSheets; i++)
        {
            Sheet sheet = poiBook.getSheetAt(i);
            List<Map<String, String>> list = readSheetToList(sheet);
            installInfos.addAll(list);
        }
        return installInfos;
    }

    private List<Map<String, String>> readSheetToList ( Sheet sheet ) throws Exception
    {
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        int totalRowNumbers = sheet.getLastRowNum() + 1 - sheet.getFirstRowNum();
        Map<Integer, String> titleMap = new HashMap<Integer, String>();
        // 读取标题到Map
        readTitleRow(sheet.getRow(0), titleMap);

        // 校验标题都存在
        List<String> titleList = getTitleList();
        Collection<String> titleSet = titleMap.values();
        for (String title : titleList)
        {
            if (!titleSet.contains(title))
            {
                throw new Exception("缺少" + title + "列!");
            }
        }

        for (int rowNumber = 1; rowNumber < totalRowNumbers; rowNumber++)
        {
            Row row = sheet.getRow(rowNumber);
            if (row != null)
            {
                list.add(readRowToList(row, rowNumber, titleMap));
            }
        }
        return list;
    }

    private List<String> getTitleList ()
    {
        List<String> prjMainTitl = new ArrayList<String>();
        prjMainTitl.add("名称");
        prjMainTitl.add("地址");
        prjMainTitl.add("端口号");
        prjMainTitl.add("用户名");
        prjMainTitl.add("密码");
        prjMainTitl.add("连接方式");
        prjMainTitl.add("连接端口");
        prjMainTitl.add("脚本路径");
        prjMainTitl.add("脚本参数");
        prjMainTitl.add("所属网段");
        return prjMainTitl;
    }

    private void readTitleRow ( Row titleRow, Map<Integer, String> titleMap )
    {
        for (int columnNumber = 0; columnNumber < titleRow.getLastCellNum(); columnNumber++)
        {
            Cell cell = titleRow.getCell((short) columnNumber);
            // 获取Excel第一行的列名
            if (cell != null)
            {
                titleMap.put(columnNumber, getStringCellValue(cell).replaceAll("\n", "").trim());
            }
        }
    }

    public Workbook fisToPOI ( String fileName, InputStream fis ) throws EswitchException
    {
        Workbook workbook = null;
        try
        {
            if (fileName.toLowerCase().endsWith("xlsx") || fileName.toLowerCase().endsWith("xlsm"))
            {
                workbook = new XSSFWorkbook(fis);
            } else if (fileName.toLowerCase().endsWith("xls") || fileName.toLowerCase().endsWith("et"))
            {
                workbook = new HSSFWorkbook(fis);
            }
        } catch (Exception e)
        {
            logger.error("fis2POI is error ! :", e);
            throw new EswitchException(e);
        }
        return workbook;
    }

    private Map<String, String> readRowToList ( Row row, int curRow, Map<Integer, String> titleMap )
            throws EswitchException, RepositoryException
    {
        boolean iscib = false;
        boolean isczb = false;
        try
        {
            iscib = Environment.getInstance().getBankSwitchIscib();
            isczb = Environment.getInstance().getBankSwitchIsCzb();
        } catch (Exception e)
        {
            logger.error("readRowToList.getBankSwitch is error");
        }
        int totalColumnNumbers = row.getLastCellNum();
        Map<String, String> modelMap = new HashMap<String, String>();
        // 提取ROWTOOBJECT
        for (int columnNumber = 0; columnNumber < totalColumnNumbers; columnNumber++)
        {
            Cell cell = row.getCell((short) columnNumber);
            // 获取Excel第一行的列名
            String columnName = titleMap.get(columnNumber);
            // excel内容转换后的字符串
            String cellValueString = getStringCellValue(cell);
            modelMap.put("isBatch", "1");
            // 并发步骤
            if ("名称".equals(columnName))
            {
                if (iscib)
                {
                    if (null != cellValueString || !"".equals(cellValueString))
                    {
                        modelMap.put("iagentname", cellValueString);
                    } else
                    {
                        modelMap.put("iagentname", "");
                    }
                } else
                {
                    if (null == cellValueString || "".equals(cellValueString))
                    {
                        throw new EswitchException("名称为必填项！");
                    } else if (cellValueString.length() > 128)
                    {
                        throw new EswitchException("名称不能超过256个字节！");
                    }
                    modelMap.put("iagentname", cellValueString);
                }

            }// 步骤序号
            else if ("地址".equals(columnName))
            {
                if (null == cellValueString || "".equals(cellValueString))
                {
                    throw new EswitchException("地址为必填项！");
                }
                if (isDNS(cellValueString))
                {
                    throw new EswitchException("地址不能超过250个字节！");
                }
                modelMap.put("iagent_ip", cellValueString);
            } else if ("端口号".equals(columnName))
            {
                if (null == cellValueString || "".equals(cellValueString))
                {
                    throw new EswitchException("端口号为必填项！");
                }
                int portVal = Integer.parseInt(cellValueString);
                if (0 < portVal && portVal < 65535)
                {
                    modelMap.put("iagent_port", cellValueString);
                } else
                {
                    throw new EswitchException("端口号格式不正确！");
                }

            } else if ("用户名".equals(columnName))
            {
                if (null == cellValueString || "".equals(cellValueString))
                {
                    throw new EswitchException("用户名为必填项！");
                } else if (cellValueString.length() > 128)
                {
                    throw new EswitchException("用户名不能超过256个字节！");
                }
                modelMap.put("ioperation_user", cellValueString);
            } else if ("密码".equals(columnName))
            {
                if (iscib || isczb)
                {
                    if (null == cellValueString || "".equals(cellValueString))
                    {
                        cellValueString = getUspPassword(modelMap);// 通过堡垒机获取密码
                    } else if (cellValueString.length() > 128)
                    {
                        throw new EswitchException("密码不能超过256个字节！");
                    }
                    modelMap.put("ioperation_password", cellValueString);
                } else
                {
                    if (null == cellValueString || "".equals(cellValueString))
                    {
                        throw new EswitchException("密码为必填项！");
                    } else if (cellValueString.length() > 128)
                    {
                        throw new EswitchException("密码不能超过256个字节！");
                    }
                    modelMap.put("ioperation_password", cellValueString);
                }
            } else if ("连接方式".equals(columnName))
            {
                if (null == cellValueString || "".equals(cellValueString))
                {
                    throw new EswitchException("连接方式为必填项！");
                }
                if ("ssh".equalsIgnoreCase(cellValueString) || "net".equalsIgnoreCase(cellValueString))
                {
                    modelMap.put("connect_type", cellValueString);
                } else
                {
                    throw new EswitchException("不支持此连接方式！" + cellValueString);
                }

            } else if ("连接端口".equals(columnName))
            {
                int portVal = Integer.parseInt(cellValueString);
                if (0 < portVal && portVal < 65535)
                {
                    modelMap.put("iconnect_port", cellValueString);
                } else
                {
                    throw new EswitchException("连接端口格式不正确！");
                }
            } else if ("脚本路径".equals(columnName))
            {
                if (null == cellValueString || "".equals(cellValueString))
                {
                    throw new EswitchException("脚本路径为必填项！");
                }

                modelMap.put("iinstallScript", cellValueString);
            } else if ("脚本参数".equals(columnName))
            {
                modelMap.put("iparameter", cellValueString == null ? "" : cellValueString);
            } else if ("所属网段".equals(columnName))
            {
                modelMap.put("ipsegment", cellValueString == null ? "" : cellValueString);
            } else if ("数据中心".equals(columnName))
            {
                modelMap.put("idcid", getDataCenterByName(cellValueString) == -1 ? ""
                        : String.valueOf(getDataCenterByName(cellValueString)));
            }else if ("超时时间".equals(columnName))
            {
                if(StringUtils.isNotBlank(cellValueString)){
                    if (!cellValueString.matches("\\d+")){
                        throw new EswitchException("超时时间格式不正确！");
                    }
                    modelMap.put("timeOut",cellValueString);
                }
            }
        }
        return modelMap;
    }

    /**
     * CIB堡垒机获取密码
     */
    private String getCibPassword ( Map<String, String> modelMap ) throws EswitchException
    {
        String ip = modelMap.get("iagent_ip");
        String userName = modelMap.get("ioperation_user");

        UspService usp = new UspService();
        String result = usp.getUspMessage(ip, userName);

        if (!"1".equals(result))
        {
            return result;
        } else
        {
            throw new EswitchException(ip + "通过堡垒机获取密码失败");
        }
    }

    public Map saveAgentByBatch ( List<Map<String, String>> infoList, int systype, HttpServletRequest request )
            throws Exception
    {
        Map res = new HashMap();
        String iagent_ip = null;
        String iagent_port = null;
        String iagentname = null;
        String ipsegment = "";
        long idcid = -1L;
        Map<String, String> map = null;
        Connection checkConn = null;

        boolean iscib = Environment.getInstance().getBankSwitchIscib();
        String userName = SessionData.getSessionData(request).getUserName();
        long userid = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        List<Map<String, Object>> agentList = new ArrayList<Map<String, Object>>();
        checkConn = DBResource.getConnection("saveAgentMaitainInfos", logger, systype);
        for (int i = 0; i < infoList.size(); i++)
        {
            map = infoList.get(i);
            iagentname = map.get("iagentname");
            iagent_port = map.get("iagent_port");
            iagent_ip = map.get("iagent_ip");
            ipsegment = String.valueOf(map.get("ipsegment"));
            idcid = getIdcid(map.get("idcid"));
            Map<String, Object> agentmap = new HashMap<String, Object>();
            agentmap.put("iagentip", iagent_ip);
            agentmap.put("iagentport", iagent_port);
            agentmap.put("iid", "");
            agentmap.put("iagentdesc", "");
            agentmap.put("iagentclustername", "");
            agentmap.put("ienvtype", "");
            agentmap.put("ipsegment", ipsegment);
            agentmap.put("idcid", Long.valueOf(idcid));
            // 兴业银行对名称进行校验
            if (iscib)
            {
                int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
                if (!"".equals(iagentname))
                {
                    int count = AgentMaintainManager.getInstance().checkAgentName(iagentname, sysType);
                    if (count < 1)
                    {
                        iagentname = AgentMaintainManager.getInstance().getOldAgentNameByIp(iagent_ip, iagent_port,
                            checkConn);
                    }
                } else
                {
                    iagentname = AgentMaintainManager.getInstance().getOldAgentNameByIp(iagent_ip, iagent_port,
                        checkConn);
                }
            }
            agentmap.put("iagentname", iagentname);
            agentList.add(agentmap);

        }
        List<Agent> repAgents = new ArrayList<Agent>();
        Map<String, String> tmpContains = new HashMap<String, String>();

        for (Iterator it = agentList.iterator(); it.hasNext();)
        {

            Map<String, Object> agentMap = (Map<String, Object>) it.next();
            Agent agent = _convertToAgent(agentMap);
            String key = agent.getIagentip() + agent.getIagentport();
            if (!tmpContains.containsKey(key))
            {
                tmpContains.put(key, key);
            } else
            {
                res.put(MESSAGE_TEXT, "Agent IP 有重复：" + agent.getIagentip());
                res.put(SUCCESS_TEXT, false);
                return res;
            }
            if (manager.isAgentIpExistsAndUpdateClusterId(agent, checkConn, systype))
            {
                it.remove();
            } else
            {
                if (agent.getIid() == -1)
                {
                    agent.setIcreateuser(userid);
                }

                repAgents.add(agent);
            }
        }

        if (checkConn != null)
        {
            DBResource.closeConnection(checkConn, "saveAgentMaitainInfos", logger);
        }
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        { // 没有基线源，不可以
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeAgentMaitainInfoSql(repAgents, baseConn, dbConns);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {
                    DBResource.closeConnection(baseConn, "saveAgentMaitainInfos", logger);
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "数据保存成功！");
                    logger.info("用户名:" + userName + "操作:Agent管理保存操作。");

                    // 发送Agent设备同步信息 新增一个是否开启该功能的开关
                    if (ServerEnv.getServerEnv().isSendAgentInfoSwitch())
                    {
                        // 组织Agent信息，内容详见pdf文档
                        try
                        {
                            if (!repAgents.isEmpty())
                            {

                                for (int h = 0; h < repAgents.size(); h++)
                                {

                                    CiSimple agent = getAgentMainInfo(repAgents.get(h));
                                    if (null != agent)
                                    {
                                        logger.info("调用发送CMDB-Agent管理集成线程！");

                                        SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                        thread.start();
                                        logger.info("调用发送CMDB-Agent管理集成线程结束！");
                                    }
                                }
                            }

                        } catch (Exception e)
                        {
                            logger.error("Agent信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                        }
                    }

                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, "saveAgentMaitainInfos", logger);
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "deleteAgentMaintainInfos", logger);
                }
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }

    }

    private long getIdcid ( String str )
    {
        if ("".equals(str) || null == str)
        {
            return -1L;
        }
        return Long.valueOf(str);
    }

    /**
    * 
    * <li>Description:更新agent暂停状态</li> 
    * <AUTHOR>
    * 2019年3月27日 
    * @param jsonData
    * @return
    * return boolean
    */
    public boolean updateAgentStatePause ( String jsonData, int sysType )
    {
        boolean flag = false;
        boolean stateFlag = false;
        String method = "updateAgentStatePause";
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection(method, logger, sysType);
            JSONArray js = JSONArray.fromObject(jsonData);
            for (int i = 0, len = js.size(); i < len; i++)
            {
                String id = String.valueOf(js.get(i));
                stateFlag = AgentMaintainManager.getInstance().queryAgentState(conn, id);
                if (stateFlag)
                {
                    AgentMaintainManager.getInstance().updateAgentStatePause(conn, id);
                }
            }
            conn.commit();
            flag = true;
        } catch (RepositoryException e)
        {
            logger.error("updateAgentStatePause has RepositoryException: " + e.getMessage());
        } catch (SQLException e)
        {
            logger.error("updateAgentStatePause has SQLException: " + e.getMessage());
            try
            {
                DBResource.rollback(conn, Constants.IEAI_IEAI_BASIC, e, method, logger);
            } catch (RepositoryException e1)
            {
                logger.error("updateAgentStatePause at rollback is error: " + e.getMessage());
            }
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }

        return flag;
    }

    /**
    * 
    * <li>Description:恢复agent</li> 
    * <AUTHOR>
    * 2019年3月27日 
    * @param jsonData
    * @return
    * return boolean
    */
    public boolean updateAgentStateRecover ( String jsonData, int sysType )
    {
        boolean flag = false;
        boolean stateFlag = false;
        String method = "updateAgentStateRecover";
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection(method, logger, sysType);
            JSONArray js = JSONArray.fromObject(jsonData);
            for (int i = 0, len = js.size(); i < len; i++)
            {
                String id = String.valueOf(js.get(i));
                stateFlag = AgentMaintainManager.getInstance().queryAgentStateIsPause(conn, id);
                if (stateFlag)
                {
                    AgentMaintainManager.getInstance().updateAgentStateRecover(conn, id);
                }
            }
            conn.commit();
            flag = true;
        } catch (RepositoryException e)
        {
            logger.error("updateAgentStateRecover has RepositoryException: " + e.getMessage());
        } catch (SQLException e)
        {
            logger.error("updateAgentStateRecover has SQLException: " + e.getMessage());
            try
            {
                DBResource.rollback(conn, Constants.IEAI_IEAI_BASIC, e, method, logger);
            } catch (RepositoryException e1)
            {
                logger.error("updateAgentStateRecover at rollback is error: " + e.getMessage());
            }
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return flag;
    }

    private static final Map<String, String> INMAP = new LinkedHashMap<String, String>();
    private static final String              TRUE  = "true";
    private static final String              FALSE = "false";
    private static final String              UTF_8 = "UTF-8";
    static
    {
        INMAP.put("地址", "name");
        INMAP.put("端口号", "inumber");
        INMAP.put("应用标识", "name2");
        INMAP.put("模块类型", "name3");
    }

    /**
     * 
     * @Title: exportIsystemTypeExcel   
     * @Description:Agent管理导出应用标识
     * @param response
     * @param ids
     * @param itype      
     * @author: yunpeng_zhang 
     * @date:   2019年5月23日 上午11:31:18
     */
    public void exportIsystemTypeExcel ( HttpServletResponse response, String ids, int itype ,String flag )
    {
        response.setContentType("application/vnd.ms-excel; GBK");
        if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
            response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".et");
        }else{
            response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".xls");
        }
        response.setCharacterEncoding(UTF_8);
        try
        {
            List<ResultBean> resultList = AgentMaintainManager.getInstance().getExportIsystemTypeExcelDataLoop(ids,
                itype);
            PoiUtil.exportExcel("应用标识", INMAP, resultList, response.getOutputStream());
        } catch (RepositoryException e1)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
        } catch (JspException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } catch (IOException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
    }

    /**
     * 
     * @Title: importIsystemTypeExcel   
     * @Description: Agent管理导入应用标识
     * @param file
     * @param response
     * @param itype      
     * @author: yunpeng_zhang 
     * @date:   2019年5月23日 下午4:07:55
     */
    public void importIsystemTypeExcel ( CommonsMultipartFile file, HttpServletResponse response, int itype ,long userId)
    {
        LinkedHashMap<String, String> notExistsMap = new LinkedHashMap<String, String>();// 不存在的agent集合，用于组织向前台发回提示信息
        HashMap<String, Long> existsMap = new HashMap<String, Long>();// 存在的agent集合，用于获取agentId
        LinkedHashMap<Long, List<Map>> finalExistsMap = new LinkedHashMap<Long, List<Map>>();// 最终多写的数据
        String success = TRUE;
        StringBuilder message = new StringBuilder();
        String rsStr = "";
        long size = file.getSize();
        if (size > 20971520)
        {
            success = FALSE;
            message.append("Excel文件大于20M。");
        } else
        {
            try
            {
                List<ResultBean> inList = PoiUtil.parseExcel(file.getOriginalFilename(), file.getInputStream(),
                    new ResultBean(), INMAP);
                List<ResultBean> outList = new ArrayList<ResultBean>();

                if (inList == null || inList.isEmpty())
                {
                    success = FALSE;
                    message.append("无导入数据。");

                } else
                {
                    // 校验excel导入的数据
                    AgentMaintainManager.getInstance().validateImportIsystemTypeExcelLoop(existsMap, notExistsMap,
                        inList, outList, itype);
                    boolean fj = ServerEnv.getInstance().getBooleanConfig(Environment.RESOURCE_SERVER_NEW_SWITCH, false);
                    if(fj){
                        //判断是否有该agent的查询权限，无权限无法导入
                        isAgentAuthority(outList,itype,userId);
                        //需求变更，取消导入业务系统限制
                        //isMouType(outList,itype);
                    }
                    if (outList == null || outList.isEmpty())
                    {
                        success = FALSE;
                        message.append("无符合要求的数据。规则：1、IP+PORT必须存在 2、同Agent下不存在同名应用标识");
                    }else if(isDuplicate(outList))
                    {
                        success = FALSE;
                        message.append("导入数据中，相同地址下应用标识重复，请修改后重新导入");
                    }else
                    {
                        ResourceServerService service = new ResourceServerService();
                        for (ResultBean rb1 : outList)
                        {
                            Long agentId = existsMap.get("(IP:" + rb1.getName() + ",PORT:" + rb1.getInumber() + ")");
                            Map tempMap = new HashMap();
                            if (rb1.getIid() == 0){
                                tempMap.put("id", null);
                            }else {
                                tempMap.put("id",rb1.getIid());
                            }

                            tempMap.put("systemType", rb1.getName2());
                            tempMap.put("imodeltype", rb1.getName3());
                            if (finalExistsMap.get(agentId) != null)
                            {
                                // 组织最终多写数据
                                finalExistsMap.get(agentId).add(tempMap);
                            } else
                            {
                                // 组织最终多写数据
                                List<Map> tempList = new ArrayList<Map>();
                                tempList.add(tempMap);
                                finalExistsMap.put(agentId, tempList);
                            }
                        }
                        int succCount = 0;
                        Set<Long> ks2 = finalExistsMap.keySet();
                        Iterator<Long> it = ks2.iterator();
                        while (it.hasNext())
                        {
                            Long key = it.next();
                            List<Map> tempList1 = finalExistsMap.get(key);
                            // 将list转成json对象，用于组织复用多写方法的数据
                            net.sf.json.JSONArray jsonArry = net.sf.json.JSONArray.fromObject(tempList1);
                            logger.info(key + "||" + jsonArry.toString());
                            // 调用多写方法
                            service.saveAgentServersMultiple(jsonArry.toString(), key);
                            succCount += tempList1.size();
                        }
                        message.append("符合要求的数据" + outList.size() + "条，成功导入数据" + succCount + "条。");
                    }

                }
                if (!notExistsMap.isEmpty())
                {
                    message.append("以下Agent不存在,不保存相关的应用标识:");
                    Set<String> ks = notExistsMap.keySet();
                    Iterator<String> it = ks.iterator();
                    while (it.hasNext())
                    {
                        String key = it.next();
                        message.append(key);
                    }

                }

            } catch (Exception e)
            {
                success = FALSE;
                message = new StringBuilder();
                message.append(e.getMessage().replace("\n", "||").replace("'", "\\'"));
                String regex = ".*[\\u4e00-\\u9fa5]+.*";
                //包含中文报错的不打印
                if(!e.getMessage().matches(regex)){
                   logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
               }
            }
        }
        rsStr = "{success:" + success + ",message:'" + message + "'}";
        response.setContentType("text/html");// 必须设置返回类型为text，否则ext无法正确解析json字符串
        response.setCharacterEncoding(UTF_8);// 设置编码字符集为utf-8，否则ext无法正确解析
        PrintWriter outs = null;
        try
        {
            outs = response.getWriter();
            outs.write(rsStr);
        } catch (IOException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            if (outs != null)
            {
                outs.close();
            }
        }

    }
    
    public boolean isDuplicate(List<ResultBean> userList) {
        Set<String> allSet = new HashSet<>();

        for(ResultBean user : userList) {
            String name = user.getName();
            Integer port = user.getInumber();
            String name2 = user.getName2();
            String all = name + port + name2;
            if(allSet.contains(all)) {
                return true;  // 如果集合中已经含有该元素，则说明字段重复
            }
            allSet.add(all);
        }

        return false;  // 如果没有重复的字段，返回false
    }

    /**
     * 福建农信-Agent导入应用标识校验模块类型
     * @param userList
     * @param itype
     * @return
     * @throws RepositoryException
     */
    public boolean isMouType(List<ResultBean> userList,int itype) throws RepositoryException {
        boolean found = false;
        Map resourceMap = new HashMap();
        List<ResourceGroupDTO> resourceList = new ArrayList();
        for(ResultBean user : userList) {
            found = false;
            String name = user.getName();
            Integer port = user.getInumber();
            Long systemid = AgentMaintainManager.getInstance().querySysTemId(name,port,itype);
            resourceMap = ResourceGroupManager.getInstance().queryModuleTypeDown(systemid,itype);
            resourceList = (List) resourceMap.get("dataList");
            String[] moulNames = user.getName3().split(",");
            if(user.getName3().endsWith(",")){
                throw new RuntimeException("导入的模块类型存在已【，】结尾，请检查！");
            }
            for(String moulName : moulNames){
                found = false;
                for (ResourceGroupDTO bean : resourceList) {
                    if (moulName.equals(bean.getName())) {
                        found = true;
                        break;
                    }
                }
                if(!found){
                    throw new RuntimeException("服务器:【"+user.getName()+"】导入的模块类型不存在该系统下，请检查！");
                }
            }
        }

        return found;
    }

    public Map getAgentTaskExt ()
    {
        Map res = new HashMap();
        try
        {
            res = AgentMaintainManager.getInstance().getAgentTaskExt();
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return res;
    }

    public Map getSysnamebyCpid ( long cpid ) throws RepositoryException
    {
        return AgentMaintainManager.getInstance().getSysnamebyCpid(cpid);
    }

    public Map updateInactiveNum ( XmlRpcClient rpcClient, String agentHost, Long agentPort, String serverHost )
    {
        Vector params = new Vector();
        params.addElement(agentHost);
        params.addElement(serverHost);
        Map map = new HashMap();
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            if (proxySwitch)
            {
                Hashtable ha = new Hashtable();

                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, agentHost, Math.toIntExact(agentPort), sysType);

                if (pm.isProxy())
                {
                    ha.put("proxyJson", pm.getProxyJsonData());
                    ha.put("level", pm.getLevel());
                    ha.put("isProxy", pm.isProxy());
                    params.addElement(ha);
                }
            }
            String inactiveInfos = (String) rpcClient.execute("IEAIAgent.getInactiveInfo", params);
            // **********,15000,num
            int agentinactivenum = 0;
            if (!"false".equals(inactiveInfos))
            {
                String[] inactiveInfo = inactiveInfos.split(",");
                if (inactiveInfo.length > 0)
                {
                    String updateAgentHost = inactiveInfo[0];
                    Long updateAgentPort = Long.parseLong(inactiveInfo[1]);
                    agentinactivenum = Integer.parseInt(inactiveInfo[2]);
                    if (updateAgentHost.equals(agentHost) && agentPort.equals(updateAgentPort))
                    {
                        manager.updateInactiveNum(updateAgentHost, updateAgentPort, agentinactivenum);
                    }
                }
            } else
            {
                manager.updateInactiveNum(agentHost, agentPort, agentinactivenum);
            }
            map.put("agentinactivenum", agentinactivenum);
        } catch (RepositoryException re){
            logger.error("AgentMaintainService.updateInactiveNum is RepositoryException" , re);
        } catch (Exception e)
        {
            if (e.getMessage().contains("NoSuchMethodException"))
            {
                logger.error(agentHost + ":" + agentPort + "-" + "agentInactivenum" + ":" + 0 + "-请升级Agent后再重新操作！");

            } else
            {
                logger.error(
                        agentHost + ":" + agentPort + "-" + "agentInactivenum" + ":" + 0 + "-Agent连接异常，无法获取Agent信息！");
            }
            logger.error("AgentMaintainService.updateInactiveNum is error" + e.getMessage(), e);
        }
        return map;
    }

    /**
     * 
     * @Title: exportAgentMonitorExcel   
     * @Description: Agent监控导出   
     * @throws Exception      
     * @author: lei_wang 
     * @date:   2020年3月12日 上午11:11:15
     */
    public void exportAgentMonitorExcel ( HttpServletResponse response, String iagentName, String iagentIp,
            String iagentDesc, String iagentState, String iagentosname, String proId, String icreateuser,
            String istarttime, String iendtime, int start, int limit, int sysType, Map<String, String> sortMap,
            String iagentcomputername, String icustommess, String iagentversion, String cname, String ctype,
            String systeminfo, String idcid, String iids, String excelLineData ,String flag) throws Exception
    {
        response.setContentType("application/vnd.ms-excel; GBK");
        if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
            response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".et");
        }else{
            response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".xls");
        }
        response.setCharacterEncoding(UTF_8);
        // 获取需要导出的字段
        Map<String, String> map = jsonTranslateExcelLine(excelLineData);
        try
        {
            Map agentMonitorMap = manager.getAgentMonitorList(iagentName, iagentIp, iagentDesc, iagentState,
                iagentosname, proId, icreateuser, istarttime, iendtime, start, limit, sysType, sortMap,
                iagentcomputername, icustommess, iagentversion, cname, ctype, systeminfo, idcid, iids);
            List<Map> dataList = (ArrayList<Map>) agentMonitorMap.get("dataList");
            // 组织导出的数据
            dataList = orgExportData(dataList);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String res = simpleDateFormat.format(new Date());
            String fileName = "Agent自监控" + res;
            OutputStream output = response.getOutputStream();
            response.reset();
            // 设置响应头信息
            if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
                response.setHeader("Content-disposition",
                        "attachment;filename=" + new String(fileName.getBytes(), "iso8859-1") + ".et");
            }else{
                response.setHeader("Content-disposition",
                        "attachment;filename=" + new String(fileName.getBytes(), "iso8859-1") + ".xls");
            }
            // 数据以EXCEL 的形式输出
            if (!dataList.isEmpty())
            {
                AgentMaintainManager.getInstance().exportExcelWorkbook(map, dataList, output);
            }
        } catch (JspException e)
        {
            logger.error("exportAgentMonitorExcel is JspException", e);
        } catch (IOException e)
        {
            logger.error("exportAgentMonitorExcel is IOException", e);
        }

    }

    private Map<String, String> jsonTranslateExcelLine ( String excelLineData )
    {
        Map<String, String> retMap = new LinkedHashMap<String, String>();
        if (!"".equals(excelLineData) && !"null".equals(excelLineData) && null != excelLineData)
        {
            JSONArray jsonArr = JSONArray.fromObject(excelLineData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                String key = jsonArr.getJSONObject(i).optString("text");
                String value = jsonArr.getJSONObject(i).optString("value");
                retMap.put(key, value);
            }
        }
        return retMap;
    }

    private List<Map> orgExportData ( List<Map> dataList ) throws RepositoryException
    {
        // 导出数据的Key==列的value
        List<Map> retList = new ArrayList<Map>();
        Map<String, String> retMap = null;
        if (!dataList.isEmpty())
        {
            for (Map<String, Object> dataMap : dataList)
            {
                retMap = new HashMap<String, String>();
                for (Map.Entry<String, Object> data : dataMap.entrySet())
                {
                    String key = data.getKey();
                    String value = String.valueOf(data.getValue());
                    if ("cpid".equals(key))
                    {
                        Map sysnames = AgentMaintainManager.getInstance().getSysnamebyCpid(Integer.parseInt(value));
                        String sysname = String.valueOf(sysnames.get("sysnames"));
                        String priority = String.valueOf(sysnames.get("prioritys"));
                        retMap.put("sysnames", sysname);
                        retMap.put("prioritys", priority);
                    } else if ("iagentstate".equals(key))
                    {
                        String stateValue = "新建";
                        if ("0".equals(value))
                        {
                            stateValue = "正常";
                        } else if ("1".equals(value))
                        {
                            stateValue = "异常";
                        } else if ("2".equals(value))
                        {
                            stateValue = "升级中";
                        } else if ("3".equals(value))
                        {
                            stateValue = "未知";
                        } else if ("4".equals(value))
                        {
                            if (ServerEnv.getInstance().getAgentPauseRecoverSwitch())
                            {
                                stateValue = "暂停";
                            } else
                            {
                                stateValue = "异常";
                            }
                        }
                        retMap.put(key, stateValue);
                    } else if ("iagentCchange".equals(key))
                    {
                        String change = "否";
                        if ("1".equals(value))
                        {
                            change = "是";
                        }
                        retMap.put(key, change);
                    } else if ("ifsendMsg".equals(key))
                    {
                        String fsendMsg = "发送";
                        if ("1".equals(value))
                        {
                            fsendMsg = "不发送";
                        }
                        retMap.put(key, fsendMsg);
                    } else if ("issued".equals(key))
                    {
                        String issued = "否";
                        if ("1".equals(value))
                        {
                            issued = "是";
                        }
                        retMap.put(key, issued);
                    } else if ("idcid".equals(key))
                    {
                        String idcid = "";
                        if ("1".equals(value))
                        {
                            idcid = "生产中心";
                        } else if ("2".equals(value))
                        {
                            idcid = "同城中心";
                        } else if ("3".equals(value))
                        {
                            idcid = "异地中心";
                        }
                        retMap.put(key, idcid);
                    } else
                    {
                        if ("null".equals(value))
                        {
                            value = "";
                        }
                        retMap.put(key, value);
                    }
                }
                retList.add(retMap);
            }
        }
        return retList;
    }

    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2020年8月10日 
     * @param iip
     * @param
     */
    public Map updateAzName ( String iip, String azName )
    {
        Connection conn = null;
        Map resp = new HashMap();
        logger.info("AgentMaintainService.updateAzName param iip:" + iip);
        try
        {
            if (iip != null && !"".equals(iip))
            {
                resp = this.updateAzNameImpl(iip, azName);
            }

        } catch (Exception e)
        {
            logger.error("AgentMaintainService.updateAzName has an error:" + e.getMessage(), e);
        }
        return resp;

    }

    public Map updateAzNameImpl ( String iip, String azName ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeUpdateAzNameBeanSql(azName, iip, baseConn);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQL_TEXT),
                    (List<String>) orgSql.get(ROLLBACKSQL_TEXT)))
                {
                    DBResource.closeConnection(baseConn, "updateAzName", logger);
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, "updateAzName", logger);
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, ERRORMESSAGEONE);
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, "updateAzName", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "updateAzName", logger);
                }
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, ERRORMESSAGE);
                return res;
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }
    }

    public Map organizeUpdateAzNameBeanSql ( String azName, String iip, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<>();
        List<String> rollbackSqls = new ArrayList<>();
        String sql = " ";
        try
        {
            sql = "update IEAI_AGENTINFO set iagent_azname='" + azName + "' where iagent_ip='" + iip + "'";
            exeSqls.add(sql);
            String srcAzName = manager.getAznameByIip(iip, baseConn);
            sql = "update IEAI_AGENTINFO set iagent_azname='" + srcAzName + "' where iagent_ip='" + iip + "'";
            rollbackSqls.add(sql);
        } catch (Exception e)
        {
            logger.error("organizeUpdateAzNameBeanSql error", e);
            isSuccess = false;
        }
        res.put(SUCCESS_TEXT, isSuccess);
        res.put(EXECSQL_TEXT, exeSqls);
        res.put(ROLLBACKSQL_TEXT, rollbackSqls);
        return res;
    }

    public void addDataCollectAlarmAndSendMessage ( Agent agent )
    {
        CollectalarmManage collectalarmManage = new CollectalarmManage();
        DataCollectAlarmModel model = getDataCollectAlarmModel(agent);
        // 判断防止重复保存数据 true-表示重复不再保存 false-表示不重复需要新加保存
        boolean flag = collectalarmManage.isHaveWarnMessage(model);
        if (!flag)
        {
            // 首页保存小铃铛信息
            collectalarmManage.addDataCollectAlarm(model);

            // 发送报警平台
            collectalarmManage.sendAgentStateErrorMsgToTevilo(model, 0);
        }
    }

    public DataCollectAlarmModel getDataCollectAlarmModel ( Agent agent )
    {
        DataCollectAlarmModel model = new DataCollectAlarmModel();
        model.setIagentip(agent.getIagentip());
        model.setIport(agent.getFtpPort());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
        model.setWarntime(format.format(new Date()));
        model.setIwarntime(System.currentTimeMillis());
        model.setIwarnmsg("Agent: " + agent.getIagentip() + " 状态异常报警");
        return model;
    }

    public boolean checkToken ( String token )
    {
        String plainText = "ideal.info";
        SM4Utils sm4 = new SM4Utils();
        sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
        String inputToken = SM4Utils.decryptData_ECB(token);
        return plainText.equals(inputToken);
    }

    public Map saveCibAgentInfo ( com.alibaba.fastjson.JSONObject resultJsonObject, HttpServletResponse response )
            throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> data = new HashMap<>();
        Iterator<Entry<String, Object>> it = resultJsonObject.entrySet().iterator();
        while (it.hasNext())
        {
            Map.Entry<String, Object> entry = it.next();
            data.put(entry.getKey(), entry.getValue());
        }
        List agentInfoList = (List) data.get("agentInfoList");
        String token = String.valueOf(data.get("token"));
        logger.info("Token is :" + token);
        if (token == null || "".equals(token))
        {
            logger.error("云平台调用接口传递的Token为空！");
            response.setStatus(401);
            res.put(SUCCESS, false);
            res.put(MESSAGE_TEXT, "云平台调用接口传递的Token为空！");
            return res;
        } else
        {
            boolean flag = this.checkToken(token);
            if (!flag)
            {
                logger.error("云平台调用接口传递的Token验证不通过！");
                response.setStatus(401);
                res.put(SUCCESS, false);
                res.put(MESSAGE_TEXT, "云平台调用接口传递的Token验证不通过！");
                return res;
            }
        }
        // 校验传入参数agent信息中是否有ip，端口号相同的
        if (!checkParamter(agentInfoList))
        {
            res.put(SUCCESS_TEXT, false);
            return res;
        }
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeSaveCibAgentInfoSql(agentInfoList, baseConn);
            if ((Boolean) orgSql.get(SUCCESS_TEXT))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                    (List<String>) orgSql.get("rollbackSqls")))
                {
                    DBResource.closeConnection(baseConn, SAVECIBAGENTINFO, logger);
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, SAVECIBAGENTINFO, logger);
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, ERRORMESSAGEONE);
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, SAVECIBAGENTINFO, logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, SAVECIBAGENTINFO, logger);
                }
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, ERRORMESSAGE);
                return res;
            }
        } else
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            return res;
        }
    }

    private Map organizeSaveCibAgentInfoSql ( List agentInfoList, Connection baseConn )
    {
        Map res = new HashMap();
        // 将list中的map转化为bean
        List<Agent> newAgentInfoList = manager.transformBeanList(agentInfoList, baseConn);
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<>();
        List<String> rollbackSqls = new ArrayList<>();
        String insertSqlTemplate = " insert into IEAI_AGENTINFO ( IAGENTINFO_ID,IAGENT_NAME,IAGENT_DES,IAGENT_IP,IPALIAS,IAGENT_PORT,IAGENT_STATE,IENV_TYPE,ISSUED,IDCID,IAGENT_WEBPORT,IAGENT_GUARDPORT,SYSTEMID) values({iid},'{iagentname}','{iagentdesc}','{iagentip}',{ipalias},{iagentport},{iagentState},{ienvtype},{issued},{idcid},{iagentwebport},{iagentguardport},{systemId}) ";
        String deleteSqlTemplate = " delete from IEAI_AGENTINFO where IAGENTINFO_ID=";
        String updateSqlTemplate = " update IEAI_AGENTINFO set IAGENT_NAME='{iagentname}', IDCID={idcid} where IAGENT_IP='{iagentip}' and IAGENT_PORT={iagentport}";

        // 查询agent所属proxy代理网段 绑定proxy
        List proxyInfoList = manager.getProxyInfoList(baseConn);
        try
        {
            for (Agent agent : newAgentInfoList)
            {
                if (manager.isIpAndPortRepeat(agent.getIagentip(), agent.getIagentport(), baseConn))
                {
                    long id = IdGenerator.createId("IEAI_AGENTINFO", baseConn);
                    agent.setIid(id);
                    BeanFormatter<Agent> bf = new BeanFormatter<>(insertSqlTemplate);
                    String s = bf.format(agent);
                    exeSqls.add(s);
                    rollbackSqls.add(deleteSqlTemplate + id);
                    // 组织绑定proxy sql
                    if (!proxyInfoList.isEmpty())
                    {
                        organizeBindProxySql(exeSqls, rollbackSqls, proxyInfoList, agent, baseConn);
                    }
                } else
                {
                    BeanFormatter<Agent> bf = new BeanFormatter<>(updateSqlTemplate);
                    String s = bf.format(agent);
                    exeSqls.add(s);
                    Agent backModel = manager.getAgentOne(agent.getIagentip(), agent.getIagentport(), baseConn);
                    if (null != backModel)
                    {
                        BeanFormatter<Agent> rbf = new BeanFormatter<>(updateSqlTemplate);
                        String r = rbf.format(backModel);
                        rollbackSqls.add(r);
                    }
                }
            }
        } catch (RepositoryException e)
        {
            logger.error("organizeSaveCibAgentInfoSql error", e);
            isSuccess = false;
        } catch (Exception e)
        {
            logger.error("organizeSaveCibAgentInfoSql error", e);
            isSuccess = false;
        }
        res.put(SUCCESS_TEXT, isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }
    
    
    
    

    public Boolean checkParamter ( List agentInfoList )
    {
        for (int i = 0; i < agentInfoList.size(); i++)
        {
            Map mapa = (Map) agentInfoList.get(i);
            for (int j = 0; j < agentInfoList.size(); j++)
            {
                if (i == j)
                {
                    continue;
                }
                Map mapb = (Map) agentInfoList.get(j);
                String ip = String.valueOf(mapa.get("agentIp"));
                String port = String.valueOf(mapa.get("agentPort") == null ? 15000 : mapa.get("agentPort"));
                String ip1 = String.valueOf(mapb.get("agentIp"));
                String port1 = String.valueOf(mapb.get("agentPort") == null ? 15000 : mapb.get("agentPort"));
                if ("".equals(ip) || "".equals(port) || "".equals(ip1) || "".equals(port1))
                {
                    return false;
                }
                if ((ip + port).equals(ip1 + port1))
                {
                    return false;
                }
            }
        }
        return true;
    }

    private void organizeBindProxySql ( List<String> exeSqls, List<String> rollbackSqls, List proxyInfoList,
            Agent agent, Connection baseConn )
    {
        for (int i = 0; i < proxyInfoList.size(); i++)
        {
            Map map = (Map) proxyInfoList.get(i);
            organizeBindProxySqlPartOne(exeSqls, rollbackSqls, agent, baseConn, map);
        }
    }

    private void organizeBindProxySqlPartOne ( List<String> exeSqls, List<String> rollbackSqls, Agent agent,
            Connection baseConn, Map map )
    {
        try
        {
            String isIp = "^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(1\\d\\d|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$";
            String isSegment = "^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.){2}(1\\d\\d|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$";
            Iterator<Entry<Long, String[]>> entries = map.entrySet().iterator();
            while (entries.hasNext())
            {
                Entry<Long, String[]> entry = entries.next();
                long proxyId = entry.getKey();
                String[] ipsegments = entry.getValue();
                List ipsegmentsList = Arrays.asList(ipsegments);
                for (int j = 0; j < ipsegmentsList.size(); j++)
                {
                    String str = String.valueOf(ipsegmentsList.get(j)).trim();
                    Pattern p = Pattern.compile(isIp);
                    Matcher m = p.matcher(str);
                    if (m.find() && agent.getIagentip().equals(str))
                    {
                        long id = IdGenerator.createId("IEAI_PROXY_LIST_RELATION", baseConn);
                        String insertSqlTemplate = "insert into IEAI_PROXY_LIST_RELATION (iid,proxyid,proxynextid,proxynexttype) values("
                                + id + "," + proxyId + "," + agent.getIid() + ",1)";
                        String deleteSqlTemplate = "delete from IEAI_PROXY_LIST_RELATION where IID=" + id;
                        exeSqls.add(insertSqlTemplate);
                        rollbackSqls.add(deleteSqlTemplate);
                    }
                    Pattern p1 = Pattern.compile(isSegment);
                    Matcher m1 = p1.matcher(str);
                    if (m1.find()
                            && agent.getIagentip().subSequence(0, agent.getIagentip().lastIndexOf('.')).equals(str))
                    {
                        long id = IdGenerator.createId("IEAI_PROXY_LIST_RELATION", baseConn);
                        String insertSqlTemplate = "insert into IEAI_PROXY_LIST_RELATION (iid,proxyid,proxynextid,proxynexttype) values("
                                + id + "," + proxyId + "," + agent.getIid() + ",1)";
                        String deleteSqlTemplate = "delete from IEAI_PROXY_LIST_RELATION where IID=" + id;
                        exeSqls.add(insertSqlTemplate);
                        rollbackSqls.add(deleteSqlTemplate);
                    }
                }
            }
        } catch (RepositoryException e)
        {
            logger.error("organizeBindProxySqlPartOne error", e);
        }

    }

    public void warnAgentOutTime ( Agent agent )
    {
        String message = "ip为" + agent.getIagentip() + ",端口为" + agent.getIagentport() + "连接超时。";
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentconnectouttime");
        ieaiWarnModel.setIlevelcode("five");
        ieaiWarnModel.setIip(agent.getIagentip());
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
        if (Environment.getInstance().getCibTivoliSwitch())
        {
            SendCibSyslog syslog = new SendCibSyslog();
            StringBuilder sb = new StringBuilder();
            sb.append("severity=CRITICAL hostname=").append(agent.getIagentname() == null ? "" : agent.getIagentname())
                    .append(" ").append("ip=" + agent.getIagentip()).append(" ")
                    .append("msg=\"" + agent.getIagentip() + "Agent连接超时" + "\"");
            logger.info(sb.toString());
            syslog.sendsysLog(sb.toString());
        }
    }

    public void batchStartStopAgent ( List<ComputerDataModel> list, String scriptPath, int type, String username )
            throws Exception
    {
        Vector<AgentStartStopTread> threads = new Vector<>();
        for (int i = 0; i < list.size(); i++)
        {
            ComputerDataModel model = list.get(i);
            Map<String, String> map = new HashMap();
            map.put("ioperation_user", model.getIoperation_user());
            if ("".equals(model.getIoperation_password().trim()) && Environment.getInstance().getBankSwitchIscib())
            {
                model.setIoperation_password(getUspPassword(map));
            }
            s.acquire();
            AgentStartStopTread thread = new AgentStartStopTread(model, scriptPath, type, username, s);
            threads.add(thread);
            thread.start();
        }
        for (AgentStartStopTread ithread : threads)
        {
            ithread.join(180000);// 设置超时时间3分钟
            if (ithread.isAlive())
            {
                ithread.closeExec();
                ithread.join(10000);
                if (ithread.isAlive())
                {
                    ithread.interrupt();
                }
            }
        }
    }

    public void saveStartStopAgentInfo ( List list, String username, int type )
            throws RepositoryException, DBException, EswitchException
    {
        // 保存批量启停的Agent信息
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeStartStopAgentSql(list, username, type, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),
                    (List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, "batchStartStopAgent", logger);
                } else
                {
                    DBResource.closeConnection(baseConn, "batchStartStopAgent", logger);
                    throw new EswitchException("执行信息变更sql时出现错误！");
                }
            } else
            {
                DBResource.closeConnection(baseConn, "batchStartStopAgent", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "batchStartStopAgent", logger);
                }
                throw new EswitchException("组织信息sql时出现错误！");
            }
        } else
        {
            throw new EswitchException("没有基线源, 无法保存！");
        }
    }

    private Map organizeStartStopAgentSql ( List<ComputerDataModel> list, String username, int type,
            Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<>();
        List<String> rollbackSqls = new ArrayList<>();
        String insertSqlTemplate = " INSERT INTO IEAI_Agent_start_stop (iid,iagentip,iagentport,iosname,istate,iusername,itype,iupdatestate,itime) VALUES({iid},'{iagentip}',{iagentport},'{iagentos}',0,'"
                + username + "'," + type + ",0,FUN_GET_DATE_NUMBER_NEW(" + Constants.getCurrentSysDate() + ",8)) ";
        String deleteSqlTemplate = " delete from IEAI_Agent_start_stop where IID=";

        try
        {
            for (ComputerDataModel model : list)
            {
                long id = IdGenerator.createId("IEAI_Agent_start_stop", baseConn);
                model.setIid(id);
                BeanFormatter<ComputerDataModel> bf = new BeanFormatter<>(insertSqlTemplate);
                String ss = bf.format(model);
                exeSqls.add(ss);
                rollbackSqls.add(deleteSqlTemplate + id);
            }
        } catch (RepositoryException e)
        {
            logger.error("organizeStartStopAgentSql error", e);
            isSuccess = false;
        } catch (Exception e)
        {
            logger.error("organizeStartStopAgentSql error", e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    public Map getStartStopResult ( String queryString, String itime, Integer start, Integer limit, int sysType )
            throws ParseException
    {
        Connection conn = null;
        String sqlWhere = "";
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (queryString != null && !"".equals(queryString))
            {
                sqlWhere = sqlWhere + " and (a.iagentip like '%" + queryString + "%')";
            }
            if (itime != null && !"".equals(itime))
            {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
                Calendar cal = Calendar.getInstance();
                cal.setTime(sdf.parse(itime));
                long time1 = cal.getTimeInMillis();
                sqlWhere = sqlWhere + " and (a.itime = " + time1 + ")";
            } else
            {
                // 判断最近一次启停是否结束，未结束默认查询最近一次启停记录
                Map reMap = manager.getRecentlyRecords(conn);
                if (Long.parseLong(String.valueOf(reMap.get("cou"))) > 0)
                {
                    sqlWhere = sqlWhere + " and (a.itime = " + reMap.get("itime") + ")";
                } else if (queryString != null && !"".equals(queryString))
                {
                    sqlWhere = sqlWhere + " and (a.iagentip like '%" + queryString + "%')";
                } else
                {
                    map.put("dataList", new ArrayList());
                    map.put("total", 0);
                    return map;
                }
            }

            String sqlList = "";
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqlList = "select a.iid,a.Iagentip,a.iagentport,a.iosname,a.istate,a.iusername,a.itime,a.Imessage,a.itype from IEAI_Agent_start_stop a where 1=1 "
                        + sqlWhere + " order by a.istate desc LIMIT ?,?";
            } else
            {
                sqlList = "select * from (select row_number() over (order by a.istate desc) AS ro,a.iid,a.Iagentip,a.iagentport,a.iosname,a.istate,a.iusername,a.itime,a.Imessage,a.itype from IEAI_Agent_start_stop a where 1=1 "
                        + sqlWhere + ") where ro>? and ro<=?";
            }
            String sqlCount = "select count(a.iid) as countNum from IEAI_Agent_start_stop a where 1=1 " + sqlWhere;

            List list = manager.getStartStopResultList(conn, sqlList, start, limit);
            int count = manager.getStartStopResultCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            logger.error("AgentMaintainService.getStartStopResult is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getStartStopResult", logger);
        }
        return map;
    }

    public Map getStartStopTime ( int sysType )
    {
        Connection conn = null;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = manager.getStartStopTime(conn);
            map.put("dataList", list);
        } catch (Exception e)
        {
            logger.error("AgentMaintainService.getStartStopTime is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getStartStopTime", logger);
        }
        return map;
    }

    public synchronized Workbook fis2POI ( String fileName, InputStream fis ) throws IOException
    {
        Workbook workbook = null;
        if (fileName.toLowerCase().endsWith("xlsx") || fileName.toLowerCase().endsWith("xlsm"))
        {
            workbook = new XSSFWorkbook(fis);
        } else if (fileName.toLowerCase().endsWith("xls") || fileName.toLowerCase().endsWith("et"))
        {
            workbook = new HSSFWorkbook(fis);
        }
        return workbook;
    }

    public Map daemonsStartStopExecl(CommonsMultipartFile file, int type,String userName,int sysType){
        String msg="";
        String agentOpt="";
        Map map = new HashMap();
        try
        {
            if(type==0){
                agentOpt="start";
            }else {
                agentOpt="stop";
            }
            //读取Excel数据
            List<Agent> list= deamonsExcel(file);
            if(list.size()==0){
                throw new EswitchException("Excel读取数据失败");
            }
            for(Agent agent:list){
                //查询Agent ID
                List<AgentOpModel> agents = AgentMaintainManager.getInstance().getAgentID(agent.getIagentip(),agent.getIagentport(), sysType);
                if(agents.size()==0){
                    msg+="IP"+agent.getIagentip()+"不存在"+"\n";
                    continue;
                }else {
                    if(agents.get(0).getiConnPort()==0){
                        msg+="IP"+agent.getIagentip()+"守护进程端口不存在"+"\n";
                        continue;
                    }
                    //调用 停止或启动线程方法
                    DeamonsStartAndStopAgentThread thread = new DeamonsStartAndStopAgentThread(agents.get(0).getiAgentId(),agentOpt,sysType,userName);
                    thread.start();
                }
            }
            if(StringUtils.isNotBlank(msg)){
                map.put("message", msg);
            }else{
                map.put("message", "守护进程发送成功");
            }
            map.put("success", true);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", "守护进程启停失败"+e.getMessage());
        }
        return map;
    }

    public Map getAgentStateList(HttpServletRequest request, String user, String token,String agentIp,String agentPort, int sysType) {
        Map res = new HashMap<>();
        try
        {

            PublicTokenResponse tokenResult = PublicTokenService.getInstance().validToken(user, token);//验证token
            if(tokenResult.getIsOk()){//token验证成功
                logger.info("token验证成功:"+tokenResult.getToken());
                // 参数效验
                Map validateMap = validationReqParameter(request,user,token,agentIp,agentPort,sysType);
                if (Boolean.parseBoolean(String.valueOf(validateMap.get("isOk"))))
                {//参数通过
                    logger.info("=========validateMap:"+validateMap);
                    List dataList = (List) validateMap.get("dataList");
                    // 组织 获取agent信息参数
                    Long[] agentIds = new Long[dataList.size()];
                    for (int i = 0; i < dataList.size(); i++) {
                        Map resultMap = (Map) dataList.get(i);
                        agentIds[i] = (Long) resultMap.get("iid");
                    }
                    String userName = SessionData.getSessionData(request).getUserName();
                    Map agentMap = new HashMap<>();
                    // 获取agent信息
                    agentMap = fetchAgentInfoQuery(agentIds,sysType,userName,Constants.AGENT_MAINTAIN_MONITOR);
                    logger.info("=========agentMap:"+agentMap);
                    if(Boolean.parseBoolean(String.valueOf(agentMap.get(SUCCESS_TEXT)))){
                        res.put("status", "success");
                        res.put("message", "Agent通讯正常！");
                    }else {
                        res.put("status", "fail");
                        res.put("message", "Agent无法连接！");
                    }
                }else{
                    // 参数错误 返回信息
                    res = (Map) validateMap.get("data");
                }
            }else {
                res.put("status", "fail");
                res.put("message",tokenResult.getMessage());
            }
        } catch (Exception e)
        {
            res.put("status", "fail");
            res.put("message", "操作失败！");
            logger.error("getAgentForStateList is error ", e);
        }
        return res;
    }
    //参数效验
    private Map validationReqParameter (HttpServletRequest request,String user,String token,String agentIp,String agentPort,int sysType) throws Exception {
        Map<String, String> sortMap =ParseJson.getOrderBy(request);//获取排序字段
        Map validateMap = new HashMap();
        Map res = new HashMap();
        if (StringUtils.isBlank(user))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少user参数或者user参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }
        if (StringUtils.isBlank(token))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少token参数或者token参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }
        if (StringUtils.isBlank(agentIp))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少agentIp参数或者agentIp参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }
        if (StringUtils.isBlank(agentPort))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少agentPort参数或者agentPort参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }
        // 查询需要的 相关参数,并效验Agent信息
        validateMap =  manager.getAgnetInformation(agentIp,agentPort, "", 0, 10000, sysType, sortMap);
        if(Integer.parseInt(String.valueOf(validateMap.get("total")))>0){
            validateMap.put("isOk", true);
        }else {
            res.put("status", "fail");
            res.put("message", "未查询出对应的agent信息,请检查请求参数是否正确！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
        }
        return validateMap;
    }

    /**
     * 获取Agent信息包含的所有操作系统名称
     * @return result
     */
    public Map<String, Object> getOsNameFromAgent() {
        return AgentMaintainManager.getInstance().getOsNameFromAgent();
    }

    public Map<String, Object> updateAgentStateOffline(String jsonData, int sysType) {
        Map resp = new HashMap();
        Connection conn = null;
        String method = "updateAgentStateOffline";
        List<Map<String, Object>> agents = null;
        try
        {
            agents = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            resp.put(SUCCESS_TEXT, false);
            resp.put(MESSAGE_TEXT, "解析Agent数据出现问题！");
            return resp;
        }
        try {
            conn = DBResource.getConnection(method, logger, sysType);
            for (int i = 0; i < agents.size(); i++) {
                Map<String, Object> agentMap = agents.get(i);
                long agentPort = agentMap.get("agentPort") != null && !"".equals(String.valueOf(agentMap.get("agentPort")).trim())
                        ? Long.parseLong(String.valueOf(agentMap.get("agentPort")))
                        : 15000;
                boolean b = AgentMaintainManager.getInstance().updateAgentStateOffline(conn, String.valueOf(agentMap.get("agentIp")), agentPort);
                if(!b){
                    resp.put(SUCCESS, false);
                    resp.put(MESSAGE, "下线失败");
                    return resp;
                }
            }
            conn.commit();
        }catch (Exception e){
            e.printStackTrace();
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
            return resp;
        }finally {
            DBResource.closeConnection(conn, method, logger);
        }
        resp.put(SUCCESS, true);
        resp.put(MESSAGE, "下线成功");
        return resp;
    }

    public Map<String, Object> fjnxSyncAgent(Map map, int sysType) throws Exception {
        Map<String, Object> resp = new HashMap<String, Object>();
        String token = "";
        String user = "";
        token = (String) map.get("token");
        user = (String) map.get("userName");
        Map<String, Object> checkToken = checkValidToken(token,user);
        if(!checkToken.isEmpty() && checkToken.size()>0){
            //token校验失败
            return checkToken;
        }
        Integer operationType = (Integer) map.get("operationType");
        if(operationType == 0){
            //新增
            String jsonData = String.valueOf(map.get("agentInfo"));
            resp = fjnxSaveAgentMaitainInfos(jsonData, sysType);

        }else{
            //回收
            String jsonData = String.valueOf(map.get("agentInfo"));
            resp = updateAgentStateOffline(jsonData, sysType);
        }
        return resp;
    }

    public Map<String, Object> checkValidToken ( String token, String user)
    {
        Map<String, Object> result = new HashMap();
        // token验证
        try
        {

            if (StringUtils.isEmpty(user))
            {
                result.put(SUCCESS, false);
                result.put(MESSAGE, "请求缺少用户登录名参数或者户登录名参数为空！");
                return result;
            }

            if (StringUtils.isEmpty(token))
            {
                result.put(SUCCESS, false);
                result.put(MESSAGE, "请求缺少token参数或者token参数为空！");
                return result;
            }

            // 验证token是否在库中存在
            boolean tokenStr = PublicTokenManager.getInstance().getTokenStr(user, token);
            if (!tokenStr)
            {
                result.put(SUCCESS, false);
                result.put(MESSAGE, "用户名或token不存在或token已经过期！");
                return result;
            }

        } catch (Exception e)
        {
            result.put(SUCCESS, false);
            result.put(MESSAGE, "验证token有效性时出错!");
            return result;
        }
        return result;
    }

    public Map<String, Object> cmdbSaveAgent(CmdbSaveAgentRequestDTO data, int sysType) {
        Map res = new HashMap();
        List<CmdbSaveAgentDTO> agents = data.getList();
        Connection checkConn = null;
        List<Connection> dbConns = null;
        Connection baseConn = null;
        List<Agent> repAgents = new ArrayList<Agent>();
        try {
            checkConn = DBResource.getConnection("cmdbSaveAgent", logger, sysType);
            Map<String, String> tmpContains = new HashMap<String, String>();
            for (int i = 0; i < agents.size(); i++) {
                CmdbSaveAgentDTO cmdbSaveAgentDTO = agents.get(i);
                Agent agent = new Agent();
                agent.setIid(-1);
                agent.setIagentip(cmdbSaveAgentDTO.getAgentIp());
                agent.setIagentdesc(cmdbSaveAgentDTO.getAgentDesc());
                agent.setIagentname(cmdbSaveAgentDTO.getAgentName());
                Long port = cmdbSaveAgentDTO.getAgentPort();
                agent.setIagentport(port != null && port!=0
                        ? port
                        : 15000);
                agent.setIagentState(-1);
                agent.setIdcid(-1);
                agent.setIagentwebport(0L);
                agent.setIagentguardport(0L);
                agent.setIsoncloud("");
                agent.setIbusinessip("");
                Integer isSSL = cmdbSaveAgentDTO.getIsSSL();
                agent.setIsSSL(isSSL == null?0:isSSL);
                String key = agent.getIagentip() + agent.getIagentport();
                if (!tmpContains.containsKey(key)) {
                    tmpContains.put(key, key);
                } else {
                    logger.info("Agent IP 有重复：" + agent.getIagentip());
                    continue;
                }
                if (manager.isAgentIpExistsAndUpdateClusterId(agent, checkConn, sysType)) {
                    logger.info("数据库中已经存在IP为：" + agent.getIagentip() + " 的Agent信息。");
                    continue;
                }
                //判断是否启动守护进程agent监控
                if (manager.isStartAgentMonitor(agent, checkConn, sysType)) {
                    if (manager.isdiffertentIpOrPort(agent, checkConn, sysType)) {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "修改的IP为 ：" + agent.getIagentip() + " 端口为  ：" + agent.getIagentport() + " 的Agent，已启动守护进程agent监控，修改IP需先关闭守护进程agent监控");
                        return res;
                    }
                }
                // 修改agent时验证是否更改，判断新巡检配置中是否已经使用了查agent
                Boolean canSave = true;
                // 添加开关控制保存agent时是否验证是否已经在新巡检中使用
                Boolean checkSave = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.HC_AGENT_SAVE_CHECK_WEBPORT_CHANGE_SWITCH, false);
                Boolean isNewCheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
                if (checkSave && isNewCheck) {
                    HcLinkPlatformManager hcmanager = new HcLinkPlatformManager();
                    canSave = hcmanager.diffWebPortCheckForAgent(null, agent.getIid(), agent.getIagentwebport().toString());
                    if (!canSave) {
                        String mss = "修改的Agent IP【" + agent.getIagentip()
                                + "】的WEB端口号已经绑定并且启动了巡检操作，更改端口号需要先停止关联此Agent的设备巡检 。";
                        List<String> startComputerForAgent = hcmanager.getStartComputerForAgent(agent.getIid(), null);
                        if (startComputerForAgent != null && !startComputerForAgent.isEmpty()) {
                            mss = mss + "关联设备如下：</br>" + StringUtils.join(startComputerForAgent, ",");
                        }
                        res.put("success", false);
                        res.put("message", mss);
                        return res;
                    }
                }
                // 新增时保存纳管用户和纳管时间
                if (agent.getIid() == -1) {
                    //agent.setIcreateuser(userid);
                    agent.setIcreatetime(System.currentTimeMillis() + "");
                }
                repAgents.add(agent);
            }
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            baseConn = (Connection) connInfo.get("baseConn");
            if (null != baseConn) { // 没有基线源，不可以
                dbConns = (List<Connection>) connInfo.get("dbConns");
                Map orgSql = manager.organizeAgentMaitainInfoSql(repAgents, baseConn, dbConns);
                if ((Boolean) orgSql.get(SUCCESS_TEXT)) {
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                            (List<String>) orgSql.get("rollbackSqls"))) {
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "数据保存成功！");
                        List<CmdbAgentDTO> agentIds = new ArrayList<>();
                        for(Agent agent : repAgents){
                            if(agent.getIid()!=-1){
                                CmdbAgentDTO cmdbAgentDTO = new CmdbAgentDTO();
                                cmdbAgentDTO.setAgentIp(agent.getIagentip());
                                cmdbAgentDTO.setAgentPort(agent.getIagentport());
                                cmdbAgentDTO.setAgentId(agent.getIid());
                                agentIds.add(cmdbAgentDTO);
                            }
                        }
                        res.put("agentIds",agentIds);
                        // 发送Agent设备同步信息 新增一个是否开启该功能的开关
                        if (ServerEnv.getServerEnv().isSendAgentInfoSwitch()) {
                            // 组织Agent信息，内容详见pdf文档
                            try {
                                if (!repAgents.isEmpty()) {
                                    for (int h = 0; h < repAgents.size(); h++) {
                                        CiSimple agent = getAgentMainInfo(repAgents.get(h));
                                        if (null != agent) {
                                            logger.info("调用发送CMDB-Agent管理集成线程！");
                                            SendAgentInfoThread thread = new SendAgentInfoThread(agent);
                                            thread.start();
                                            logger.info("调用发送CMDB-Agent管理集成线程结束！");
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Agent信息发送失败 ,异常信息 ： " + e.getMessage(), e);
                            }
                        }
                        return res;
                    } else {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                        return res;
                    }
                } else {
                    if(orgSql.get(MESSAGE_TEXT) != null){
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, orgSql.get(MESSAGE_TEXT));
                        return res;
                    }else{
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                        return res;
                    }

                }
            } else {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                return res;
            }
        }catch (Exception e){
            logger.error("", e);
        }finally {
            DBResource.closeConnection(baseConn, "deleteAgentMaintainInfos", logger);
            DBResource.closeConnection(checkConn, "cmdbSaveAgent", logger);
            if(dbConns != null) {
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "deleteAgentMaintainInfos", logger);
                }
            }
        }
        return res;
    }

    public class DeamonsStartAndStopAgentThread extends Thread{
        //指定线程并发数量
        private  Semaphore msemaphoretryroom = null;
        private String userName;
        private String agentOpt;
        private long agentId;
        private int sysType;

        //给属性planBean 赋值
        public DeamonsStartAndStopAgentThread(long agentId, String agentOpt, int sysType, String userName){
            super("安装Agent线程，IP：");
            this.agentOpt=agentOpt;
            this.userName=userName;
            this.agentId=agentId;
            this.sysType=sysType;
            String threadMax=Environment.getInstance().getSysConfig("thread.max","30");
            Integer max=Integer.parseInt(threadMax);
            msemaphoretryroom=new Semaphore(max);
        }
        /**
         * 重写run方法
         */
        @Override
        public void run() {
            startAndStopAgent();
        }
        /**
         * 守护线程批量安装Agennt
         *  create_by wang_biao 2021-07-16
         */
        public void startAndStopAgent() {
            try {
                //shoproom可用数量减少
                msemaphoretryroom.acquire();
                //业务代码
                optAgentMsg(agentId,agentOpt,sysType,userName,"","");
                //释放当前走完线程 启动新的线程
                msemaphoretryroom.release();
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }

    private List<Agent> deamonsExcel(CommonsMultipartFile file) throws Exception {
        InputStream fis = null;
        List<Agent> list=new ArrayList<>();
        Map map = new HashMap();
        try{
            fis = file.getInputStream();
            String fullFileName = file.getFileItem().getName();
            Workbook workbook = null;
            if (!fullFileName.toLowerCase().endsWith("xlsx") && !fullFileName.toLowerCase().endsWith("xlsm")) {
                if (fullFileName.toLowerCase().endsWith("xls")) {
                    workbook = new HSSFWorkbook(fis);
                }
            } else {
                workbook = new XSSFWorkbook(fis);
            }
            int a=workbook.getSheetAt(0).getRow(0).getLastCellNum();
            String[][]   validateArr=null;
            if(a==2){
                validateArr = new String[][]{{"iagentip", ""}, {"iagentport", "^\\d+$"}};
            }else{
                throw new EswitchException("文件格式不正确");
            }
            map = ImportUtil.parseExcel(workbook, validateArr,  Agent.class, "Sheet1");
            if(null!=map && Boolean.valueOf(map.get("success").toString())) {
                list = (List<Agent>) map.get("list");
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            list=new ArrayList<>();
        }finally {
            fis.close();
        }
        return list;
    }

    public Map<String, Object> uploadBatchStartStopExecl ( String filename, InputStream fis, String userName,
            String istartstoppath, int type, int sysType )
    {
        List<ComputerDataModel> list = new ArrayList();
        Map res = new HashMap();
        Map map = new HashMap();
        String cellNumName = null;
        String cellValueString = null;
        Row row = null;
        Cell cell = null;
        // 获取全部需要的列
        List<String> exceList = Arrays.asList(UPLOAD_START_STOP_AGENT);
        List<String> bak = new ArrayList<>();
        bak.addAll(exceList);
        Workbook workbook = null;
        Connection checkConn = null;
        try
        {
            checkConn = DBManager.getInstance().getJdbcConnection(sysType);
            try
            {
                workbook = fis2POI(filename, fis);
            } catch (Exception e)
            {
                throw new EswitchException("Excel文件版本格式转换发生异常！");
            }
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getLastRowNum() + 1 - sheet.getFirstRowNum();
            // 检查Excel格式长度
            row = sheet.getRow(0);
            // 格式通过后进行数据导入
            for (int k = 0; k <= rows; k++)
            {
                ComputerDataModel model = new ComputerDataModel();
                row = sheet.getRow(k);
                if (row != null && !isRowEmpty(row))
                {
                    int colum = row.getLastCellNum();
                    for (int j = 0; j < colum; j++)
                    {
                        cell = row.getCell(j);
                        if (k == 0)
                        {
                            if (cell != null)
                            {
                                map.put(j, getStringCellValue(cell));
                                bak.remove(getStringCellValue(cell));
                            }
                        } else
                        {
                            if (!bak.isEmpty())
                            {
                                throw new EswitchException("Excel 文件中缺少列:" + bak + ",请添加后再执行！");
                            }
                            cellNumName = (String) map.get(j);
                            cellValueString = getStringCellValue(cell);
                            int colNum = j + 1;
                            int rowNum = k + 1;
                            if (UPLOAD_START_STOP_AGENT[0].equals(cellNumName))
                            {
                                if (cellValueString == null || "".equals(cellValueString))
                                {
                                    throw new EswitchException(
                                            "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不能为空,请修改后重试!");
                                }
                                if (!manager.isIpRepeat(cellValueString))
                                {
                                    throw new EswitchException(
                                            "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不存在,请修改后重试!");
                                }
                                model.setIagentip(cellValueString);
                            }
                            if (UPLOAD_START_STOP_AGENT[1].equals(cellNumName))
                            {
                                if (cellValueString != null || !"".equals(cellValueString))
                                {
                                    model.setIagentport(Integer.parseInt(cellValueString));
                                } else
                                {
                                    model.setIagentport(15000);
                                }
                            }
                            if (UPLOAD_START_STOP_AGENT[2].equals(cellNumName))
                            {
                                if (cellValueString == null && "".equals(cellValueString))
                                {
                                    throw new EswitchException(
                                            "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不能为空,请修改后重试!");
                                } else if (!"aix".equalsIgnoreCase(cellValueString)
                                        && !"linux".equalsIgnoreCase(cellValueString)
                                        && !"windows".equalsIgnoreCase(cellValueString))
                                {
                                    throw new EswitchException(
                                            "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不支持的操作系统类型,请修改后重试!");
                                }
                                model.setIagentos(cellValueString);
                            }
                            if (UPLOAD_START_STOP_AGENT[3].equals(cellNumName))
                            {
                                if (cellValueString == null || "".equals(cellValueString))
                                {
                                    throw new EswitchException(
                                            "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不能为空,请修改后重试!");
                                }
                                model.setIoperation_user(cellValueString);
                            }
                            if (UPLOAD_START_STOP_AGENT[4].equals(cellNumName))
                            {
                                if (cellValueString == null || "".equals(cellValueString))
                                {
                                    if (Environment.getInstance().getBankSwitchIscib())
                                    {
                                        model.setIoperation_password("");
                                    } else
                                    {
                                        throw new EswitchException(
                                                "第" + rowNum + "行,第" + colNum + "列的" + cellNumName + "不能为空,请修改后重试!");
                                    }
                                } else
                                {
                                    model.setIoperation_password(cellValueString);
                                }
                            }
                        }
                    }
                    if (k != 0)
                    {
                        Agent agent = manager.getAgentOne(model.getIagentip(),
                            Long.parseLong(String.valueOf(model.getIagentport())), checkConn);
                        if (agent.getIid() != 0)
                        {
                            model.setIid(agent.getIid());
                            list.add(model);
                        }
                    }
                }
            }
            if (!list.isEmpty())
            {
                this.saveStartStopAgentInfo(list, userName, type);
                Thread t = new Thread()
                {
                    @Override
                    public void run ()
                    {
                        try
                        {
                            batchStartStopAgent(list, istartstoppath, type, userName);
                        } catch (Exception e)
                        {
                            logger.error("batchStartStopAgent is error", e);
                        }
                    }
                };
                t.start();
            }
            res.put(SUCCESS, true);
            res.put("message", "上传成功！");
        } catch (Exception ee)
        {
            logger.error("uploadBatchStartStopExecl", ee);
            res.put(SUCCESS, false);
            res.put("message", ee.getMessage());
        } finally
        {
            DBResource.closeConnection(checkConn, "uploadBatchStartStopExecl", logger);
        }
        return res;
    }

    public List getProTypeNew ( int sysType )
    {
        return AgentMaintainManager.getInstance().getProTypeNew(sysType);
    }

    public Map getPerformTaskList ( Long agentId, String iagentip, String sysType, String iagentport, int start,
            int limit ) throws Exception
    {
        return manager.getPerformTaskList(agentId, iagentip, Integer.parseInt(sysType), Integer.parseInt(iagentport),
            start, limit);
    }

    public Map getCMDBPerformTaskList ( int start, int limit ) throws Exception
    {
        return manager.getCMDBPerformTaskList(start, limit);
    }

    public Map<String, Object> agentMonitorTaskForcestop ( String iagentip, String iagentport, String requuid,
            String sysType, int type )
    {
        return manager.agentMonitorTaskForcestop(iagentip, Integer.parseInt(iagentport), requuid, sysType, type);
    }

    /**
     * <li>Description:启动CMDB定时同步</li> 
     * <AUTHOR>
     * 2021年6月9日 
     * @param cron
     * @param sysType
     * return void
     */
    public void saveCmdbTimeTask ( String cron, int sysType )
    {
        Connection conn = null;
        long workItemid = 0;
        long standardiid = 0;
        long executionid = 0;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            ExecutionStrategyHandler.getInstance().stopExecutionStrategyTaskJobClass(sysType, AGENTCMDBJOB);
            StrategyBean strategyBean = new StrategyBean();
            strategyBean.setWorkItemid(workItemid);
            strategyBean.setStandardiid(standardiid);
            strategyBean.setCron(cron);
            strategyBean.setJobClass(AGENTCMDBJOB);
            executionid = ExecutionStrategyHandler.getInstance().addExecutionStrategyTask(sysType, strategyBean);
        } catch (Exception e)
        {
            logger.error("saveCmdbTimeTask 新增定时任务失败", e);
        } finally
        {
            DBResource.closeConnection(conn, "saveCmdbTimeTask", logger);
        }
    }

    /**
     * <li>Description:关闭CMDB定时同步</li> 
     * <AUTHOR>
     * 2021年6月9日 
     * @param sysType
     * return Map<String,Object>
     * @throws SQLException 
     */
    public Map<String, Object> updateCmdbTimeTask ( int sysType ) throws SQLException
    {
        Connection conn = null;
        boolean flag = false;
        Map<String, Object> map = new HashMap<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            ExecutionStrategyHandler.getInstance().stopExecutionStrategyTaskJobClass(sysType, AGENTCMDBJOB);
            conn.commit();
            flag = true;
            map.put(SUCCESS, true);
            map.put("message", "关闭成功");
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put("message", "关闭失败");
            if (conn != null)
            {
                conn.rollback();
            }
            logger.error("updateCmdbTimeTask is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "updateCmdbTimeTask", logger);
        }
        return map;
    }
    
    public Map<String, Object> importIsystemTypeExceldes ( String filename, InputStream fis, String iclassIid, int iisbase, HttpServletRequest request )
    {

        List<Agent> list = new ArrayList<>();
        String userName = SessionData.getSessionData(request).getUserName();
        Long time=System.currentTimeMillis();
        Map res = new HashMap();
        Map map = new HashMap();
        Map bakcMap = new HashMap();
        Map bakcMap1 = new HashMap();
        String cellNumName = null;
        String cellValueString = null;
        Row row = null;
        Cell cell = null;
        Workbook workbook = null;
        boolean exceptionFlag=false;
        String mes="";
        try
        {
            InfoExeclServices service = new InfoExeclServices();
            workbook = service.fis2POI(filename, fis);

            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getLastRowNum() + 1 - sheet.getFirstRowNum();

            // 检查Excel格式长度
            // 格式通过后进行数据导入
            Row rowHead = sheet.getRow(0);
            if (rowHead == null || isRowEmpty(rowHead))
            {
                mes += "第一行无标题列，请修改后再上传！";
                throw new CompareException(mes);
            }

            for (int i = 0; i < rows; i++)
            {
                Agent obj = new Agent();
                row = sheet.getRow(i);
                int colum = row.getLastCellNum();
                if (i == 0)
                {
                    uploadBaseDataOne(colum, rowHead, map,AGENT_UPLOAD_DATA_DES);
                } else
                {
                    for (int j = 1; j <= colum; j++)
                    {
                        cell = row.getCell(j);
                        cellNumName = (String) map.get(j);
                        cellValueString = getStringCellValue(cell);
                        int colNum = j + 1;
                        int rowNum = i + 1;
                        
                        throwValueLengthException(cellValueString, cellNumName, colNum, rowNum);
                        obj = validateBaseData(cellNumName, cellValueString, obj, colNum, rowNum,bakcMap1);
                        if((boolean)(bakcMap1.get("flag"))) {
                            exceptionFlag=true;
                            mes+=(String)bakcMap1.get("mes");
                        }
                        
                    }
                  
                }
                list.add(obj);
            }
            
                if(exceptionFlag) {
                    throw new CompareException(mes);
                }
                // 保存Excel信息
                res = saveCollectInfoByExcel(list);


        } catch (Exception ee)
        {
            res.put(SUCCESS, false);
            res.put(MESSAGE, ee.getMessage());
        }
        try
        {
            fis.close();
        } catch (Exception e)
        {
            logger.error("CollectModelService.uploadBaseData is error", e);
        }
        return res;
    }
    
    private void uploadBaseDataOne ( int colum, Row row, Map map,String[] headStr ) throws CompareException
    {
        for (int j = 0; j < colum; j++)
        {
            Cell cell = row.getCell(j);
            throwTitleException(cell);
            map.put(j, getStringCellValue(cell));
        }
        StringBuilder nullValue = getNullValue(map,headStr);
        throwNullValueException(nullValue);
    }
    
    public void throwTitleException ( Cell cell ) throws CompareException
    {
        if (cell == null)
        {
            throw new CompareException("excel中列名不正确！");
        }
    }
    
    public StringBuilder getNullValue ( Map map,String[] headStr )
    {
        StringBuilder value = new StringBuilder();
        for (int k = 0; k < headStr.length; k++)
        {
            if (!map.containsValue(headStr[k]))
            {
                value.append(headStr[k]);
                value.append(",");
            }
        }
        return value;
    }
    
    public void throwNullValueException ( StringBuilder nullValue ) throws CompareException
    {
        String str = nullValue == null ? "" : nullValue.toString();
        if (!"".equals(str))
        {
            throw new CompareException("excel中不存在列名为：" + str.substring(0, str.length() - 1) + "的列，请查看后再操作！");
        }
    }
    
    private void throwValueLengthException ( String cellValueString, String cellNumName, int colNum, int rowNum )
            throws CompareException
    {
        if (cellValueString.length() > 255)
        {
            String mes = "在" + rowNum + "行," + colNum + "列中" + "“" + cellNumName + "”" + "的数据过长！";
            throw new CompareException(mes);
        }
    }  
    
    public Agent validateBaseData ( String cellNumName, String cellValueString, Agent obj,
            int colNum, int rowNum,Map map) throws CompareException
        {
        String mes="";
        boolean exceptionFlag=false;
        String[] BASE_UPLOAD_DATA;

        //描述导入1
     //   { "序号","地址", "端口", "描述"};
        if (AGENT_UPLOAD_DATA_DES[1].equals(cellNumName))
        {
            if(cellNumName!=null) {
                obj.setIagentip(cellValueString);
            }else{
                exceptionFlag=true;
                mes="在" + rowNum + "行," + colNum + "列中,请填写地址";
            }
            
        } else if (AGENT_UPLOAD_DATA_DES[2].equals(cellNumName)) {
            if(cellNumName!=null &&!"".equals(cellNumName)) {
                obj.setIagentport(Long.valueOf(cellValueString));  
            }else {
                exceptionFlag=true;
                mes="在" + rowNum + "行," + colNum + "列中,请填写端口号";
            }
            
        } else if (AGENT_UPLOAD_DATA_DES[3].equals(cellNumName))
        {
            if(cellNumName!=null) {
                obj.setIagentdesc(cellValueString);  
            }
        }
        map.put("flag", exceptionFlag);
        map.put("mes", mes);
        return obj;
    }
 
    public Map saveCollectInfoByExcel ( List<Agent> list ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
            Map orgSql = organizeAddCollectModelSql(list, baseConn);
            
            
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),
                    (List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, SAVECOMPAREBASEBYEXCEL, logger);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "导入数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, SAVECOMPAREBASEBYEXCEL, logger);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, SQLERROR);
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, SAVECOMPAREBASEBYEXCEL, logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, SAVECOMPAREBASEBYEXCEL, logger);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, ORGSQL);
                return res;
            }
        } else
        {
            res.put(SUCCESS, false);
            res.put(MESSAGE, MESSAGEERROR);
            return res;
        }
    }
    
    public Map organizeAddCollectModelSql ( List<Agent> agentslist, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

//        String insertSqlTemplate = " insert into IEAI_AGENTINFO (IAGENTINFO_ID,IAGENT_NETID,IEQUIPMENT_OR_VM_ID,IAGENT_NAME,IAGENT_DES,IAGENT_IP,IPALIAS,IAGENT_PORT,ICLUSTER_ID,IAGENT_STATE,IENV_TYPE,ICREATEUSER,ISSUED,IDCID,IAGENT_WEBPORT,IAGENT_GUARDPORT,IOSBASEVALUE) values({iid},'{iagentnetid}',{iequipmentorvmid},'{iagentname}','{iagentdesc}','{iagentip}',{ipalias},{iagentport},{iagentclusterid},{iagentState},{ienvtype},{icreateuser},{issued},{idcid},{iagentwebport},{iagentguardport},'{iosbasevalue}') ";
//        String deleteSqlTemplate = " delete from IEAI_AGENTINFO where IAGENTINFO_ID=";
        String updateSqlTemplate = " update IEAI_AGENTINFO set IAGENT_DES='{iagentdesc}' where IAGENT_IP='{iagentip}' and IAGENT_PORT={iagentport} ";


        try
        {
            for (Agent agent : agentslist)
            {
                
                 // 更新
                    BeanFormatter<Agent> bf = new BeanFormatter<Agent>(updateSqlTemplate);
                    String s = bf.format(agent);
                    exeSqls.add(s);
            }
        } catch (RepositoryException e)
        {
            logger.error("AgentMaintainManager is err",e);
            isSuccess = false;
        } catch (Exception e)
        {
            logger.error("AgentMaintainManager is err",e);
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    } 
    
    public void exportIsystemTypeExceldes ( HttpServletResponse response, HttpServletRequest request ,String flag)
    {
        OutputStream out = null;
       
        try
        {
            HSSFWorkbook wb = new HSSFWorkbook();
            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);
            String fileName ="";
            if(StringUtils.isNotEmpty(flag) && "et".equals(flag)){
                fileName = dateString + "_agent描述模板.et";
            }else{
                fileName = dateString + "_agent描述模板.xls";
            }
            response.addHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO-8859-1"));

            HSSFSheet sheet = wb.createSheet("agent信息");

            HSSFRow row = sheet.createRow((int) 0);

            sheet.setDefaultColumnWidth(20);

            sheet.createRow((int) 1);
            sheet.createRow((int) 2);
            sheet.createRow((int) 3);
            sheet.createRow((int) 4);

            HSSFCellStyle style = wb.createCellStyle();
            row.setHeightInPoints(15);
            HSSFFont font = wb.createFont();
            font.setBold(true);// 粗体显示
            style.setFont(font);
            style.setFillPattern(SOLID_FOREGROUND); // 填充单元格
            style.setFillForegroundColor(IndexedColors.GOLD.getIndex());
            style.setAlignment(HorizontalAlignment.CENTER);// //居中显示

            HSSFCell cell = row.createCell(0);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(1);
            cell.setCellStyle(style);
            cell.setCellValue("地址");

            cell = row.createCell(2);
            cell.setCellStyle(style);
            cell.setCellValue("端口");

            cell = row.createCell(3);
            cell.setCellStyle(style);
            cell.setCellValue("描述");

            out = response.getOutputStream();
            wb.write(out);

        } catch (Exception e)
        {
            logger.error("AgentMaintainService.exportIsystemTypeExceldes is error", e);
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                logger.error("AgentMaintainService.exportIsystemTypeExceldes is error", e);
            }
        }
    }

    public Long[] getAgentIds(String[] agentIps, int sysType) {
        return manager.getAgentInfoIds(agentIps, sysType);
    }
    /**
     * 民生银行
     * 守护进程安装agent
     * @param ids agentIds
     * @param sysType 类型
     * @param userName 用户名
     * @return
     */
    public Map daemonsScriptUpgrade ( Long[] ids , int sysType, String userName)
    {
        String upgradeAgentScriptName="";
        Map res = new HashMap();

        //第一步 判断升级脚本是否存在
        try{
            upgradeAgentScriptName= ParameterConfigManager.getInstance()
                    .getParamInfo("upgradeAgentScriptNameMSBank");
        }catch (Exception e2){
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "IEAI_PARAMETER_CONFIG表获取数据错误");
            return res;
        }
        if(StringUtils.isBlank(upgradeAgentScriptName)){
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "upgradeAgentScriptNameMSBank 升级脚本不存在");
            return res;
        }
        //第二步 获取agent信息和升级信息
        List<Agent> agents;
        try
        {
            agents = manager.getAgentsByIds(Arrays.asList(ids), sysType);
        } catch (Exception e1)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "获得Agent信息出现错误！" + e1.getMessage());
            return res;
        }

        //第三步 参数效验
        for (int i = 0; i < agents.size(); i++)
        {
            String version= agents.get(i).getIagentVersion();
            if(StringUtils.isNotBlank(version)){
                String str = version.substring(0,version.indexOf("/"));
                agents.get(i).setIagentVersion(str);
            }
            String downToPath = agents.get(i).getDownToPath();
            String ftpServer = agents.get(i).getFtpServer();
            String iosName=agents.get(i).getIosName();
            String agentip = agents.get(i).getIagentip();
            int agendamontport = Integer.parseInt(String.valueOf(agents.get(i).getIagentguardport()));
            int agentport = Integer.parseInt(String.valueOf(agents.get(i).getIagentport()));
            if(StringUtils.isBlank(downToPath)){
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "请绑定配置信息"+agentip+":"+agentport);
                return res;
            }
            if(StringUtils.isBlank(iosName)){
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "操作系统名称不能为空" +agentip+":"+agentport);
                return res;
            }

            if(StringUtils.isBlank(ftpServer)){
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "FTP地址不能为空！请绑定后在升级" +agentip+":"+agentport);
                return res;
            }

            if(agendamontport==0){
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "守护进程端口不能为空" + agentip+":"+agentport);
                return res;
            }
        }

        boolean guardflag = false;
        String strMsg="";
        try
        {
            guardflag = ServerEnv.getInstance().getBooleanConfig("is.guard.opt", false);
            if (guardflag)
            {
                String startcmd = null;
                for (int i = 0; i < agents.size(); i++)
                {
                    String downToPath = agents.get(i).getDownToPath();
                    String backupPath = agents.get(i).getBackupPath();
                    String ftpServer = agents.get(i).getFtpServer();
                    int ftpPort = agents.get(i).getFtpPort();
                    String ftpUser = agents.get(i).getFtpUser();
                    String ftpPwd = agents.get(i).getFtpPwd();
                    String iosName=agents.get(i).getIosName();
                    String agentip = agents.get(i).getIagentip();
                    Long iagentport = agents.get(i).getIagentport();
                    int agendamontport = Integer.parseInt(String.valueOf(agents.get(i).getIagentguardport()));
                    int agentport = Integer.parseInt(String.valueOf(agents.get(i).getIagentport()));
                    if ((iosName.toLowerCase().indexOf("windows") > -1))
                    {
                        startcmd = "cmd.exe /c start /b "+upgradeAgentScriptName+".bat "
                                +downToPath+" "+backupPath+" "+ftpServer+" "+ftpPort+" "+ftpUser+" "+ftpPwd+" "+agentip+" "+iagentport;
                    } else
                    {
                        startcmd = "sh "+upgradeAgentScriptName+".sh "
                                +downToPath+" "+backupPath+" "+ftpServer+" "+ftpPort+" "+ftpUser+" "+ftpPwd+" "+agentip+" "+iagentport;
                    }
                    AgentMaintainAct agentMaintainAct = null;
                    try
                    {
                        String adpDefUuid=UUID.randomUUID().toString();
                        agentMaintainAct = manager.createAgentMaintainAct(agents.get(i),
                                "upgrade", adpDefUuid, "",sysType);
                        agentMaintainAct.setUpdateVersion(agents.get(i).getIagentVersion());
                        manager.createAgentMaintainTask(agentMaintainAct,"upgrade");
                        // 第四部发送脚本到守护进程 并执行脚本
                        optAgent(agentip, agentport, agendamontport, sysType, startcmd);
                    } catch (Exception e1)
                    {
                        logger.error(e1);
                        manager.updateAgentMaintainTaskLog(agentMaintainAct.getId(),10,
                                "升级异常，请检查守护进程是否运行状态"+e1.getMessage());
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "升级异常，请检查守护进程是否运行状态"+e1.getMessage());
                        return res;
                    }
                    // 第四部通过线程 进行分流 设置并发大小
                    long iid = agents.get(i).getIid();
                    AgentActivityThread agentAct=new AgentActivityThread(iid,agentMaintainAct.getId(),userName, agentip, agentport, sysType);
                    agentAct.start();
                    res.put(SUCCESS_TEXT, true);
                    res.put(MESSAGE_TEXT, "agent升级线程已发送，2分钟后可查看升级结果");
                }
            }else{
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "请启动守护线程开关，升级需要守护进程为运行状态");
            }
        } catch (Exception e)
        {
            logger.error("startAgentInfo is error", e);
            res.put(SUCCESS_TEXT, false);
            if (guardflag)
            {
                res.put(MESSAGE_TEXT, "操作失败！请检查“守护端口号”,“守护进程”,“操作系统”等！</br>异常信息：" + e.getMessage());
            } else
            {
                res.put(MESSAGE_TEXT, e.getMessage());
            }
        }
        return res;
    }

    public class AgentActivityThread extends Thread{
        //指定线程并发数量
        private  Semaphore msemaphoretryroom = null;
        private String userName;
        private String agentip;
        private String requestID;
        private int agentport;
        private int sysType;
        private Long id;
        //给属性planBean 赋值
        public AgentActivityThread(Long id,String requestID,String userName,String agentip,int agentport, int sysType){
            super("安装Agent线程，IP："+agentip+":"+agentport);
            this.agentip=agentip;
            this.userName=userName;
            this.agentport=agentport;
            this.sysType=sysType;
            this.requestID=requestID;
            this.id=id;
            msemaphoretryroom=new Semaphore(8);
        }
        /**
         * 重写run方法
         */
        @Override
        public void run() {
            Map map=new HashMap();
            try {
                //shoproom可用数量减少
                msemaphoretryroom.acquire();
                //业务代码 先睡眠2分钟 在进行 查看agent 状态
                Thread.sleep(1000*90);
                // 第一步 查看agent 状态 是否启动
                map = connAgent(userName, agentip, agentport, sysType);
                logger.info("agentip=="+ agentport +"  agentport=="+agentport);
                // 第二部通过返回结果查看升级状态
                if((boolean)map.get(SUCCESS_TEXT)){
                    try{
                        synchronized (this) {
                            Long[] ids=new Long[1];
                            ids[0]=id;
                            //获取agent 状态
                            fetchAgentInfoQuery ( ids,sysType,userName, Constants.AGENT_MAINTAIN_MONITOR );
                            Thread.sleep(30*1000);
                            //获取agent新版本号
                            List<Agent>  agents = manager.getAgentsByIds(Arrays.asList(ids), sysType);
                            String version=agents.get(0).getIagentVersion();
                            logger.info("version=="+version);
                            String strVersion="";
                            if(StringUtils.isNotBlank(version)){
                                strVersion = version.substring(0,version.indexOf("/"));
                            }
                            manager.updateAgentMaintainTaskVersionandLog(strVersion,requestID,2,
                                    "升级成功");
                        }
                    }catch (Exception ee){
                        logger.error("agentip"+ agentport +":agentport"+agentport+"Exception"+ee);
                    }
                    //修改升级状态
                }else{
                    manager.updateAgentMaintainTaskLog(requestID,10,
                            "agent不是运行状态，升级失败");
                }
                //释放当前走完线程 启动新的线程
                msemaphoretryroom.release();
            } catch (Exception e) {
                try {
                    manager.updateAgentMaintainTaskLog(requestID,10,
                            "agent不是运行状态"+e.getMessage());
                } catch (RepositoryException repositoryException) {
                    repositoryException.printStackTrace();
                }
                logger.error(e.getMessage());
                logger.error(map.get(MESSAGE_TEXT).toString()+e.getMessage());
            }
        }

    }
    
    public Map saveCibAgentInfoXyyg ( com.alibaba.fastjson.JSONObject resultJsonObject, HttpServletResponse response )
            throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> data = new HashMap<>();
        Iterator<Entry<String, Object>> it = resultJsonObject.entrySet().iterator();
        while (it.hasNext())
        {
            Map.Entry<String, Object> entry = it.next();
            data.put(entry.getKey(), entry.getValue());
        }
        List agentInfoList = (List) data.get("agentInfoList");
        String token = String.valueOf(data.get("token"));
        logger.info("Token is :" + token);
        if (token == null || "".equals(token))
        {
            logger.error("云平台调用接口传递的Token为空！");
            response.setStatus(401);
            res.put(SUCCESS, false);
            res.put(MESSAGE_TEXT, "云平台调用接口传递的Token为空！");
            return res;
        } else
        {
            boolean flag = this.checkToken(token);
            if (!flag)
            {
                logger.error("云平台调用接口传递的Token验证不通过！");
                response.setStatus(401);
                res.put(SUCCESS, false);
                res.put(MESSAGE_TEXT, "云平台调用接口传递的Token验证不通过！");
                return res;
            }
        }
        List Listmakeup = new ArrayList<>();
        List Listbackup = new ArrayList<>();
        List Listmater = new ArrayList<>();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Map<String, Object> connInfo1 = Tools.getConnectionInfo();
        Map<String, Object> connInfo2 = Tools.getConnectionInfo();

        Connection baseConn = (Connection) connInfo.get("baseConn");
        Connection baseConn1 = (Connection) connInfo1.get("baseConn");
        Connection baseConn2 = (Connection) connInfo2.get("baseConn");
        
        List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
        List<Connection> dbConns1 = (List<Connection>) connInfo1.get("dbConns");
        List<Connection> dbConns2 = (List<Connection>) connInfo2.get("dbConns");
        try
        {
            for (int i = 0; i < agentInfoList.size(); i++)
            {
                Map map = (Map) agentInfoList.get(i);
                Map<String, Object> equipment = (Map<String, Object>) map.get("equipment");
                Map agentInfomater = (Map) equipment.get("mater");
                Map agentInfobackup = (Map) equipment.get("backup");
                Map agentInfomakeup = (Map) equipment.get("makeup");

                String dcFlag1=String.valueOf(agentInfomater.get("dcFlag"));
                String dcFlag2=String.valueOf(agentInfobackup.get("dcFlag"));
                String dcFlag3=String.valueOf(agentInfomakeup.get("dcFlag"));
                if(dcFlag1.equals(dcFlag2)||dcFlag1.equals(dcFlag3)||dcFlag3.equals(dcFlag2)) {
                    res.put("success", false);
                    res.put("message", "同一组数据中存在重复dcFlag");
                    return res;
                }
                
                Listmakeup.add(agentInfomakeup);
                Listbackup.add(agentInfobackup);
                Listmater.add(agentInfomater);
            }
            if (null != baseConn)
            {
                Map orgSql = organizeSaveCibAgentInfoSql(Listmakeup, baseConn);
                Map orgSq2 = organizeSaveCibAgentInfoSql(Listbackup, baseConn1);
                Map orgSq3 = organizeSaveCibAgentInfoSql(Listmater, baseConn2);
                List allList = new ArrayList();
                allList.addAll(Listmakeup);
                allList.addAll(Listbackup);
                allList.addAll(Listmater);
             // 校验传入参数agent信息中是否有ip，端口号相同的
                if (!checkParamter(allList,res))
                {
                    res.put(SUCCESS_TEXT, false);
                    return res;
                }
                XyyhReleaseZbProject.getInstance().releaseZbProject(agentInfoList);
//                String row =s.replace("&quot;","\\\"");
//                StringEscapeUtils.unescapeJava(row);
                
//                pm.importProjectInfos("freemarker", row.toString(), Constants.IEAI_DAILY_OPERATIONS);
//                if ((Boolean) orgSql.get(SUCCESS_TEXT))
//                {
                    if (DBUtil.hitDatabase(dbConns1, (List<String>) orgSq2.get("exeSqls"),
                        (List<String>) orgSq2.get("rollbackSqls")))
                    {
                        DBResource.closeConnection(baseConn1, SAVECIBAGENTINFOXYYG, logger);
                        res.put("backup", "backup数据保存成功！");
                    } else
                    {
                        DBResource.closeConnection(baseConn1, SAVECIBAGENTINFOXYYG, logger);
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, ERRORMESSAGEONEBACKUP);
                        return res;
                    }

                    if (DBUtil.hitDatabase(dbConns2, (List<String>) orgSq3.get("exeSqls"),
                        (List<String>) orgSq3.get("rollbackSqls")))
                    {
                        DBResource.closeConnection(baseConn2, SAVECIBAGENTINFOXYYG, logger);
                        res.put("mater", "mater数据保存成功！");
                    }else
                    {
                        DBResource.closeConnection(baseConn2, SAVECIBAGENTINFOXYYG, logger);
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, ERRORMESSAGEONEMATER);
                        return res;
                    }

                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                        (List<String>) orgSql.get("rollbackSqls")))
                    {
                        DBResource.closeConnection(baseConn, SAVECIBAGENTINFOXYYG, logger);

                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "makeup数据保存成功！");
                        return res;
                    } else
                    {
                        DBResource.closeConnection(baseConn, SAVECIBAGENTINFOXYYG, logger);
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, ERRORMESSAGEONEMAKEUP);
                        return res;
                    }
//                } else
//                {
//                    DBResource.closeConnection(baseConn, SAVECIBAGENTINFOXYYG, logger);
//                    for (Connection conn : dbConns)
//                    {
//                        DBResource.closeConnection(conn, SAVECIBAGENTINFOXYYG, logger);
//                    }
//                    res.put(SUCCESS_TEXT, false);
//                    res.put(MESSAGE_TEXT, ERRORMESSAGE);
//                    return res;
//                }
            } else
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                return res;
            }
        } catch (Exception e)
        {
            logger.error("数据保存出错");
        }finally {
            for (Connection conn : dbConns)
            {
                DBResource.closeConnection(conn, SAVECIBAGENTINFOXYYG, logger);
            }
            for (Connection conn : dbConns1)
            {
                DBResource.closeConnection(conn, SAVECIBAGENTINFOXYYG, logger);
            }
            for (Connection conn : dbConns2)
            {
                DBResource.closeConnection(conn, SAVECIBAGENTINFOXYYG, logger);
            }
        }
        
        return res;
    }
    
    public Boolean checkParamter ( List agentInfoList,Map res )
    {
        for (int i = 0; i < agentInfoList.size(); i++)
        {
            Map mapa = (Map) agentInfoList.get(i);
            String agentname=String.valueOf(mapa.get("agentName"));
            if(StringUtils.isEmpty(agentname)) {
                res.put(SUCCESS, false);
                res.put(MESSAGE, "agentName存在空值");
                return false;
            }
            String agentSystem=String.valueOf(mapa.get("agentSystem"));
            if(StringUtils.isEmpty(agentSystem)) {
                res.put(SUCCESS, false);
                res.put(MESSAGE, "agentSystem存在空值");
                return false;
            }
            String hostName=String.valueOf(mapa.get("hostName"));
            if(StringUtils.isEmpty(hostName)) {
                res.put(SUCCESS, false);
                res.put(MESSAGE, "hostName存在空值");
                return false;
            }
            String deploymentMode=String.valueOf(mapa.get("deploymentMode"));
            if(StringUtils.isEmpty(deploymentMode)) {
                res.put(SUCCESS, false);
                res.put(MESSAGE, "deploymentMode存在空值");
                return false;
            }
            String dcFlag=String.valueOf(mapa.get("dcFlag"));
            if(StringUtils.isEmpty(dcFlag)) {
                res.put(SUCCESS, false);
                res.put(MESSAGE, "dcFlag存在空值");
                return false;
            }
            
            for (int j = 0; j < agentInfoList.size(); j++)
            {
                if (i == j)
                {
                    continue;
                }
                Map mapb = (Map) agentInfoList.get(j);
                String ip = String.valueOf(mapa.get("agentIp"));
                String port = String.valueOf(StringUtils.isEmpty(String.valueOf(mapa.get("agentPort"))) ? 15000 : mapa.get("agentPort"));
                mapa.put("agentPort", port);
                String ip1 = String.valueOf(mapb.get("agentIp"));
                String port1 = String.valueOf(StringUtils.isEmpty(String.valueOf(mapb.get("agentPort"))) ? 15000 : mapb.get("agentPort"));
                mapb.put("agentPort", port1);
                if ("".equals(ip) || "".equals(port) || "".equals(ip1) || "".equals(port1))
                {
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "ip或者port存在空值");
                    return false;
                }
                if ((ip + port).equals(ip1 + port1))
                {
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "存在重复ip和port");
                    return false;
                }
            }
        }
        return true;
    }

    private List<InstallResult> getExportAgentInstallResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        String s1 = rname;
        if (rname != null && !"".equals(rname.trim()))
        {
            sqlWhere += " and (lower(a.iagent_name) LIKE ? or a.iagent_ip like ? or a.create_name like ? )";
        }

        if (StringUtils.isNotBlank(ifinish))
        {
            sqlWhere += " and a.ifinish=? ";
        }
        if (StringUtils.isNotBlank(iinstallState))
        {
            sqlWhere += " and a.iinstall_state=? ";
        }


        if (StringUtils.isNotBlank(istarttime))
        {
            sqlWhere += " and a.icreatetime >=? ";
        }

        if (StringUtils.isNotBlank(iendtime))
        {
            sqlWhere += " and a.icreatetime <=? ";
        }

        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc";
        } else
        {
            sql = "select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere ;
        }
        return  manager.getExportAgentInstallResult( istarttime, iendtime,  iinstallState, ifinish,rname, sql, sysType);
    }

    public void exportInstallationResult (HttpServletResponse response, HttpServletRequest request
            , String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int sysType)
    {
        OutputStream out = null;
        List<InstallResult> results =null;
        try
        {
            String fileName = null;
            results=getExportAgentInstallResult(istarttime,iendtime,iinstallState,ifinish,rname,sysType);

            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);

            Workbook wb = new SXSSFWorkbook();
            fileName = dateString + "_安装结果表.xlsx";

            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            Sheet sheet = wb.createSheet("安装结果表");

            Row row = sheet.createRow(0);

            sheet.setDefaultColumnWidth(20);

            sheet.createRow(1);
            sheet.createRow(2);
            sheet.createRow(3);
            sheet.createRow(4);
            sheet.createRow(5);
            sheet.createRow(6);
            sheet.createRow(7);
            sheet.createRow(8);

            CellStyle style = wb.createCellStyle();

            Cell cell = row.createCell(0);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(1);
            cell.setCellStyle(style);
            cell.setCellValue("名称");

            cell = row.createCell(2);
            cell.setCellStyle(style);
            cell.setCellValue("地址");

            cell = row.createCell(3);
            cell.setCellStyle(style);
            cell.setCellValue("端口号");

            cell = row.createCell(4);
            cell.setCellStyle(style);
            cell.setCellValue("安装结果");

            cell = row.createCell(5);
            cell.setCellStyle(style);
            cell.setCellValue("安装进度");

            cell = row.createCell(6);
            cell.setCellStyle(style);
            cell.setCellValue("安装信息");

            cell = row.createCell(7);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");

            cell = row.createCell(8);
            cell.setCellStyle(style);
            cell.setCellValue("创建时间");


            if (results != null)
            {

                for (int i = 0; i < results.size(); i++)
                {
                    Row row1 = sheet.createRow(i + 1);
                    InstallResult model = (InstallResult) results.get(i);
                    row1.createCell(0).setCellValue(i + 1);
                    row1.createCell(1).setCellValue(model.getIagent_name());
                    row1.createCell(2).setCellValue(model.getIagent_ip());
                    row1.createCell(3).setCellValue(model.getIagent_port());
                    String str="";
                    if (model.getIinstall_state()==0){
                        str="成功";
                    }
                    if (model.getIinstall_state()==1){
                        str="失败";
                    }
                    if (model.getIinstall_state()==2){
                        str="超时";
                    }
                    String strFinish="";
                    if(model.getIfinish()==1){
                        strFinish="完成";
                    }
                    if(model.getIfinish()==2){
                        strFinish="待安装";
                    }
                    if(model.getIfinish()==0){
                        strFinish="安装中";
                    }
                    row1.createCell(4).setCellValue(str);
                    row1.createCell(5).setCellValue(strFinish);
                    row1.createCell(6).setCellValue(model.getIinstall_msg());
                    row1.createCell(7).setCellValue(model.getCreateName());
                    row1.createCell(8).setCellValue(model.getIcreatetime());
                }
            }
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e)
        {
            logger.error("exportInstallationResult", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportInstallationResult", e);
                }
            }
        }
    }


    public void exportUnloadResult (HttpServletResponse response, HttpServletRequest request
            , String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int sysType)
    {
        OutputStream out = null;
        List<InstallResult> results =null;
        try
        {
            String fileName = null;
            results=getExportAgentUnloadResult(istarttime,iendtime,iinstallState,ifinish,rname,sysType);

            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);

            Workbook wb = new SXSSFWorkbook();
            fileName = dateString + "_卸载结果表.xlsx";

            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            Sheet sheet = wb.createSheet("卸载结果表");

            Row row = sheet.createRow(0);

            sheet.setDefaultColumnWidth(20);

            sheet.createRow(1);
            sheet.createRow(2);
            sheet.createRow(3);
            sheet.createRow(4);
            sheet.createRow(5);
            sheet.createRow(6);
            sheet.createRow(7);
            sheet.createRow(8);

            CellStyle style = wb.createCellStyle();

            Cell cell = row.createCell(0);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(1);
            cell.setCellStyle(style);
            cell.setCellValue("名称");

            cell = row.createCell(2);
            cell.setCellStyle(style);
            cell.setCellValue("地址");

            cell = row.createCell(3);
            cell.setCellStyle(style);
            cell.setCellValue("端口号");

            cell = row.createCell(4);
            cell.setCellStyle(style);
            cell.setCellValue("卸载结果");

            cell = row.createCell(5);
            cell.setCellStyle(style);
            cell.setCellValue("卸载进度");

            cell = row.createCell(6);
            cell.setCellStyle(style);
            cell.setCellValue("卸载信息");

            cell = row.createCell(7);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");

            cell = row.createCell(8);
            cell.setCellStyle(style);
            cell.setCellValue("创建时间");


            if (results != null)
            {

                for (int i = 0; i < results.size(); i++)
                {
                    Row row1 = sheet.createRow(i + 1);
                    InstallResult model = (InstallResult) results.get(i);
                    row1.createCell(0).setCellValue(i + 1);
                    row1.createCell(1).setCellValue(model.getIagent_name());
                    row1.createCell(2).setCellValue(model.getIagent_ip());
                    row1.createCell(3).setCellValue(model.getIagent_port());
                    String str="";
                    if (model.getIinstall_state()==0){
                        str="成功";
                    }
                    if (model.getIinstall_state()==1){
                        str="失败";
                    }
                    if (model.getIinstall_state()==2){
                        str="超时";
                    }
                    String strFinish="";
                    if(model.getIfinish()==1){
                        strFinish="完成";
                    }
                    if(model.getIfinish()==2){
                        strFinish="待卸载";
                    }
                    if(model.getIfinish()==0){
                        strFinish="卸载中";
                    }
                    row1.createCell(4).setCellValue(str);
                    row1.createCell(5).setCellValue(strFinish);
                    row1.createCell(6).setCellValue(model.getIinstall_msg());
                    row1.createCell(7).setCellValue(model.getCreateName());
                    row1.createCell(8).setCellValue(model.getIcreatetime());
                }
            }
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e)
        {
            logger.error("exportUnloadResult", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportUnloadResult", e);
                }
            }
        }
    }


    private List<InstallResult> getExportAgentUnloadResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        String s1 = rname;
        if (rname != null && !"".equals(rname.trim()))
        {
            sqlWhere += " and (lower(a.iagent_name) LIKE ? or a.iagent_ip like ? or a.create_name like ? )";
        }

        if (StringUtils.isNotBlank(ifinish))
        {
            sqlWhere += " and a.ifinish=? ";
        }
        if (StringUtils.isNotBlank(iinstallState))
        {
            sqlWhere += " and a.iinstall_state=? ";
        }


        if (StringUtils.isNotBlank(istarttime))
        {
            sqlWhere += " and a.icreatetime >=? ";
        }

        if (StringUtils.isNotBlank(iendtime))
        {
            sqlWhere += " and a.icreatetime <=? ";
        }

        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc";
        } else
        {
            sql = "select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere ;
        }
        return  manager.getExportAgentInstallResult(istarttime,iendtime,iinstallState,ifinish,rname, sql, sysType);
    }


    public Map<String, Object> getAgentUnloadResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int start, int limit, int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        List<Map<String, Object>> datalist = null;
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        int li = 0;
        String s1 = rname;
        if (rname != null && !"".equals(rname.trim()))
        {
            sqlWhere += " and (lower(a.iagent_name) LIKE ? or a.iagent_ip like ? or a.create_name like ? )";
        }

        if (StringUtils.isNotBlank(ifinish))
        {
            sqlWhere += " and a.ifinish=? ";
        }
        if (StringUtils.isNotBlank(iinstallState))
        {
            sqlWhere += " and a.iinstall_state=? ";
        }


        if (StringUtils.isNotBlank(istarttime))
        {
            sqlWhere += " and a.icreatetime >=? ";
        }

        if (StringUtils.isNotBlank(iendtime))
        {
            sqlWhere += " and a.icreatetime <=? ";
        }

        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc limit ?,?";
            li = limit;
        } else
        {
            sql = "select * from (select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere + ") where ro>? and ro<=?";
            li = start + limit;
        }
        //datalist = manager.getAgentInstallResult(rname, sql, start, li, sysType);
        datalist = manager.getAgentInstallResult (  istarttime, iendtime,  iinstallState, ifinish, rname,  sql,  start,  li,  sysType );


        String sqlCount = " SELECT COUNT(*) AS NUM FROM ieai_agentunload_result a where 1=1 " + sqlWhere;
        //int total = manager.getCount(sqlCount, sysType);
        int total = manager.getCount ( istarttime, iendtime,  iinstallState, ifinish, rname, sqlCount,  sysType ) ;
        res.put("dataList", datalist);
        res.put("total", total);
        return res;
    }

    public Map<String, Object>  getAgentInstallResult (long iid,int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        List<Map<String, Object>> datalist = null;
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        int limit = 30;
        int start =0;
        int li =30;
        if (iid>0)
        {
            sqlWhere += " and a.iid="+iid;
        }
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc limit ?,?";
            li = limit;
        } else
        {
            sql = "select * from (select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentinstall_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere + ") where ro>? and ro<=?";
            li = start + limit;
        }
        datalist = manager.getAgentInstallResult("", sql, start, li, sysType);
        res.put("resList",datalist);
        return res;
    }


    public Map<String, Object>  getUploadResult (long iid,int sysType )
            throws EswitchException, RepositoryException
    {
        String sql = "";
        List<Map<String, Object>> datalist = null;
        Map<String, Object> res = new HashMap<String, Object>();
        String sqlWhere = "";
        int limit = 30;
        int start =0;
        int li =30;
        if (iid>0)
        {
            sqlWhere += " and a.iid="+iid;
        }
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime,b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result  a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  WHERE 1=1 "
                    + sqlWhere + " order by ifinish,icreatetime desc,iinstall_state desc limit ?,?";
            li = limit;
        } else
        {
            sql = "select * from (select row_number() over (order by a.icreatetime desc,a.ifinish desc,a.iinstall_state desc) AS ro,a.iid,a.iagent_name,a.iagent_ip,a.iagent_port,a.ifinish ,a.iinstall_state,a.iinstall_msg,a.icreatetime, b.isoncloud,a.ioperation_user,a.create_name from ieai_agentunload_result a left join ieai_agentinfo b on  a.iagent_ip=b.iagent_ip and a.iagent_port=b.iagent_port  where 1=1 "
                    + sqlWhere + ") where ro>? and ro<=?";
            li = start + limit;
        }
        datalist = manager.getAgentInstallResult("", sql, start, li, sysType);
        res.put("resList",datalist);
        return res;
    }
    
    
    public Map startNetScan ( String jsonData, int sysType, HttpServletRequest request ) throws Exception
    {
        Map map=new HashMap();
        List<Map> mapList = new ArrayList<Map>();

        //String paramOne = ParameterConfigManager.getInstance().getParamInfo("netscan_param_one");
       // String paramOne=" nmap -O -PR -T5 --system-dns ";
        String paramOne=" nmap -O -T5 --system-dns ";
        // String paramTwo=ParameterConfigManager.getInstance().getParamInfo("netscan_param_two");
        // String
        // paramThree=ParameterConfigManager.getInstance().getParamInfo("netscan_param_three");
        String defHome; // default value
        if (System.getProperty("os.name").indexOf("Windows") >= 0)
        {
            defHome = "c:/ieai"; // only default
        } else
        {
            defHome = System.getProperty("user.home") + "/ieai";
        }
        String ieai_home = System.getProperty("IEAI_HOME", defHome); // really getting
        JSONArray jsonArr = JSONArray.fromObject(jsonData);
        Map allAgentMap=getAllAgentMap();
        for (int i = 0; i < jsonArr.size(); i++)
        {
            String netString = jsonArr.getJSONObject(i).optString("isrcpath");
            String port = jsonArr.getJSONObject(i).optString("iagentport");
            int iport=Integer.parseInt(port);
            //String lz="sh /opt/Entegor_Server/netscan.sh nmap -O -PR -T5 --system-dns *************-254 -oX /opt/Entegor_Server/*************-254.xml";
            String cmd = paramOne + " " + netString + " -oX " + ieai_home + File.separator + netString + ".xml";
            //logger.info("startNetScan " + cmd);
            // long start=System.currentTimeMillis();
            // System.out.println("startTime=========="+start);
            // Process process =Runtime.getRuntime().exec(cmd);
            // process.waitFor();
            // runNetScanSh(cmd);
            //logger.info("sh " + ieai_home + File.separator + "netscan.sh " + cmd);
            long execCmdStart = System.currentTimeMillis();
            logger.info("execCmdStart================" + execCmdStart);
            if (System.getProperty("os.name").indexOf("Windows") >= 0)
            {
                execCmd2(ieai_home + File.separator+"baseScript"+ File.separator + "netscan.bat " + cmd);
                logger.info(ieai_home + File.separator +"baseScript"+ File.separator+ "netscan.bat " + cmd);
              map.put(SUCCESS, false);
              map.put(MESSAGE, "暂不支持windows系统！");
              return map;
            } else
            {
                execCmd2("sh " + ieai_home + File.separator+"baseScript"+ File.separator + "netscan.sh " + cmd);
                logger.info("sh " + ieai_home + File.separator+"baseScript"+ File.separator + "netscan.sh " + cmd);

            }
            //execCmd2("sh " + ieai_home + File.separator + "netscan.sh " + cmd);
            
            long execCmdEnd = System.currentTimeMillis();
            logger.info(
                "execCmdEnd================" + execCmdEnd + "  and execCmdtime===== " + (execCmdEnd - execCmdStart));
                       xmlinfo(ieai_home + File.separator + netString + ".xml",iport ,mapList,allAgentMap, request);
            long xmlEnd = System.currentTimeMillis();
            logger.info("xmlinfoEnd================" + xmlEnd + "  and xmltime===== " + (xmlEnd - execCmdEnd));

        }
        long saveAgentInfoStart = System.currentTimeMillis();
        map.put("dataList", mapList);
        map.put(SUCCESS, true);
        map.put(MESSAGE, "扫描成功！");
        return map;
    }
    
    private List xmlinfo ( String filename,int port, List<Map> reList,Map allAgentMap, HttpServletRequest request )
            throws  FileNotFoundException, UnsupportedEncodingException,
            DocumentException
    {  
        // String filename = "E:\\temp\\202207-poc\\3.xml";
        Reader inReader = null;

        SAXReader reader = new SAXReader();
        File file = new File(filename);
        List<Map<String, String>> sysList = new ArrayList<Map<String, String>>();
        // try {
        inReader = new InputStreamReader(new FileInputStream(file), "UTF-8");
        Document doc = reader.read(inReader);
        // 获得根元素
        Element rootElement = doc.getRootElement();
        // 获得子元素
        List<Element> hostList = rootElement.elements("host");
        //System.out.println("hostList-->" + hostList.size());
        for (Element element : hostList)
        {
            Map map=new HashMap();
            // try {
            List<Element> addressList = element.elements("address");
            System.out.println("addressList-->" + addressList.size());
            String addr = "";
            if (addressList != null && addressList.size() > 0)
            {
                addr = addressList.get(0).attributeValue("addr");
                //System.out.println("addr-->" + addr);
                map.put("iip", addr);
            }

            //AgentDomainManager agentDomainManager = new AgentDomainManager();
 //           long iid = agentDomainManager.getAgentInfoId(addr);
            List<Element> osList = element.element("os").elements("osmatch");
            String osmatch_name = "";
            if (osList != null && osList.size() > 0)
            {
                Element osmatch = osList.get(0);
                osmatch_name = osmatch.attributeValue("name");
                //System.out.println("osmatch-->" + osmatch_name);
//                if(osmatch_name!=null && osmatch_name.contains("Linux")) {
//                    osmatch_name="Linux";
//                      
//                }
                map.put("iosmatch", osmatch_name); 
                
            }
            
   
            if(allAgentMap.get(addr)!=null) {
                map.put("isexist", true);                 
            }else {
                boolean connAgentFlag=getAgentVersion(addr, port);
                map.put("isexist", connAgentFlag); 
                map.put("iport", port);
            }
            reList.add(map);
            
            // Element osmatch = element.element("os").element("osmatch");
            // String osmatch_name=osmatch.attributeValue("name");
//            System.out.println("osmatch-->" + osmatch_name);
        }

        boolean flag = file.delete();
        if (flag)
        {
            logger.info("文件删除成功!");
        } else
        {
            logger.info("文件不存在!");
        }
        return reList;
    }
    
    public void execCmd2 ( String cmd )
    {
        InputStream stdin = null;
        BufferedReader stdReader = null;
        String line = null;

        try
        {
            logger.info("execCmd2-1->" + System.currentTimeMillis());
            // 查询cpu及内存
            Process _execProcess = Runtime.getRuntime().exec(cmd);
            stdin = _execProcess.getInputStream();
            // stdReader = new BufferedReader(new InputStreamReader(stdin));
            logger.info("execCmd2-2->" + System.currentTimeMillis());
            stdin.close();
            logger.info("execCmd2-3->" + System.currentTimeMillis());
            _execProcess.waitFor();
            logger.info("execCmd2-4->" + System.currentTimeMillis());
        } catch (IOException e)
        {
            e.printStackTrace();
            logger.error("execCmd2 error:" + e);
        } catch (InterruptedException e1)
        {
            e1.printStackTrace();
        }
    }

    private  boolean getAgentVersion(String ip,int port) {
        boolean flag=false;
        
        try
        {
            XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, 0);
            //rpcClient.execute("IEAIAgent.executeAct", new Vector());
            String version=(String)rpcClient.execute("IEAIAgent.getVersion", new Vector());
            
            flag=true;
        } catch (Exception e)
        {
            flag=false;
        }
        
        return flag;
    }
    
    private Map<String,Long> getAllAgentMap ()
    {
        Connection conn = null;
        String sqlWhere ="";
        Map<String,Long> map=new HashMap<>();

        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select a.iagentinfo_id,a.iagent_ip from ieai_agentinfo a where 1=1 "
                    + sqlWhere + " order by a.iagentinfo_id desc ";
        } else
        {
            sqlList = " select a.iagentinfo_id,a.iagent_ip from ieai_agentinfo a where 1=1 "
                    + sqlWhere + " order by a.iagentinfo_id desc";
        }

        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            map = manager.getAllAgentMap(conn, sqlList);
        } catch (Exception e)
        {
            logger.error("CollectSystemService.getAllAgentMap is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getAllAgentMap", logger);
        }
        return map;
    }
    
    public Map exportAllNetScan (HttpServletResponse response, HttpServletRequest request, String jsonData )
    {
        OutputStream out = null;
        Map<String,Object> map =new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        list = (List<Map<String, Object>>) JSONArray.toCollection(
                JSONArray.fromObject(jsonData), HashMap.class);
        
        try
        {
            String fileName = null;

            


            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);

            Workbook wb = new SXSSFWorkbook();
            fileName = dateString + "_网段扫描.xlsx";

            response.addHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("GB2312"), StandardCharsets.ISO_8859_1));

            Sheet sheet = wb.createSheet("网段扫描结果");

            Row row = sheet.createRow(0);

            sheet.setDefaultColumnWidth(20);

            sheet.createRow(1);
            sheet.createRow(2);
            sheet.createRow(3);
            sheet.createRow(4);

            CellStyle style = wb.createCellStyle();

            Cell cell = row.createCell(0);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(1);
            cell.setCellStyle(style);
            cell.setCellValue("ip");

//            cell = row.createCell(2);
//            cell.setCellStyle(style);
//            cell.setCellValue("操作系统类型");

            cell = row.createCell(2);
            cell.setCellStyle(style);
            cell.setCellValue("是否存在Agent");

            

            if (list != null)
            {

                for (int i = 0; i < list.size(); i++)
                {
                    Row row1 = sheet.createRow(i + 1);
                    Map model = (Map) list.get(i);
                    row1.createCell(0).setCellValue(i + 1);
                    row1.createCell(1).setCellValue((String)model.get("iip"));
                    //row1.createCell(2).setCellValue((int)model.get("iport"));
                    //row1.createCell(2).setCellValue(model.get("iosmatch")==null?"":(String)model.get("iosmatch"));
                    row1.createCell(2).setCellValue((boolean)model.get("isexist")?"是":"否");                
                }
            }

            out = response.getOutputStream();
            wb.write(out);

        } catch (Exception e)
        {
            logger.error("exportAllNetScan", e);
        } finally
        {
            if (null != out)
            {
                try
                {
                    out.close();
                } catch (IOException e)
                {
                    logger.error("exportAllNetScan", e);
                }
            }
        }
        return map;
    }

    public Map stopAgentInfo ( int sysType, String userName ) throws Exception
    {
        Map map = new HashMap();
        List<Agent> agents = manager.getAllAgentMaintainList(false, sysType); // 正在升级的agent不获取agent信息
        List<Long> list =new ArrayList<>();
        for (Agent agent : agents) {
            String ip = agent.getIagentip();
            int port = Integer.parseInt(agent.getIagentport() + "");
            Vector params = new Vector();
            SM4Utils sm4 = new SM4Utils();
            sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
            String token = sm4.encryptData_ECB("ideal.info");
            params.add("stopAgent");
            params.add(token);
            params.add("");
            try {
                Hashtable table = new Hashtable();
                boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                        Environment.FALSE_BOOLEAN);
                int ssltype = RpcSslType.getInstance().exec(ip, port);

                if (proxySwitch) {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    ProxyModel pm = ps.getPerformDataJsonData(table, ip, port, Constants.IEAI_DATA_COLLECT);
                    ip = pm.getIp();
                    port = pm.getPort();
                    ssltype = pm.getSsl();
                }
                params.add(table);
                XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, ssltype);
                Object execute = rpcClient.execute("IEAIAgent.taskOperation", params);
                list.add(agent.getIid());
                if (list.size()>100){
                    logger.info("list size  ="+list.size());
                    Long[] ids = list.stream().toArray(Long[]::new);
                    manager.editAgentstate(ids);
                    list.clear();
                    logger.info("list size clear ="+list.size());
                }
            } catch (Exception e) {
                list.add(agent.getIid());
                map.put(Constants.STR_SUCCESS, false);
                map.put(Constants.STR_MESSAGE, e.getLocalizedMessage());
            }
        }
        Long[] ids = list.stream().toArray(Long[]::new);
        logger.info("list size  ="+list.size());
        manager.editAgentstate(ids);
        map.put(Constants.STR_SUCCESS, true);
        map.put(Constants.STR_MESSAGE, "操作完成");
        return map;
    }

    public Map stopAgentInfoQuery ( Long[] ids, int sysType) throws Exception
    {
        Map map = new HashMap();
        List<Agent> agents = manager.getAgentMaintainList(ids, false, sysType); // 正在升级的agent不获取agent信息
        for (Agent agent : agents) {
            String ip = agent.getIagentip();
            int port = Integer.parseInt(agent.getIagentport() + "");
            Vector params = new Vector();
            SM4Utils sm4 = new SM4Utils();
            sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
            String token = sm4.encryptData_ECB("ideal.info");
            params.add("stopAgent");
            params.add(token);
            params.add("");
            try {
                Hashtable table = new Hashtable();
                boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                        Environment.FALSE_BOOLEAN);
                int ssltype = RpcSslType.getInstance().exec(ip, port);

                if (proxySwitch) {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    ProxyModel pm = ps.getPerformDataJsonData(table, ip, port, Constants.IEAI_DATA_COLLECT);
                    ip = pm.getIp();
                    port = pm.getPort();
                    ssltype = pm.getSsl();
                }
                params.add(table);
                XmlRpcClient rpcClient = new AgentXmlRpcClient(ip, port, ssltype);
                rpcClient.execute("IEAIAgent.taskOperation", params);
            } catch (Exception e) {
                logger.error("stopAgentInfoQuery = "+e.getMessage());
            }
        }
        manager.editAgentstate(ids);
        map.put(Constants.STR_SUCCESS, true);
        map.put(Constants.STR_MESSAGE, "操作完成");
        return map;
    }

    /**
     * @Title: getAgentBeanList
     * @Description: 查询简单AGent信息列表
     * @param start
     * @param limit
     * @param queryString
     * @param sysType
     * @return
     * @author: xinglintian
     * @date:   2019年12月23日 下午3:12:31
     */
    public Map getAgentBeanList ( Long userId, Integer start, Integer limit, String queryString, String iagentName,
                                  String iagentIp,
                                  String iagentState, String isystemname, int sysType )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        AgentMaintainManager manage = AgentMaintainManager.getInstance();
        String allSql = "SELECT COUNT(1) FROM IEAI_USERINHERIT T,"
                + " IEAI_SYS_PERMISSION T2 WHERE T.IROLEID = T2.IROLEID "
                + "AND T2.IPERMISSION = 1 AND T.IUSERID = ? AND T2.IPROID = -1";
        StringBuilder sqlWhere = new StringBuilder("");
        getAgentBeanListWhere(iagentName, iagentIp, iagentState, isystemname, sqlWhere);
        String sqlList = "";
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            boolean isAll = manage.getAllPermission(conn, userId, allSql);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqlList = "SELECT A.IAGENTINFO_ID,A.IAGENT_NAME,A.ISYSTEMNAME,A.IAGENT_IP,A.IAGENT_PORT,A.IOS_NAME,A.IAGENT_ACTIVITY_NUM,A.IAGENT_STATE  FROM IEAI_AGENTINFO A WHERE 1=1 "
                        + sqlWhere + " ORDER BY A.IAGENTINFO_ID LIMIT ?,?";
            } else
            {
                sqlList = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY A.IAGENTINFO_ID DESC) AS RO,A.IAGENT_NAME,A.IAGENTINFO_ID,A.ISYSTEMNAME,A.IAGENT_IP,A.IAGENT_PORT,A.IOS_NAME,A.IAGENT_ACTIVITY_NUM,A.IAGENT_STATE  FROM IEAI_AGENTINFO A WHERE 1=1 "
                        + sqlWhere + ") WHERE RO>? AND RO<=?";
            }
            String sqlCount = "SELECT COUNT(A.IAGENTINFO_ID) AS COUNTNUM FROM IEAI_AGENTINFO A WHERE 1=1 " + sqlWhere;
            if (!isAll)
            {
                String perSqlString = ",(SELECT MAX(IID) AS IID, IPRJ.ISYSNAME FROM IEAI_PROJECT IPRJ, (SELECT PRJ.INAME, IPROID FROM IEAI_USERINHERIT UR, IEAI_ROLE R, IEAI_SYS_PERMISSION SP, IEAI_PROJECT PRJ WHERE UR.IROLEID = R.IID AND R.IID = SP.IROLEID AND UR.IUSERID = ? AND SP.IPERMISSION = 1 AND SP.IPROID = PRJ.IID AND (PRJ.PROTYPE = 1 OR PRJ.PROTYPE = -1)) QX WHERE (IPRJ.IPKGCONTENTID <> 0 or IPRJ.IPKGCONTENTID is null) AND IPRJ.IFREEZED = 0 AND IPRJ.ILATESTID = IPRJ.IID AND (QX.INAME = IPRJ.INAME OR - QX.IPROID = IPRJ.PROTYPE) AND IID != -1 AND IPRJ.IGROUPID = 1 GROUP BY IPRJ.ISYSNAME ORDER BY IPRJ.ISYSNAME) K";
                if (JudgeDB.IEAI_DB_TYPE == 3)
                {
                    sqlList = "SELECT A.IAGENTINFO_ID,A.IAGENT_NAME,A.ISYSTEMNAME,A.IAGENT_IP,A.IAGENT_PORT,A.IOS_NAME,A.IAGENT_ACTIVITY_NUM,A.IAGENT_STATE  FROM IEAI_AGENTINFO A"
                            + perSqlString + "  WHERE K.ISYSNAME=A.ISYSTEMNAME " + sqlWhere
                            + " ORDER BY A.IAGENTINFO_ID LIMIT ?,?";
                } else
                {
                    sqlList = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY A.IAGENTINFO_ID DESC) AS RO,A.IAGENTINFO_ID,A.IAGENT_NAME,A.ISYSTEMNAME,A.IAGENT_IP,A.IAGENT_PORT,A.IOS_NAME,A.IAGENT_ACTIVITY_NUM,A.IAGENT_STATE  FROM IEAI_AGENTINFO A "
                            + perSqlString + "  WHERE K.ISYSNAME=A.ISYSTEMNAME " +
                            sqlWhere + ") WHERE RO>? AND RO<=?";
                }
                sqlCount = "SELECT COUNT(A.IAGENTINFO_ID) AS COUNTNUM FROM IEAI_AGENTINFO A  " + perSqlString
                        + "  WHERE K.ISYSNAME=A.ISYSTEMNAME " + sqlWhere;
            }
            List list = manage.getAgentBeanList(conn, sqlList, start, limit, isAll, userId);
            int count = manage.getAgentBeanCount(conn, sqlCount, isAll, userId);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            logger.error("method is error!", e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return map;
    }

    /**
     * @Title: getAgentBeanListWhere
     * @Description: 查询简单AGent信息列表拼写查询条件
     * @param iagentName
     * @param iagentIp
     * @param iagentState
     * @param isystemname
     * @param sqlWhere
     * @author: xinglintian
     * @date:   2019年12月23日 下午3:23:22
     */
    private void getAgentBeanListWhere ( String iagentName, String iagentIp, String iagentState, String isystemname,
                                         StringBuilder sqlWhere )
    {
        if (org.apache.commons.lang3.StringUtils.isNoneEmpty(iagentName))
        {
            sqlWhere.append(" AND A.IAGENT_NAME LIKE '%").append(iagentName).append("%' ");
        }
        if (org.apache.commons.lang3.StringUtils.isNoneEmpty(iagentIp))
        {
            sqlWhere.append(" AND A.IAGENT_IP LIKE '%").append(iagentIp).append("%' ");
        }
        if (org.apache.commons.lang3.StringUtils.isNoneEmpty(isystemname))
        {
            sqlWhere.append(" AND A.ISYSTEMNAME LIKE '%").append(isystemname).append("%' ");
        }
        if (org.apache.commons.lang3.StringUtils.isNoneEmpty(iagentState))
        {
            sqlWhere.append(" AND A.IAGENT_STATE =").append(iagentState).append(" ");
        }
    }

    public Map<String, Object> banAgent(Long agentId,String agentOpt) {
        int statuse=3;
        if("ban".equals(agentOpt)) {
            statuse=7;
        }else if("use".equals(agentOpt)) {
            statuse=0;
        }

        return manager.updateBanAgent(agentId,statuse);
    }


    public void startInstallAgent (Vector resp, long agentId, int sysType, String userName,String ioperationuser,String ioperationpassword ,String isource_path)
    {
        Map res = new HashMap();

        String agentServiceName = "EntegorAgentServer";
        List<AgentOpModel> list = new ArrayList<AgentOpModel>();
        boolean guardflag = false;
        String  agentip="";
        try
        {

            boolean flag = AgentMaintainManager.getInstance().checkAgentCfg(agentId, sysType);
            if (flag)
            {
                list = AgentMaintainManager.getInstance().getAgentInfoOp(agentId, sysType);
                for (int i = 0; i < list.size(); i++)
                {
                    AgentOpModel aom = new AgentOpModel();
                    aom = list.get(i);
                    if(StringUtils.isNotBlank(ioperationuser)){
                        aom.setiAgentUser(ioperationuser);
                    }
                    if(StringUtils.isNotBlank(ioperationpassword)){
                        aom.setiAgentPasswd(ioperationpassword);
                    }
                    agentip = aom.getiAgentIp();
                    int agentport = Integer.parseInt(String.valueOf(aom.getiAgentPort()));
                    // 针对windows系统采用net用服务形式启动Agent
                    if ((aom.getiAgentOs().toLowerCase().indexOf("windows") > -1))
                    {

                        AgentState state = new AgentState();
//                            Map map = state.runbat("net use \\\\" + aom.getiAgentIp() + "\\ipc$ "
//                                    + aom.getiAgentPasswd() + " /user:" + aom.getiAgentUser());
//                            if (Integer.parseInt(map.get("exitValue").toString()) == 0)
//                            {
//                                state.runbat("sc \\\\" + aom.getiAgentIp() + " start " + agentServiceName);
//                                state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
//                            } else
//                            {
//                                state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
//                                res.put(SUCCESS_TEXT, false);
//                                res.put(MESSAGE_TEXT, map.get(MESSAGE_TEXT).toString());
//                                return res;
//                            }
//                            state.runbat(isource_path);

                        Map map = state.runbat("net use \\\\" + aom.getiAgentIp() + "\\ipc$ "
                                + aom.getiAgentPasswd() + " /user:" + aom.getiAgentUser());
                        if (Integer.parseInt(map.get("exitValue").toString()) == 0)
                        {
                            state.runbat(isource_path);
                            state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
                            res.put("ip",aom.getiAgentIp());
                            res.put(SUCCESS_TEXT, true);
                            // res.put("errMsg", map.get(MESSAGE_TEXT).toString());
                            resp.add(res);
                        } else
                        {
                            state.runbat("net use \\\\" + aom.getiAgentIp() + " /del");
                            res.put("ip",aom.getiAgentIp());
                            res.put(SUCCESS_TEXT, false);
                            res.put("errMsg", map.get(MESSAGE_TEXT).toString());
                            resp.add(res);
                        }
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "操作成功");
                        // 针对aix系统采用net用服务形式启动Agent
                    } else if (aom.getiConnType() == 1)
                    {
                        TelnetUtils telnet = new TelnetUtils(aom.getiAgentOs());
                        telnet.login(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                aom.getiAgentPasswd());
//                            telnet.sendCommand("cd " + aom.getiAgentPath());
//                            telnet.sendCommand(" nohup ./Agent_Server >nohup.out 2>&1 & ");

                        telnet.sendCommand(isource_path);
                        res.put("ip",aom.getiAgentIp());
                        res.put(SUCCESS_TEXT, true);
                        res.put("errMsg", "操作成功");
                        //   res.put("errMsg", map.get(MESSAGE_TEXT).toString());
                        resp.add(res);
                        Thread.sleep(2000);
                        telnet.disconnect();
                        // 针对Linux系统采用net用服务形式启动Agent
                    } else if (aom.getiConnType() == 2)
                    {
                        try
                        {
                            SSHUtils ssh = new SSHUtils(aom.getiAgentIp(), aom.getiConnPort(), aom.getiAgentUser(),
                                    aom.getiAgentPasswd());
//                                ssh.execNewCmd(
//                                    "cd " + aom.getiAgentPath() + ";" + " nohup ./Agent_Server >nohup.out 2>&1 & ");
                            ssh.execNewCmd(isource_path);
                            res.put("ip",aom.getiAgentIp());
                            res.put(SUCCESS_TEXT, true);
                            // res.put("errMsg", map.get(MESSAGE_TEXT).toString());
                            resp.add(res);
                            Thread.sleep(2000);
                            ssh.closeSession();
                        } catch (Exception e)
                        {
                            logger.error("startInstallAgent is error", e);
                            res.put(SUCCESS_TEXT, false);
                            res.put("errMsg", "SSH连接失败！");
                            res.put("ip",aom.getiAgentIp());

                            //res.put("errMsg", map.get(MESSAGE_TEXT).toString());
                            resp.add(res);
                        }
                    }
                    // res = connAgent(userName, agentip, agentport, sysType);
                }
            } else
            {

                res.put(SUCCESS_TEXT, false);
                res.put("errMsg", "Agent没有配置信息！");
                res.put("ip",agentip);

                resp.add(res);
            }


        } catch (Exception e)
        {
            logger.error("startInstallAgent is error", e);
            res.put(SUCCESS_TEXT, false);
            //res.put(MESSAGE_TEXT, e.getMessage());

            res.put(SUCCESS_TEXT, false);
            res.put("errMsg", "e.getMessage()");
            res.put("ip",agentip);
            resp.add(res);

        }

    }

    public Map getEnvTypeByAgentId(long agentId) {
        return AgentMaintainManager.getInstance().getEnvTypeByAgentId(agentId);
    }

    public void saveAgentEnvsMultiple(String jsonData, Long agentId) throws Exception {
        List<Map<String, Object>> envTypes;
        Agent agentInfo = new Agent();
        try
        {
            envTypes = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            logger.error(" saveAgentEnvsMultiple failed");
            throw new ServiceException(e);
        }

        try
        {
            agentInfo = this.getAgentMaintainInfoById(agentId);
            Map<String, Object> connInfo = com.ideal.ieai.server.util.Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

            if (null != baseConn&&null!=agentInfo)
            { // 没有基线源，不可以
                Map orgSql = AgentMaintainManager.getInstance().saveEnvTypeByAgentSql(baseConn, envTypes, Constants.RESOURCE_SUS,agentInfo);
                if ((Boolean) orgSql.get("success"))
                {
                    DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"), (List<String>) orgSql.get("rollbackSqls"));
                }
            }
        } catch (Exception e1)
        {
            throw e1;
        }
    }

    public boolean deleteAgentEnvs(String deleteIds) {
        return AgentMaintainManager.getInstance().deleteAgentEnvs(deleteIds);
    }

    private static final Map<String, String> ENVMAP = new LinkedHashMap<String, String>();
    static
    {
        ENVMAP.put("地址", "name");
        ENVMAP.put("端口号", "inumber");
        ENVMAP.put("环境类型", "name2");
    }

    /**
     *
     * @Title: exportIEnvTypeExcel
     * @Description:福建农信Agent管理导出环境类型
     * @param response
     * @author: liu_yang
     * @date:   2024年4月12日 上午10:21:55
     */
    public void exportIEnvTypeExcel ( HttpServletResponse response, String ids, int itype )
    {
        response.setContentType("application/vnd.ms-excel; GBK");
        response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".xls");
        response.setCharacterEncoding(UTF_8);
        try
        {
            List<ResultBean> resultList = AgentMaintainManager.getInstance().getExportIEnvTypeExcelData(ids,
                    itype);
            PoiUtil.exportExcel("环境类型", ENVMAP, resultList, response.getOutputStream());
        } catch (RepositoryException e1)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
        } catch (JspException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } catch (IOException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
    }

    /**
     *
     * @Title: importIEnvTypeExcel
     * @Description:福建农信Agent管理导入环境类型
     * @param file
     * @param response
     * @author: liu_yang
     * @date:   2024年4月12日 下午4:57:43
     */
    public void importIEnvTypeExcel ( CommonsMultipartFile file, HttpServletResponse response, int itype ,long userId)
    {
        LinkedHashMap<String, String> notExistsMap = new LinkedHashMap<String, String>();// 不存在的agent集合，用于组织向前台发回提示信息
        HashMap<String, Long> existsMap = new HashMap<String, Long>();// 存在的agent集合，用于获取agentId
        LinkedHashMap<Long, List<Map>> finalExistsMap = new LinkedHashMap<Long, List<Map>>();// 最终多写的数据
        String success = TRUE;
        StringBuilder message = new StringBuilder();
        String rsStr = "";
        long size = file.getSize();
        if (size > 20971520)
        {
            success = FALSE;
            message.append("Excel文件大于20M。");
        } else
        {
            try
            {
                List<ResultBean> inList = PoiUtil.parseExcel(file.getOriginalFilename(), file.getInputStream(),
                        new ResultBean(), ENVMAP);
                List<ResultBean> outList = new ArrayList<ResultBean>();

                if (inList == null || inList.isEmpty())
                {
                    success = FALSE;
                    message.append("无导入数据。");

                } else
                {
                    // 校验excel导入的数据
                    validateImportIEnvTypeExcel(existsMap, notExistsMap,
                            inList, outList, itype);
                    if (outList == null || outList.isEmpty())
                    {
                        success = FALSE;
                        message.append("无符合要求的数据。规则：1、IP+PORT必须存在 2、同Agent下不存在同名环境类型 3、平台必须存在该环境类型。");
                    }else if(!notExistsMap.isEmpty()){
                        Set<String> ks = notExistsMap.keySet();
                        Iterator<String> it = ks.iterator();
                        message.append("导入失败！");
                        while (it.hasNext())
                        {
                            String key = it.next();
                            message.append(notExistsMap.get(key));
                        }
                    }else if(isDuplicate(outList))
                    {
                        success = FALSE;
                        message.append("导入数据中，相同地址下环境类型重复，请修改后重新导入");
                    }else
                    {
                        //校验agent是否有权限修改
                        isAgentAuthority(outList, itype, userId);
                        for (ResultBean rb1 : outList)
                        {
                            Long agentId = existsMap.get("(IP:" + rb1.getName() + ",PORT:" + rb1.getInumber() + ")");
                            Map tempMap = new HashMap();
                            tempMap.put("id", 0);
                            tempMap.put("envType", rb1.getName2());
                            if (finalExistsMap.get(agentId) != null)
                            {
                                // 组织最终多写数据
                                finalExistsMap.get(agentId).add(tempMap);
                            } else
                            {
                                // 组织最终多写数据
                                List<Map> tempList = new ArrayList<Map>();
                                tempList.add(tempMap);
                                finalExistsMap.put(agentId, tempList);
                            }
                        }
                        int succCount = 0;
                        Set<Long> ks2 = finalExistsMap.keySet();
                        Iterator<Long> it = ks2.iterator();
                        while (it.hasNext())
                        {
                            Long key = it.next();
                            List<Map> tempList1 = finalExistsMap.get(key);
                            // 将list转成json对象，用于组织复用多写方法的数据
                            net.sf.json.JSONArray jsonArry = net.sf.json.JSONArray.fromObject(tempList1);
                            logger.info(key + "||" + jsonArry.toString());
                            // 调用多写方法
                            this.saveAgentEnvsMultiple(jsonArry.toString(), key);
                            succCount += tempList1.size();
                        }
                        message.append("符合要求的数据" + outList.size() + "条，成功导入数据" + succCount + "条。");
                    }

                }

            } catch (Exception e)
            {
                success = FALSE;
                message = new StringBuilder();
                message.append(e.getMessage().replace("\n", "||").replace("'", "\\'"));
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        }
        rsStr = "{success:" + success + ",message:'" + message + "'}";
        response.setContentType("text/html");// 必须设置返回类型为text，否则ext无法正确解析json字符串
        response.setCharacterEncoding(UTF_8);// 设置编码字符集为utf-8，否则ext无法正确解析
        PrintWriter outs = null;
        try
        {
            outs = response.getWriter();
            outs.write(rsStr);
        } catch (IOException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            if (outs != null)
            {
                outs.close();
            }
        }

    }

    /**
     * 福建农信 agent环境标识导入权限判断
     * @param outList 导入数据
     * @param itype itype
     * @param userId 登录人id
     * @throws RepositoryException RepositoryException
     */
    private void isAgentAuthority(List<ResultBean> outList, int itype ,long userId) throws RepositoryException {
        //该用户拥有权限的业务系统
        String systemIdArr = AgentMaintainManager.getInstance().getPermissionSystemId(userId);
        List<String> systemIds = Arrays.asList(systemIdArr.split(","));
        if(StringUtils.isEmpty(systemIdArr)){
            throw new RuntimeException("您没有导入权限");
        }
        for(ResultBean user : outList) {
            String name = user.getName();
            Integer port = user.getInumber();
            //当前agent绑定业务系统
            Long systemid = AgentMaintainManager.getInstance().querySysTemId(name,port,itype);
            //判断是否有权限
            if(!systemIds.contains(String.valueOf(systemid))){
                throw new RuntimeException("无权限导入服务器:【"+user.getName()+"】,请修改");
            }
        }

    }

    public void validateImportIEnvTypeExcel(HashMap<String, Long> existsMap, LinkedHashMap<String, String> notExistsMap, List<ResultBean> inList, List<ResultBean> outList, int itype) throws RepositoryException {
        Connection con = null;
        try
        {
            con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), logger, itype);
            for (ResultBean rb1 : inList)
            {
                // 环境类型为空，不处理
                if (!StringUtils.isBlank(rb1.getName2()))
                {
                    String keyString = "(IP:" + rb1.getName() + ",PORT:" + rb1.getInumber() + ")";
                    if (notExistsMap.get(keyString) == null)
                    {
                        // 如果在map中不存在，则说明ip+port在数据库中存在，则继续操作此应用标识
                        Long agentId = AgentMaintainManager.getInstance().getAgentId(con, rb1);
                        // 判断ip+port在数据库中是否存在
                        if (agentId > 0)
                        {
                            // 将AgentId保存到map中，保存方法中使用，无需再次请求数据库
                            existsMap.put(keyString, agentId);
                            long envId = AgentMaintainManager.getInstance().getEnvIid(rb1.getName2());
                            if(envId > 0){
                                if (!AgentMaintainManager.getInstance().checkEnvType(con, agentId, envId))
                                {
                                    // 环境类型存在且与agent无绑定关系，则处理本条数据导入数据库
                                    outList.add(rb1);
                                }else{
                                    String mes = "环境类型：" + rb1.getName2() + "已与agent：" + keyString +"绑定。";
                                    notExistsMap.put(keyString, mes);
                                }
                            }else{
                                notExistsMap.put(keyString, "环境类型：" + rb1.getName2() + "不存在。");
                            }

                        } else
                        {
                            // 不存在将ip+port放进map中
                            notExistsMap.put(keyString, "Agent：" +keyString+"不存在,不保存相关的环境类型。");
                        }
                    }
                }

            }

        } catch (SQLException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
        }
    }

    /**
     *
     * <li>Description:Agent配置导入</li>
     * <AUTHOR>
     * 2016年8月18日
     * @param filename
     * @param fis
     * @param type
     * @return
     * return boolean
     */
    public Map<String, Object> uploadAgentFJNX ( String filename, InputStream fis, int type, HttpServletRequest request )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Agent> list = new ArrayList();
        Map res = new HashMap();
        Map map = new HashMap();
        String cellNumName = null;
        String CellValueString = null;
        Row row = null;
        Cell cell = null;
        Workbook workbook = null;
        Map<String, List<String>> tmpIpMaps = new HashMap<String, List<String>>();
        Connection checkConn = null;
        String userName = SessionData.getSessionData(request).getUserName();
        long iuserid = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        try
        {
            InfoExeclServices service = new InfoExeclServices();
            try
            {
                workbook = service.fis2POI(filename, fis);
            } catch (Exception e)
            {
                throw new EswitchException("Excel文件版本格式转换发生异常！");
            }
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getLastRowNum() + 1 - sheet.getFirstRowNum();
            //{ "序号", "业务系统", "IP地址", "端口" };
            Map<String, Object> obmap = new HashMap<String, Object>()
            {
                {
                    put(AGENT_UPLOAD_DATA_FJNX[0], 0);
                    put(AGENT_UPLOAD_DATA_FJNX[1], 0);
                    put(AGENT_UPLOAD_DATA_FJNX[2], 0);
                    put(AGENT_UPLOAD_DATA_FJNX[3], 0);
                    put(AGENT_UPLOAD_DATA_FJNX[4], 0);
                    put(AGENT_UPLOAD_DATA_FJNX[5], 0);
                }
            };
            // 检查Excel格式长度
            row = sheet.getRow(0);
            // 格式通过后进行数据导入
            for (int k = 0; k <= rows; k++)
            {
                Agent agent = new Agent();
                agent.setIid(AGENT_ID_NULL);
                row = sheet.getRow(k);
                if (row != null && !isRowEmpty(row))
                {
                    int colum = row.getLastCellNum();
                    for (int j = 0; j < AGENT_UPLOAD_DATA_FJNX.length; j++)
                    {
                        cell = row.getCell(j);
                        if (k == 0)
                        {
                            if (cell != null)
                            {
                                map.put(j, getStringCellValue(cell));
                                if (obmap.containsKey(getStringCellValue(cell).trim()))
                                {
                                    obmap.put(getStringCellValue(cell).trim(), 1);
                                }
                            }
                        } else
                        {
                            StringBuilder nullValue = new StringBuilder();
                            for (int m = 0; m < AGENT_UPLOAD_DATA_FJNX.length; m++)
                            {
                                String value = "";
                                if (obmap.containsKey(AGENT_UPLOAD_DATA_FJNX[m]))
                                {
                                    value = obmap.get(AGENT_UPLOAD_DATA_FJNX[m]).toString();
                                }
                                if ("0".equals(value))
                                {
                                    if ("".equals(nullValue))
                                    {
                                        nullValue.append(AGENT_UPLOAD_DATA_FJNX[m]);
                                    } else
                                    {
                                        nullValue.append(",").append(AGENT_UPLOAD_DATA_FJNX[m]);
                                    }
                                }
                            }
                            if (!"".equals(nullValue.toString()))
                            {
                                res.put(SUCCESS_TEXT, false);
                                res.put(MESSAGE_TEXT, "excel中不存在列名为：" + nullValue + "的列，请查看后再操作！");
                                return res;
                            }
                            cellNumName = (String) map.get(j);
                            CellValueString = getStringCellValue(cell);
                            int colNum = j + 1;
                            int rowNum = k + 1;
                            if (AGENT_UPLOAD_DATA_FJNX[0].equals(cellNumName))
                            {
                                if ("".equals(CellValueString))
                                {
                                    break;
                                }
                            }
                            if (AGENT_UPLOAD_DATA_FJNX[1].equals(cellNumName))
                            {
                                String ibusinesssys = CellValueString==null?"":CellValueString;
                                if(StringUtil.isEmptyStr(ibusinesssys)){
                                    String mes = "在" + rowNum + "行," + colNum + "列中业务系统内容为空，导入失败！";
                                    res.put(SUCCESS_TEXT, false);
                                    res.put(MESSAGE_TEXT, mes);
                                    return res;
                                }
                                long projectId = ProjectManager.getInstance().getPrjIdByName(ibusinesssys);
                                if(projectId > 0){
                                    if(ProjectManager.getInstance().queryUserBandProject(iuserid,projectId)){
                                        agent.setIbusinesssys(ibusinesssys);
                                    }else{
                                        res.put(SUCCESS_TEXT, false);
                                        res.put(MESSAGE_TEXT, "用户：" + userName + "没有该系统权限：" +ibusinesssys );
                                        return res;
                                    }
                                }else{
                                    String mes = "在" + rowNum + "行," + colNum + "列中业务系统："+ibusinesssys +"不存在，导入失败！";
                                    res.put(SUCCESS_TEXT, false);
                                    res.put(MESSAGE_TEXT, mes);
                                    return res;
                                }
                            }
                            if (AGENT_UPLOAD_DATA_FJNX[2].equals(cellNumName))
                            {
                                if (PersonalityEnv.isAgentDnsSwitchValue())
                                {
                                    boolean isIP = isIP(CellValueString);
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        if (isIP)
                                        {
                                            agent.setIagentip(CellValueString);
                                            agent.setIpalias(IPTools.getDigitalIP(CellValueString));//根据IP 设置 转译后的数字IPv4,IPv6
                                        } else
                                        {
                                            String mes = "在" + rowNum + "行," + colNum + "列中IP信息："+CellValueString +"有误，导入失败！";
                                            res.put(SUCCESS_TEXT, false);
                                            res.put(MESSAGE_TEXT, mes);
                                            return res;
                                        }
                                    }
                                } else
                                {
                                    boolean isDNS = isDNS(CellValueString);
                                    if (CellValueString != null && !"".equals(CellValueString))
                                    {
                                        if (!isDNS)
                                        {
                                            agent.setIagentip(CellValueString);
                                            agent.setIpalias(IPTools.getDigitalIP(CellValueString));//根据IP 设置 转译后的数字IPv4,IPv6
                                        } else
                                        {
                                            throwCheckDNSException(CellValueString, colNum, rowNum);
                                        }
                                    } else
                                    {
                                        throwException(cellNumName, colNum, rowNum);
                                    }
                                }
                            }
                            if (AGENT_UPLOAD_DATA_FJNX[3].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    int portVal = new Double(CellValueString).intValue();
                                    if (0 < portVal && portVal < 65535)
                                    {
                                        agent.setIagentport(Long.parseLong(String.valueOf(portVal)));
                                    } else
                                    {
                                        String mes = "在" + rowNum + "行," + colNum + "列中端口号信息："+CellValueString +"有误，导入失败！";
                                        res.put(SUCCESS_TEXT, false);
                                        res.put(MESSAGE_TEXT, mes);
                                        return res;
                                    }
                                } else
                                {
                                    String mes = "在" + rowNum + "行," + colNum + "列中端口号为空，导入失败！";
                                    res.put(SUCCESS_TEXT, false);
                                    res.put(MESSAGE_TEXT, mes);
                                    return res;
                                }
                                Agent a= AgentMaintainManager.getInstance().getAgentByIpPort(agent.getIagentip(), Math.toIntExact(agent.getIagentport()),Constants.IEAI_IEAI_BASIC);
                                if(a.getIid()>0){
                                    String mes = "在" + rowNum + "行，agent已存在，导入失败！";
                                    res.put(SUCCESS_TEXT, false);
                                    res.put(MESSAGE_TEXT, mes);
                                    return res;
                                }else{
                                    list.add(agent);
                                }
                            }
                            if (AGENT_UPLOAD_DATA_FJNX[4].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    agent.setIagentdesc(CellValueString);
                                }
                            }
                            if (AGENT_UPLOAD_DATA_FJNX[5].equals(cellNumName))
                            {
                                if (CellValueString != null && !"".equals(CellValueString))
                                {
                                    if("是".equals(CellValueString)){
                                        agent.setIsSSL(1);
                                    }else if("否".equals(CellValueString)){
                                        agent.setIsSSL(0);
                                    } else
                                    {
                                        throwCommonException(colNum, rowNum,"列中数据格式错误,请确认为是或否！");
                                    }
                                } else
                                {
                                    throwException(cellNumName, colNum, rowNum);
                                }
                            }
                            //agent状态，默认为新建状态
                            agent.setIagentState(-1);
                        }
                    }
                }
            }
            if(list.isEmpty()){
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "导入模板内容为空，导入失败！");
                return res;
            }
            try
            {
                checkConn = DBResource.getConnection(method, logger, type);
                List listSim = new ArrayList();
                for (int i = 0; i < list.size(); i++)
                {
                    Agent agents = list.get(i);
                    checkAgent(listSim, agents);
                    if (manager.isAgentIpExistsAndUpdateClusterId(agents, checkConn, type))
                    {
                        boolean importAgentToComputer = Environment.getInstance()
                                .getBooleanConfig("import.agent.to.computer", false);
                        if (importAgentToComputer)
                        {
                            Map mapAgent = AgentMaintainManager.getInstance().getAgentInfoByIP(agents.getIagentip(),
                                    agents.getIagentport(), type, checkConn);
                            String iagentinfoid = mapAgent.get(IAGENTINFO_ID) == null ? ""
                                    : (String) mapAgent.get(IAGENTINFO_ID);
                            agents.setIid("".equals(iagentinfoid) ? -1 : Long.valueOf(iagentinfoid));
                            updateComputerAgent(agents);
                        }
                        /**
                         * 是否更新Agent开关
                         */
                        boolean ifupdataAgent = Environment.getInstance().getBooleanConfig("import.agent.if.update",
                                false);
                        if (ifupdataAgent)
                        {
                            Map mapAgent = AgentMaintainManager.getInstance().getAgentInfoByIP(agents.getIagentip(),
                                    agents.getIagentport(), type, checkConn);
                            String iagentinfoid = mapAgent.get(IAGENTINFO_ID) == null ? "-1"
                                    : (String) mapAgent.get(IAGENTINFO_ID);
                            agents.setIcreateuser(iuserid);
                            agents.setIid(Long.valueOf(iagentinfoid));

                            listSim.add(agents);

                        }
                        continue;
                    }
                    // 增加纳管用户
                    agents.setIcreateuser(iuserid);
                    listSim.add(agents);

                }
                // 关闭检查Agent 是否存在IP连接
                if (checkConn != null)
                {
                    DBResource.closeConnection(checkConn, method, logger);
                }

                Map<String, Object> connInfo = Tools.getConnectionInfo();
                Connection baseConn = (Connection) connInfo.get("baseConn");
                List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

                if (null != baseConn)
                { // 没有基线源，不可以
                    Map orgSql = manager.organizeAgentMaitainInfoSql(listSim, baseConn, dbConns);
                    if ((Boolean) orgSql.get(SUCCESS_TEXT))
                    {
                        if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                                (List<String>) orgSql.get("rollbackSqls")))
                        {
                            DBResource.closeConnection(baseConn, method, logger);
                            res.put(SUCCESS_TEXT, true);
                            res.put(MESSAGE_TEXT, "数据保存成功！");
                            // OperationService.getInstance().calladdoperRecord("Agent管理",userName,"保存",Constants.IEAI_EMERGENCY_SWITCH);
                            logger.info("用户名:" + userName + "操作:Agent管理保存操作。");
                            return res;
                        } else
                        {
                            DBResource.closeConnection(baseConn, method, logger);
                            res.put(SUCCESS_TEXT, false);
                            res.put(MESSAGE_TEXT, "执行Agent信息变更sql时出现错误！");
                            return res;
                        }
                    } else
                    {
                        // 执行有异常关闭连接
                        DBResource.closeConnection(baseConn, method, logger);
                        for (Connection conn : dbConns)
                        {
                            DBResource.closeConnection(conn, method, logger);
                        }
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "组织agent信息sql时出现错误！");
                        return res;
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
                    return res;
                }
            } catch (Exception e)
            {
                String mes = "插入数据库发生异常！";
                throw new EswitchException(mes);

            }
        } catch (Exception ee)
        {
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, ee.getMessage());
        }
        try
        {
            fis.close();
        } catch (Exception e)
        {
            logger.error(method, e);
        }
        return res;
    }


    public Map getAgentActInfo ( long agentId, Integer start, Integer limit, int sysType )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        AgentMaintainManager manage = AgentMaintainManager.getInstance();
        String sqlList;
        String sqlCount = "SELECT COUNT(COU.IAGENTINFO_ID) AS COUNTNUM FROM " ;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqlList = "SELECT \n" +
                        "    t.iagentinfo_id,\n" +
                        "    t.iagent_ip,\n" +
                        "    t.iagent_port,\n" +
                        "    t.iid,\n" +
                        "    t.iname,\n" +
                        " S.IPRJNAME,\n" +
                        "    S.IWORKFLOWNAME,\n" +
                        "    S.IACTNAME,\n" +
                        "    S.igroupname\n" +
                        "FROM(\n" +
                        "SELECT \n" +
                        "    a.iagentinfo_id,\n" +
                        "    a.iagent_ip,\n" +
                        "    a.iagent_port,\n" +
                        "    g.iid,\n" +
                        "    g.iname\n" +
                        "FROM \n" +
                        "    ieai_agentinfo AS a\n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentinfo_group AS ag\n" +
                        "    ON a.iagentinfo_id = ag.inodeid \n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentgroup AS g\n" +
                        "    ON ag.igroupid = g.iid\n" +
                        "WHERE \n" +
                        "    a.iagentinfo_id = ?\n" +
                        ") AS T,IEAI_PRJACT_AGENTINFO AS S\n" +
                        "    WHERE (\n" +
                        "        ((S.igroupname = '' or S.igroupname is NULL) AND T.iagent_ip = S.iagentip  AND T.iagent_port = S.IAGENTPORT)\n" +
                        "        OR\n" +
                        "        (S.igroupname IS NOT NULL AND s.igroupname = T.iname)\n" +
                        "    )\n" +
                        "\n" +
                        "UNION ALL\n" +
                        "SELECT \n" +
                        "    e1.iagentinfo_id,\n" +
                        "    e1.iagent_ip,\n" +
                        "    e1.iagent_port,\n" +
                        "    e1.iid,\n" +
                        "    e1.iname,\n" +
                        "    e.imainproname,\n" +
                        "    e.IACTNAME as flowname,\n" +
                        "    e.IACTNAME,\n" +
                        "    e.iagentsourcegroup\n" +
                        "FROM (SELECT \n" +
                        "    a1.iagentinfo_id,\n" +
                        "    a1.iagent_ip,\n" +
                        "    a1.iagent_port,\n" +
                        "    g1.iid,\n" +
                        "    g1.iname\n" +
                        "FROM \n" +
                        "    ieai_agentinfo AS a1\n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentinfo_group AS ag1\n" +
                        "    ON a1.iagentinfo_id = ag1.inodeid \n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentgroup AS g1\n" +
                        "    ON ag1.igroupid = g1.iid  \n" +
                        "WHERE \n" +
                        "    a1.iagentinfo_id = ?) as e1,ieai_excelmodel as e\n" +
                        "where ((e.iagentsourcegroup = e1.iname  )\n" +
                        "        OR\n" +
                        "        (e.iagentsourcegroup = CONCAT(e1.iagent_ip, ':', e1.iagent_port)))\n" +
                        "\n" ;
                sqlCount = sqlCount+" ( "+sqlList +" ) AS COU";
                sqlList += "limit ?,? ";
            } else
            {
                sqlList = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY iagentinfo_id DESC) AS RO, \n" +
                        "    t.iagentinfo_id,\n" +
                        "    t.iagent_ip,\n" +
                        "    t.iagent_port,\n" +
                        "    t.iid,\n" +
                        "    t.iname,\n" +
                        "\t\tS.IPRJNAME,\n" +
                        "    S.IWORKFLOWNAME,\n" +
                        "    S.IACTNAME,\n" +
                        "    S.igroupname\n" +
                        "FROM(\n" +
                        "SELECT \n" +
                        "    a.iagentinfo_id,\n" +
                        "    a.iagent_ip,\n" +
                        "    a.iagent_port,\n" +
                        "    g.iid,\n" +
                        "    g.iname\n" +
                        "FROM \n" +
                        "    ieai_agentinfo AS a\n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentinfo_group AS ag\n" +
                        "    ON a.iagentinfo_id = ag.inodeid \n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentgroup AS g\n" +
                        "    ON ag.igroupid = g.iid\n" +
                        "WHERE \n" +
                        "    a.iagentinfo_id = 109\n" +
                        ") AS T,IEAI_PRJACT_AGENTINFO AS S\n" +
                        "    WHERE (\n" +
                        "        ((S.igroupname = '' or S.igroupname is NULL) AND T.iagent_ip COLLATE utf8_general_ci = S.iagentip COLLATE utf8_general_ci AND T.iagent_port = S.IAGENTPORT)\n" +
                        "        OR\n" +
                        "        (S.igroupname IS NOT NULL AND s.igroupname COLLATE utf8_general_ci = T.iname COLLATE utf8_general_ci)\n" +
                        "    )\n" +
                        "\n" +
                        "UNION ALL\n" +
                        "SELECT \n" +
                        "    e1.iagentinfo_id,\n" +
                        "    e1.iagent_ip,\n" +
                        "    e1.iagent_port,\n" +
                        "    e1.iid,\n" +
                        "    e1.iname,\n" +
                        "    e.imainproname,\n" +
                        "    e.IACTNAME as flowname,\n" +
                        "    e.IACTNAME,\n" +
                        "    e.iagentsourcegroup\n" +
                        "FROM (SELECT \n" +
                        "    a1.iagentinfo_id,\n" +
                        "    a1.iagent_ip,\n" +
                        "    a1.iagent_port,\n" +
                        "    g1.iid,\n" +
                        "    g1.iname\n" +
                        "FROM \n" +
                        "    ieai_agentinfo AS a1\n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentinfo_group AS ag1\n" +
                        "    ON a1.iagentinfo_id = ag1.inodeid \n" +
                        "LEFT JOIN\n" +
                        "    ieai_agentgroup AS g1\n" +
                        "    ON ag1.igroupid = g1.iid  \n" +
                        "WHERE \n" +
                        "    a1.iagentinfo_id = 109) as e1,ieai_excelmodel as e\n" +
                        "where ((e.iagentsourcegroup = e1.iname  )\n" +
                        "        OR\n" +
                        "        (e.iagentsourcegroup = CONCAT(e1.iagent_ip, ':', e1.iagent_port))) "
                        + " WHERE RO>? AND RO<=?";
            }


            List list = manage.getAgentActList(conn, sqlList, start, limit, agentId);
            int count = manage.getAgentActCount(conn, sqlCount, agentId);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            logger.error("method is error!", e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return map;
    }
}
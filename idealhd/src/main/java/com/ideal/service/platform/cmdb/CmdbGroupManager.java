package com.ideal.service.platform.cmdb;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.timetask.repository.manage.IpselectQueryCondition;
import com.ideal.ieai.server.util.BeanFormatter;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CmdbGroupManager
{
    static Logger                       _log                     = Logger.getLogger(CmdbGroupManager.class);
    //单例模式
    private CmdbGroupManager(){}
    static private CmdbGroupManager _intance                    = new CmdbGroupManager();
    static public CmdbGroupManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new CmdbGroupManager();
        }
        return _intance;
    }
    
    /**
     * 
     * @Title: getComputerGroupList   
     * @Description:信息采集--设备分组--数据   
     * @param: @param type
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: List<CmdbGroupBean>      
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月14日 下午6:34:10
     */
    public Object[] getComputerGroupList ( String qryCon, int start, int limit, int type ) throws RepositoryException
    {
        long total = 0;
        List<CmdbGroupBean> resultList = new ArrayList<CmdbGroupBean>();

        String orderBy = " ORDER BY IGROUPNAME ";
        String sql = "SELECT * FROM IEAI_CMDB_GROUP where 1=1 ";
        // 处理查询条件
        if (null != qryCon && !"".equals(qryCon))
        {
            sql += " and IGROUPNAME LIKE '%" + qryCon + "%' ";
        }
        sql = sql + orderBy;
        String cntSql = " select count(*) COU from (" + sql + ") tt";
        String pagingSql = SQLUtil.getQueryPageSQLNew("db2", sql);
        if(3==JudgeDB.IEAI_DB_TYPE)
        {
            //mysql
            pagingSql = SQLUtil.getQueryPageSQLNew("mysql", sql);
        }else if(DBManager.Orcl_Faimily())
        {
            //oracle
            pagingSql = SQLUtil.getQueryPageSQL("oracle", sql);
        }
        CmdbGroupBean bean = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                PreparedStatement cntPS = null;
                ResultSet cntRS = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    // 查询总数
                    cntPS = con.prepareStatement(cntSql);
                    cntRS = cntPS.executeQuery();
                    if (cntRS.next())
                    {
                        total = cntRS.getLong("COU");
                    }
                    // 查询数据
                    actStat = con.prepareStatement(pagingSql);
                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        // mysql
                        actStat.setInt(1, start);
                        actStat.setInt(2, limit);
                    } else if (DBManager.Orcl_Faimily())
                    {
                        // oracle
                        actStat.setInt(1, start + limit);
                        actStat.setInt(2, start);
                    } else
                    {
                        // db2
                        actStat.setInt(1, start);
                        actStat.setInt(2, start + limit);
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean = new CmdbGroupBean();
                        bean.setIid(actRS.getLong("IID"));
                        bean.setIgroupName(actRS.getString("IGROUPNAME"));
                        bean.setIgroupDes(actRS.getString("IGROUPDES"));
                        resultList.add(bean);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(cntRS, cntPS, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closeConn(con, actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        
        // 组织返回数据
        Object[] obj = new Object[2];
        obj[0] = resultList;
        obj[1] = total;
        return obj;
    }

    /**
     * 
     * @Title: orgSaveSqlForComputerGroupList   
     * @Description:信息采集--任务分组--保存组组织SQL（多写）   
     * @param: @param groupList
     * @param: @param baseConn
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月14日 下午7:41:44
     */
    public Map orgSaveSqlForComputerGroupList ( List<CmdbGroupBean> groupList, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        // 保存SQL
        // IEAI_CMDB_GROUP表
        String insertSql_group = "INSERT INTO IEAI_CMDB_GROUP(IID,IGROUPNAME,IGROUPDES) values ({iid},'{igroupName}','{igroupDes}') ";
        String updateSql_group = "UPDATE IEAI_CMDB_GROUP set IGROUPNAME='{igroupName}',IGROUPDES='{igroupDes}' where IID={iid} ";

        try
        {
            for (CmdbGroupBean bean : groupList)
            {
                if (0 == bean.getIid())
                {
                    // insert
                    // 插入SQL
                    long iid = IdGenerator.createId("IEAI_CMDB_GROUP", baseConn);
                    bean.setIid(iid);
                    BeanFormatter<CmdbGroupBean> bf = new BeanFormatter<CmdbGroupBean>(insertSql_group);
                    String s = bf.format(bean);
                    exeSqls.add(s);

                    // 组织回滚SQL
                    String deleteSql_group = "DELETE FROM IEAI_CMDB_GROUP WHERE IID=" + bean.getIid();
                    rollbackSqls.add(deleteSql_group);
                } else
                {
                    // update
                    // 更新SQL
                    // ieai_chk_info
                    BeanFormatter<CmdbGroupBean> bf = new BeanFormatter<CmdbGroupBean>(updateSql_group);
                    String s = bf.format(bean);
                    exeSqls.add(s);

                    // 组织回滚SQL
                    // ieai_chk_info
                    CmdbGroupBean oldGroupBean = this.getOldGroupBeanByIid(bean.getIid(), baseConn);
                    BeanFormatter<CmdbGroupBean> bf2 = new BeanFormatter<CmdbGroupBean>(updateSql_group);
                    String oldbeanSql = bf2.format(oldGroupBean);
                    rollbackSqls.add(oldbeanSql);
                }
            }
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    private CmdbGroupBean getOldGroupBeanByIid ( long iid, Connection baseConn ) throws RepositoryException
    {
        CmdbGroupBean bean = null;
        String sql = "SELECT * FROM IEAI_CMDB_GROUP WHERE IID=" + iid;
        for (int i = 0; i < 10; i++)
        {
            bean = null;
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    if (rs.next())
                    {
                        bean = new CmdbGroupBean();
                        bean.setIid(iid);
                        bean.setIgroupName(rs.getString("IGROUPNAME"));
                        bean.setIgroupDes(rs.getString("IGROUPDES"));
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return bean;
    }

    public Map orgDelSqlForComputerGroupList ( String idsStr, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        // 删除SQL
        String deleteSql_group = "DELETE FROM IEAI_CMDB_GROUP WHERE IID IN (" + idsStr + ") ";
        String deleteSql_mapping = "DELETE FROM IEAI_CMDB_GROUP_MAPPING WHERE IGID IN(" + idsStr + ") ";

        // 回滚SQL
        // IEAI_CMDB_GROUP表
        String insertSql_group = "INSERT INTO IEAI_CMDB_GROUP(IID,IGROUPNAME,IGROUPDES) values ({iid},'{igroupName}','{igroupDes}') ";
        // IEAI_CMDB_GROUP_MAPPING
        String insertSql_mapping = "INSERT INTO IEAI_CMDB_GROUP_MAPPING(IID,ICPID,IGID) values({iid},{icpId},{igId}) ";
        try
        {
            // 组织删除SQL
            exeSqls.add(deleteSql_group);
            exeSqls.add(deleteSql_mapping);

            // 组织回滚SQL
            // IEAI_CMDB_GROUP
            List<CmdbGroupBean> groupList = this.getComputerGroupListByIidsStr(idsStr, baseConn);
            for (CmdbGroupBean bean : groupList)
            {
                BeanFormatter<CmdbGroupBean> bf = new BeanFormatter<CmdbGroupBean>(insertSql_group);
                String sql = bf.format(bean);
                rollbackSqls.add(sql);
            }
            // IEAI_CMDB_GROUP_MAPPING
            List<CmdbGroupMappingBean> mappingList = this.getMappingListByGidStr(idsStr, baseConn);
            for (CmdbGroupMappingBean bean : mappingList)
            {
                BeanFormatter<CmdbGroupMappingBean> bf = new BeanFormatter<CmdbGroupMappingBean>(
                        insertSql_mapping);
                String sql = bf.format(bean);
                rollbackSqls.add(sql);
            }
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    private List<CmdbGroupMappingBean> getMappingListByGidStr ( String idsStr, Connection baseConn )
            throws RepositoryException
    {
        List<CmdbGroupMappingBean> list = new ArrayList<CmdbGroupMappingBean>();
        String sql = "SELECT * FROM IEAI_CMDB_GROUP_MAPPING WHERE IGID in (" + idsStr + ") ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        CmdbGroupMappingBean bean = new CmdbGroupMappingBean();
                        bean.setIid(rs.getLong("IID"));
                        bean.setIcpId(rs.getLong("ICPID"));
                        bean.setIgId(rs.getLong("IGID"));
                        list.add(bean);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    private List<CmdbGroupBean> getComputerGroupListByIidsStr ( String idsStr, Connection baseConn )
            throws RepositoryException
    {
        List<CmdbGroupBean> list = new ArrayList<CmdbGroupBean>();
        String sql = "SELECT * FROM IEAI_CMDB_GROUP WHERE IID in (" + idsStr + ") ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        CmdbGroupBean bean = new CmdbGroupBean();
                        bean.setIid(rs.getLong("IID"));
                        bean.setIgroupName(rs.getString("IGROUPNAME"));
                        bean.setIgroupDes(rs.getString("IGROUPDES"));
                        list.add(bean);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 
     * @Title: getComputerListSelectedByForGroup   
     * @Description:信息采集--设备分组--所属设备--数据  
     * @param: @param conditionBean
     * @param: @param parseLong
     * @param: @param type
     * @param: @return      
     * @return: Object[]      
     * @throws RepositoryException 
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月15日 上午9:11:28
     */
    public Object[] getComputerListSelectedByForGroup ( IpselectQueryCondition conditionBean, long groupId, int type )
            throws RepositoryException
    {
        Object[] obj = new Object[2];
        List list = new ArrayList();
        long total = 0;
        String sqlOrderBy = " order by CP.IP ";
        String sql = "SELECT CP.CPID AS CPID, CP.IP AS IP FROM IEAI_CMDB_LIST CP, IEAI_CMDB_GROUP_MAPPING R WHERE CP.CPID=R.ICPID AND R.IGID=? ";

        // 处理IP条件
        String sqlWhere = "";
        // 如果两个输入框都有值，则按IP段查询
        if (conditionBean.getIpBeginLongValue() > 0 && conditionBean.getIpEndLongValue() > 0)
        {
            // ip段起
            sqlWhere = sqlWhere + " AND CP.IPALIAS >=" + conditionBean.getIpBeginLongValue();
            // ip段至
            sqlWhere = sqlWhere + " AND CP.IPALIAS <=" + conditionBean.getIpEndLongValue();
        } else
        {
            // 如果只有一个输入框都有值，则按IP模糊查询
            if (conditionBean != null && null != conditionBean.getIpBegin() && !"".equals(conditionBean.getIpBegin()))
            {
                sqlWhere = sqlWhere + " AND CP.IP LIKE '%" + conditionBean.getIpBegin() + "%' ";
            }
            if (conditionBean != null && null != conditionBean.getIpEnd() && !"".equals(conditionBean.getIpEnd()))
            {
                sqlWhere = sqlWhere + " AND CP.IP LIKE '%" + conditionBean.getIpEnd() + "%' ";
            }
        }
        sql += sqlWhere + sqlOrderBy;
        // 查询总数sql
        String cntSql = "SELECT COUNT(*) AS COU FROM (" + sql + ") cc ";

        int dbtype = JudgeDB.IEAI_DB_TYPE;
        String pagingSql = "";
        if (DBManager.Orcl_Faimily())
        {
            pagingSql = SQLUtil.getQueryPageSQL("oracle", sql);
        } else if (3 == dbtype)
        {
            // mysql
            pagingSql = SQLUtil.getQueryPageSQLNew("mysql", sql);
        } else
        {
            pagingSql = SQLUtil.getQueryPageSQL("db2", sql);
        }

        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    // 查询总数
                    actStat = con.prepareStatement(cntSql);
                    actStat.setLong(1, groupId);
                    actRS = actStat.executeQuery();
                    if (actRS.next())
                    {
                        total = actRS.getLong("COU");
                    }
                    // 查询分页数据
                    actStat = con.prepareStatement(pagingSql);
                    actStat.setLong(1, groupId);
                    if (DBManager.Orcl_Faimily())
                    {
                        actStat.setLong(2, conditionBean.getStart() + conditionBean.getLimit());
                        actStat.setLong(3, conditionBean.getStart());
                    } else if (3 == dbtype)
                    {
                        // mysql
                        actStat.setLong(2, conditionBean.getStart());
                        actStat.setLong(3, conditionBean.getLimit());
                    } else
                    {
                        actStat.setLong(2, conditionBean.getStart() + 1);
                        actStat.setLong(3, conditionBean.getStart() + conditionBean.getLimit());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map tmpMap = new HashMap();
                        tmpMap.put("cpId", actRS.getLong("CPID"));
                        tmpMap.put("ip", actRS.getString("IP"));
                        list.add(tmpMap);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        obj[0] = list;
        obj[1] = total;
        return obj;
    }

    /**
     * 
     * @Title: getComputerListNoSelectedForGroup   
     * @Description:信息采集--设备分组--待选设备--数据     
     * @param: @param conditionBean
     * @param: @param groupId
     * @param: @param type
     * @param: @return      
     * @return: Object[]      
     * @throws RepositoryException 
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月15日 上午9:48:52
     */
    public Object[] getComputerListNoSelectedForGroup ( IpselectQueryCondition conditionBean, long groupId, int type )
            throws RepositoryException
    {
        Object[] obj = new Object[2];
        List list = new ArrayList();
        long total = 0;

        String sqlOrderBy = " order by CP.IP ";
        String sql = "SELECT CP.CPID AS CPID, CP.IP AS IP FROM IEAI_CMDB_LIST CP WHERE 1=1 ";
        // 处理已在组内的条件
        String sqlWhere = "";
        sqlWhere += " AND CP.CPID NOT IN (SELECT R.ICPID AS CPID FROM IEAI_CMDB_GROUP_MAPPING R WHERE R.IGID=? )";
        // 处理IP条件
        // 如果两个输入框都有值，则按IP段查询
        if (conditionBean.getIpBeginLongValue() > 0 && conditionBean.getIpEndLongValue() > 0)
        {
            // ip段起
            sqlWhere = sqlWhere + " AND CP.IPALIAS >=" + conditionBean.getIpBeginLongValue();
            // ip段至
            sqlWhere = sqlWhere + " AND CP.IPALIAS <=" + conditionBean.getIpEndLongValue();
        } else
        {
            // 如果只有一个输入框都有值，则按IP模糊查询
            if (conditionBean != null && null != conditionBean.getIpBegin() && !"".equals(conditionBean.getIpBegin()))
            {
                sqlWhere = sqlWhere + " AND CP.IP LIKE '%" + conditionBean.getIpBegin() + "%' ";
            }
            if (conditionBean != null && null != conditionBean.getIpEnd() && !"".equals(conditionBean.getIpEnd()))
            {
                sqlWhere = sqlWhere + " AND CP.IP LIKE '%" + conditionBean.getIpEnd() + "%' ";
            }
        }
        sql += sqlWhere + sqlOrderBy;
        // 查询总数sql
        String cntSql = "SELECT COUNT(*) AS COU FROM (" + sql + ") cc";

        int dbtype = JudgeDB.IEAI_DB_TYPE;
        String pagingSql = "";
        if (DBManager.Orcl_Faimily())
        {
            pagingSql = SQLUtil.getQueryPageSQL("oracle", sql);
        }
        else if (3 == dbtype)
        {
            // mysql
            pagingSql = SQLUtil.getQueryPageSQLNew("mysql", sql);
        }
        else
        {
            pagingSql = SQLUtil.getQueryPageSQL("db2", sql);
        }

        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    // 查询总数
                    actStat = con.prepareStatement(cntSql);
                    actStat.setLong(1, groupId);
                    actRS = actStat.executeQuery();
                    if (actRS.next())
                    {
                        total = actRS.getLong("COU");
                    }
                    // 查询分页数据
                    actStat = con.prepareStatement(pagingSql);
                    actStat.setLong(1, groupId);
                    if (DBManager.Orcl_Faimily())
                    {
                        actStat.setLong(2, conditionBean.getStart() + conditionBean.getLimit());
                        actStat.setLong(3, conditionBean.getStart());
                    } else if (3 == dbtype)
                    {
                        // mysql
                        actStat.setLong(2, conditionBean.getStart());
                        actStat.setLong(3, conditionBean.getLimit());
                    } else
                    {
                        actStat.setLong(2, conditionBean.getStart() + 1);
                        actStat.setLong(3, conditionBean.getStart() + conditionBean.getLimit());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map tmpMap = new HashMap();
                        tmpMap.put("cpId", actRS.getLong("CPID"));
                        tmpMap.put("ip", actRS.getString("IP"));
                        list.add(tmpMap);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        obj[0] = list;
        obj[1] = total;
        return obj;
    }

    /**
     * 
     * @Title: orgDelSqlForComputerGroupMapping   
     * @Description:设备分组--所属设备--删除（多写） 
     * @param: @param groupId
     * @param: @param idsStr
     * @param: @param baseConn
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月15日 上午10:27:25
     */
    public Map orgDelSqlForComputerGroupMapping ( long groupId, String cpidsStr, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        // 删除SQL
        String deleteSql_mapping = "DELETE FROM IEAI_CMDB_GROUP_MAPPING WHERE IGID=" + groupId + " AND ICPID IN("
                + cpidsStr + ") ";

        // 回滚SQL
        String insertSql_mapping = "INSERT INTO IEAI_CMDB_GROUP_MAPPING(IID,ICPID,IGID) values({iid},{icpId},{igId}) ";
        try
        {
            // 组织删除SQL
            exeSqls.add(deleteSql_mapping);

            // 组织回滚SQL
            List<CmdbGroupMappingBean> mappingList = this.getMappingListByGidAndCpidStr(groupId, cpidsStr,
                baseConn);
            for (CmdbGroupMappingBean bean : mappingList)
            {
                BeanFormatter<CmdbGroupMappingBean> bf = new BeanFormatter<CmdbGroupMappingBean>(
                        insertSql_mapping);
                String sql = bf.format(bean);
                rollbackSqls.add(sql);
            }
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    private List<CmdbGroupMappingBean> getMappingListByGidAndCpidStr ( long groupId, String idsStr,
            Connection baseConn ) throws RepositoryException
    {
        List<CmdbGroupMappingBean> list = new ArrayList<CmdbGroupMappingBean>();
        String sql = "SELECT * FROM IEAI_CMDB_GROUP_MAPPING WHERE IGID=" + groupId + " AND ICPID in (" + idsStr
                + ") ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        CmdbGroupMappingBean bean = new CmdbGroupMappingBean();
                        bean.setIid(rs.getLong("IID"));
                        bean.setIcpId(rs.getLong("ICPID"));
                        bean.setIgId(rs.getLong("IGID"));
                        list.add(bean);
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 
     * @Title: orgSaveSqlForComputerGroupMapping   
     * @Description:设备分组--待选设备--新增（多写） 
     * @param: @param groupId
     * @param: @param cpidsStr
     * @param: @param baseConn
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: manxi_zhao. 
     * @date:   2017年8月15日 上午11:02:56
     */
    public Map orgSaveSqlForComputerGroupMapping ( long groupId, String cpidsStr, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        try
        {
            String[] cpidArr = cpidsStr.split(",");
            for (int i = 0; i < cpidArr.length; i++)
            {
                long iid = IdGenerator.createId("IEAI_CMDB_GROUP_MAPPING", baseConn);
                long icpid = Long.parseLong(cpidArr[i]);
                // 保存SQL
                String insertSql_mapping = "INSERT INTO IEAI_CMDB_GROUP_MAPPING(IID,ICPID,IGID) values(" + iid + ","
                        + icpid + "," + groupId + ") ";
                exeSqls.add(insertSql_mapping);
            }

            // 回滚SQL
            String deleteSql_mapping = "DELETE FROM IEAI_CMDB_GROUP_MAPPING WHERE IGID=" + groupId
                    + " AND ICPID IN(" + cpidsStr + ") ";
            rollbackSqls.add(deleteSql_mapping);

        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage());
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }
    
    public long getComputerGroupIdByGroupName ( String gname, Connection baseConn )
            throws RepositoryException
    {
        long gid=0;
        String sql = "SELECT IID FROM IEAI_CMDB_GROUP WHERE IGROUPNAME = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    ps.setString(1, gname);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        gid=rs.getLong("IID");
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return gid;
    }
    
    public long getAgentIdByAgentIP ( String gname, Connection baseConn )
            throws RepositoryException
    {
        long aid=0;
        String sql = "SELECT IAGENTINFO_ID AS IID  FROM IEAI_AGENTINFO WHERE IAGENT_IP = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    ps.setString(1, gname);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        aid=rs.getLong("IID");
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return aid;
    }
    public long getComputerGroupMapping ( long gid, long cpid, Connection baseConn )
            throws RepositoryException
    {
        long aid=0;
        String sql = "SELECT IID  FROM IEAI_CMDB_GROUP_MAPPING WHERE  IGID=?   AND  ICPID = ?  ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    ps.setLong(1, gid);
                    ps.setLong(2, cpid);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        aid=rs.getLong("IID");
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return aid;
    }
    public long getComputerIdByIp ( String ip, Connection baseConn )
            throws RepositoryException
    {
        long cpid=0;
        String sql = "SELECT CPID FROM IEAI_CMDB_LIST WHERE IP=?  ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    ps = baseConn.prepareStatement(sql);
                    ps.setString(1, ip);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        cpid=rs.getLong("CPID");
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return cpid;
    }

    /**
     * 
     * @Title: isGroupNameNotExist   
     * @Description:校验设备组名称DB中是否已存在
     * @param: @param groupName
     * @param: @param baseConn
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: boolean      
     * @throws   
     * @author: Administrator 
     * @date:   2017年8月24日 上午11:30:38
     */
    public boolean isGroupNameNotExist ( String groupName, Connection baseConn ) throws RepositoryException
    {
        boolean returnValue = false;
        String sql = "SELECT count(*) COU FROM IEAI_CMDB_GROUP WHERE IGROUPNAME=? ";
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = baseConn.prepareStatement(sql);
            ps.setString(1, groupName);
            rs = ps.executeQuery();
            if (rs.next())
            {
                long cnt = rs.getLong("COU");
                if (0 == cnt)
                {
                    returnValue = true;
                }
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return returnValue;
    }

}

package com.ideal.service.infocollect.start;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.server.emergency.repository.start.SelectIpInfoBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.disRecoverSwitchStart.DisRecoverSwitchStartData;
import com.ideal.ieai.server.repository.hd.infocollect.start.StartInfocollectonManager;

public class StartInfocollectService
{
    private static final Logger            log     = Logger.getLogger(StartInfocollectService.class);
    private static StartInfocollectService intance = new StartInfocollectService();
    public static final String             MESSAGE = "message";
    public static final String             SUCCESS = "success";

    public static StartInfocollectService getInstance ()
    {
        if (intance == null)
        {
            intance = new StartInfocollectService();
        }
        return intance;
    }

    public List<Map<String, Object>> getAuditorList ( String loginName, long sysId, long allSysId )
            throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().getAuditorList(loginName, sysId, allSysId);
    }

    public boolean updateInstanceInsInfo ( List<Map<String, Object>> instanceInfos, String userLoginName )
            throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().updateInstanceInsInfo(instanceInfos, userLoginName);
    }

    public String getIpStrByInfoId ( long iinfoId, short type ) throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().getIpStrByInfoId(iinfoId, type);
    }

    public boolean checkSysRunOnAgent ( List<SelectIpInfoBean> ipList, int type ) throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().checkSysRunOnAgent(ipList, type);
    }

    public /* boolean */Object[] saveWorkItem ( String iidString, String sysName, String instName, String version,
            String startUser, String execUser, int istate, long iinstanceid, List<SelectIpInfoBean> ipList,
            String legalPersonp, String accountDate, String descName, int type ) throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().saveWorkItem(iidString, sysName, instName, version, startUser,
            execUser, istate, iinstanceid, ipList, legalPersonp, accountDate, descName, type);
    }

    public List<SelectIpInfoBean> getIpListByWorkitemid ( Long iworkItemid, short type ) throws RepositoryException
    {
        return StartInfocollectonManager.getInstance().getIpListByWorkitemid(iworkItemid, type);
    }

    /**
     * 
     * @Title: startIcSystem   
     * @Description: 启动信息采集   
     * @param request
     * @param emSysName
     * @param planName
     * @param iworkItemid
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Administrator 
     * @date:   2019年9月11日 上午8:38:59
     */
    public Map startIcSystem ( HttpServletRequest request, String emSysName, String planName, Long iworkItemid,
            int type ) throws RepositoryException
    {
        Map map = new HashMap();

        try
        {
            SessionData sessionData = SessionData.getSessionData(request);
            String userId = sessionData.getUserInnerCode();
            String userName = sessionData.getUserName();
            if (null != emSysName && !emSysName.equals(""))
            {
                Map instanceList = StartInfocollectonManager.getInstance().getIcALlStartInfo(emSysName, iworkItemid,
                    planName, true, type);
                List ins = (List) instanceList.get("inst");
                Map step = (Map) instanceList.get("step");
                if (null != step && step.size() > 0)
                {
                    List instanceIds = StartInfocollectonManager.getInstance().startIcInfo(ins, step, userId, userName,
                        iworkItemid, type);
                    map.put(SUCCESS, true);
                    map.put(MESSAGE, "操作成功！");

                    String sysnames = "";

                    if (ins != null && !ins.isEmpty())
                    {
                        for (int j = 0; j < ins.size(); j++)
                        {
                            DisRecoverSwitchStartData bean = (DisRecoverSwitchStartData) ins.get(j);
                            if (j == 0)
                            {
                                sysnames = bean.getSysName();
                            } else
                            {
                                sysnames = sysnames + "," + bean.getInstanceName();
                            }
                        }
                        log.info("用户:" + userName + ";系统:" + sysnames + ";操作：信息采集");
                    }

                } else
                {
                    map.put(SUCCESS, false);
                    map.put(MESSAGE, "没有对应信息采集分类的数据！");
                }
            }
        } catch (Exception e)
        {
            e.printStackTrace();
            map.put(SUCCESS, false);
            map.put(MESSAGE, "操作失败！" + e.getMessage());
        }

        return map;
    }

}

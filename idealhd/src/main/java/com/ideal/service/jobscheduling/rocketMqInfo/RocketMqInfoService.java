package com.ideal.service.jobscheduling.rocketMqInfo;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.jobscheduling.bean.OnsMessageBean;
import com.ideal.ieai.server.jobscheduling.repository.onsMessage.OnsMessageManager;
import com.ideal.ieai.server.repository.RepositoryException;
import org.apache.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

public class RocketMqInfoService {
    private static final Logger log      = Logger.getLogger(RocketMqInfoService.class);
    public static final String         MESSAGE        = "message";
    public static final String         SUCCESS        = "success";
    public static final String         DATA_LIST      = "dataList";
    public static final String         TOTAL          = "total";
    public static final String         RESULT_SUCCESS = "查询成功！";
    public static final String         RESULT_FAIL    = "查询失败！";
    public static final String         SAVE_SUCCESS   = "保存成功！";
    public static final String         SAVE_FAIL      = "保存失败！";

    static private RocketMqInfoService _intance = new RocketMqInfoService();

    static public RocketMqInfoService getInstance ()
    {
        if (_intance == null)
        {
            _intance = new RocketMqInfoService();
        }
        return _intance;
    }

    public Map<String, Object> getRocketMqInfo(OnsMessageBean bean) {
        return OnsMessageManager.getInstance().getRocketMqInfo(bean);
    }

    public Map<String, Object> queryActName(String childName) {
        return OnsMessageManager.getInstance().queryActName(childName);

    }

    public Map<String, Object> queryJobName(String childName, String actName) {
        return OnsMessageManager.getInstance().queryJobName(childName,actName);
    }

    public Map<String, Object> saveSubmitMqMessage(OnsMessageBean bean) {
        Map<String, Object> map = new HashMap<String, Object>();
        OnsMessageManager manager = OnsMessageManager.getInstance();
        String bhMqUrl = Environment.getInstance().getBhMqUrl();

        try {
            /**  String cmd  = ' java -jar mq-ons-producer.jar START 测试子步骤5 ceshi111w 2022-09-03 测试子步骤5 ceshi_wchild ' */
            String cmd = "java -jar " + bhMqUrl + " " + bean.getJobStatus() + " " + bean.getJobName() + " " + bean.getActName() + " "
                    + bean.getDateTime() + " " + bean.getEndTag() + " " + bean.getChildName();
            log.info("手工补录MQ消息命令: " + cmd);
            boolean result = manager.execCmd(cmd);

            if (result) {
                boolean saveMessage = manager.saveSubmitMqMessage(bean);
                if (saveMessage) {
                    map.put(MESSAGE, "发送成功!");
                    map.put(SUCCESS, true);

                } else {
                    map.put(MESSAGE, "发送成功!保存手工补录MQ消息失败!");
                    map.put(SUCCESS, false);

                }
            } else {
                map.put(MESSAGE, "发送手工补录MQ消息失败!请检查Mq相关配置");
                map.put(SUCCESS, false);

            }

        } catch (Exception e) {
            log.info("saveSubmitMqMessage is error :", e);
            map.put(MESSAGE, "手工补录MQ消息失败!");
            map.put(SUCCESS, false);

        }
        return map;
    }

    public Map<String, Object> deleteRocketMqInfo(OnsMessageBean bean) {
        Map<String, Object> map  = new HashMap();
        try {
            map = OnsMessageManager.getInstance().deleteRocketMqInfo(bean);
            map.put(MESSAGE, "删除成功!");
            map.put(SUCCESS, true);
        } catch (RepositoryException e) {
            map.put(MESSAGE, "删除失败!");
            map.put(SUCCESS, false);
        }
        return map;

    }
}

package com.ideal.service.ic.supperhc.api;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.List;

import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.ic.supperhc.api.HcThreeAlarmApiBean;
import com.ideal.ieai.server.repository.hd.ic.supperhc.api.HcThreeAlarmApiConstants.JsonKey;
import com.ideal.ieai.server.repository.hd.ic.supperhc.api.HcThreeAlarmApiData;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.HcThreeAlarmApiDecryptUtil;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;

public class HcThreeAlarmApiService
{
    private static final Logger log = Logger.getLogger(HcThreeAlarmApiService.class);


    public boolean save (HcThreeAlarmApiBean bean ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean saveFlag = true;
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement updateps = null;
        
        PreparedStatement ps2 = null;
        
        PreparedStatement ps1= null;
        
        PreparedStatement ps3 = null;
        
        int index = 0;
        try {
            
            conn = DBResource.getJDBCConnection(method, log, Constants.IEAI_HEALTH_INSPECTION);
            String insertSql = " INSERT INTO IEAI_THIRD_PARTY_PATROL(IID,PLATFORM,GRADE,INAME,ALARMHOST,ALARMINFORMATION,ALARMTIME,ISTATE,ALARMSN,FDATE)"
                    + " VALUES(?,?,?,?,?,?,?,?,?,?) ";
            
            String updateSql=" update IEAI_THIRD_PARTY_PATROL set PLATFORM=?,GRADE=?,INAME=?,ALARMHOST=?,ALARMINFORMATION=?,ALARMTIME=?,ISTATE=? where ALARMSN=? ";
            
            
            String insertHisSql = " insert into IEAI_THIRD_PARTY_PATROL_HIS(IID,PLATFORM,GRADE,INAME,ALARMHOST,ALARMINFORMATION,ALARMTIME,ALARMSN,ISTATE ) "
                    + " select IID,PLATFORM,GRADE,INAME,ALARMHOST,ALARMINFORMATION,ALARMTIME,ALARMSN,ISTATE from  IEAI_THIRD_PARTY_PATROL where ALARMSN=? ";
            
            
            String upSql = " update  IEAI_THIRD_PARTY_PATROL_HIS set ISTATE=? where ALARMSN=? ";
            
            String delSql = " delete from IEAI_THIRD_PARTY_PATROL where ALARMSN=? ";
            
            
            
            ps = conn.prepareStatement(insertSql);
            
            updateps = conn.prepareStatement(updateSql);
            
            ps2 = conn.prepareStatement(insertHisSql);
            
            ps1 = conn.prepareStatement(upSql);
                    
            ps3 = conn.prepareStatement(delSql);
            
                    
            List<HcThreeAlarmApiData> dataList =  bean.getData();
            for (HcThreeAlarmApiData detailModel : dataList) {
                index = 0;
                if(1==detailModel.getOptType()){
                    Long iid = IdGenerator.createId("IEAI_THIRD_PARTY_PATROL", conn);
                    ps.setLong(++index, iid);
                    ps.setString(++index, "1");//华为
                    ps.setString(++index, String.valueOf(translateWarnLevel(detailModel.getPerceivedSeverity())));//告警级别 0：不确定  1：紧急   2：重要   3：次要   4：提示  5：已清除

                    ps.setString(++index, detailModel.getAlarmName()); //名称              华为：告警名称
                    ps.setString(++index, detailModel.getNeName()); //告警主机      华为：管理对象名称
                    
                    ps.setString(++index, detailModel.getObjectInstance()); //告警信息      华为：定位信息
                    
                    
                    ps.setTimestamp(++index, new Timestamp(detailModel.getEventTime())); //告警时间   华为-首次告警时间
                    
                    
                    ps.setLong(++index, detailModel.getOptType()); //状态  华为： 1：新增告警  2：清除告警   3：确认告警  4：反确认告警   5：变更告警   6：新增事件
                    ps.setInt(++index, detailModel.getAlarmSN()); 
                    ps.setLong(++index, System.currentTimeMillis()); 
                    ps.addBatch();
                }
                
                index=0;
                
                if(3==detailModel.getOptType()){
                 
                    updateps.setString(++index, "1");//华为
                    updateps.setString(++index, String.valueOf(translateWarnLevel(detailModel.getPerceivedSeverity())));//告警级别 0：不确定  1：紧急   2：重要   3：次要   4：提示  5：已清除

                    updateps.setString(++index, detailModel.getAlarmName()); //名称              华为：告警名称
                    updateps.setString(++index, detailModel.getNeName()); //告警主机      华为：管理对象名称
                    
                    updateps.setString(++index, detailModel.getObjectInstance()); //告警信息      华为：定位信息
                    
                    
                    updateps.setTimestamp(++index, new Timestamp(detailModel.getEventTime())); //告警时间   华为-首次告警时间
                    
                    
                    updateps.setLong(++index, detailModel.getOptType()); //状态  华为： 1：新增告警  2：清除告警   3：确认告警  4：反确认告警   5：变更告警   6：新增事件
                    
                    updateps.setInt(++index , detailModel.getAlarmSN());
                    
                    updateps.addBatch();
                }
                
                index=0;
                if(2==detailModel.getOptType()){ //删除
                  
                    ps2.setInt(++index, detailModel.getAlarmSN()); 
                    ps2.addBatch();
                 
                    index=0;
                    ps1.setLong(++index, detailModel.getOptType()); //状态  华为： 1：新增告警  2：清除告警   3：确认告警  4：反确认告警   5：变更告警   6：新增事件
                    ps1.setInt(++index, detailModel.getAlarmSN()); 
                    ps1.addBatch();
                    
                    index=0;
                    ps3.setInt(++index, detailModel.getAlarmSN());
                    ps3.addBatch();
                    
                }
                
            }
            ps.executeBatch(); 
            updateps.executeBatch();
            ps2.executeBatch();
            ps1.executeBatch();
            ps3.executeBatch();
            
            conn.commit();
            
        } catch (Exception e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, method, log);
            saveFlag = false;
            // 失败后数据回滚
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_INSERT);
            log.error(method, e);
            throw e;

        } finally {
            DBResource.closePreparedStatement(updateps, method, log);
            DBResource.closePreparedStatement(ps2, method, log);
            DBResource.closePreparedStatement(ps1, method, log);
            DBResource.closePreparedStatement(ps3, method, log);
            DBResource.closePSConn(conn, ps, method, log);
        }
        
        return saveFlag;
    }
    
    
    public int translateWarnLevel(Integer level) {
        if(level==null) {
            return 1;
        }
        if(level==5) {
            return 1;
        }else if(level==4) {
            return 2;
        }else if(level==3) {
            return 3;
        }else if(level==2) {
            return 4;
        }else if(level==1) {
            return 5;
        }else {
            return 0;
        }
            
    }

    public HcThreeAlarmApiBean dealJson ( JSONObject jsonobject ) throws Exception
    {
        HcThreeAlarmApiBean apiBean = new HcThreeAlarmApiBean();
        
        JSONArray data = jsonobject.getJSONArray(JsonKey.DATA);
        
        log.info("三方告警接口报文为：" + data);
        
//      String datastr= HcThreeAlarmApiDecryptUtil.decrypt(data.toString());
//        
//      log.info("三方告警接口解密后data报文为：" + data);
//        
//      List<HcThreeAlarmApiData> dataList = JSONObject.parseArray(datastr, HcThreeAlarmApiData.class);
//      apiBean.setData(dataList);
        
        List<HcThreeAlarmApiData> dataList = JSONObject.parseArray(data.toString(), HcThreeAlarmApiData.class);
        apiBean.setData(dataList);
        
        
        String resourceURI = HcThreeAlarmApiDecryptUtil.getParam(jsonobject, JsonKey.RESOURCEURI);
        log.info("resourceURI：" + resourceURI);
        apiBean.setResourceURI(resourceURI);
        
        
        String msgtype = HcThreeAlarmApiDecryptUtil.getParam(jsonobject, JsonKey.MSGTYPE);
        log.info("msgtype：" + msgtype);
        apiBean.setMsgType(Integer.parseInt(msgtype));
        
        
        String description = HcThreeAlarmApiDecryptUtil.getParam(jsonobject, JsonKey.DESCRIPTION);
        log.info("description：" + description);
        apiBean.setDescription(description);
        
        
        String timestamp = HcThreeAlarmApiDecryptUtil.getParam(jsonobject, JsonKey.TIMESTAMP);
        log.info("timestamp：" + timestamp);
        apiBean.setTimestamp(timestamp);
        
        
        return apiBean;
        
        
        
    }

    private JSONArray parseArray(String reqJson) throws Exception {
        reqJson = HcThreeAlarmApiDecryptUtil.checkParam("请求的JSON报文", reqJson);
        try {
            JSONArray jsonArray = JSONArray.parseArray(reqJson);
            return jsonArray;
        } catch (Exception e) {
            log.error(e);
            throw new Exception("解析JSON报文不正确，请检查",e);
        }
    }
    
    public HcThreeAlarmApiBean dealJson ( String json) throws Exception
    {
        HcThreeAlarmApiBean apiBean = new HcThreeAlarmApiBean();
        if(null!=json && !"".equals(json)){
            JSONArray data =  parseArray(json);
            List<HcThreeAlarmApiData> dataList = JSONObject.parseArray(data.toString(), HcThreeAlarmApiData.class);
            apiBean.setData(dataList);
        }
        return apiBean;
    }
    

}

package com.ideal.service.yearendactaskrun;

import java.util.Map;

import com.ideal.ieai.server.repository.hd.yearendactaskrun.YearendactaskrunManage;

public class YearendactaskrunService
{

    public Map<String, Object> getYearendactask ( String taskName )
    {
        return YearendactaskrunManage.getInstance().getYearendactask(taskName);
    }

    public Map<String, Object> runYearendactask ( String ids, String username, String insName )
    {
        return YearendactaskrunManage.getInstance().runYearendactask(ids, username, insName);
    }

    public Map<String, Object> getProcedureDataList ( String sysName, String iinstancename )
    {
        String excuteTable = "ieai_run_instance";
        int state = YearendactaskrunManage.getInstance().judgeSwitchState(sysName, iinstancename);
        if (state == 0)
        {
            excuteTable = "ieai_run_instance_his";
        }
        return YearendactaskrunManage.getInstance().getProcedureDataList(excuteTable, sysName, iinstancename);

    }
}

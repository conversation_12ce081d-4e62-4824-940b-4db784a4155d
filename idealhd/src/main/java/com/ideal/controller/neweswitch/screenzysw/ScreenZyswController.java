package com.ideal.controller.neweswitch.screenzysw;

import com.ideal.ieai.commons.Constants;
import com.ideal.service.eswitch.bjscreen.BjScreenService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Controller
public class ScreenZyswController {

    private static final Logger log = Logger.getLogger(ScreenZyswController.class);
    @RequestMapping("initScreenZysw.do")
    public String initScreenZysw ( HttpServletRequest request )
    {
        BjScreenService service=new BjScreenService();
        try
        {
            Map map = service.getRunningProLogicList(Constants.IEAI_EMERGENCY_SWITCH);
            request.setAttribute("logicList", map.get("list"));
        } catch (Exception e)
        {
            log.error(e);
        }
        return "eswitch/zyjrsw/zyswscreen";
    }
}

package com.ideal.controller.agentMaintain;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.SessionData;
import com.ideal.controller.sus.resourcemanage.exception.ServiceException;
import com.ideal.entity.CmdbSaveAgentRequestDTO;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.pubtoken.PublicTokenResponse;
import com.ideal.ieai.server.quartz.TimetaskService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.agent.AgentDataBean;
import com.ideal.ieai.server.repository.agent.AgentDownBean;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentActModel;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentInfoModel;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.ComputerDataModel;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.timetask.repository.TimetaskInfoBean;
import com.ideal.service.agentMaintain.AgentMaintainService;
import com.ideal.service.agentMaintain.UninstallAgentService;
import com.ideal.service.ic.hccomputeroperate.HcComputerOperateService;
import com.ideal.service.pubtoken.PublicTokenService;
import com.ideal.service.sus.resourcemanage.ResourceServerService;
import com.ideal.util.StringUtil;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <ul>
 * <li>Title: FFiveController.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2016年6月29日
 */
@Controller
public class AgentMaintainController
{
    public static final String SUCCESS = "success";
    public static final String MESSAGE = "message";
    public static final String DATALIST = "dataList";
    static Logger              log      = Logger.getLogger(AgentMaintainController.class);

    /**
     * <li>Description:应用标识配置</li> 
     * <AUTHOR>
     * 2017年6月12日 
     * @param
     * @return
     * return String
     */
    @RequestMapping("appMark.do")
    public String rolePermissionIndex ( Integer iid,String iagentcomputername )
    {
        return "agentMaintain/app_mark";
    }

    @RequestMapping("saveAgentServers.do")
    @ResponseBody
    public Object saveAgentServers ( String jsonData, Long agentId )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        ResourceServerService service = new ResourceServerService();
        try
        {
            service.saveAgentServersMultiple(jsonData, agentId);
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, "Save successfully!");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    @RequestMapping("getServerByIp.do")
    @ResponseBody
    public Object getServerByIp ( HttpServletRequest requst, String ip, String port )
    {
        Object resp = null;
        Map<String, Object> result = new HashMap<String, Object>();
        ResourceServerService service = new ResourceServerService();
        String start = null;
        String limit = null;
        try
        {
            start = requst.getParameter("start");
            limit = requst.getParameter("limit");
            resp = service.getServerByIp(start, limit, result, ip,port);
            result.put(DATALIST, resp);
        } catch (ServiceException e)
        {
            result.put(SUCCESS, false);
            result.put(MESSAGE, e.getMessage());
            resp = result;
        }
        return result;
    }

    /**
     * 福建农信-Agent管理应用标识-模块类型下拉选
     * @param request
     * @return
     */
    @RequestMapping("queryAgentModuleTypeDown.do")
    @ResponseBody
    public Map queryAgentModuleTypeDown ( HttpServletRequest request )
    {
        Map map = new HashMap();
        ResourceServerService rgSvc = new ResourceServerService();

        Long systemId =  -1L;
        if(null!=request.getParameter("systemIdForCiCd") || !"".equals(request.getParameter("systemIdForCiCd"))){
            systemId =  Long.parseLong(request.getParameter("systemIdForCiCd"));
        }
        try
        {
            int type = AgentMaintainManager.getInstance().getBasicGroupId();
            map=rgSvc.queryAgentModuleTypeDown(systemId,type);
            map.put("success", true);
            map.put("message", "操作成功");
        }catch(Exception e )
        {
            log.error("AgentMaintainController queryAgentModuleTypeDown is error",e);
            map.put("success", false);
            map.put("message", "操作失败");
        }
        return map;
    }

    @RequestMapping("agentMaintainIndex.do")
    public String agentMaintainIndex ()
    {
        return "agentMaintain/agent_maintain_index";
    }

    @RequestMapping("agentQuery.do")
    public String agentQuery ()
    {
        return "jobScheduling/agentquery/agentQuery";
    }


    @RequestMapping("agentMaintainIndexsd.do")
    public String agentMaintainIndexsd ()
    {
        return "agentMaintain/agent_maintain_index_sd";
    }
    
    @RequestMapping("agentMonitor.do")
    public String agentMonitor ()
    {
        return "agentMaintain/agentmonitor";
    }
    
    @RequestMapping("agentpanel.do")
    public String inspectionComputer ( HttpServletRequest request )
    {

        int agenttotalnum = 0;
        int effectagentnum = 0;
        int unusualagentnum = 0;
        AgentMaintainService service = new AgentMaintainService();
        Map res = null;
        try
        {
            res = service.getAgentNumDeital();
            request.setAttribute("agenttotalnum", res.get("total"));
            request.setAttribute("effectagentnum", res.get("effecttotal"));
            request.setAttribute("unusualagentnum", res.get("uneffecttotal"));
        } catch (Exception e)
        {
            log.error("getAgentNumDeital is error ", e);
        }
        return "agentMaintain/overview";
    }
    
    @RequestMapping("getAgentTaskExt.do")
    @ResponseBody
    public Object getAgentTaskExt () throws Exception
    {
        AgentMaintainService service = new AgentMaintainService();
        Map statistic = service.getAgentTaskExt();
        Map res = new HashMap();
        Map ss = new HashMap();

        List list = new ArrayList();

        ss.put("name", "运行作业");
        ss.put("data", statistic.get("successTotal"));
        list.add(ss);
        Map ss1 = new HashMap();
        ss1.put("name", "等待作业");
        ss1.put("data", statistic.get("failTotal"));
        list.add(ss1);
        res.put("dataList", list);
        return res;
    }

    
    @RequestMapping("getAgentNumExt.do")
    @ResponseBody
    public Object getAgentNumExt () throws Exception
    {
        AgentMaintainService service = new AgentMaintainService();
        Map<String, List> statistic = service.getAgentNumExt();

        return statistic;

    }
    @RequestMapping("AgentUpgradeYZMonitor.do")
    public String AgentUpgradeYZMonitor ()
    {
        return "agentMaintain/agent_upgrade_yz_monitor";
    }

    @RequestMapping("AgentUpgradeMonitor.do")
    public String fFiveSyncMonitor ()
    {
        return "agentMaintain/agent_upgrade_monitor";
    }

    @RequestMapping("getAgentMaintainList.do")
    @ResponseBody
    public Object getAgentMaintainList (HttpServletRequest request,String iagentName, String iagentIp, String iagentDesc, String iagentState, String iagentosname, String proId
            ,String icreateuser,String istarttime,String iendtime, int start, int limit,String iagentcomputername
            ,String icustommess, String iagentversion, String idcid,String os_smalltype,String proxyName
            ,String systeminfo,String centername,String sysadmin_a,String appadmin_a,String serverName
            ,String iequipmentOrVmId ,String iteam, String fjnxSysName,String envName)
    {
        Object res = null;
        String proIdN = "";// 不用工程id了
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            Map<String, String> sortMap =ParseJson.getOrderBy(request);//获取排序字段
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            long userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
            //agent 管理 查询增加服务名称 设备或者虚拟机 jiaMing 2023-5-17
            res = service.getAgentMaintainList(iagentName, iagentIp, iagentDesc, iagentState, iagentosname, proIdN,
                icreateuser, istarttime, iendtime,
                start, limit, sysType,sortMap, iagentcomputername,icustommess,iagentversion,idcid,os_smalltype,proxyName, systeminfo, centername, sysadmin_a, appadmin_a,serverName,iequipmentOrVmId,iteam,fjnxSysName,envName,userId);

        } catch (Exception e)
        {
            log.error("getAgentMaintainList is error ", e);
        }
        return res;
    }

    //Agent记录状态查询接口
    @RequestMapping("getAgentForStateList.do")
    @ResponseBody
    public Object getAgentForStateList (HttpServletRequest request)
    {
        Map res = new HashMap<>();
        String proIdN = "";// 不用工程id了
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            Map<String, String> sortMap =ParseJson.getOrderBy(request);//获取排序字段
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            String user = request.getParameter("user");
            String token = request.getParameter("token");
            String iagentIp = request.getParameter("iagentIp");
            String start = request.getParameter("start");
            String limit = request.getParameter("limit");
            log.info("user:"+user+",token:"+token+",iagentIp:"+iagentIp+",start:"+start+",limit:"+limit);
            PublicTokenResponse tokenResult =PublicTokenService.getInstance().validToken(user, token);//验证token
            if(tokenResult.getIsOk()){//token验证成功
                log.info("token验证成功:"+tokenResult.getToken());
                Map validateMap = validateParameter(request,user,token,iagentIp,start,limit);
                if (Boolean.parseBoolean(String.valueOf(validateMap.get("isOk"))))
                {
                    Map map = new HashMap();
                    map = service.getAgentForStateList(iagentIp,proIdN,
                            Integer.parseInt(start), Integer.parseInt(limit), sysType,sortMap);
                    res.put("status", "success");
                    res.put("message", "操作成功！");
                    res.put("content", map);
                }else{
                    // 参数错误 返回信息
                    res = (Map) validateMap.get("data");
                }
            }else {
                res.put("status", "fail");
                res.put("message",tokenResult.getMessage());
            }
        } catch (Exception e)
        {
            res.put("status", "fail");
            res.put("message", "操作失败！");
            log.error("getAgentForStateList is error ", e);
        }
        return res;
    }

    private Map validateParameter (HttpServletRequest request,String user,String token,String iagentIp,String start,String limit)
    {
        Map validateMap = new HashMap();
        Map res = new HashMap();
        if (StringUtils.isBlank(user) || StringUtils.isBlank(token) || StringUtils.isBlank(iagentIp))
        {
            if (StringUtils.isBlank(user))
            {
                res.put("status", "fail");
                res.put("message", "请求缺少user参数或者user参数为空！");
                validateMap.put("isOk", false);
                validateMap.put("data", res);
                return validateMap;
            }
            if (StringUtils.isBlank(token))
            {
                res.put("status", "fail");
                res.put("message", "请求缺少token参数或者token参数为空！");
                validateMap.put("isOk", false);
                validateMap.put("data", res);
                return validateMap;
            }
            if (StringUtils.isBlank(iagentIp))
            {
                res.put("status", "fail");
                res.put("message", "请求缺少iagentIp参数或者iagentIp参数为空！");
                validateMap.put("isOk", false);
                validateMap.put("data", res);
                return validateMap;
            }
        } else
        {
            validateMap.put("isOk", true);
        }
        if (StringUtils.isBlank(start))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少start参数或者start参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }else {
            if(!StringUtil.isNumberic(start)){
                res.put("status", "fail");
                res.put("message", "请求参数start必须为数字类型！");
                validateMap.put("isOk", false);
                validateMap.put("data", res);
                return validateMap;
            }
            validateMap.put("isOk", true);
        }
        if (StringUtils.isBlank(limit))
        {
            res.put("status", "fail");
            res.put("message", "请求缺少limit参数或者limit参数为空！");
            validateMap.put("isOk", false);
            validateMap.put("data", res);
            return validateMap;
        }else {
            if(!StringUtil.isNumberic(limit)){
                res.put("status", "fail");
                res.put("message", "请求参数limit必须为数字类型！");
                validateMap.put("isOk", false);
                validateMap.put("data", res);
                return validateMap;
            }
            validateMap.put("isOk", true);
        }
        return validateMap;
    }


    @RequestMapping("getAgentMonitorList.do")
    @ResponseBody
    public Object getAgentMonitorList (HttpServletRequest request,String iagentName, String iagentIp, String iagentDesc, String iagentState, String iagentosname, String proId,String icreateuser,String istarttime,String iendtime, int start, int limit,String iagentcomputername,String icustommess, String iagentversion, String cname, String ctype,String systeminfo, String idcid)
    {
        Object res = null;
        String proIdN = "";// 不用工程id了
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            Map<String, String> sortMap =ParseJson.getOrderBy(request);//获取排序字段
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentMonitorList(iagentName, iagentIp, iagentDesc, iagentState, iagentosname, proIdN,
                icreateuser, istarttime, iendtime,
                start, limit, sysType, sortMap, iagentcomputername, icustommess, iagentversion, cname, ctype,
                systeminfo, idcid, "");

        } catch (Exception e)
        {
            log.error("getAgentMonitorList is error ", e);
        }
        return res;
    }

    @RequestMapping("getAgentInstallResult.do")
    @ResponseBody
    public Map getAgentInstallResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int start, int limit )
    {
        Map res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentInstallResult( istarttime,iendtime,iinstallState, ifinish,rname, start, limit, sysType);

        } catch (Exception e)
        {
            log.error("getAgentInstallResult is error ", e);
        }
        return res;
    }
    @RequestMapping("getInstallResult.do")
    @ResponseBody
    public Map<String, Object> getInstallResult (long iid)
    {
        Map<String, Object> res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentInstallResult( iid, sysType);

        } catch (Exception e)
        {
            log.error("getAgentInstallResult is error ", e);
        }
        return res;
    }
    @RequestMapping("getUploadResult.do")
    @ResponseBody
    public Map<String, Object> getUploadResult (long iid)
    {
        Map<String, Object> res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getUploadResult( iid, sysType);

        } catch (Exception e)
        {
            log.error("getAgentInstallResult is error ", e);
        }
        return res;
    }
    
    @RequestMapping("agentConcurrentMonitor.do")
    @ResponseBody
    public Map<String, Object> agentConcurrentMonitor ( String ip, int port, String date )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        List<AgentDataBean> dataList = service.listMonitorData(Constants.IEAI_IEAI_BASIC, ip, port, date);
        res.put(DATALIST, dataList);
        return res;
    }

    @RequestMapping("agentHourMonitor.do")
    @ResponseBody
    public Map<String, Object> agentHourMonitor ( String ip, int port, String date, String hour )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        List<AgentDataBean> dataList = service.listMonitorHourData(Constants.IEAI_IEAI_BASIC, ip, port, date, hour);
        res.put(DATALIST, dataList);
        return res;
    }

    @RequestMapping("downloadAgentLog.do")
    public void downloadAgentLog ( HttpServletResponse response, String ip, int port, String filename )
    {
        AgentMaintainService service = new AgentMaintainService();

        try
        {
            response.reset();
            byte[] data = null;
            if(null!=filename && filename.indexOf("../")>-1) {
                data = new byte[0];
            }else {
                data = service.downLog(ip, port, filename);
                // 设置响应的报头信息(中文问题解决办法)
                response.setHeader("content-disposition", "attachment;fileName=" + URLEncoder.encode(filename, "UTF-8"));
            }
            // 设置响应的报头信息(中文问题解决办法)
            response.setHeader("content-disposition", "attachment;fileName=" + URLEncoder.encode(filename, "UTF-8"));
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream; charset=UTF-8");
            ServletOutputStream stream = response.getOutputStream();
            stream.write(data);
            stream.flush();
            stream.close();
        } catch (Exception e)
        {
            log.error("downloadAgentLog is error ", e);
        }
    }

    @RequestMapping("agentDownList.do")
    @ResponseBody
    public Map<String, Object> agentDownList ( String ip, int port )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        List<AgentDownBean> dataList = new ArrayList<AgentDownBean>();
        try
        {
            dataList = service.downList(ip, port);
        } catch (Exception e)
        {
            log.error("agentDownList is error ", e);
        }
        res.put(DATALIST, dataList);
        return res;
    }

    @RequestMapping("getAgentMaintainUpgradeInfo.do")
    @ResponseBody
    public Object getAgentMaintainUpgradeInfo ( int start, int limit )
    {
        Object res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentMaintainUpgradeInfo(start, limit, sysType);

        } catch (Exception e)
        {
            log.error("getAgentMaintainUpgradeInfo is error ", e);
        }
        return res;
    }

    @RequestMapping("getAgentActList.do")
    @ResponseBody
    public Object getAgentActList ( Long agentId, String proTypeString )
    {
        Object res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentActList(agentId, proTypeString, sysType);

        } catch (Exception e)
        {
            log.error("getAgentActList is error ", e);
        }
        return res;
    }

    @RequestMapping("getAgentMaintainClusterList.do")
    @ResponseBody
    public Object getAgentMaintainClusterList ()
    {
        Object res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentMaintainClusterList(sysType);
        } catch (Exception e)
        {
            log.error("getAgentMaintainClusterList is error ", e);
        }
        return res;
    }

    @RequestMapping("saveAgentMaitainInfos.do")
    @ResponseBody
    public Object saveAgentMaitainInfos ( String jsonData, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.saveAgentMaitainInfos(jsonData, sysType, request);

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }


    /**
     *  福建农信云管平台发送agent数据，保存接口
     * @param map
     * @return
     */
    @RequestMapping(value = "saveAgent.do",method = RequestMethod.POST)
    @ResponseBody
    public Object saveAgent (@RequestBody Map map )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            //保存或下线agent
            resp = service.fjnxSyncAgent(map,sysType);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     *  对cmdb平台推送的Agent数据进行保存
     * @param data CmdbSaveAgentRequestDTO
     * @return result
     */
    @RequestMapping(value = "cmdbSaveAgent.do",method = RequestMethod.POST)
    @ResponseBody
    public Object cmdbSaveAgent (@RequestBody CmdbSaveAgentRequestDTO data)
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            //保存agent
            resp = service.cmdbSaveAgent(data,sysType);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:浙商银行CMDB同步更新或插入agent和设备</li> 
     * <AUTHOR>
     * 2021年6月1日 
     * return Map
     */
    @RequestMapping("syncCmdbAndSave.do")
    public Map syncCmdbAndSave (HttpServletRequest request)
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.saveAgentAndEquipInfos();
            log.info("用户名： "+ userName + ",操作：cmdb同步");
        } catch (Exception e)
        {
           log.error("CMDB信息同步失败");
        }
        return resp;
    }
    
    @RequestMapping("deleteAgentMaintainInfos.do")
    @ResponseBody
    public Object deleteAgentMaintainInfos ( Long[] deleteIds, HttpServletRequest request,int state )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.deleteAgentMaintainInfos(deleteIds, sysType, userName,state);

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * @Title: deleteAgentMaintainInfos   
     * @Description: 删除Agent
     * @param deleteIds
     * @param request
     * @param state
     * @return      
     * @author: haijiao_dong 
     * @date:   2019年4月15日 下午3:00:46
     */
    @SuppressWarnings("unchecked")
    @RequestMapping("deleteTimeTaskAgentMaintainInfos.do")
    @ResponseBody
    public Object deleteTimeTaskAgentMaintainInfos ( Long[] deleteIds, HttpServletRequest request, int state )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            /**
             *  int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
             */
            
            
            Boolean candel = true;
            Boolean stopResult  = true;
            Boolean stopNewHcResult  = true;
            UserInfo userInfo = new UserInfo();
            userInfo.setId(Long.valueOf(SessionData.getSessionData(request).getUserInnerCode()));
            userInfo.setFullName(userName);
            HcComputerOperateService service2 = new HcComputerOperateService();
            stopResult = service2.stopHcForDelAgent(deleteIds, userInfo);
               
             //新巡检停止方法：
            boolean  suppercheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
            if(suppercheck) {
                log.info("执行删除agent停止新巡检开关开启。。准备进行当前agent所关联的巡检设备巡检终止");
               stopNewHcResult = service2.stopNewHcForDelAgent(deleteIds);
            }
            candel = stopResult && stopNewHcResult;
            
            // 定时任务失效方法:
            // 判断是否有需要停止的定时任务
            Map<String, Object> map = AgentMaintainManager.getInstance().isContainsRunningTimerTask(deleteIds);
            if ((Boolean) map.get(SUCCESS))
            {
                List<TimetaskInfoBean> taskList = (List<TimetaskInfoBean>) map.get("taskList");
                TimetaskService.getInstance().preStopTasks(taskList, null);
                TimetaskService.getInstance().setSopInsFlagForTasks(taskList);
            }
            //新增判断，停止巡检流程成功，才进行agent删除
            if(candel) {
                resp = service.deleteTimeTaskAgentMaintainInfos(deleteIds, userName, state);
            }else {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "停止待删除Agent关联的巡检设备巡检任务时，停止失败!");
            }

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    } 
    /**
     * 
     * @Title: syncAgentMaintainInfos   
     * @Description: CMDB同步Agent信息  
     * @param deleteIds
     * @param
     * @return      
     * @author: Administrator 
     * @date:   2018年8月31日 下午4:46:34
     */
    @RequestMapping("syncAgentMaintainInfos.do")
    @ResponseBody
    public Object syncAgentMaintainInfos ( Long[] deleteIds )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            service.syncAgentMaintainInfos(deleteIds);
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, "CMDB同步Agent设备成功！");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "CMDB同步Agent设备失败！");
        }
        return resp;
    }

    @RequestMapping("upgradeAgent.do")
    @ResponseBody
    public Object upgradeAgent ( Long[] ids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.upgradeAgent(ids, sysType, userName);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**   
     * @Title: upgradeAgentForCMDB   
     * @Description: 脚本服务化—CMDB集成—升级按钮
     * @param ids
     * @param request
     * @return      
     * @author: Administrator 
     * @date:   2019年8月12日 上午11:20:31   
     */
    @RequestMapping("upgradeAgentForCMDB.do")
    @ResponseBody
    public Object upgradeAgentForCMDB ( Long[] ids, HttpServletRequest request )
    {
        return this.upgradeAgent(ids, request);
    }

    /**
     * 
     * <li>Description:Agent安装</li> 
     * <AUTHOR>
     * 2021年5月26日 
     * @param installJson
     * @return
     * return List
     */
    @RequestMapping("installAgent.do")
    @ResponseBody
    public List installAgent ( String installJson )
    {
        Vector resp = new Vector();
        AgentMaintainService service = new AgentMaintainService();
        List<ComputerDataModel> list = (List<ComputerDataModel>) JSONArray.toCollection(
            JSONArray.fromObject(installJson), ComputerDataModel.class);
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.installAgent(list, sysType);
        } catch (Exception e)
        {
            log.error("installAgent is error ", e);
        }

        return resp;
    }

    @RequestMapping("installAgentNew.do")
    @ResponseBody
    public List installAgentNew ( String installJson, HttpServletRequest request )
    {
        Vector resp = new Vector();
        String userName = SessionData.getSessionData(request).getUserName();
        AgentMaintainService service = new AgentMaintainService();
        List<ComputerDataModel> list = (List<ComputerDataModel>) JSONArray.toCollection(
                JSONArray.fromObject(installJson), ComputerDataModel.class);
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            //resp = service.installAgent(list, sysType);
            for(int i=0; list!=null && i<list.size(); i++) {
                ComputerDataModel model=list.get(i);
                service.startInstallAgent ( resp, model.getIid(),  sysType,  userName,model.getIoperation_user(),model.getIoperation_password(),model.getIsource_path() );
            }

        } catch (Exception e)
        {
            log.error("installAgent is error ", e);
        }

        return resp;
    }

    @RequestMapping("deleteUpgradeAgentFlag.do")
    @ResponseBody
    public Object deleteUpgradeAgentFlag ( Long[] ids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.deleteUpgradeAgentFlag(ids, userName);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**   
     * @Title: deleteUpgradeAgentFlagForCMDB   
     * @Description: 脚本服务化—CMDB集成—清除升级标记按钮
     * @param ids
     * @param request
     * @return      
     * @author: Administrator 
     * @date:   2019年8月12日 上午10:27:26   
     */
    @RequestMapping("deleteUpgradeAgentFlagForCMDB.do")
    @ResponseBody
    public Object deleteUpgradeAgentFlagForCMDB ( Long[] ids, HttpServletRequest request )
    {
        return this.deleteUpgradeAgentFlag(ids, request);
    }

    @RequestMapping("fetchAgentInfo.do")
    @ResponseBody
    public Object fetchAgentInfo ( HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.fetchAgentInfo(sysType, userName);

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:获取对应id的Agent信息</li> 
     * <AUTHOR>
     * 2016年10月20日 
     * @param agentIds
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("fetchAgentInfoQuery.do")
    @ResponseBody
    public Object fetchAgentInfoQuery ( Long[] agentIds, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.fetchAgentInfoQuery(agentIds, sysType, userName, Constants.AGENT_MAINTAIN_MONITOR);

        } catch (Exception e)
        {
            log.error("fetchAgentInfoQuery is error ", e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    //Agent实时状态探测接口，对外调用，Server探测Agent并返回状态。
    @RequestMapping("getAgentStatusList.do")
    @ResponseBody
    public Object getAgentStatusList ( Long[] agentIds, HttpServletRequest request )
    {
        Map res = new HashMap<>();
        try {
            AgentMaintainService service = new AgentMaintainService();
            String user = request.getParameter("user");
            String token = request.getParameter("token");
            String agentIp = request.getParameter("agentIp");
            String agentPort = request.getParameter("agentPort");
            log.info("user:"+user+",token:"+token+",agentIp:"+agentIp+",agentPort:"+agentPort);
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentStateList(request,user,token,agentIp,agentPort,sysType);
        }catch (Exception e){
            res.put("status", "fail");
            res.put("message", "操作失败！");
            log.error("getAgentStatusList is error ", e);
        }
        return res;
    }


    @RequestMapping("getAgentUpgradeMonitorList.do")
    @ResponseBody
    public Object getAgentUpgradeMonitorList ( String istarttime, String iendtime, String iagentip, Integer istate,
            int start, int limit )
    {
        Object res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            res = service
                    .getAgentUpgradeMonitorList(start, limit, istarttime.trim(), iendtime.trim(), iagentip, istate);
        } catch (Exception e)
        {
            log.error("getAgentUpgradeMonitorList is error ", e);
        }
        return res;
    }

    @RequestMapping("agentBindUpgradeInfo.do")
    @ResponseBody
    public Object agentBindUpgradeInfo ( Long[] agentIds, Long agentUpId, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.agentBindUpgradeInfo(agentIds, agentUpId, userName);

        } catch (Exception e)
        {
            log.error("agentBindUpgradeInfo is error ", e);
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent管理配置信息Execl导入</li> 
     * <AUTHOR>
     * 2016年8月16日 
     * @param file
     * @param request
     * @param response
     * return void
     * @throws Exception 
     */
    @RequestMapping("uploadAgentCfg.do")
    public void uploadAgent ( @RequestParam("fileName") CommonsMultipartFile file, HttpServletRequest request,
            HttpServletResponse response ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        String userName = SessionData.getSessionData(request).getUserName();
        InputStream fis = null;

        AgentMaintainService service = new AgentMaintainService();
        String filename = file.getOriginalFilename();
        if (filename.toLowerCase().endsWith("xlsx") || filename.toLowerCase().endsWith("xlsm")
                || filename.toLowerCase().endsWith("xls") || filename.toLowerCase().endsWith("et"))
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            fis = file.getInputStream();
            resp = service.uploadAgent(filename, fis, sysType,request);
            log.info("用户名:" + userName + "操作:Agent管理导入操作。");
        } else
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "导入的文件类型不符合要求,请检查后再导入!(仅支持：.xlsx, .xlsm, .xls, .et文件类型)");
        }
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write("{'success':" + resp.get(SUCCESS) + ",'message':'" + resp.get(MESSAGE) + "'}");

    }

    private  AgentInfoModel paramert(HttpServletRequest request){
        AgentInfoModel agent=new AgentInfoModel();
        String iagentName =request.getParameter("iagentName");
        String iagentIp =request.getParameter("iagentIp");
        String iagentosname =request.getParameter("iagentosname");
        String iagentcomputername =request.getParameter("iagentcomputername");
        String icustommess =request.getParameter("icustommess");
        String iagentState =request.getParameter("iagentState");
        String iagentDesc =request.getParameter("iagentDesc");
        String iagentversion =request.getParameter("iagentversion");
        String istarttime =request.getParameter("istarttime");
        String iendtime =request.getParameter("iendtime");
        String os_smalltype =request.getParameter("os_smalltype");
        String proxyName =request.getParameter("proxyName");
        String createuser =request.getParameter("createuser");
        //agent 管理 查询增加服务名称 设备或者虚拟机 jiaMing 2023-5-17
        String serverName =request.getParameter("serverName");
        String iequipmentOrVmId =request.getParameter("iequipmentOrVmId");
        agent.setCreateuser(createuser);
        agent.setIagentcomputername(iagentcomputername);
        agent.setIagentDesc(iagentDesc);
        agent.setIagentIp(iagentIp);
        agent.setIagentosname(iagentosname);
        agent.setIagentName(iagentName);
        agent.setIagentState(iagentState);
        agent.setIagentversion(iagentversion);
        agent.setIcustommess(icustommess);
        agent.setIendtime(iendtime);
        agent.setIstarttime(istarttime);
        agent.setOs_smalltype(os_smalltype);
        agent.setProxyName(proxyName);
        agent.setServerName(serverName);
        agent.setIequipmentOrVmId(iequipmentOrVmId);
        return agent;
    }


    @RequestMapping("exportIPAll.do")
    public void exportIPAll (String id,String flag, HttpServletResponse response, HttpServletRequest request )
    {
        AgentInfoModel agent =paramert(request);
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            if(Environment.getInstance().getBankSwitchIsFjnx()){
                //福建农信导出
                service.exportFjnxComputerInfo(response, request, id, sysType,agent);
            }else{
                service.exportComputerInfo(response, request, id, sysType,agent,flag);
            }
            log.info("用户名:" + userName + "操作:Agent管理导出操作。");
        } catch (Exception e1)
        {
            log.error("exportIPAll is error ", e1);
        }
    }

    @RequestMapping("downloadAgentInfoTemplate.do")
    public void downloadTemplate(HttpServletResponse response, HttpServletRequest request ,String flag) {
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            service.downloadComputerInfoTemplate(response, request,flag);
            log.info("用户名:" + userName + "操作:Agent管理导出操作。");
        } catch (Exception e1)
        {
            log.error("exportIPAll is error ", e1);
        }
    }
    
    /**
     * 
     * <li>Description:是否变化信息</li> 
     * <AUTHOR>
     * 2017年11月13日 
     * @param iid
     * @param request
     * @param response
     * @return
     * @throws Exception
     * return Map
     */
    @RequestMapping("selectAgentInfo.do")
    @ResponseBody
    public Map selectAgentInfo(String iid,HttpServletRequest request, HttpServletResponse response)throws Exception{
        
        String str="";
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
         // 查看基础库的GroupId
           int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
           str =service.selectAgentInfo(iid,sysType);
            map.put(SUCCESS, str);
        } catch (Exception e)
        {
            log.error("selectAgentInfo is error ", e);
        }
       return map;
    }
    @RequestMapping("optAgentMsg.do")
    @ResponseBody
    public Map optAgentMsg ( HttpServletRequest request, HttpServletResponse response, 
            long agentId,String agentOpt ,String ioperationuser,String ioperationpassword)
    {
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        int sysType = Constants.IEAI_OPM;
        try
        {
            sysType = AgentMaintainManager.getInstance().getBasicGroupId();
        } catch (Exception e)
        {
            log.error("selectAgentInfo is error ", e);
        }
        Map map = service.optAgentMsg(agentId,agentOpt, sysType,userName,ioperationuser,ioperationpassword);
        return map;
    }
    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2017年12月9日 
     * @param request
     * @param response
     * @param
     * @param
     * @return
     * return Map
     */
    @RequestMapping("sendMsgCfg.do")
    @ResponseBody
    public Map sendMsgCfg ( HttpServletRequest request, HttpServletResponse response, 
            long[] agentIds,int operType)
    {
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        Map map = new HashMap();
        try
        {
            service.sendMsgCfg(agentIds,operType,userName);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            log.error("sendMsgCfg is error ", e);
            map.put(SUCCESS, false);
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:查询模块类型</li> 
     * <AUTHOR>
     * 2018年1月4日 
     * @return
     * @throws Exception
     * return Map
     */
    @RequestMapping("getProType.do")
    @ResponseBody
    public Map getProType ()
    {
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        List list = new ArrayList();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            list = service.getProType(sysType);
            map.put(DATALIST, list);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            log.error("getProType is error ", e);
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:重置</li> 
     * <AUTHOR>
     * 2018年1月5日 
     * @param request
     * @param response
     * @param agentId
     * @param jsonData
     * @return
     * return Map
     */
    @RequestMapping("resetNownum.do")
    @ResponseBody
    public Map resetNownum( HttpServletRequest request, HttpServletResponse response, long agentId, String[] jsonData){
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.resetNownum(agentId,jsonData,sysType);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "重置成功");
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "重置失败");
            log.error("resetNownum is error ", e);
        }
        return map;
    }
    
    @RequestMapping("updateIcustomCmd.do")
    @ResponseBody
    public Object updateIcustomCmd ( String iids, String icustomcmd )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            resp = service.updateIcustomCmd(iids, icustomcmd);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }
    @RequestMapping("batchInstallAgent.do")
    @ResponseBody
    public Object batchInstallAgent ( @RequestParam("importFile") CommonsMultipartFile file, HttpServletRequest request,
            HttpServletResponse response )
    {
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.batchInstallAgent(file,request,sysType);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "安装进行中...!");
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "安装失败!" + e.getMessage());
        }
        return map;
    }


    @RequestMapping("batchUnloadAgent.do")
    @ResponseBody
    public Object batchUnloadAgent ( @RequestParam("importFile") CommonsMultipartFile file, HttpServletRequest request,
                                      HttpServletResponse response )
    {
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.batchUnloadAgent(file,request,sysType);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "安装进行中...!");
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "安装失败!" + e.getMessage());
        }
        return map;
    }
    /**
     * 
     * <li>Description:暂停agent</li> 
     * <AUTHOR>
     * 2019年3月27日 
     * @param jsonData
     * @return
     * return Map
     */
    @RequestMapping("updateAgentStatePause.do")
    @ResponseBody
    public Map updateAgentStatePause(String jsonData){
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            boolean flag = new AgentMaintainService().updateAgentStatePause(jsonData,sysType);
            
            if(flag){
                map.put(SUCCESS, flag);
                map.put(MESSAGE, "暂停成功！");
            }else{
                map.put(SUCCESS, flag);
                map.put(MESSAGE, "暂停失败");
            }
        } catch (RepositoryException e)
        {
            map.put(MESSAGE, "暂停失败");
            log.error("updateAgentStatePause is error ", e);
        }
        
        return map;
    }
    
    /**
     * 
     * <li>Description:恢复agent</li> 
     * <AUTHOR>
     * 2019年3月27日 
     * @param jsonData
     * @return
     * return Map
     */
    @RequestMapping("updateAgentStateRecover.do")
    @ResponseBody
    public Map updateAgentStateRecover(String jsonData){
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            boolean flag = new AgentMaintainService().updateAgentStateRecover(jsonData,sysType);
            
            if(flag){
                map.put(SUCCESS, flag);
                map.put(MESSAGE, "恢复成功！");
            }else{
                map.put(SUCCESS, flag);
                map.put(MESSAGE, "恢复失败");
            }
        } catch (RepositoryException e)
        {
            map.put(MESSAGE, "恢复失败");
            log.error("updateAgentStateRecover is error ", e);
        }
        
        return map;
    }

    /**
     * 
     * @Title: exportIsystemTypeExcel   
     * @Description:Agent管理导出应用标识 
     * @param response      
     * @author: yunpeng_zhang 
     * @date:   2019年5月23日 上午10:21:55
     */
    @RequestMapping("exportIsystemTypeExcel.do")
    public void exportIsystemTypeExcel ( HttpServletResponse response, String ids,String flag )
    {
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            new AgentMaintainService().exportIsystemTypeExcel(response, ids, sysType,flag);
        } catch (RepositoryException e)
        {
            log.error("exportIsystemTypeExcel is error ", e);
        }

    }

    /**
     * 
     * @Title: importIsystemTypeExcel   
     * @Description:Agent管理导入应用标识
     * @param file
     * @param request
     * @param response      
     * @author: yunpeng_zhang 
     * @date:   2019年5月23日 下午4:57:43
     */
    @RequestMapping("importIsystemTypeExcel.do")
    public void importIsystemTypeExcel ( @RequestParam("fileName") CommonsMultipartFile file,
            HttpServletRequest request, HttpServletResponse response )
    {
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            long userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
            new AgentMaintainService().importIsystemTypeExcel(file, response, sysType,userId);
        } catch (RepositoryException e)
        {
            log.error("importIsystemTypeExcel is error ", e);
        }

    }

    @RequestMapping("getSysnamebyCpid.do")
    @ResponseBody
    public Map getSysnamebyCpid ( HttpServletResponse response, long cpid )
    {
        Map map = new HashMap();
        try
        {
            map = new AgentMaintainService().getSysnamebyCpid(cpid);
        } catch (RepositoryException e)
        {
            log.error("getSysnamebyCpid is error ", e);
        }
        return map;
    }

    @RequestMapping("deleteAgentValidForTT.do")
    @ResponseBody
    public Object deleteAgentValidForTT ( Long[] deleteIds, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            resp = AgentMaintainManager.getInstance().isContainsRunningTimerTask(deleteIds);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    @RequestMapping("exportAgentMonitorExcel.do")
    public void exportAgentMonitorExcel ( HttpServletResponse response, HttpServletRequest request, String iagentName,
            String iagentIp, String iagentDesc, String iagentState, String iagentosname,
            String icreateuser, String istarttime, String iendtime, int start, int limit, String iagentcomputername,
            String icustommess, String iagentversion, String cname, String ctype, String systeminfo, String idcid,
            String iids, String excelLineData ,String flag)
    {

      
        String proIdN = "";// 不用工程id了
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            Map<String, String> sortMap = ParseJson.getOrderBy(request);// 获取排序字段
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.exportAgentMonitorExcel(response, iagentName, iagentIp, iagentDesc, iagentState, iagentosname,
                proIdN, icreateuser, istarttime, iendtime, start, limit, sysType, sortMap, iagentcomputername,
                icustommess, iagentversion, cname, ctype, systeminfo, idcid, iids, excelLineData,flag);

        } catch (Exception e)
        {
            log.error("exportAgentMonitorExcel is error ", e);
        }
    }
    
    @RequestMapping("saveCibAgentInfo.do")
    @ResponseBody
    public Map saveCibAgentInfo(HttpServletRequest request, HttpServletResponse response){
        if(true) {
            return saveCibAgentInfoXy(request, response);
        }
        JSONObject resultJsonObject = null;
        AgentMaintainService service = new AgentMaintainService();
        BufferedReader br = null;
        Map map = new HashMap();
        map.put(SUCCESS, true);
        map.put(MESSAGE, "接口调用成功！");
        try
        {
            br = request.getReader();
            String str = "";
            StringBuilder requestParam = new StringBuilder();
            while((str = br.readLine()) != null) {
                requestParam.append(str);
            }
            String token = request.getHeader("Authorization");
            resultJsonObject = JSONObject.parseObject(requestParam.toString());
            resultJsonObject.put("token", token);
            Map saveMap = service.saveCibAgentInfoXyyg(resultJsonObject,response);
            if(!(boolean) saveMap.get(SUCCESS)){
                map.put(SUCCESS, false);
                map.put(MESSAGE, saveMap.get(MESSAGE));
                log.error("接口执行异常！");
            }
        } catch (Exception e)
        {
            log.error("AgentMaintainController.saveCibAgentInfo is error",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "云平台调用接口出现异常！");
        }finally {
            try
            {
                if(br!=null) {
                    br.close();
                }
            } catch (Exception e)
            {
                log.error(e.getMessage());
            }
        }
        return map;      
    }
    /**
     * 
     * <li>Description:Agent批量启停</li> 
     * <AUTHOR>
     * 2021年3月3日 
     * @param request
     * @param installJson
     * @param scriptPath
     * @param type
     * @param ioperationuser
     * @param ioperationpassword
     * return void
     */
    @RequestMapping("batchStartStopAgent.do")
    @ResponseBody
    public void batchStartStopAgent ( HttpServletRequest request, String installJson,String scriptPath,int type,String ioperationuser,String ioperationpassword )
    {
        String userName = SessionData.getSessionData(request).getUserName();
        String str = type ==0?"批量启动":"批量停止" ;
        AgentMaintainService service = new AgentMaintainService();
        List<ComputerDataModel> list = (List<ComputerDataModel>) JSONArray.toCollection(
            JSONArray.fromObject(installJson), ComputerDataModel.class);
        try
        {
            for(ComputerDataModel model:list){
                model.setIoperation_user(ioperationuser);
                model.setIoperation_password(ioperationpassword);
            }
            // 查看基础库的GroupId
            //int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            //保存批量启停的Agent信息
            service.saveStartStopAgentInfo(list,userName,type);
            service.batchStartStopAgent(list,scriptPath, type,userName);
            log.info("用户名:" + userName + "操作:Agent"+str+"操作。");
        } catch (Exception e)
        {
            log.error("batchStartStopAgent is error ", e);
        }
    }
    /**
     * 
     * <li>Description:获取Agent批量启停结果</li> 
     * <AUTHOR>
     * 2021年3月3日 
     * @param request
     * @param queryString
     * @param itime
     * @param start
     * @param limit
     * @return
     * return Map
     */
    @RequestMapping("getStartStopResult.do")
    @ResponseBody
    public Map getStartStopResult(HttpServletRequest request,String queryString,String itime,Integer start, Integer limit){
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            map = service.getStartStopResult(queryString,itime,start,limit,sysType);
        } catch (Exception e)
        {
            log.error("getStartStopResult is error ", e);
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:获取Agent批量启停时间</li> 
     * <AUTHOR>
     * 2021年3月3日 
     * @param request
     * @return
     * return Map
     */
    @RequestMapping("getStartStopTime.do")
    @ResponseBody
    public Map getStartStopTime(HttpServletRequest request){
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            map = service.getStartStopTime(sysType);
        } catch (RepositoryException e)
        {
            log.error("getStartStopTime is error ", e);
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:导入Excel批量启停Agent</li> 
     * <AUTHOR>
     * 2021年3月3日 
     * @param file
     * @param istartstoppath
     * @param type
     * @param request
     * @return
     * return Map
     */
    @RequestMapping("importBatchStartStopAgent.do")
    @ResponseBody
    public Map importBatchStartStopAgent (  @RequestParam("importFile") CommonsMultipartFile file,@RequestParam("istartstoppath") String istartstoppath,@RequestParam("type") int type,HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<>();
        String userName = SessionData.getSessionData(request).getUserName();
        AgentMaintainService service = new AgentMaintainService();
        InputStream fis = null;
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            String filename = file.getOriginalFilename();
            if (filename.toLowerCase().endsWith("xlsx") || filename.toLowerCase().endsWith("xlsm")
                    || filename.toLowerCase().endsWith("xls") || filename.toLowerCase().endsWith("et"))
            {
                fis = file.getInputStream();
                resp = service.uploadBatchStartStopExecl(filename, fis,userName,istartstoppath,type, sysType);
            }else{
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "上传失败， 仅支持Excel文件上传！");
            }
        } catch (Exception e)
        {
            log.error("Agent批量启停导入失败",e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "上传失败！"+e.getMessage());
        } finally{
            try
            {
                if(fis != null){
                    fis.close();
                }
            } catch (Exception ee)
            {
                log.error("读取文件异常",ee);
            }
        }
        return resp;
    }


    /**
     * <li>Description:守护进程批量启停Agent</li>
     * <AUTHOR>
     * 2021年12月14日
     * @param file 文件
     * @param type 类型
     * @param request 请求
     * @return 返回值
     */
    @RequestMapping("daemonsStartStopAgent.do")
    @ResponseBody
    public Map daemonsStartStopAgent (  @RequestParam("importFile") CommonsMultipartFile file,@RequestParam("type") int type,HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<>();
        String userName = SessionData.getSessionData(request).getUserName();
        AgentMaintainService service = new AgentMaintainService();
        InputStream fis = null;
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.daemonsStartStopExecl(file,type,userName,sysType);
        } catch (Exception e)
        {
            log.error("Agent批量启停导入失败",e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "上传失败！"+e.getMessage());
        } finally{
            try
            {
                if(fis != null){
                    fis.close();
                }
            } catch (Exception ee)
            {
                log.error("读取文件异常",ee);
            }
        }
        return resp;
    }



    @RequestMapping("getProTypeNew.do")
    @ResponseBody
    public Map getProTypeNew ()
    {
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        List list = new ArrayList();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            list = service.getProTypeNew(sysType);
            map.put(DATALIST, list);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            log.error("getProTypeNew is error ", e);
        }
        return map;
    }
    
    
    @RequestMapping("getPerformTaskList.do")
    @ResponseBody
    public Map getPerformTaskList ( Long agentId,String iagentip, String proType ,String iagentport,int start, int limit )
    {
        long begin = System.currentTimeMillis();
        log.info("getPerformTaskList : controller开始");
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            map = service.getPerformTaskList(agentId,iagentip, proType,iagentport,start,limit);

        } catch (Exception e)
        {
            log.error("getPerformTaskList is error ", e);
        }
        log.info("getPerformTaskList : controller结束,耗时：" + (System.currentTimeMillis() - begin));

        return map;
    }
    
    
    @RequestMapping("getCMDBPerformTaskList.do")
    @ResponseBody
    public Map getCMDBPerformTaskList ( int start, int limit )
    {
        Map map = new HashMap();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            map = service.getCMDBPerformTaskList(start,limit);

        } catch (Exception e)
        {
            log.error("getAgentActList is error ", e);
        }
        return map;
    }
    
    @RequestMapping("agentMonitorTaskForcestop.do")
    @ResponseBody
    public Object agentMonitorTaskForcestop ( String iagentip, String iagentport,String requuid,String sysType,String iflowid)
    {
        Map<String, Object> map = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            map = service.agentMonitorTaskForcestop(iagentip,iagentport, requuid,sysType,Integer.parseInt(sysType));

        } catch (Exception e)
        {
            log.error("agentMonitorTaskForcestop is error ", e);
        }
      
        return map;

    }
    
    /**
     * <li>Description:启动CMDB定时请求</li> 
     * <AUTHOR>
     * 2021年6月9日 
     * @param cron
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("saveCmdbTimeTask.do")
    @ResponseBody
    public Map<String,Object> saveCmdbTimeTask ( String cron)
    {
        Map<String, Object> map = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.saveCmdbTimeTask(cron, sysType);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "启动成功");
        } catch (Exception e)
        {
            log.error("启动失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "启动失败");
        }
        return map;
    }
    
    /**
     * <li>Description:关闭CMDB定时请求</li> 
     * <AUTHOR>
     * 2021年6月9日 
     * @param
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("deleteCmdbTimeTask.do")
    @ResponseBody
    public Map<String,Object> updateCmdbTimeTask ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            map = service.updateCmdbTimeTask(sysType);
        } catch (Exception e)
        {
            log.error("关闭失败", e);
        }
        return map;
    }
    
    
    /**
     * <li>Description:Agent管理描述模板导出</li> 
     * <AUTHOR>
     * 2021年9月14日 
     * @param
     * @param response
     * @param request
     * @param
     */
    @RequestMapping("exportIsystemTypeExceldes.do")
    public void exportAllCollectMoelData (  HttpServletResponse response, HttpServletRequest request,String flag)
    {

        AgentMaintainService service = new AgentMaintainService();
        try
        {
            service.exportIsystemTypeExceldes(response, request, flag);

        } catch (Exception e1)
        {
            log.error("操作失败", e1);
        }
    }
    
    
    /**
     * 
     * @Title: importIsystemTypeExcel   
     * @Description:Agent管理导入描述
     * @param file
     * @param request
     * @param response      
     * @date:   2021年9月14日 上午10:17:23
     */     
    @RequestMapping("importIsystemTypeExceldes.do")
    public void uploadBaseData ( @RequestParam("fileName") CommonsMultipartFile file,String iclassIid, HttpServletRequest request,
            HttpServletResponse response ) throws IOException
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        InputStream fis = null;
        AgentMaintainService service = new AgentMaintainService();
        String filename = file.getOriginalFilename();
        if (filename.toLowerCase().endsWith("xlsx") || filename.toLowerCase().endsWith("xlsm")
                || filename.toLowerCase().endsWith("xls") || filename.toLowerCase().endsWith("et"))
        {
            fis = file.getInputStream();
            resp = service.importIsystemTypeExceldes(filename, fis,iclassIid,1,request);
        } else
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "导入的文件类型不符合要求,请检查后再导入!(仅支持：.xlsx, .xlsm, .xls, .et文件类型)");
        }
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write("{'success':" + resp.get(SUCCESS) + ",'message':'" + resp.get(MESSAGE) + "'}");
    }

    /**
     *卸载agent
     * @param jsonData
     * @return
     */

    @RequestMapping("uninstallAgent.do")
    @ResponseBody
    public Map uninstallAgent ( String jsonData )
    {
        Map map = new HashMap();
        try
        {
            map= UninstallAgentService.getInstance().exect(jsonData, Constants.IEAI_COMPARE);
        } catch (Exception e)
        {
            log.error("卸载失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "卸载失败");
        }
        return map;
    }
    @RequestMapping("upgradeAgentMSBank.do")
    @ResponseBody
    public Object upgradeAgentMSBank ( Long[] ids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.daemonsScriptUpgrade(ids, sysType, userName);
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }
    
    
    /**
     * 
     * <li>Description:兴业云管对接生成切换模板</li> 
     * <AUTHOR>
     * Nov 28, 2022 
     * @param request
     * @param response
     * @return
     * return Map
     */
    @RequestMapping(value="saveCibAgentInfoXy.do", method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    @ResponseBody
    public Map saveCibAgentInfoXy(HttpServletRequest request, HttpServletResponse response){
        JSONObject resultJsonObject = null;
        AgentMaintainService service = new AgentMaintainService();
        BufferedReader br = null;
        Map map = new HashMap();
        map.put(SUCCESS, true);
        map.put(MESSAGE, "接口调用成功！");
        try
        {
            br = request.getReader();
            String str = "";
            StringBuilder requestParam = new StringBuilder();
            while((str = br.readLine()) != null) {
                requestParam.append(str);
            }
            String token = request.getHeader("Authorization");
            resultJsonObject = JSONObject.parseObject(requestParam.toString());
            resultJsonObject.put("token", token);
            Map saveMap = service.saveCibAgentInfoXyyg(resultJsonObject,response);
            if(!(boolean) saveMap.get(SUCCESS)){
                map.put(SUCCESS, false);
                map.put(MESSAGE, saveMap.get(MESSAGE));
                log.error("接口执行异常！");
            }
        } catch (Exception e)
        {
            log.error("AgentMaintainController.saveCibAgentInfoXy is error",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "云平台调用接口出现异常！");
        }finally {
            try
            {
                if(br!=null) {
                    br.close();
                }
            } catch (Exception e)
            {
                log.error(e.getMessage());
            }
        }
        return map;
    }

    @RequestMapping("installationResultExport.do")
    public void installationResultExport (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, HttpServletResponse response, HttpServletRequest request )
    {
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.exportInstallationResult(response, request,istarttime,iendtime,iinstallState,ifinish,rname,sysType);
            log.info("用户名:" + userName + "操作:Agent管理导出操作。");
        } catch (Exception e1)
        {
            log.error("exportIPAll is error ", e1);
        }
    }


    @RequestMapping("unloadResultExport.do")
    public void unloadResultExport (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, HttpServletResponse response, HttpServletRequest request )
    {
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            service.exportUnloadResult(response, request,istarttime,iendtime,iinstallState,ifinish,rname,sysType);
            log.info("用户名:" + userName + "操作:Agent管理导出操作。");
        } catch (Exception e1)
        {
            log.error("exportIPAll is error ", e1);
        }
    }

    @RequestMapping("getAgentUnloadResult.do")
    @ResponseBody
    public Map getAgentUnloadResult (String istarttime,String iendtime, String iinstallState,String ifinish,String rname, int start, int limit )
    {
        Map res = null;
        AgentMaintainService service = new AgentMaintainService();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            res = service.getAgentUnloadResult( istarttime,iendtime,iinstallState, ifinish,rname, start, limit, sysType);

        } catch (Exception e)
        {
            log.error("getAgentInstallResult is error ", e);
        }
        return res;
    }
    
    @RequestMapping("startNetScan.do")
    @ResponseBody
    public Map startNetScan ( String jsonData, HttpServletRequest request )
    {
        
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {

            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            map=service.startNetScan(jsonData,sysType,  request);
        } catch (Exception e)
        {
            log.error("操作失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "扫描失败！");
        }
        return map;
    }
    
    
    @RequestMapping("exportAllNetScan.do")
    public void exportAllNetScan (  HttpServletResponse response, HttpServletRequest request,String jsonData)
    {

        AgentMaintainService service = new AgentMaintainService();
        try
        {
            service.exportAllNetScan(response, request,jsonData);

        } catch (Exception e1)
        {
            log.error("操作失败", e1);
        }
    }
    @RequestMapping("stopAgentInfo.do")
    @ResponseBody
    public Object stopAgentInfo ( HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.stopAgentInfo(sysType, userName);

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     *
     * <li>Description:获取对应id的Agent信息</li>
     * <AUTHOR>
     * 2016年10月20日
     * @param agentIds
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("stopAgentInfoQuery.do")
    @ResponseBody
    public Object stopAgentInfoQuery ( Long[] agentIds, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.stopAgentInfoQuery(agentIds, sysType);

        } catch (Exception e)
        {
            log.error("fetchAgentInfoQuery is error ", e);
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    @RequestMapping("getAgentBeanList.do")
    @ResponseBody
    public Map getAgentBeanList ( HttpServletRequest request, Integer start, Integer limit, String iagentName,
                                  String iagentIp, String iagentState,
                                  String isystemname )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        long userId = Long.parseLong(sessionData.getUserInnerCode());
        String queryString = "";
        AgentMaintainService service = new AgentMaintainService();
        Map map = new HashMap();
        try
        {
            map = service.getAgentBeanList(userId, start, limit, queryString, iagentName, iagentIp, iagentState,
                    isystemname, Constants.IEAI_IEAI_BASIC);
        } catch (Exception e)
        {
            log.error("查询失败", e);
        }
        return map;
    }

    @RequestMapping("banAgent.do")
    @ResponseBody
    public Object banAgent ( Long agentId,String agentOpt, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentMaintainService service = new AgentMaintainService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            // 查看基础库的GroupId

            resp = service.banAgent(agentId, agentOpt);

        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * <li>Description:环境类型维护</li>
     * <AUTHOR>
     * 2024年4月10日
     * @param
     * @return
     * return String
     */
    @RequestMapping("envTypeMain.do")
    public String envTypeMain (HttpServletRequest request, Integer iid,String projectName )
    {
        request.setAttribute("iid", iid);
        Long projectId = null;
        try {
            projectId = ProjectManager.getInstance().getPrjIdByName(projectName);
        } catch (RepositoryException e) {
            throw new RuntimeException(e);
        }
        request.setAttribute("projectId", projectId);
        return "agentMaintain/env_main";
    }

    @RequestMapping("getEnvTypeByAgentId.do")
    @ResponseBody
    public Object getEnvTypeByAgentId ( HttpServletRequest requst, long agentId )
    {
        Map<String, Object> result = new HashMap<String, Object>();
        result = AgentMaintainService.getInstance().getEnvTypeByAgentId(agentId);
        return result;
    }

    @RequestMapping("saveAgentEnvs.do")
    @ResponseBody
    public Object saveAgentEnvs ( String jsonData, Long agentId )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            AgentMaintainService.getInstance().saveAgentEnvsMultiple(jsonData, agentId);
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, "Save successfully!");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * <li>Description: 根据deleteIds 删除相应的agent环境绑定</li>
     *
     * @param deleteIds
     * @return return Object
     * <AUTHOR>
     * 2024-4-11
     */
    @RequestMapping("deleteAgentEnvs.do")
    @ResponseBody
    public Object deleteAgentEnvs (String deleteIds)
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        boolean isOK = AgentMaintainService.getInstance().deleteAgentEnvs(deleteIds);
        if (isOK)
        {
            resp.put("success", true);
            resp.put("message", "Delete successfully!");
        } else
        {
            resp.put("success", false);
            resp.put("message", "Delete failed!");
        }
        return resp;
    }

    /**
     *
     * @Title: exportIEnvTypeExcel
     * @Description:福建农信Agent管理导出环境类型
     * @param response
     * @author: liu_yang
     * @date:   2024年4月12日 上午10:21:55
     */
    @RequestMapping("exportIEnvTypeExcel.do")
    public void exportIEnvTypeExcel ( HttpServletResponse response, String ids )
    {
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            AgentMaintainService.getInstance().exportIEnvTypeExcel(response, ids, sysType);
        } catch (RepositoryException e)
        {
            log.error("exportIEnvTypeExcel is error ", e);
        }

    }

    /**
     *
     * @Title: importIEnvTypeExcel
     * @Description:福建农信Agent管理导入环境类型
     * @param file
     * @param request
     * @param response
     * @author: liu_yang
     * @date:   2024年4月12日 下午4:57:43
     */
    @RequestMapping("importIEnvTypeExcel.do")
    public void importIEnvTypeExcel ( @RequestParam("fileName") CommonsMultipartFile file,
                                         HttpServletRequest request, HttpServletResponse response )
    {
        try
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            long userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
            AgentMaintainService.getInstance().importIEnvTypeExcel(file, response, sysType, userId);
        } catch (RepositoryException e)
        {
            log.error("importIEnvTypeExcel is error ", e);
        }

    }

    /**
     *
     * <li>Description:Agent管理配置信息Execl导入</li>
     * <AUTHOR>
     * 2024年4月13日
     * @param file
     * @param request
     * @param response
     * return void
     * @throws Exception
     */
    @RequestMapping("uploadAgentCfgFJNX.do")
    public void uploadAgentFJNX ( @RequestParam("fileName") CommonsMultipartFile file, HttpServletRequest request,
                              HttpServletResponse response ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        String userName = SessionData.getSessionData(request).getUserName();
        InputStream fis = null;

        AgentMaintainService service = new AgentMaintainService();
        String filename = file.getOriginalFilename();
        if (filename.toLowerCase().endsWith("xlsx") || filename.toLowerCase().endsWith("xlsm")
                || filename.toLowerCase().endsWith("xls"))
        {
            // 查看基础库的GroupId
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            fis = file.getInputStream();
            resp = service.uploadAgentFJNX(filename, fis, sysType,request);
            log.info("用户名:" + userName + "操作:Agent管理导入操作。");
        } else
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, "导入的文件类型不符合要求,请检查后再导入!(仅支持：.xlsx, .xlsm, .xls文件类型)");
        }
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write("{'success':" + resp.get(SUCCESS) + ",'message':'" + resp.get(MESSAGE) + "'}");

    }

    /**
     * 获取Agent信息包含的所有操作系统名称
     * @return
     */
    @RequestMapping("getOsNameFromAgent.do")
    @ResponseBody
    public Object getOsNameFromAgent ()
    {
        Map<String, Object> result = new HashMap<String, Object>();
        result = AgentMaintainService.getInstance().getOsNameFromAgent();
        return result;
    }

    @RequestMapping("getAgentActInfo.do")
    @ResponseBody
    public Object getAgentActInfo ( long agentId, Integer start, Integer limit )
    {
        Map<String, Object> result;
        result = AgentMaintainService.getInstance().getAgentActInfo(agentId,start,limit,Constants.IEAI_IEAI_BASIC);
        return result;
    }

    @RequestMapping("exportAgentActInfo.do")
    public void exportAgentActInfo ( long agentId,HttpServletRequest request, HttpServletResponse response )
    {
        String userName = SessionData.getSessionData(request).getUserName();
        try (OutputStream out = response.getOutputStream()) {
            // 查询要导出的数据
            List<AgentActModel> dataList = Collections.emptyList();
            try
            {
                Map<String, Object> result = AgentMaintainService.getInstance().getAgentActInfo(agentId,0,10000,Constants.IEAI_IEAI_BASIC);
                if(!result.isEmpty()){
                    dataList = (List) result.get("dataList");
                }
            } catch (Exception e)
            {
                log.error("getAgentActInfo is error ", e);
            }
            // 设置响应头，告诉浏览器这是一个 Excel 文件下载
            response.setContentType("application/x-download");
            response.setCharacterEncoding("utf-8");
            String fileName = "AgentActInfo_" + System.currentTimeMillis() + ".xls";
            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1"));

            // 使用 EasyExcel 将数据写入 Excel
            EasyExcel.write(out)
                    .head(AgentActModel.class)
                    .autoCloseStream(true)
                    .excelType(ExcelTypeEnum.XLS)
                    .sheet("Sheet1")
                    .doWrite(dataList);
            out.flush();
            log.info("用户：" + userName + "，成功导出 AgentActInfo 数据");
        } catch (Exception e) {
            log.error("exportAgentActInfo 发生错误 ", e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }
}
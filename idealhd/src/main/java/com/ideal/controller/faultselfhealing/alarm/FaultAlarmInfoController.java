package com.ideal.controller.faultselfhealing.alarm;

import com.ideal.controller.ControlConstants;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.faultselfhealing.repository.taskmonitor.TaskMonitorManage;
import com.ideal.ieai.server.faultselfhealing.repository.taskmonitor.TaskMonitorModel;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.service.faultselfhealing.alarminfo.AlarmInfoService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * @ClassName:  FaultAlarmInfoController   
 * @Description:故障自愈报警信息报表   
 * @author: lei_wang 
 * @date:   2019年11月6日 上午10:16:04   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@Controller
public class FaultAlarmInfoController
{
    private Logger log = Logger.getLogger(FaultAlarmInfoController.class);

    @RequestMapping("faultSelfHealing/initAlarmInfo.do")
    public String initAlarmCombination ()
    {
        return "faultselfhealing/alarminfo/alarmInfo";
    }

    @RequestMapping("getFaultAlarmInfo.do")
    @ResponseBody
    public Map<String, Object> getAlarmInfoList ( HttpServletRequest request, Integer start, Integer limit,
            String platformName, String schemeName, String hostIp, Long firstStartTime, Long lastStartTime ,String handelDetail,String ialarmcontent,String isysName,String alarmId)
    {
        AlarmInfoService alarmInfoService = AlarmInfoService.getInstance();
        String isTrigger = request.getParameter("isTrigger");
        return alarmInfoService.getAlarmInfoListNew(start, limit, platformName, schemeName, hostIp, firstStartTime, lastStartTime, handelDetail,ialarmcontent,
            isysName,isTrigger, Constants.IEAI_FSH,alarmId);
    }

    /**
     * 
     * @Title: getAlarmTypeName   
     * @Description: 根据自愈类型id查询自愈类型信息 
     * @param ialarmTypeId
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月5日 上午9:22:38
     */
    @RequestMapping("faultSelfHealing/getAlarmTypeName.do")
    @ResponseBody
    public Map<String, String> getAlarmTypeName ( Long ialarmTypeId )
    {
        Map<String, String> map = new HashMap<String, String>();
        try
        {
            AlarmInfoService service = AlarmInfoService.getInstance();
            map = service.getAlarmTypeName(ialarmTypeId, Constants.IEAI_FSH);
            if (map != null)
            {
                map.put(ControlConstants.MAP_MESSAGE, map.get("iname"));
            }
        } catch (Exception e)
        {
            map.put(ControlConstants.MAP_MESSAGE, e.getMessage());
            log.error("FaultAlarmInfoController.getAlarmTypeName Error! " + e.getMessage());
        }
        return map;

    }

    @RequestMapping("faultSelfHealing/getSelfHealingSchemeName.do")
    @ResponseBody
    public Map<String, Object> getSelfHealingSchemeName ( Long ischemeId )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            AlarmInfoService service = AlarmInfoService.getInstance();
            map = service.getSelfHealingSchemeName(ischemeId, Constants.IEAI_FSH);
            if (map != null)
            {
                map.put(ControlConstants.MAP_MESSAGE, map.get("iname"));
                map.put("sceneId", map.get("sceneId"));
            }
        } catch (Exception e)
        {
            map.put(ControlConstants.MAP_MESSAGE, e.getMessage());
            log.error("FaultAlarmInfoController.getSelfHealingSchemeName Error! " + e.getMessage());
        }
        return map;

    }

//    @RequestMapping("monitorGetAlarmType.do")
//    @ResponseBody
//    public Map monitorGetScheme ( HttpServletRequest request )
//    {
//        AlarmInfoService service = AlarmInfoService.getInstance();
//        return service.getAlarmType(Constants.IEAI_FSH);
//    }

    @RequestMapping("faultSelfHealing/getSelfHealingExecresult.do")
    @ResponseBody
    public Map<String, String> getSelfHealingExecresult ( long selfId )
    {
        Map<String, String> map = new HashMap<String, String>();
        Connection conn = null;
        try
        {
            TaskMonitorManage taskMonitorManage = new TaskMonitorManage();
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_FSH);
            TaskMonitorModel model = taskMonitorManage.getTaskMonitorOne(selfId, conn);
            map.put("message", model.getItaskExecresult());
        } catch (Exception e)
        {
            map.put("message", e.getMessage());
            log.error("FaultAlarmInfoController.getSelfHealingExecresult Error! " + e.getMessage());
        } finally
        {
            DBResource.closeConnection(conn, "getSelfHealingExecresult", log);
        }
        return map;
    }
}

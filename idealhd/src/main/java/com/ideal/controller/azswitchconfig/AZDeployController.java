package com.ideal.controller.azswitchconfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.cmdb.service.CronExpression;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.azconfig.AzSwitchConfigManager;
import com.ideal.ieai.server.repository.azconfig.thread.AZSwitchSchedulerThread;
import com.ideal.ieai.server.repository.hd.releaseMonitor.model.RMQuery;
import com.ideal.ieai.server.repository.hd.releaseMonitor.model.ReleaseMonitorGroup;
import com.ideal.service.ReleaseMonitorService;
import com.ideal.service.az.AZSwitchBaseService;

/**
 * 
 * <ul>
 * <li>Title: AzDeployController.java</li>
 * <li>Description:浦发需求:AZ切换处理Controller层</li>
 * <li>Copyright: Copyright 2020</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020年8月6日
 */
@Controller
public class AZDeployController
{

    private static final Logger   log             = Logger.getLogger(AZDeployController.class);
    private static final String   SUCCESS         = "success";
    private static final String   MESSAGE         = "message";
    public static final String    NOICON          = "no-icon";
    public static final String    AGENTIP         = "代理服务器:";
    private String                cronVal         = "timingCronValue";                                                                                                                                                         // AZ切换配置页面定时任务cron表达式
    private AzSwitchConfigManager azConfigManager = AzSwitchConfigManager.getInstance();
    private AZSwitchBaseService   azBaseService   = AZSwitchBaseService.getInstance();
    public static final String    PAGEBACKTYPE    = "pageBackType";

    // 去到AZ切换参数配置页面
    @RequestMapping("azinfoconfig.do")
    public String azInfoConfig ()
    {
        return "/azSwitchConfig/azConfig";
    }

    // 去到AZ切换系统列表页面
    @RequestMapping("azinfoSysList.do")
    public String azinfoSysList ()
    {
        return "/azSwitchSysList/azSysList";
    }

    // 去到AZ切换历史页面
    @RequestMapping("azinfoHistoryList.do")
    public String azinfoHistoryList (HttpServletRequest request)
    {
        String sysName = request.getParameter("sysName");
        if(sysName==null) {
            sysName=request.getParameter("instancename");
        }
        request.setAttribute("sysName", sysName);
        // 变更单号
        String version = request.getParameter("version");
        request.setAttribute("version", version);
        // 变更说明
        String insName = request.getParameter("insName");
        if(insName==null) {
            insName=request.getParameter("instname");
        }
        request.setAttribute("insName", insName);
        request.setAttribute("islcbp", false);
        if(null!=sysName&&null!=version&&null!=insName) {
            request.setAttribute("islcbp", true);
        }
        return "/azSwitchHistory/azHistory";
    }

    /**
     * 
     * <li>Description:AZ切换参数配置获取cron表达式</li> 
     * <AUTHOR>
     * 2020年8月6日 
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("getAZCronVal.do")
    @ResponseBody
    public Map<String, Object> getCronVal ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        String cron = "";
        try
        {
            cron = azConfigManager.getCronValue(Constants.IEAI_AZ, cronVal);
            log.info("---------AZ切换获取cron表达式为:" + cron);
            map.put("cronValue", cron);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put("cronValue", "");
            map.put(SUCCESS, false);
            log.error("getCronVal is error:" + e.getMessage());
        }
        return map;
    }

    // 修改AZ切换参数配置cron表达式
    @RequestMapping("saveCron.do")
    @ResponseBody
    public Map<String, Object> modifyCron ( String cronValue )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        // 校验cron表达式格式
        boolean valid = CronExpression.isValidExpression(cronValue);
        if (!valid)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "非法的cron表达式");
            log.info("-----AZ切换cron表达式非法,禁止修改-------");
            return map;
        }
        try
        {
            azConfigManager.modifyCronValue(Constants.IEAI_AZ, cronVal, cronValue);
            // 重新设置定时任务(已经启动的任务不受影响,待到任务执行完后按照修改后新的cron表达式执行任务)
            new AZSwitchSchedulerThread().modifyCron(cronValue);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "修改成功");
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "非法的cron表达式");
            log.info("modifyCron is error" + e.getMessage());
        }
        return map;
    }

    /**
     * 
     * <li>Description:获取AZ切换系统列表</li> 
     * <AUTHOR>
     * 2020年8月7日 
     * @param request
     * @param start
     * @param limit
     * @param appName
     * @param sceneName
     * @param crashName
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("getAZSysList.do")
    @ResponseBody
    public Map<String, Object> getAZSysList ( HttpServletRequest request, Integer start, Integer limit, String appName,
            String sceneName, String crashName, String azType )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        String userName = sessionData.getUserName();
        Long userId = Long.parseLong(sessionData.getUserInnerCode());
        return azConfigManager.getAZSysList(userName, userId, start, limit, appName, Constants.IEAI_AZ, sceneName,crashName ,azType);
            
    }

    /**
     * 
     * <li>Description:删除AZ切换系统列表</li> 
     * <AUTHOR>
     * 2020年8月7日 
     * @param iids
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("delAZsystems.do")
    @ResponseBody
    public Map<String, Object> delAZSysterms ( String[] iids )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            map = azBaseService.delAZSysterms(iids);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", "数据删除失败！");

        }
        return map;
    }

    /**
     * 
     * <li>Description:获取AZ切换历史</li> 
     * <AUTHOR>
     * 2020年8月10日 
     * @param request
     * @param query
     * @return
     * return Object
     */
    @RequestMapping("getAZHistory.do")
    @ResponseBody
    public Object getAZHistory ( HttpServletRequest request, RMQuery query )
    {
        Object res = null;
        Map<String, String> sortMap = ParseJson.getOrderBy(request);// 获取排序字段
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        try
        {
            res = azBaseService.getAZHistoryList(userInfo, query, Constants.IEAI_AZ, sortMap);
        } catch (Exception e)
        {
            log.error("获取AZ切换历史数据异常：getAZHistory()：" + e.getMessage());
        }
        return res;
    }

    /**
     * 
     * <li>Description:AZ切换历史页面导出</li> 
     * <AUTHOR>
     * 2020年8月12日 
     * @param request
     * @param response
     * @param query
     * return void
     */
    @RequestMapping("exportAZHistoryExcel.do")
    public void exportAZHistoryExcel ( HttpServletRequest request, HttpServletResponse response, RMQuery query )
    {
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        azBaseService.exportHistoryListExcel(response, userInfo, query, Constants.IEAI_AZ);
    }

    
    /**
     * 
     * <li>Description:去到AZ历史二级页面</li> 
     * <AUTHOR>
     * 2020年8月13日 
     * @param request
     * @return
     * return String
     */
    @RequestMapping("releaseAZHistoryFlowGroup.do")
    public String releaseAZHistoryFlowGroup ( HttpServletRequest request )
    {
        try
        {
            long insId = Long.parseLong(request.getParameter("insId"));
            String pageBackType = request.getParameter(PAGEBACKTYPE);
            String checkFlowFlag = request.getParameter("checkFlowFlag");
            String checkHisFlag = request.getParameter("checkHisFlag");
            int countIp = ReleaseMonitorService.getInstance().getCountIpforInstanceHisLoop(insId, Constants.IEAI_AZ);
            request.setAttribute("countIp", countIp);
            request.setAttribute(PAGEBACKTYPE, pageBackType);
            request.setAttribute("checkFlowFlag", checkFlowFlag);
            request.setAttribute("checkHisFlag", checkHisFlag);

            /* 点击系统名称进入子页面后 传递过来页面原有查询条件 */
            // 系统名称
            String sysName = request.getParameter("sysName");
            request.setAttribute("sysName", sysName);
            // 变更说明
            String insName = request.getParameter("insName");
            request.setAttribute("insName", insName);
            // 变更单号
            String version = request.getParameter("version");
            request.setAttribute("version", version);
            // 工单号
            String workOrderNum = request.getParameter("workOrderNum");
            request.setAttribute("workOrderNum", workOrderNum);
            // 启动用户
            String startUser = request.getParameter("startUser");
            request.setAttribute("startUser", startUser);
            // 开始时间
            String startTimeQuery = request.getParameter("startTimeQuery");
            request.setAttribute("startTimeQuery", startTimeQuery);
            // 结束时间
            String endTimeQuery = request.getParameter("endTimeQuery");
            request.setAttribute("endTimeQuery", endTimeQuery);
            // 当前页数
            String currentPage = request.getParameter("currentPage");
            request.setAttribute("currentPage", currentPage);
            // 起始start
            String start = request.getParameter("start");
            request.setAttribute("start", start);
            // 每页数据量
            String pageSize = request.getParameter("pageSize");
            request.setAttribute("pageSize", pageSize);

        } catch (RepositoryException e)
        {
            request.setAttribute("countIp", 0);
            log.info(e);
        }
        return "azSwitchHistory/detail/azHistoryDetail";
    }

    /**
     * 
     * <li>Description:获取浦发AZ切换历史二级页面数据</li> 
     * <AUTHOR>
     * 2020年8月13日 
     * @param insId
     * @param step
     * @param showFinishFlow
     * @param isHistory
     * @param pkgName
     * @param groupByStep
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("getAzDetailGroupForTreePanel.do")
    @ResponseBody
    public Object getAzDetailGroupForTreePanel ( Long insId, Integer step, Integer showFinishFlow, Integer isHistory,
            String pkgName, String groupByStep, HttpServletRequest request )
    {
        // 浦发样式展示
        boolean pfHistoryShow = Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_SUS_FLOWHISTORY_SHOW,false);
        boolean isShowFinishFlow = (showFinishFlow == 1) ? true : false;
        List<ReleaseMonitorGroup> res = null;
        List<ReleaseMonitorGroup> finalList = new ArrayList<ReleaseMonitorGroup>();
        ReleaseMonitorService service = new ReleaseMonitorService();
        // 用于判断是 AZ切换 --> 40 
        String releaseFlowType = request.getParameter(PAGEBACKTYPE);
        if (null != releaseFlowType && !"null".equals(releaseFlowType) && !"".equals(releaseFlowType))
        {
            ReleaseMonitorService.setReleaseFlowType(releaseFlowType);
        } else
        {
            ReleaseMonitorService.setReleaseFlowType("");
        }
        try
        {

            res = service.getReleaseMonitorGroupList(insId, step, isShowFinishFlow, isHistory, pkgName);
            if (pfHistoryShow)// 按浦发样式展示
            {
                return getPfReleaseMonitorGroupList(res, finalList);
            }
            if (groupByStep != null && groupByStep.equals("1"))// 按步骤分组
            {
                return getReleaseMonitorGroupByStep(res, finalList);
            }
            for (ReleaseMonitorGroup rmg : res)
            {
                boolean pkgExisit = false;
                String pkgNameSon = rmg.getPkgName();
                // 判断包名节点是否存在
                for (ReleaseMonitorGroup rmgf : finalList)
                {
                    String pkgNameSonTmpString = "包名:" + pkgNameSon;
                    if (releaseFlowType.equals(String.valueOf(Constants.IEAI_AZ)))
                    {
                        pkgNameSonTmpString = pkgNameSon;
                    }

                    if (rmgf.getActName().equals(pkgNameSonTmpString))
                    {
                        pkgExisit = true;
                        boolean ipExisit = false;
                        // 判断ip是否存在
                        for (ReleaseMonitorGroup rmgIp : rmgf.getChildren())
                        {
                            if (rmgIp.getActName().equals(AGENTIP + rmg.getIp()))
                            {
                                ipExisit = true;
                                rmg.setLeaf(true);
                                rmg.setIconCls(NOICON);
                                rmgIp.getChildren().add(rmg);
                            }

                        }
                        if (!ipExisit)
                        {
                            // 如果ip节点不存在则新增
                            ReleaseMonitorGroup newRmg = new ReleaseMonitorGroup();
                            newRmg.setActName(AGENTIP + rmg.getIp());
                            newRmg.setIconCls(NOICON);
                            rmg.setLeaf(true);
                            rmg.setIconCls(NOICON);
                            newRmg.getChildren().add(rmg);
                            newRmg.setExpanded(true);
                            rmgf.getChildren().add(newRmg);
                        }

                    }
                }
                if (!pkgExisit)
                {
                    // 如果包名节点不存在则新增
                    ReleaseMonitorGroup newIp = new ReleaseMonitorGroup();
                    newIp.setActName(AGENTIP + rmg.getIp());
                    newIp.setIconCls(NOICON);
                    newIp.setExpanded(true);
                    rmg.setLeaf(true);
                    rmg.setIconCls(NOICON);
                    newIp.getChildren().add(rmg);
                    ReleaseMonitorGroup newpkg = new ReleaseMonitorGroup();
                    if (releaseFlowType.equals(String.valueOf(Constants.IEAI_AZ)))
                    {
                        newpkg.setActName(pkgNameSon);
                    }
                    newpkg.setIconCls(NOICON);
                    newpkg.setExpanded(true);
                    newpkg.getChildren().add(newIp);
                    finalList.add(newpkg);
                }

            }
        } catch (Exception e)
        {
            log.info("getAzDetailGroupForTreePanel() is error:" + e.getMessage());
        }
        return finalList;
    }

    /**
     * 
     * <li>Description:AZ切换历史详情浦发样式展示</li> 
     * <AUTHOR>
     * 2020年8月13日 
     * @param res
     * @param finalList
     * @return
     * return Object
     */
    private Object getPfReleaseMonitorGroupList ( List<ReleaseMonitorGroup> res, List<ReleaseMonitorGroup> finalList )
    {
        StringBuilder actNames = new StringBuilder();
        List<ReleaseMonitorGroup> firstLevel = new ArrayList<ReleaseMonitorGroup>();
        for (ReleaseMonitorGroup rmg : res)
        {
            if (!actNames.toString().contains(rmg.getPkgName()))
            {
                actNames.append(rmg.getPkgName() + ",");
                ReleaseMonitorGroup newpkg = new ReleaseMonitorGroup();
                newpkg.setActName("包名:" + rmg.getPkgName());
                newpkg.setIconCls(NOICON);
                newpkg.setExpanded(true);
                firstLevel.add(newpkg);
            }
        }
        for (ReleaseMonitorGroup rmgs : firstLevel)  // 只按按包名分组
        {
            StringBuilder leafActNames = new StringBuilder();
            for (ReleaseMonitorGroup rmg : res)
            {
                if (rmgs.getPkgName() == null)
                {
                    rmgs.setPkgName("");
                }
                if (rmg.getPkgName() == null)
                {
                    rmg.setPkgName("");
                }
                if (("包名:" + rmg.getPkgName()).equals(rmgs.getActName()))
                {
                    leafActNames.append(rmg.getActName() + ",");
                    rmg.setLeaf(true);
                    rmg.setIconCls(NOICON);
                    rmgs.getChildren().add(rmg);
                }
            }
            finalList.add(rmgs);
        }
        return finalList;
    }

    private Object getReleaseMonitorGroupByStep ( List<ReleaseMonitorGroup> res, List<ReleaseMonitorGroup> finalList )
    {
        StringBuilder actNames = new StringBuilder();
        List<ReleaseMonitorGroup> firstLevel = new ArrayList<ReleaseMonitorGroup>();
        for (ReleaseMonitorGroup rmg : res)
        {
            if (!actNames.toString().contains(rmg.getPkgName()))
            {
                actNames.append(rmg.getPkgName() + ",");
                ReleaseMonitorGroup newpkg = new ReleaseMonitorGroup();
                newpkg.setActName("包名:" + rmg.getPkgName());
                newpkg.setIconCls(NOICON);
                newpkg.setExpanded(true);
                firstLevel.add(newpkg);
            }
        }
        for (ReleaseMonitorGroup rmgs : firstLevel)  // 先按包名分组
        {
            StringBuilder leafActNames = new StringBuilder();
            for (ReleaseMonitorGroup rmg : res)
            {
                if (rmgs.getPkgName() == null)
                {
                    rmgs.setPkgName("");
                }
                if (rmg.getPkgName() == null)
                {
                    rmg.setPkgName("");
                }
                if (!leafActNames.toString().contains(rmg.getActName()) && (rmg.getPkgName().equals(rmgs.getPkgName())
                        || ("包名:" + rmg.getPkgName()).equals(rmgs.getActName())))
                {
                    leafActNames.append(rmg.getActName() + ",");
                    ReleaseMonitorGroup newRmg = new ReleaseMonitorGroup();// 按步骤分组
                    newRmg.setActName(rmg.getActName());
                    newRmg.setIconCls(NOICON);
                    rmg.setLeaf(true);
                    rmg.setIconCls(NOICON);
                    newRmg.setExpanded(true);
                    for (ReleaseMonitorGroup rmgIp : res)
                    {
                        if (rmgIp.getPkgName() == null)
                        {
                            rmgIp.setPkgName("");
                        }
                        if (rmgIp.getActName().equals(rmg.getActName()) && rmgIp.getPkgName().equals(rmg.getPkgName())
                                && !newRmg.getChildren().contains(rmgIp))
                        {
                            rmgIp.setLeaf(true);
                            rmgIp.setIconCls(NOICON);
                            newRmg.getChildren().add(rmgIp);
                            newRmg.setExpanded(true);
                            newRmg.setIid(rmgIp.getSerner());
                        }
                    }
                    rmgs.getChildren().add(newRmg);// 按步骤分组 按顺序步骤排序
                    sortList(rmgs);
                }
            }
            finalList.add(rmgs);
        }
        return finalList;
    }

    private void sortList ( ReleaseMonitorGroup rmgs )
    {
        Collections.sort(rmgs.getChildren(), new Comparator<ReleaseMonitorGroup>()
        {
            public int compare ( ReleaseMonitorGroup r1, ReleaseMonitorGroup r2 )
            {
                long diff = r1.getIid() - r2.getIid();
                if (diff > 0)
                {
                    return 1;
                } else if (diff < 0)
                {
                    return -1;
                }
                return 0;
            }
        });
    }
    
    /**
     * 
     * <li>Description:保存AZ状态获取接口地址信息</li> 
     * <AUTHOR>
     * 2020年10月26日 
     * @param request
     * @param jsonData
     * @return
     * return Object
     */
    @RequestMapping("saveAzInterfaceProperties.do")
    @ResponseBody
    public Object saveAzInterfaceProperties( HttpServletRequest request, String jsonData){
        Map<String, Object> resp = new HashMap<>();
        try
        {
            boolean returnValue = false;
            JSONObject jsonObject = JSONObject.fromObject(jsonData);
            JSONArray njsArray = jsonObject.getJSONArray("interfaceData");
            for(int i=0;i<njsArray.size();i++){
                JSONObject obj = JSONObject.fromObject(njsArray.get(i));
                returnValue = AZSwitchBaseService.getInstance().savePropertiesByKey(resp,Constants.IEAI_AZ,
                    obj.getString("interfaceAdd"),obj.getString("ipropertyName"),
                    "interfaceAddressValue"+(int)(Math.random()*100000),
                    obj.getString("interfaceUser"),obj.getString("interfacePwd"),"AZ切换状态获取接口地址配置");
                if(!returnValue){
                    break;
                }
            }
            if (returnValue)
            {
                resp.put(Constants.STR_SUCCESS, true);
                resp.put(Constants.STR_MESSAGE, "保存成功！");
            } else
            {
                if(resp.isEmpty()){
                    resp.put(Constants.STR_SUCCESS, false);
                    resp.put(Constants.STR_MESSAGE, "保存失败！");
                }
            }
        } catch (Exception e)
        {
            resp.put(Constants.STR_SUCCESS, false);
            resp.put(Constants.STR_MESSAGE, e.getMessage());
        }
        return resp;
    }
    
    /**
     * 
     * <li>Description:获取AZ状态获取接口地址信息</li> 
     * <AUTHOR>
     * 2020年10月27日 
     * @param start
     * @param limit
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("getInterfaceAddress.do")
    @ResponseBody
    public Map<String,Object> getInterfaceAddProperties(int start,int limit){
        Map<String,Object> map = new HashMap<>();
        try
        {
            map = AZSwitchBaseService.getInstance().getInterfaceAddProperties(start,limit,Constants.IEAI_AZ,"interfaceAddressValue");
        } catch (Exception e)
        {
            log.error("getInterfaceAddProperties is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:删除AZ状态获取接口配置地址</li> 
     * <AUTHOR>
     * 2020年10月27日 
     * @param jsonData
     * @return
     * return Map<String,Object>
     */
    @RequestMapping("delInterfaceAddress.do")
    @ResponseBody
    public Map<String,Object> delInterfaceAddress(String jsonData){
        Map<String,Object> map = new HashMap<>(5);
        boolean flag = AZSwitchBaseService.getInstance().delInterfaceAddress(jsonData, Constants.IEAI_AZ);
        if(flag){
            map.put(Constants.SUS_SUCCESS, flag);
            map.put(Constants.SUS_MESSAGE, "删除成功！");
        }else{
            map.put(Constants.SUS_SUCCESS, flag);
            map.put(Constants.SUS_MESSAGE, "删除失败！");
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:AZ切换系统列表导出</li> 
     * <AUTHOR>
     * 2020年10月27日 
     * @param request
     * @param response
     * @param iids
     * @param azType
     * return void
     */
    @RequestMapping("exportAZSysListExcel.do")
    public void exportAZSysListExcel (HttpServletRequest request,HttpServletResponse response, String[] iids)
    {
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        azBaseService.exportAZSysListExcel(response, userInfo,iids, Constants.IEAI_AZ);
    }
    
}

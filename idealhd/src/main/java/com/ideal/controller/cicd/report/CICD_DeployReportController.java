package com.ideal.controller.cicd.report ;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.hd.cicd.flow.report.CICD_DeployReport;
import com.ideal.ieai.server.repository.hd.cicd.flow.report.CICD_DeployReportService;
import com.ideal.util.StringUtil;
import com.ideal.util.httpsclient.HttpClientUtil;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
   *
   * @ClassName: CICD_DeployReportController
   * @Description: CICD-报表管理-部署统计报表
   * @author: xingchen_song
   * @date: 2023年03月13日 21:06:36
   * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
   */
@Controller
public class CICD_DeployReportController 
{
   private static Logger _log =Logger.getLogger(CICD_DeployReportController.class);
   /**
   *
   * @Title: 
   * @Description: CICD-报表管理-部署统计报表页面的访问
   * @author: xingchen_song
   * @date: 2023年03月13日 21:06:36
   */
   @RequestMapping("accessCICD_DeployReport.do")
   public String accessCICD_DeployReport (HttpServletRequest request )
   {
      return "sus/CICD_DeployReport/CICD_DeployReport";
   }



   /**
   *
   * @Title: 
   * @Description: CICD-报表管理-部署统计报表的查询
   * @author: xingchen_song
   * @date: 2023年03月13日 21:06:36
   */
   @RequestMapping("queryCICD_DeployReport.do")
   @ResponseBody
   public Map queryCICD_DeployReport (HttpServletRequest request , CICD_DeployReport cicd_deployreport , int start , int limit )
   {
      Map map =new HashMap();

      //表示当前环境是ci还是cd   1表示cd
      String cicdConfig = Environment.getInstance().getConfig("sus.cicd.flag");
      String remoteUrl = Environment.getInstance().getConfig("rise.http.to.cd.url");
      CICD_DeployReportService cICD_DeployReportService =new CICD_DeployReportService();
      Map<String,String> resp =new LinkedHashMap<String,String>();
      String isysname = request.getParameter("isysname");
      try {
         if (!"1".equals(cicdConfig) && !StringUtil.isEmptyStr(remoteUrl)){
            String sysName = cicd_deployreport.getIsystem();
            String replaceBeginTime = cicd_deployreport.getBeginTime().replace(" ", "T");
            String replaceEndTime = cicd_deployreport.getEndTime().replace(" ", "T");
            remoteUrl =  remoteUrl + "queryCICD_DeployReport.do?page=1&start=0&limit=25&beginTime="
                    + replaceBeginTime + "&endTime=" + replaceEndTime+"&isysname="+sysName;

            String s = HttpClientUtil.doGet(1, remoteUrl, "", "UTF-8", 3000);
            map = JSON.parseObject(s,Map.class);
            return map;
         }
         cicd_deployreport.setIsystem(isysname);
         int type =Constants.IEAI_SUS;
         map=cICD_DeployReportService.queryCICD_DeployReport(cicd_deployreport, start, limit, type);
         map.put("success", true);
         map.put("message", "操作成功");
      }catch(Exception e )
      {
         e.printStackTrace();
         _log.error(e);
         map.put("success", false);
         map.put("message", "操作失败");
      }
      return map;
   }



//
//   /**
//   *
//   * @Title:
//   * @Description: CICD-报表管理-部署统计报表的保存
//   * @author: xingchen_song
//   * @date: 2023年03月13日 21:06:36
//   */
//   @RequestMapping("saveCICD_DeployReport.do")
//   @ResponseBody
//   public Map<String,Object> saveCICD_DeployReport (HttpServletRequest request , String json )
//   {
//      Map<String,Object> resp =new LinkedHashMap<String,Object>();
//      try
//      {
//         int type =Constants.IEAI_SUS;
//         List<Map<String,Object>> dataList =new ArrayList<Map<String,Object>>();
//         dataList=ParseJson.JSON2List(json);
//         CICD_DeployReportService cICD_DeployReportService =new CICD_DeployReportService();
//         cICD_DeployReportService.saveCICD_DeployReport(dataList, type);
//         resp.put("success", true);
//      }catch(Exception e )
//      {
//         e.printStackTrace();
//         _log.error(e);
//         resp.put("success", false);
//      }
//      return resp;
//   }
//
//
//
//   /**
//   *
//   * @Title:
//   * @Description: CICD-报表管理-部署统计报表的删除
//   * @author: xingchen_song
//   * @date: 2023年03月13日 21:06:36
//   */
//   @RequestMapping("deleteCICD_DeployReport.do")
//   @ResponseBody
//   public Map<String,Object> deleteCICD_DeployReport (HttpServletRequest request , String iids )
//   {
//      Map<String,Object> resp =new LinkedHashMap<String,Object>();
//      try
//      {
//         int type =Constants.IEAI_SUS;
//         CICD_DeployReportService cICD_DeployReportService =new CICD_DeployReportService();
//         cICD_DeployReportService.deleteCICD_DeployReport(iids, type);
//         resp.put("success", true);
//      }catch(Exception e )
//      {
//         e.printStackTrace();
//         _log.error(e);
//         resp.put("success", false);
//      }
//      return resp;
//   }



}
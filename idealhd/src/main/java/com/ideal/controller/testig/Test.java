package com.ideal.controller.testig;

import java.net.URI;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;

public class Test
{
    // public static String rootUrl = "http://10.0.1.126:8888/aoms/";
    // public static String postFileUrl = "";
    // public static String getTokenURL = "/infoCollection/token.do";
    // public static String postFilePath = "";
    public static Log log = LogFactory.getLog(Test.class);

    public static void main ( String[] args )
    {
        Test test = new Test();
        String token = test.getIGTokenByHttpClient("root", "rootroot");
        if (!StringUtils.isEmpty(token))
        {
            test.postIGDateByRestTemplate(token);
            test.logOut(token);
        }
    }

    public String getIGTokenByHttpClient ( String userName, String password )
    {
        String token = "";
        HttpGet httpGet = new HttpGet();
        DefaultHttpClient httpClient = new DefaultHttpClient();
        try
        {
            String authorization = getToken(userName, password);
            httpGet.addHeader("Authorization", authorization);
            // 参数转换为字符串
            String url = "http://10.0.1.30:8080/IG/rest/np/login";
            httpGet.setURI(new URI(url));
            // 执行get请求.
            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200)
            {
                HttpEntity entity = response.getEntity();
                token = EntityUtils.toString(entity);
                log.info("返回结果为 " + token);
            } else
            {
                log.error("调用获取token接口错误,错误码为:" + statusCode);
            }
        } catch (Exception ex)
        {
            log.error("调用接口上传文件错误", ex);
        } finally
        {
        }
        return token;
    }

    public void postIGDateByRestTemplate ( String token )
    {
        HttpPost httpPost = new HttpPost();
        DefaultHttpClient httpClient = new DefaultHttpClient();
        try
        {
            httpPost.addHeader("Content-Type", "Application/json");
            httpPost.addHeader("charset", "UTF-8");
            httpPost.addHeader("tokenId", token);
            httpPost.setURI(new URI("http://10.0.1.30:8080/IG/rest/np/saveTaskInfo"));
            httpPost.setEntity(new StringEntity(JSONObject.toJSONString(new ReturnEntity())));
            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200)
            {
                HttpEntity entity = response.getEntity();
                log.info("返回结果为 " + EntityUtils.toString(entity));
            } else
            {
                log.error("调用获取token接口错误,错误码为:" + statusCode);
            }
        } catch (Exception e)
        {

        } finally
        {

        }
    }

    public void logOut ( String tokenId )
    {
        HttpDelete httpPost = new HttpDelete();
        DefaultHttpClient httpClient = new DefaultHttpClient();
        try
        {
            httpPost.addHeader("Content-Type", "Application/json");
            httpPost.addHeader("charset", "UTF-8");
            httpPost.addHeader("tokenId", tokenId);
            httpPost.setURI(new URI("http://10.0.1.30:8080/IG/rest/np/logout"));
            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200)
            {
                HttpEntity entity = response.getEntity();
                log.info("返回结果为 " + EntityUtils.toString(entity));
            } else
            {
                log.error("调用获取token接口错误,错误码为:" + statusCode);
            }
        } catch (Exception e)
        {

        } finally
        {

        }

        // RestTemplate restTemplate = new RestTemplate();
        //// restTemplate.getMessageConverters().add(new )
        // HttpHeaders requestHeaders = new HttpHeaders();
        // requestHeaders.add("tokenId", token);
        // requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        // HttpEntity<String> request = new HttpEntity<>(JSONObject.toJSONString(new
        // TaskResultEntity()), requestHeaders);
        // ResponseEntity<String> response =
        // restTemplate.postForEntity("http://127.0.0.1:8080/IG/rest/np/saveTaskInfo", request,
        // String.class);
        // if(response.getStatusCode().equals(HttpStatus.OK)) {
        // System.out.println(JSONObject.toJSONString(response.getBody()));
        // }
    }

    /**
     * 获取token
     * @param username
     * @param password
     * @return
     */
    public static String getToken ( String username, String password )
    {
        String auth = username + ":" + password;
        String encodedAuth = Base64.encode(auth.getBytes());
        String authHeader = "Basic " + encodedAuth.trim();
        return authHeader;
    }
}
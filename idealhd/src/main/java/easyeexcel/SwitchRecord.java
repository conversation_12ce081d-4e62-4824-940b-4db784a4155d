package easyeexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

@ContentRowHeight(20)
@HeadRowHeight(25)
@ColumnWidth(25)
public class SwitchRecord {
    @ExcelProperty("系统名称")
    private String systemName;

    @ExcelProperty("切换人")
    private String switcher;

    @ExcelProperty("切换方向")
    private String switchDirection;

    @ExcelProperty("开始时间")
    private String startTime;

    @ExcelProperty("结束时间")
    private String endTime;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getSwitcher() {
        return switcher;
    }

    public void setSwitcher(String switcher) {
        this.switcher = switcher;
    }

    public String getSwitchDirection() {
        return switchDirection;
    }

    public void setSwitchDirection(String switchDirection) {
        this.switchDirection = switchDirection;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSwitchDuration() {
        return switchDuration;
    }

    public void setSwitchDuration(String switchDuration) {
        this.switchDuration = switchDuration;
    }

    public String getTotalSteps() {
        return totalSteps;
    }

    public void setTotalSteps(String totalSteps) {
        this.totalSteps = totalSteps;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsAbnormal() {
        return isAbnormal;
    }

    public void setIsAbnormal(String isAbnormal) {
        this.isAbnormal = isAbnormal;
    }

    @ExcelProperty("切换耗时")
    private String switchDuration;

    @ExcelProperty("全步骤数")
    private String totalSteps;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("是否异常")
    private String isAbnormal;

    // Getters and Setters
}
